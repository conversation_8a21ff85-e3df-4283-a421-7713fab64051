package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.repository;

import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity.AMSRatingTaskDO;
import io.iec.edp.caf.data.orm.DataRepository;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public interface AMSRatingTaskRepository extends DataRepository<AMSRatingTaskDO, String> {
   List<AMSRatingTaskDO> findAllByResultId(String var1);
}
