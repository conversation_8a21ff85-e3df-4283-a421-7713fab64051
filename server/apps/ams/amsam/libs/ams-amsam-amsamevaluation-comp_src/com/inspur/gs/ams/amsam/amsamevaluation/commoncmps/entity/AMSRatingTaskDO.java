package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSRATINGTASK"
)
public class AMSRatingTaskDO {
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   @Column(
      name = "SECRETLEVEL"
   )
   private String secretlevel;
   @Column(
      name = "WEIGHTRATIO"
   )
   private String weightRatio;
   @Column(
      name = "AFFILIATEDUNITDBY"
   )
   private String affiliatedUnitDBy;
   private String code;
   @Column(
      name = "RESULTID"
   )
   private String resultId;
   private String rater;
   private String name;
   private String creater_createdby;
   private Date creater_createdon;
   private String creater_lastchangedby;
   private String organizationunit;
   private String unitid;
   private String creater_lastchangedon;
   private String objecttype;
   private String evaluationobject;
   private String affiliatedunit;
   private String evaluationform;
   private Date evaluationdate;
   private String ratingstatus;
   private String totalscore;
   private String field1;
   private String field2;
   private String field3;
   private String field4;
   private String field5;
   private Date ratingdate;

   public String getId() {
      return this.id;
   }

   public String getSecretlevel() {
      return this.secretlevel;
   }

   public String getWeightRatio() {
      return this.weightRatio;
   }

   public String getAffiliatedUnitDBy() {
      return this.affiliatedUnitDBy;
   }

   public String getCode() {
      return this.code;
   }

   public String getResultId() {
      return this.resultId;
   }

   public String getRater() {
      return this.rater;
   }

   public String getName() {
      return this.name;
   }

   public String getCreater_createdby() {
      return this.creater_createdby;
   }

   public Date getCreater_createdon() {
      return this.creater_createdon;
   }

   public String getCreater_lastchangedby() {
      return this.creater_lastchangedby;
   }

   public String getOrganizationunit() {
      return this.organizationunit;
   }

   public String getUnitid() {
      return this.unitid;
   }

   public String getCreater_lastchangedon() {
      return this.creater_lastchangedon;
   }

   public String getObjecttype() {
      return this.objecttype;
   }

   public String getEvaluationobject() {
      return this.evaluationobject;
   }

   public String getAffiliatedunit() {
      return this.affiliatedunit;
   }

   public String getEvaluationform() {
      return this.evaluationform;
   }

   public Date getEvaluationdate() {
      return this.evaluationdate;
   }

   public String getRatingstatus() {
      return this.ratingstatus;
   }

   public String getTotalscore() {
      return this.totalscore;
   }

   public String getField1() {
      return this.field1;
   }

   public String getField2() {
      return this.field2;
   }

   public String getField3() {
      return this.field3;
   }

   public String getField4() {
      return this.field4;
   }

   public String getField5() {
      return this.field5;
   }

   public Date getRatingdate() {
      return this.ratingdate;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setSecretlevel(String secretlevel) {
      this.secretlevel = secretlevel;
   }

   public void setWeightRatio(String weightRatio) {
      this.weightRatio = weightRatio;
   }

   public void setAffiliatedUnitDBy(String affiliatedUnitDBy) {
      this.affiliatedUnitDBy = affiliatedUnitDBy;
   }

   public void setCode(String code) {
      this.code = code;
   }

   public void setResultId(String resultId) {
      this.resultId = resultId;
   }

   public void setRater(String rater) {
      this.rater = rater;
   }

   public void setName(String name) {
      this.name = name;
   }

   public void setCreater_createdby(String creater_createdby) {
      this.creater_createdby = creater_createdby;
   }

   public void setCreater_createdon(Date creater_createdon) {
      this.creater_createdon = creater_createdon;
   }

   public void setCreater_lastchangedby(String creater_lastchangedby) {
      this.creater_lastchangedby = creater_lastchangedby;
   }

   public void setOrganizationunit(String organizationunit) {
      this.organizationunit = organizationunit;
   }

   public void setUnitid(String unitid) {
      this.unitid = unitid;
   }

   public void setCreater_lastchangedon(String creater_lastchangedon) {
      this.creater_lastchangedon = creater_lastchangedon;
   }

   public void setObjecttype(String objecttype) {
      this.objecttype = objecttype;
   }

   public void setEvaluationobject(String evaluationobject) {
      this.evaluationobject = evaluationobject;
   }

   public void setAffiliatedunit(String affiliatedunit) {
      this.affiliatedunit = affiliatedunit;
   }

   public void setEvaluationform(String evaluationform) {
      this.evaluationform = evaluationform;
   }

   public void setEvaluationdate(Date evaluationdate) {
      this.evaluationdate = evaluationdate;
   }

   public void setRatingstatus(String ratingstatus) {
      this.ratingstatus = ratingstatus;
   }

   public void setTotalscore(String totalscore) {
      this.totalscore = totalscore;
   }

   public void setField1(String field1) {
      this.field1 = field1;
   }

   public void setField2(String field2) {
      this.field2 = field2;
   }

   public void setField3(String field3) {
      this.field3 = field3;
   }

   public void setField4(String field4) {
      this.field4 = field4;
   }

   public void setField5(String field5) {
      this.field5 = field5;
   }

   public void setRatingdate(Date ratingdate) {
      this.ratingdate = ratingdate;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSRatingTaskDO)) {
         return false;
      } else {
         AMSRatingTaskDO other = (AMSRatingTaskDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$secretlevel = this.getSecretlevel();
            Object other$secretlevel = other.getSecretlevel();
            if (this$secretlevel == null) {
               if (other$secretlevel != null) {
                  return false;
               }
            } else if (!this$secretlevel.equals(other$secretlevel)) {
               return false;
            }

            Object this$weightRatio = this.getWeightRatio();
            Object other$weightRatio = other.getWeightRatio();
            if (this$weightRatio == null) {
               if (other$weightRatio != null) {
                  return false;
               }
            } else if (!this$weightRatio.equals(other$weightRatio)) {
               return false;
            }

            Object this$affiliatedUnitDBy = this.getAffiliatedUnitDBy();
            Object other$affiliatedUnitDBy = other.getAffiliatedUnitDBy();
            if (this$affiliatedUnitDBy == null) {
               if (other$affiliatedUnitDBy != null) {
                  return false;
               }
            } else if (!this$affiliatedUnitDBy.equals(other$affiliatedUnitDBy)) {
               return false;
            }

            Object this$code = this.getCode();
            Object other$code = other.getCode();
            if (this$code == null) {
               if (other$code != null) {
                  return false;
               }
            } else if (!this$code.equals(other$code)) {
               return false;
            }

            Object this$resultId = this.getResultId();
            Object other$resultId = other.getResultId();
            if (this$resultId == null) {
               if (other$resultId != null) {
                  return false;
               }
            } else if (!this$resultId.equals(other$resultId)) {
               return false;
            }

            Object this$rater = this.getRater();
            Object other$rater = other.getRater();
            if (this$rater == null) {
               if (other$rater != null) {
                  return false;
               }
            } else if (!this$rater.equals(other$rater)) {
               return false;
            }

            Object this$name = this.getName();
            Object other$name = other.getName();
            if (this$name == null) {
               if (other$name != null) {
                  return false;
               }
            } else if (!this$name.equals(other$name)) {
               return false;
            }

            Object this$creater_createdby = this.getCreater_createdby();
            Object other$creater_createdby = other.getCreater_createdby();
            if (this$creater_createdby == null) {
               if (other$creater_createdby != null) {
                  return false;
               }
            } else if (!this$creater_createdby.equals(other$creater_createdby)) {
               return false;
            }

            Object this$creater_createdon = this.getCreater_createdon();
            Object other$creater_createdon = other.getCreater_createdon();
            if (this$creater_createdon == null) {
               if (other$creater_createdon != null) {
                  return false;
               }
            } else if (!this$creater_createdon.equals(other$creater_createdon)) {
               return false;
            }

            Object this$creater_lastchangedby = this.getCreater_lastchangedby();
            Object other$creater_lastchangedby = other.getCreater_lastchangedby();
            if (this$creater_lastchangedby == null) {
               if (other$creater_lastchangedby != null) {
                  return false;
               }
            } else if (!this$creater_lastchangedby.equals(other$creater_lastchangedby)) {
               return false;
            }

            Object this$organizationunit = this.getOrganizationunit();
            Object other$organizationunit = other.getOrganizationunit();
            if (this$organizationunit == null) {
               if (other$organizationunit != null) {
                  return false;
               }
            } else if (!this$organizationunit.equals(other$organizationunit)) {
               return false;
            }

            Object this$unitid = this.getUnitid();
            Object other$unitid = other.getUnitid();
            if (this$unitid == null) {
               if (other$unitid != null) {
                  return false;
               }
            } else if (!this$unitid.equals(other$unitid)) {
               return false;
            }

            Object this$creater_lastchangedon = this.getCreater_lastchangedon();
            Object other$creater_lastchangedon = other.getCreater_lastchangedon();
            if (this$creater_lastchangedon == null) {
               if (other$creater_lastchangedon != null) {
                  return false;
               }
            } else if (!this$creater_lastchangedon.equals(other$creater_lastchangedon)) {
               return false;
            }

            Object this$objecttype = this.getObjecttype();
            Object other$objecttype = other.getObjecttype();
            if (this$objecttype == null) {
               if (other$objecttype != null) {
                  return false;
               }
            } else if (!this$objecttype.equals(other$objecttype)) {
               return false;
            }

            Object this$evaluationobject = this.getEvaluationobject();
            Object other$evaluationobject = other.getEvaluationobject();
            if (this$evaluationobject == null) {
               if (other$evaluationobject != null) {
                  return false;
               }
            } else if (!this$evaluationobject.equals(other$evaluationobject)) {
               return false;
            }

            Object this$affiliatedunit = this.getAffiliatedunit();
            Object other$affiliatedunit = other.getAffiliatedunit();
            if (this$affiliatedunit == null) {
               if (other$affiliatedunit != null) {
                  return false;
               }
            } else if (!this$affiliatedunit.equals(other$affiliatedunit)) {
               return false;
            }

            Object this$evaluationform = this.getEvaluationform();
            Object other$evaluationform = other.getEvaluationform();
            if (this$evaluationform == null) {
               if (other$evaluationform != null) {
                  return false;
               }
            } else if (!this$evaluationform.equals(other$evaluationform)) {
               return false;
            }

            Object this$evaluationdate = this.getEvaluationdate();
            Object other$evaluationdate = other.getEvaluationdate();
            if (this$evaluationdate == null) {
               if (other$evaluationdate != null) {
                  return false;
               }
            } else if (!this$evaluationdate.equals(other$evaluationdate)) {
               return false;
            }

            Object this$ratingstatus = this.getRatingstatus();
            Object other$ratingstatus = other.getRatingstatus();
            if (this$ratingstatus == null) {
               if (other$ratingstatus != null) {
                  return false;
               }
            } else if (!this$ratingstatus.equals(other$ratingstatus)) {
               return false;
            }

            Object this$totalscore = this.getTotalscore();
            Object other$totalscore = other.getTotalscore();
            if (this$totalscore == null) {
               if (other$totalscore != null) {
                  return false;
               }
            } else if (!this$totalscore.equals(other$totalscore)) {
               return false;
            }

            Object this$field1 = this.getField1();
            Object other$field1 = other.getField1();
            if (this$field1 == null) {
               if (other$field1 != null) {
                  return false;
               }
            } else if (!this$field1.equals(other$field1)) {
               return false;
            }

            Object this$field2 = this.getField2();
            Object other$field2 = other.getField2();
            if (this$field2 == null) {
               if (other$field2 != null) {
                  return false;
               }
            } else if (!this$field2.equals(other$field2)) {
               return false;
            }

            Object this$field3 = this.getField3();
            Object other$field3 = other.getField3();
            if (this$field3 == null) {
               if (other$field3 != null) {
                  return false;
               }
            } else if (!this$field3.equals(other$field3)) {
               return false;
            }

            Object this$field4 = this.getField4();
            Object other$field4 = other.getField4();
            if (this$field4 == null) {
               if (other$field4 != null) {
                  return false;
               }
            } else if (!this$field4.equals(other$field4)) {
               return false;
            }

            Object this$field5 = this.getField5();
            Object other$field5 = other.getField5();
            if (this$field5 == null) {
               if (other$field5 != null) {
                  return false;
               }
            } else if (!this$field5.equals(other$field5)) {
               return false;
            }

            Object this$ratingdate = this.getRatingdate();
            Object other$ratingdate = other.getRatingdate();
            if (this$ratingdate == null) {
               if (other$ratingdate != null) {
                  return false;
               }
            } else if (!this$ratingdate.equals(other$ratingdate)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSRatingTaskDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $secretlevel = this.getSecretlevel();
      result = result * 59 + ($secretlevel == null ? 43 : $secretlevel.hashCode());
      Object $weightRatio = this.getWeightRatio();
      result = result * 59 + ($weightRatio == null ? 43 : $weightRatio.hashCode());
      Object $affiliatedUnitDBy = this.getAffiliatedUnitDBy();
      result = result * 59 + ($affiliatedUnitDBy == null ? 43 : $affiliatedUnitDBy.hashCode());
      Object $code = this.getCode();
      result = result * 59 + ($code == null ? 43 : $code.hashCode());
      Object $resultId = this.getResultId();
      result = result * 59 + ($resultId == null ? 43 : $resultId.hashCode());
      Object $rater = this.getRater();
      result = result * 59 + ($rater == null ? 43 : $rater.hashCode());
      Object $name = this.getName();
      result = result * 59 + ($name == null ? 43 : $name.hashCode());
      Object $creater_createdby = this.getCreater_createdby();
      result = result * 59 + ($creater_createdby == null ? 43 : $creater_createdby.hashCode());
      Object $creater_createdon = this.getCreater_createdon();
      result = result * 59 + ($creater_createdon == null ? 43 : $creater_createdon.hashCode());
      Object $creater_lastchangedby = this.getCreater_lastchangedby();
      result = result * 59 + ($creater_lastchangedby == null ? 43 : $creater_lastchangedby.hashCode());
      Object $organizationunit = this.getOrganizationunit();
      result = result * 59 + ($organizationunit == null ? 43 : $organizationunit.hashCode());
      Object $unitid = this.getUnitid();
      result = result * 59 + ($unitid == null ? 43 : $unitid.hashCode());
      Object $creater_lastchangedon = this.getCreater_lastchangedon();
      result = result * 59 + ($creater_lastchangedon == null ? 43 : $creater_lastchangedon.hashCode());
      Object $objecttype = this.getObjecttype();
      result = result * 59 + ($objecttype == null ? 43 : $objecttype.hashCode());
      Object $evaluationobject = this.getEvaluationobject();
      result = result * 59 + ($evaluationobject == null ? 43 : $evaluationobject.hashCode());
      Object $affiliatedunit = this.getAffiliatedunit();
      result = result * 59 + ($affiliatedunit == null ? 43 : $affiliatedunit.hashCode());
      Object $evaluationform = this.getEvaluationform();
      result = result * 59 + ($evaluationform == null ? 43 : $evaluationform.hashCode());
      Object $evaluationdate = this.getEvaluationdate();
      result = result * 59 + ($evaluationdate == null ? 43 : $evaluationdate.hashCode());
      Object $ratingstatus = this.getRatingstatus();
      result = result * 59 + ($ratingstatus == null ? 43 : $ratingstatus.hashCode());
      Object $totalscore = this.getTotalscore();
      result = result * 59 + ($totalscore == null ? 43 : $totalscore.hashCode());
      Object $field1 = this.getField1();
      result = result * 59 + ($field1 == null ? 43 : $field1.hashCode());
      Object $field2 = this.getField2();
      result = result * 59 + ($field2 == null ? 43 : $field2.hashCode());
      Object $field3 = this.getField3();
      result = result * 59 + ($field3 == null ? 43 : $field3.hashCode());
      Object $field4 = this.getField4();
      result = result * 59 + ($field4 == null ? 43 : $field4.hashCode());
      Object $field5 = this.getField5();
      result = result * 59 + ($field5 == null ? 43 : $field5.hashCode());
      Object $ratingdate = this.getRatingdate();
      result = result * 59 + ($ratingdate == null ? 43 : $ratingdate.hashCode());
      return result;
   }

   public String toString() {
      return "AMSRatingTaskDO(id=" + this.getId() + ", secretlevel=" + this.getSecretlevel() + ", weightRatio=" + this.getWeightRatio() + ", affiliatedUnitDBy=" + this.getAffiliatedUnitDBy() + ", code=" + this.getCode() + ", resultId=" + this.getResultId() + ", rater=" + this.getRater() + ", name=" + this.getName() + ", creater_createdby=" + this.getCreater_createdby() + ", creater_createdon=" + this.getCreater_createdon() + ", creater_lastchangedby=" + this.getCreater_lastchangedby() + ", organizationunit=" + this.getOrganizationunit() + ", unitid=" + this.getUnitid() + ", creater_lastchangedon=" + this.getCreater_lastchangedon() + ", objecttype=" + this.getObjecttype() + ", evaluationobject=" + this.getEvaluationobject() + ", affiliatedunit=" + this.getAffiliatedunit() + ", evaluationform=" + this.getEvaluationform() + ", evaluationdate=" + this.getEvaluationdate() + ", ratingstatus=" + this.getRatingstatus() + ", totalscore=" + this.getTotalscore() + ", field1=" + this.getField1() + ", field2=" + this.getField2() + ", field3=" + this.getField3() + ", field4=" + this.getField4() + ", field5=" + this.getField5() + ", ratingdate=" + this.getRatingdate() + ")";
   }

   public AMSRatingTaskDO(String id, String secretlevel, String weightRatio, String affiliatedUnitDBy, String code, String resultId, String rater, String name, String creater_createdby, Date creater_createdon, String creater_lastchangedby, String organizationunit, String unitid, String creater_lastchangedon, String objecttype, String evaluationobject, String affiliatedunit, String evaluationform, Date evaluationdate, String ratingstatus, String totalscore, String field1, String field2, String field3, String field4, String field5, Date ratingdate) {
      this.id = id;
      this.secretlevel = secretlevel;
      this.weightRatio = weightRatio;
      this.affiliatedUnitDBy = affiliatedUnitDBy;
      this.code = code;
      this.resultId = resultId;
      this.rater = rater;
      this.name = name;
      this.creater_createdby = creater_createdby;
      this.creater_createdon = creater_createdon;
      this.creater_lastchangedby = creater_lastchangedby;
      this.organizationunit = organizationunit;
      this.unitid = unitid;
      this.creater_lastchangedon = creater_lastchangedon;
      this.objecttype = objecttype;
      this.evaluationobject = evaluationobject;
      this.affiliatedunit = affiliatedunit;
      this.evaluationform = evaluationform;
      this.evaluationdate = evaluationdate;
      this.ratingstatus = ratingstatus;
      this.totalscore = totalscore;
      this.field1 = field1;
      this.field2 = field2;
      this.field3 = field3;
      this.field4 = field4;
      this.field5 = field5;
      this.ratingdate = ratingdate;
   }

   public AMSRatingTaskDO() {
   }
}
