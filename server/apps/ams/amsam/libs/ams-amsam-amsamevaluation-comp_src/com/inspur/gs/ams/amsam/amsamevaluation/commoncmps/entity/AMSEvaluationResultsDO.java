package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSEVALUATIONRESULTS"
)
public class AMSEvaluationResultsDO {
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   private String assessmentlevel;
   @Column(
      name = "SECLEVEL"
   )
   private String seclevel;
   private String objecttype;
   private String evaluationobject;
   private String affiliatedunit;
   private String evaluationform;
   private Date evaluationdate;
   private String evaluationformstatus;
   private String ratingdate;
   private String totalscore;
   @Column(
      name = "STAUS"
   )
   private String staus;
   private String field1;
   private String field2;
   private String field3;
   private String field4;
   private String field5;
   private String creater_createdby;
   private Date creater_createdon;
   private String creater_lastchangedby;
   private String organizationalunit;
   private String creater_lastchangedon;

   public String getId() {
      return this.id;
   }

   public String getAssessmentlevel() {
      return this.assessmentlevel;
   }

   public String getSeclevel() {
      return this.seclevel;
   }

   public String getObjecttype() {
      return this.objecttype;
   }

   public String getEvaluationobject() {
      return this.evaluationobject;
   }

   public String getAffiliatedunit() {
      return this.affiliatedunit;
   }

   public String getEvaluationform() {
      return this.evaluationform;
   }

   public Date getEvaluationdate() {
      return this.evaluationdate;
   }

   public String getEvaluationformstatus() {
      return this.evaluationformstatus;
   }

   public String getRatingdate() {
      return this.ratingdate;
   }

   public String getTotalscore() {
      return this.totalscore;
   }

   public String getStaus() {
      return this.staus;
   }

   public String getField1() {
      return this.field1;
   }

   public String getField2() {
      return this.field2;
   }

   public String getField3() {
      return this.field3;
   }

   public String getField4() {
      return this.field4;
   }

   public String getField5() {
      return this.field5;
   }

   public String getCreater_createdby() {
      return this.creater_createdby;
   }

   public Date getCreater_createdon() {
      return this.creater_createdon;
   }

   public String getCreater_lastchangedby() {
      return this.creater_lastchangedby;
   }

   public String getOrganizationalunit() {
      return this.organizationalunit;
   }

   public String getCreater_lastchangedon() {
      return this.creater_lastchangedon;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setAssessmentlevel(String assessmentlevel) {
      this.assessmentlevel = assessmentlevel;
   }

   public void setSeclevel(String seclevel) {
      this.seclevel = seclevel;
   }

   public void setObjecttype(String objecttype) {
      this.objecttype = objecttype;
   }

   public void setEvaluationobject(String evaluationobject) {
      this.evaluationobject = evaluationobject;
   }

   public void setAffiliatedunit(String affiliatedunit) {
      this.affiliatedunit = affiliatedunit;
   }

   public void setEvaluationform(String evaluationform) {
      this.evaluationform = evaluationform;
   }

   public void setEvaluationdate(Date evaluationdate) {
      this.evaluationdate = evaluationdate;
   }

   public void setEvaluationformstatus(String evaluationformstatus) {
      this.evaluationformstatus = evaluationformstatus;
   }

   public void setRatingdate(String ratingdate) {
      this.ratingdate = ratingdate;
   }

   public void setTotalscore(String totalscore) {
      this.totalscore = totalscore;
   }

   public void setStaus(String staus) {
      this.staus = staus;
   }

   public void setField1(String field1) {
      this.field1 = field1;
   }

   public void setField2(String field2) {
      this.field2 = field2;
   }

   public void setField3(String field3) {
      this.field3 = field3;
   }

   public void setField4(String field4) {
      this.field4 = field4;
   }

   public void setField5(String field5) {
      this.field5 = field5;
   }

   public void setCreater_createdby(String creater_createdby) {
      this.creater_createdby = creater_createdby;
   }

   public void setCreater_createdon(Date creater_createdon) {
      this.creater_createdon = creater_createdon;
   }

   public void setCreater_lastchangedby(String creater_lastchangedby) {
      this.creater_lastchangedby = creater_lastchangedby;
   }

   public void setOrganizationalunit(String organizationalunit) {
      this.organizationalunit = organizationalunit;
   }

   public void setCreater_lastchangedon(String creater_lastchangedon) {
      this.creater_lastchangedon = creater_lastchangedon;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSEvaluationResultsDO)) {
         return false;
      } else {
         AMSEvaluationResultsDO other = (AMSEvaluationResultsDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$assessmentlevel = this.getAssessmentlevel();
            Object other$assessmentlevel = other.getAssessmentlevel();
            if (this$assessmentlevel == null) {
               if (other$assessmentlevel != null) {
                  return false;
               }
            } else if (!this$assessmentlevel.equals(other$assessmentlevel)) {
               return false;
            }

            Object this$seclevel = this.getSeclevel();
            Object other$seclevel = other.getSeclevel();
            if (this$seclevel == null) {
               if (other$seclevel != null) {
                  return false;
               }
            } else if (!this$seclevel.equals(other$seclevel)) {
               return false;
            }

            Object this$objecttype = this.getObjecttype();
            Object other$objecttype = other.getObjecttype();
            if (this$objecttype == null) {
               if (other$objecttype != null) {
                  return false;
               }
            } else if (!this$objecttype.equals(other$objecttype)) {
               return false;
            }

            Object this$evaluationobject = this.getEvaluationobject();
            Object other$evaluationobject = other.getEvaluationobject();
            if (this$evaluationobject == null) {
               if (other$evaluationobject != null) {
                  return false;
               }
            } else if (!this$evaluationobject.equals(other$evaluationobject)) {
               return false;
            }

            Object this$affiliatedunit = this.getAffiliatedunit();
            Object other$affiliatedunit = other.getAffiliatedunit();
            if (this$affiliatedunit == null) {
               if (other$affiliatedunit != null) {
                  return false;
               }
            } else if (!this$affiliatedunit.equals(other$affiliatedunit)) {
               return false;
            }

            Object this$evaluationform = this.getEvaluationform();
            Object other$evaluationform = other.getEvaluationform();
            if (this$evaluationform == null) {
               if (other$evaluationform != null) {
                  return false;
               }
            } else if (!this$evaluationform.equals(other$evaluationform)) {
               return false;
            }

            Object this$evaluationdate = this.getEvaluationdate();
            Object other$evaluationdate = other.getEvaluationdate();
            if (this$evaluationdate == null) {
               if (other$evaluationdate != null) {
                  return false;
               }
            } else if (!this$evaluationdate.equals(other$evaluationdate)) {
               return false;
            }

            Object this$evaluationformstatus = this.getEvaluationformstatus();
            Object other$evaluationformstatus = other.getEvaluationformstatus();
            if (this$evaluationformstatus == null) {
               if (other$evaluationformstatus != null) {
                  return false;
               }
            } else if (!this$evaluationformstatus.equals(other$evaluationformstatus)) {
               return false;
            }

            Object this$ratingdate = this.getRatingdate();
            Object other$ratingdate = other.getRatingdate();
            if (this$ratingdate == null) {
               if (other$ratingdate != null) {
                  return false;
               }
            } else if (!this$ratingdate.equals(other$ratingdate)) {
               return false;
            }

            Object this$totalscore = this.getTotalscore();
            Object other$totalscore = other.getTotalscore();
            if (this$totalscore == null) {
               if (other$totalscore != null) {
                  return false;
               }
            } else if (!this$totalscore.equals(other$totalscore)) {
               return false;
            }

            Object this$staus = this.getStaus();
            Object other$staus = other.getStaus();
            if (this$staus == null) {
               if (other$staus != null) {
                  return false;
               }
            } else if (!this$staus.equals(other$staus)) {
               return false;
            }

            Object this$field1 = this.getField1();
            Object other$field1 = other.getField1();
            if (this$field1 == null) {
               if (other$field1 != null) {
                  return false;
               }
            } else if (!this$field1.equals(other$field1)) {
               return false;
            }

            Object this$field2 = this.getField2();
            Object other$field2 = other.getField2();
            if (this$field2 == null) {
               if (other$field2 != null) {
                  return false;
               }
            } else if (!this$field2.equals(other$field2)) {
               return false;
            }

            Object this$field3 = this.getField3();
            Object other$field3 = other.getField3();
            if (this$field3 == null) {
               if (other$field3 != null) {
                  return false;
               }
            } else if (!this$field3.equals(other$field3)) {
               return false;
            }

            Object this$field4 = this.getField4();
            Object other$field4 = other.getField4();
            if (this$field4 == null) {
               if (other$field4 != null) {
                  return false;
               }
            } else if (!this$field4.equals(other$field4)) {
               return false;
            }

            Object this$field5 = this.getField5();
            Object other$field5 = other.getField5();
            if (this$field5 == null) {
               if (other$field5 != null) {
                  return false;
               }
            } else if (!this$field5.equals(other$field5)) {
               return false;
            }

            Object this$creater_createdby = this.getCreater_createdby();
            Object other$creater_createdby = other.getCreater_createdby();
            if (this$creater_createdby == null) {
               if (other$creater_createdby != null) {
                  return false;
               }
            } else if (!this$creater_createdby.equals(other$creater_createdby)) {
               return false;
            }

            Object this$creater_createdon = this.getCreater_createdon();
            Object other$creater_createdon = other.getCreater_createdon();
            if (this$creater_createdon == null) {
               if (other$creater_createdon != null) {
                  return false;
               }
            } else if (!this$creater_createdon.equals(other$creater_createdon)) {
               return false;
            }

            Object this$creater_lastchangedby = this.getCreater_lastchangedby();
            Object other$creater_lastchangedby = other.getCreater_lastchangedby();
            if (this$creater_lastchangedby == null) {
               if (other$creater_lastchangedby != null) {
                  return false;
               }
            } else if (!this$creater_lastchangedby.equals(other$creater_lastchangedby)) {
               return false;
            }

            Object this$organizationalunit = this.getOrganizationalunit();
            Object other$organizationalunit = other.getOrganizationalunit();
            if (this$organizationalunit == null) {
               if (other$organizationalunit != null) {
                  return false;
               }
            } else if (!this$organizationalunit.equals(other$organizationalunit)) {
               return false;
            }

            Object this$creater_lastchangedon = this.getCreater_lastchangedon();
            Object other$creater_lastchangedon = other.getCreater_lastchangedon();
            if (this$creater_lastchangedon == null) {
               if (other$creater_lastchangedon != null) {
                  return false;
               }
            } else if (!this$creater_lastchangedon.equals(other$creater_lastchangedon)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSEvaluationResultsDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $assessmentlevel = this.getAssessmentlevel();
      result = result * 59 + ($assessmentlevel == null ? 43 : $assessmentlevel.hashCode());
      Object $seclevel = this.getSeclevel();
      result = result * 59 + ($seclevel == null ? 43 : $seclevel.hashCode());
      Object $objecttype = this.getObjecttype();
      result = result * 59 + ($objecttype == null ? 43 : $objecttype.hashCode());
      Object $evaluationobject = this.getEvaluationobject();
      result = result * 59 + ($evaluationobject == null ? 43 : $evaluationobject.hashCode());
      Object $affiliatedunit = this.getAffiliatedunit();
      result = result * 59 + ($affiliatedunit == null ? 43 : $affiliatedunit.hashCode());
      Object $evaluationform = this.getEvaluationform();
      result = result * 59 + ($evaluationform == null ? 43 : $evaluationform.hashCode());
      Object $evaluationdate = this.getEvaluationdate();
      result = result * 59 + ($evaluationdate == null ? 43 : $evaluationdate.hashCode());
      Object $evaluationformstatus = this.getEvaluationformstatus();
      result = result * 59 + ($evaluationformstatus == null ? 43 : $evaluationformstatus.hashCode());
      Object $ratingdate = this.getRatingdate();
      result = result * 59 + ($ratingdate == null ? 43 : $ratingdate.hashCode());
      Object $totalscore = this.getTotalscore();
      result = result * 59 + ($totalscore == null ? 43 : $totalscore.hashCode());
      Object $staus = this.getStaus();
      result = result * 59 + ($staus == null ? 43 : $staus.hashCode());
      Object $field1 = this.getField1();
      result = result * 59 + ($field1 == null ? 43 : $field1.hashCode());
      Object $field2 = this.getField2();
      result = result * 59 + ($field2 == null ? 43 : $field2.hashCode());
      Object $field3 = this.getField3();
      result = result * 59 + ($field3 == null ? 43 : $field3.hashCode());
      Object $field4 = this.getField4();
      result = result * 59 + ($field4 == null ? 43 : $field4.hashCode());
      Object $field5 = this.getField5();
      result = result * 59 + ($field5 == null ? 43 : $field5.hashCode());
      Object $creater_createdby = this.getCreater_createdby();
      result = result * 59 + ($creater_createdby == null ? 43 : $creater_createdby.hashCode());
      Object $creater_createdon = this.getCreater_createdon();
      result = result * 59 + ($creater_createdon == null ? 43 : $creater_createdon.hashCode());
      Object $creater_lastchangedby = this.getCreater_lastchangedby();
      result = result * 59 + ($creater_lastchangedby == null ? 43 : $creater_lastchangedby.hashCode());
      Object $organizationalunit = this.getOrganizationalunit();
      result = result * 59 + ($organizationalunit == null ? 43 : $organizationalunit.hashCode());
      Object $creater_lastchangedon = this.getCreater_lastchangedon();
      result = result * 59 + ($creater_lastchangedon == null ? 43 : $creater_lastchangedon.hashCode());
      return result;
   }

   public String toString() {
      return "AMSEvaluationResultsDO(id=" + this.getId() + ", assessmentlevel=" + this.getAssessmentlevel() + ", seclevel=" + this.getSeclevel() + ", objecttype=" + this.getObjecttype() + ", evaluationobject=" + this.getEvaluationobject() + ", affiliatedunit=" + this.getAffiliatedunit() + ", evaluationform=" + this.getEvaluationform() + ", evaluationdate=" + this.getEvaluationdate() + ", evaluationformstatus=" + this.getEvaluationformstatus() + ", ratingdate=" + this.getRatingdate() + ", totalscore=" + this.getTotalscore() + ", staus=" + this.getStaus() + ", field1=" + this.getField1() + ", field2=" + this.getField2() + ", field3=" + this.getField3() + ", field4=" + this.getField4() + ", field5=" + this.getField5() + ", creater_createdby=" + this.getCreater_createdby() + ", creater_createdon=" + this.getCreater_createdon() + ", creater_lastchangedby=" + this.getCreater_lastchangedby() + ", organizationalunit=" + this.getOrganizationalunit() + ", creater_lastchangedon=" + this.getCreater_lastchangedon() + ")";
   }

   public AMSEvaluationResultsDO(String id, String assessmentlevel, String seclevel, String objecttype, String evaluationobject, String affiliatedunit, String evaluationform, Date evaluationdate, String evaluationformstatus, String ratingdate, String totalscore, String staus, String field1, String field2, String field3, String field4, String field5, String creater_createdby, Date creater_createdon, String creater_lastchangedby, String organizationalunit, String creater_lastchangedon) {
      this.id = id;
      this.assessmentlevel = assessmentlevel;
      this.seclevel = seclevel;
      this.objecttype = objecttype;
      this.evaluationobject = evaluationobject;
      this.affiliatedunit = affiliatedunit;
      this.evaluationform = evaluationform;
      this.evaluationdate = evaluationdate;
      this.evaluationformstatus = evaluationformstatus;
      this.ratingdate = ratingdate;
      this.totalscore = totalscore;
      this.staus = staus;
      this.field1 = field1;
      this.field2 = field2;
      this.field3 = field3;
      this.field4 = field4;
      this.field5 = field5;
      this.creater_createdby = creater_createdby;
      this.creater_createdon = creater_createdon;
      this.creater_lastchangedby = creater_lastchangedby;
      this.organizationalunit = organizationalunit;
      this.creater_lastchangedon = creater_lastchangedon;
   }

   public AMSEvaluationResultsDO() {
   }
}
