package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSEVALUATIONDOCUMENTS"
)
public class AMSEvaluationDocumentsDO {
   @Column(
      name = "EVALUATIONNAME"
   )
   private String evaluationname;
   @Column(
      name = "SECLEVEL"
   )
   private String seclevel;
   @Column(
      name = "EVALUATIONPLANID"
   )
   private String evaluationplanid;
   @Column(
      name = "AUTOMATICTASKS"
   )
   private String automatictasks;
   @Column(
      name = "STATUS"
   )
   private String status;
   @Column(
      name = "NOTES"
   )
   private String notes;
   @Column(
      name = "EVALUATIONCOUNT"
   )
   private String evaluationcount;
   @Column(
      name = "CREATOR"
   )
   private String creator;
   @Column(
      name = "CREATDATE"
   )
   private Date creatdate;
   @Column(
      name = "CHANGEOR"
   )
   private String changeor;
   @Column(
      name = "CHANGEDATE"
   )
   private Date changedate;
   private String field1;
   private String field2;
   private String field3;
   private String field4;
   @Column(
      name = "ORGANIZATIONALUNIT"
   )
   private String organizationalunit;
   private String field5;
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   private String approvalid;
   @Column(
      name = "EVALUATIONTYPE"
   )
   private String evaluationtype;

   public String getEvaluationname() {
      return this.evaluationname;
   }

   public String getSeclevel() {
      return this.seclevel;
   }

   public String getEvaluationplanid() {
      return this.evaluationplanid;
   }

   public String getAutomatictasks() {
      return this.automatictasks;
   }

   public String getStatus() {
      return this.status;
   }

   public String getNotes() {
      return this.notes;
   }

   public String getEvaluationcount() {
      return this.evaluationcount;
   }

   public String getCreator() {
      return this.creator;
   }

   public Date getCreatdate() {
      return this.creatdate;
   }

   public String getChangeor() {
      return this.changeor;
   }

   public Date getChangedate() {
      return this.changedate;
   }

   public String getField1() {
      return this.field1;
   }

   public String getField2() {
      return this.field2;
   }

   public String getField3() {
      return this.field3;
   }

   public String getField4() {
      return this.field4;
   }

   public String getOrganizationalunit() {
      return this.organizationalunit;
   }

   public String getField5() {
      return this.field5;
   }

   public String getId() {
      return this.id;
   }

   public String getApprovalid() {
      return this.approvalid;
   }

   public String getEvaluationtype() {
      return this.evaluationtype;
   }

   public void setEvaluationname(String evaluationname) {
      this.evaluationname = evaluationname;
   }

   public void setSeclevel(String seclevel) {
      this.seclevel = seclevel;
   }

   public void setEvaluationplanid(String evaluationplanid) {
      this.evaluationplanid = evaluationplanid;
   }

   public void setAutomatictasks(String automatictasks) {
      this.automatictasks = automatictasks;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   public void setNotes(String notes) {
      this.notes = notes;
   }

   public void setEvaluationcount(String evaluationcount) {
      this.evaluationcount = evaluationcount;
   }

   public void setCreator(String creator) {
      this.creator = creator;
   }

   public void setCreatdate(Date creatdate) {
      this.creatdate = creatdate;
   }

   public void setChangeor(String changeor) {
      this.changeor = changeor;
   }

   public void setChangedate(Date changedate) {
      this.changedate = changedate;
   }

   public void setField1(String field1) {
      this.field1 = field1;
   }

   public void setField2(String field2) {
      this.field2 = field2;
   }

   public void setField3(String field3) {
      this.field3 = field3;
   }

   public void setField4(String field4) {
      this.field4 = field4;
   }

   public void setOrganizationalunit(String organizationalunit) {
      this.organizationalunit = organizationalunit;
   }

   public void setField5(String field5) {
      this.field5 = field5;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setApprovalid(String approvalid) {
      this.approvalid = approvalid;
   }

   public void setEvaluationtype(String evaluationtype) {
      this.evaluationtype = evaluationtype;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSEvaluationDocumentsDO)) {
         return false;
      } else {
         AMSEvaluationDocumentsDO other = (AMSEvaluationDocumentsDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$evaluationname = this.getEvaluationname();
            Object other$evaluationname = other.getEvaluationname();
            if (this$evaluationname == null) {
               if (other$evaluationname != null) {
                  return false;
               }
            } else if (!this$evaluationname.equals(other$evaluationname)) {
               return false;
            }

            Object this$seclevel = this.getSeclevel();
            Object other$seclevel = other.getSeclevel();
            if (this$seclevel == null) {
               if (other$seclevel != null) {
                  return false;
               }
            } else if (!this$seclevel.equals(other$seclevel)) {
               return false;
            }

            Object this$evaluationplanid = this.getEvaluationplanid();
            Object other$evaluationplanid = other.getEvaluationplanid();
            if (this$evaluationplanid == null) {
               if (other$evaluationplanid != null) {
                  return false;
               }
            } else if (!this$evaluationplanid.equals(other$evaluationplanid)) {
               return false;
            }

            Object this$automatictasks = this.getAutomatictasks();
            Object other$automatictasks = other.getAutomatictasks();
            if (this$automatictasks == null) {
               if (other$automatictasks != null) {
                  return false;
               }
            } else if (!this$automatictasks.equals(other$automatictasks)) {
               return false;
            }

            Object this$status = this.getStatus();
            Object other$status = other.getStatus();
            if (this$status == null) {
               if (other$status != null) {
                  return false;
               }
            } else if (!this$status.equals(other$status)) {
               return false;
            }

            Object this$notes = this.getNotes();
            Object other$notes = other.getNotes();
            if (this$notes == null) {
               if (other$notes != null) {
                  return false;
               }
            } else if (!this$notes.equals(other$notes)) {
               return false;
            }

            Object this$evaluationcount = this.getEvaluationcount();
            Object other$evaluationcount = other.getEvaluationcount();
            if (this$evaluationcount == null) {
               if (other$evaluationcount != null) {
                  return false;
               }
            } else if (!this$evaluationcount.equals(other$evaluationcount)) {
               return false;
            }

            Object this$creator = this.getCreator();
            Object other$creator = other.getCreator();
            if (this$creator == null) {
               if (other$creator != null) {
                  return false;
               }
            } else if (!this$creator.equals(other$creator)) {
               return false;
            }

            Object this$creatdate = this.getCreatdate();
            Object other$creatdate = other.getCreatdate();
            if (this$creatdate == null) {
               if (other$creatdate != null) {
                  return false;
               }
            } else if (!this$creatdate.equals(other$creatdate)) {
               return false;
            }

            Object this$changeor = this.getChangeor();
            Object other$changeor = other.getChangeor();
            if (this$changeor == null) {
               if (other$changeor != null) {
                  return false;
               }
            } else if (!this$changeor.equals(other$changeor)) {
               return false;
            }

            Object this$changedate = this.getChangedate();
            Object other$changedate = other.getChangedate();
            if (this$changedate == null) {
               if (other$changedate != null) {
                  return false;
               }
            } else if (!this$changedate.equals(other$changedate)) {
               return false;
            }

            Object this$field1 = this.getField1();
            Object other$field1 = other.getField1();
            if (this$field1 == null) {
               if (other$field1 != null) {
                  return false;
               }
            } else if (!this$field1.equals(other$field1)) {
               return false;
            }

            Object this$field2 = this.getField2();
            Object other$field2 = other.getField2();
            if (this$field2 == null) {
               if (other$field2 != null) {
                  return false;
               }
            } else if (!this$field2.equals(other$field2)) {
               return false;
            }

            Object this$field3 = this.getField3();
            Object other$field3 = other.getField3();
            if (this$field3 == null) {
               if (other$field3 != null) {
                  return false;
               }
            } else if (!this$field3.equals(other$field3)) {
               return false;
            }

            Object this$field4 = this.getField4();
            Object other$field4 = other.getField4();
            if (this$field4 == null) {
               if (other$field4 != null) {
                  return false;
               }
            } else if (!this$field4.equals(other$field4)) {
               return false;
            }

            Object this$organizationalunit = this.getOrganizationalunit();
            Object other$organizationalunit = other.getOrganizationalunit();
            if (this$organizationalunit == null) {
               if (other$organizationalunit != null) {
                  return false;
               }
            } else if (!this$organizationalunit.equals(other$organizationalunit)) {
               return false;
            }

            Object this$field5 = this.getField5();
            Object other$field5 = other.getField5();
            if (this$field5 == null) {
               if (other$field5 != null) {
                  return false;
               }
            } else if (!this$field5.equals(other$field5)) {
               return false;
            }

            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$approvalid = this.getApprovalid();
            Object other$approvalid = other.getApprovalid();
            if (this$approvalid == null) {
               if (other$approvalid != null) {
                  return false;
               }
            } else if (!this$approvalid.equals(other$approvalid)) {
               return false;
            }

            Object this$evaluationtype = this.getEvaluationtype();
            Object other$evaluationtype = other.getEvaluationtype();
            if (this$evaluationtype == null) {
               if (other$evaluationtype != null) {
                  return false;
               }
            } else if (!this$evaluationtype.equals(other$evaluationtype)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSEvaluationDocumentsDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $evaluationname = this.getEvaluationname();
      result = result * 59 + ($evaluationname == null ? 43 : $evaluationname.hashCode());
      Object $seclevel = this.getSeclevel();
      result = result * 59 + ($seclevel == null ? 43 : $seclevel.hashCode());
      Object $evaluationplanid = this.getEvaluationplanid();
      result = result * 59 + ($evaluationplanid == null ? 43 : $evaluationplanid.hashCode());
      Object $automatictasks = this.getAutomatictasks();
      result = result * 59 + ($automatictasks == null ? 43 : $automatictasks.hashCode());
      Object $status = this.getStatus();
      result = result * 59 + ($status == null ? 43 : $status.hashCode());
      Object $notes = this.getNotes();
      result = result * 59 + ($notes == null ? 43 : $notes.hashCode());
      Object $evaluationcount = this.getEvaluationcount();
      result = result * 59 + ($evaluationcount == null ? 43 : $evaluationcount.hashCode());
      Object $creator = this.getCreator();
      result = result * 59 + ($creator == null ? 43 : $creator.hashCode());
      Object $creatdate = this.getCreatdate();
      result = result * 59 + ($creatdate == null ? 43 : $creatdate.hashCode());
      Object $changeor = this.getChangeor();
      result = result * 59 + ($changeor == null ? 43 : $changeor.hashCode());
      Object $changedate = this.getChangedate();
      result = result * 59 + ($changedate == null ? 43 : $changedate.hashCode());
      Object $field1 = this.getField1();
      result = result * 59 + ($field1 == null ? 43 : $field1.hashCode());
      Object $field2 = this.getField2();
      result = result * 59 + ($field2 == null ? 43 : $field2.hashCode());
      Object $field3 = this.getField3();
      result = result * 59 + ($field3 == null ? 43 : $field3.hashCode());
      Object $field4 = this.getField4();
      result = result * 59 + ($field4 == null ? 43 : $field4.hashCode());
      Object $organizationalunit = this.getOrganizationalunit();
      result = result * 59 + ($organizationalunit == null ? 43 : $organizationalunit.hashCode());
      Object $field5 = this.getField5();
      result = result * 59 + ($field5 == null ? 43 : $field5.hashCode());
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $approvalid = this.getApprovalid();
      result = result * 59 + ($approvalid == null ? 43 : $approvalid.hashCode());
      Object $evaluationtype = this.getEvaluationtype();
      result = result * 59 + ($evaluationtype == null ? 43 : $evaluationtype.hashCode());
      return result;
   }

   public String toString() {
      return "AMSEvaluationDocumentsDO(evaluationname=" + this.getEvaluationname() + ", seclevel=" + this.getSeclevel() + ", evaluationplanid=" + this.getEvaluationplanid() + ", automatictasks=" + this.getAutomatictasks() + ", status=" + this.getStatus() + ", notes=" + this.getNotes() + ", evaluationcount=" + this.getEvaluationcount() + ", creator=" + this.getCreator() + ", creatdate=" + this.getCreatdate() + ", changeor=" + this.getChangeor() + ", changedate=" + this.getChangedate() + ", field1=" + this.getField1() + ", field2=" + this.getField2() + ", field3=" + this.getField3() + ", field4=" + this.getField4() + ", organizationalunit=" + this.getOrganizationalunit() + ", field5=" + this.getField5() + ", id=" + this.getId() + ", approvalid=" + this.getApprovalid() + ", evaluationtype=" + this.getEvaluationtype() + ")";
   }

   public AMSEvaluationDocumentsDO(String evaluationname, String seclevel, String evaluationplanid, String automatictasks, String status, String notes, String evaluationcount, String creator, Date creatdate, String changeor, Date changedate, String field1, String field2, String field3, String field4, String organizationalunit, String field5, String id, String approvalid, String evaluationtype) {
      this.evaluationname = evaluationname;
      this.seclevel = seclevel;
      this.evaluationplanid = evaluationplanid;
      this.automatictasks = automatictasks;
      this.status = status;
      this.notes = notes;
      this.evaluationcount = evaluationcount;
      this.creator = creator;
      this.creatdate = creatdate;
      this.changeor = changeor;
      this.changedate = changedate;
      this.field1 = field1;
      this.field2 = field2;
      this.field3 = field3;
      this.field4 = field4;
      this.organizationalunit = organizationalunit;
      this.field5 = field5;
      this.id = id;
      this.approvalid = approvalid;
      this.evaluationtype = evaluationtype;
   }

   public AMSEvaluationDocumentsDO() {
   }
}
