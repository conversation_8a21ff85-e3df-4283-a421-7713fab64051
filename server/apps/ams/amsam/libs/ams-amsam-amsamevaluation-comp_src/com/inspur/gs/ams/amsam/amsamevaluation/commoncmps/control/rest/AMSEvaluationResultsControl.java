package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest;

import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.edp.svc.message.platform.api.ICommonMessageService;
import com.inspur.edp.svc.message.platform.api.IMsgTemplateService;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity.AMSRatingTaskDO;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.repository.AMSRatingTaskRepository;
import com.inspur.gs.ams.amsbd.audititemcategory.AMSRuntimeException;
import com.inspur.gs.ams.amsbd.audititemcategory.amsResult;
import com.inspur.gs.ams.amsbd.audititemcategory.control.rest.AMSOrgControl;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.core.session.WebSession;
import io.iec.edp.caf.message.api.CAFMessage;
import io.iec.edp.caf.message.api.entity.MsgScope;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Path("/amsEvaluationResultsControl")
@Produces({"application/json"})
@Consumes({"application/json"})
@RestController
@RequestMapping({"/ams/amsam/v1.0/amsamevaluation/amsEvaluationResultsControl"})
public class AMSEvaluationResultsControl {
   @Autowired
   AMSRatingTaskRepository amsRatingTaskRepository;
   private IBqlExecuter bqlExecuter;
   @Resource
   private AMSOrgControl amsOrgControl;

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   @PostMapping({"/level"})
   @POST
   @Path("/level")
   public amsResult level(@RequestBody Map map) {
      Map<String, Object> orgInfo = (Map)this.amsOrgControl.getOrgByUserId(CAFContext.current.getUserId()).getData();
      ArrayList<String> planIDs = new ArrayList();
      IDbParameter[] selParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("unitid", orgInfo.get("id"))};
      Integer score = (Integer)map.get("fraction1");
      Boolean havekp = false;
      Map<String, String> map1 = new HashMap();
      String sqlSelect = "SELECT ID,CODE,NAME,STARTINGVALUE,TERMINATIONVALUE FROM AMSEVALUATIONLEVEL WHERE AMSEVALUATIONLEVEL.UNITID = :unitid AND AMSEVALUATIONLEVEL.STATUS='1'";
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      List<DynamicResultRow> resultRows = this.bqlExecuter.executeSelectStatement(sqlSelect, planIDs, selParams);
      if (CollectionUtils.isNotEmpty(resultRows)) {
         for(int i = 0; i < resultRows.size(); ++i) {
            if (score.compareTo(Integer.parseInt(((DynamicResultRow)resultRows.get(i)).get("STARTINGVALUE").toString())) >= 0 && score.compareTo(Integer.parseInt(((DynamicResultRow)resultRows.get(i)).get("TERMINATIONVALUE").toString())) <= 0) {
               havekp = true;
               map1.put("id", ((DynamicResultRow)resultRows.get(i)).get("ID").toString());
               map1.put("code", ((DynamicResultRow)resultRows.get(i)).get("CODE").toString());
               map1.put("name", ((DynamicResultRow)resultRows.get(i)).get("NAME").toString());
            }
         }
      }

      return !havekp ? amsResult.failure() : amsResult.success(map1);
   }

   @PostMapping({"/sendMessage"})
   @POST
   @Path("/sendMessage")
   public amsResult sendMessage(@RequestBody Map map) {
      try {
         ArrayList<String> planIDs = new ArrayList();
         IDbParameter[] selParams = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("PARENTID", map.get("RATER").toString())};
         String sqlSelectUserID = "SELECT sysuser from BFEMPLOYEESYSUSER where PARENTID =:PARENTID";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         List<DynamicResultRow> userResult = this.bqlExecuter.executeSelectStatement(sqlSelectUserID, planIDs, selParams);
         if (io.iec.edp.caf.commons.utils.CollectionUtils.isEmpty(userResult)) {
            throw new AMSRuntimeException("行政人员未绑定系统用户！");
         } else {
            Set<String> receiverset = new HashSet();
            receiverset.add(((DynamicResultRow)userResult.get(0)).get("sysuser").toString());
            receiverset.add(CAFContext.current.getUserId());
            List<String> receivers = new ArrayList(receiverset);
            if (receivers.size() > 0) {
               ICommonMessageService commonMessageService = (ICommonMessageService)SpringBeanUtils.getBean(ICommonMessageService.class);
               IMsgTemplateService msgTemplateService = (IMsgTemplateService)SpringBeanUtils.getBean(IMsgTemplateService.class);
               CAFMessage message = new CAFMessage();
               message.setSender("admin");
               message.setReceivers(receivers);
               message.setSendType("EvaluationFormUrging");
               message.setScope(MsgScope.OneToGroup);
               List<Map<String, Object>> listmap = this.getCaseList(map.get("ID").toString());
               msgTemplateService.addEntity("AMSRATINGTASK", listmap);
               commonMessageService.sendMessage(message);
               return amsResult.success();
            } else {
               return amsResult.failure();
            }
         }
      } catch (Throwable var12) {
         return amsResult.failure();
      }
   }

   private List<Map<String, Object>> getCaseList(String currentID) {
      List<Map<String, Object>> listmap = new ArrayList();
      List<AMSRatingTaskDO> amsRatingTaskDO = this.amsRatingTaskRepository.findAllById(Collections.singleton(currentID));
      if (!io.iec.edp.caf.commons.utils.CollectionUtils.isEmpty(amsRatingTaskDO)) {
         Map<String, Object> map = new HashMap();
         map.put("code", ((AMSRatingTaskDO)amsRatingTaskDO.get(0)).getCode());
         map.put("name", ((AMSRatingTaskDO)amsRatingTaskDO.get(0)).getName());
         map.put("status", ((AMSRatingTaskDO)amsRatingTaskDO.get(0)).getRatingstatus());
         listmap.add(map);
      }

      return listmap;
   }

   @PostMapping({"/syncScore"})
   @POST
   @Path("/syncScore")
   public amsResult SyncScore(Map params) {
      String currentID = (String)params.get("ID");
      String evaluationScore = (String)params.get("EVALUATIONSCORE");
      String evaluationScore1 = (String)params.get("EVALUATIONSCORE1");
      String assessmentLevel = (String)params.get("ASSESSMENTLEVEL");
      String ID = UUID.randomUUID().toString();
      Date date = new Date();
      DateFormat format = new SimpleDateFormat("yyyy");
      Timestamp stamp = new Timestamp(date.getTime());
      WebSession webSession = CAFContext.current.getSession();
      String userId = webSession.getUserId();
      ArrayList<String> planIDs = new ArrayList();
      Map<String, Object> map = new HashMap();
      IDbParameter[] para = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", currentID)};
      this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
      String selectSql = "SELECT ID,DEPARTMENT,EXPATRIATEUNIT,STARTDATE,ENDDATE,CLASSIFICATION,PERSONNELNAME FROM AMSSUPERVISORYSTAFFPOOL WHERE ID=(SELECT PERSIONID FROM AMSDOCUMENTSOBJECT WHERE id=:ID)";
      List<DynamicResultRow> resultRows = this.bqlExecuter.executeSelectStatement(selectSql, planIDs, para);
      if (io.iec.edp.caf.commons.utils.CollectionUtils.isEmpty(resultRows)) {
         throw new AMSRuntimeException("人员库中未查找到该人员！");
      } else {
         map.put("DEPARTMENT", ((DynamicResultRow)resultRows.get(0)).get("DEPARTMENT"));
         map.put("EMPLOYEDBY", ((DynamicResultRow)resultRows.get(0)).get("EXPATRIATEUNIT"));
         map.put("STARTDATE4", ((DynamicResultRow)resultRows.get(0)).get("STARTDATE"));
         map.put("ENDDATE4", ((DynamicResultRow)resultRows.get(0)).get("ENDDATE"));
         map.put("PERSONNELNAME", ((DynamicResultRow)resultRows.get(0)).get("PERSONNELNAME"));
         map.put("PERSONNELLIBRARYID", ((DynamicResultRow)resultRows.get(0)).get("ID"));
         map.put("CLASSIFICATION", ((DynamicResultRow)resultRows.get(0)).get("CLASSIFICATION"));
         map.put("ID", ID);
         map.put("ANNUAL", format.format(date));
         map.put("ASSESSMENTLEVEL", assessmentLevel);
         map.put("CREATDATE", stamp);
         map.put("CREATOR", userId);
         map.put("CHANGEDATE", stamp);
         map.put("CHANGEOR", userId);
         map.put("EVALUATIONSCORE", evaluationScore);
         map.put("EVALUATIONSCORE1", evaluationScore1);
         map.put("FILLEDBY", userId);
         map.put("FILLINTHEDATE", stamp);
         map.put("STATE", "1");

         try {
            para = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", map.get("ID")), this.getBqlExecuter().makeInOutParam("DEPARTMENT", map.get("DEPARTMENT")), this.getBqlExecuter().makeInOutParam("EMPLOYEDBY", map.get("EMPLOYEDBY")), this.getBqlExecuter().makeInOutParam("STARTDATE4", map.get("STARTDATE4")), this.getBqlExecuter().makeInOutParam("ENDDATE4", map.get("ENDDATE4")), this.getBqlExecuter().makeInOutParam("CLASSIFICATION", map.get("CLASSIFICATION")), this.getBqlExecuter().makeInOutParam("PERSONNELNAME", map.get("PERSONNELNAME")), this.getBqlExecuter().makeInOutParam("ANNUAL", map.get("ANNUAL")), this.getBqlExecuter().makeInOutParam("ASSESSMENTLEVEL", map.get("ASSESSMENTLEVEL")), this.getBqlExecuter().makeInOutParam("CREATOR", map.get("CREATOR")), this.getBqlExecuter().makeInOutParam("CREATDATE", map.get("CREATDATE")), this.getBqlExecuter().makeInOutParam("CHANGEDATE", map.get("CHANGEDATE")), this.getBqlExecuter().makeInOutParam("CHANGEOR", map.get("CHANGEOR")), this.getBqlExecuter().makeInOutParam("EVALUATIONSCORE", map.get("EVALUATIONSCORE")), this.getBqlExecuter().makeInOutParam("EVALUATIONSCORE1", map.get("EVALUATIONSCORE1")), this.getBqlExecuter().makeInOutParam("FILLEDBY", map.get("FILLEDBY")), this.getBqlExecuter().makeInOutParam("FILLINTHEDATE", map.get("FILLINTHEDATE")), this.getBqlExecuter().makeInOutParam("STATE", map.get("STATE")), this.getBqlExecuter().makeInOutParam("PERSONNELLIBRARYID", map.get("PERSONNELLIBRARYID"))};
            this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
            String insertSql = "INSERT INTO AMSSUPERVISOREVALUATION (ID,DEPARTMENT,EMPLOYEDBY,STARTDATE4,ENDDATE4,CLASSIFICATION,PERSONNELNAME,ANNUAL,ASSESSMENTLEVEL,CREATOR,CREATDATE,CHANGEDATE,CHANGEOR,EVALUATIONSCORE,FILLEDBY,FILLINTHEDATE,STATE,PERSONNELLIBRARYID) VALUES(:ID,:DEPARTMENT,:EMPLOYEDBY,:STARTDATE4,:ENDDATE4,:CLASSIFICATION,:PERSONNELNAME,:ANNUAL,:ASSESSMENTLEVEL,:CREATOR,:CREATDATE,:CHANGEDATE,:CHANGEOR,:EVALUATIONSCORE1,:FILLEDBY,:FILLINTHEDATE,:STATE,:PERSONNELLIBRARYID)";
            this.bqlExecuter.executeBqlStatement(insertSql, planIDs, para);
         } catch (Exception var18) {
            return amsResult.failure();
         }

         return amsResult.success();
      }
   }
}
