package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSEVALUATIONGROUPMBER"
)
public class AMSEvaluationGroupmberDO {
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   @Column(
      name = "NAME"
   )
   private String name;
   @Column(
      name = "TYPE"
   )
   private String type;
   private String parentid;
   private String field5;
   private String persionid;
   private String field2;
   private String field3;
   private String field4;
   @Column(
      name = "AFFILIATEDUNITID"
   )
   private String affiliatedunitid;
   private String field1;

   public String getId() {
      return this.id;
   }

   public String getName() {
      return this.name;
   }

   public String getType() {
      return this.type;
   }

   public String getParentid() {
      return this.parentid;
   }

   public String getField5() {
      return this.field5;
   }

   public String getPersionid() {
      return this.persionid;
   }

   public String getField2() {
      return this.field2;
   }

   public String getField3() {
      return this.field3;
   }

   public String getField4() {
      return this.field4;
   }

   public String getAffiliatedunitid() {
      return this.affiliatedunitid;
   }

   public String getField1() {
      return this.field1;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setName(String name) {
      this.name = name;
   }

   public void setType(String type) {
      this.type = type;
   }

   public void setParentid(String parentid) {
      this.parentid = parentid;
   }

   public void setField5(String field5) {
      this.field5 = field5;
   }

   public void setPersionid(String persionid) {
      this.persionid = persionid;
   }

   public void setField2(String field2) {
      this.field2 = field2;
   }

   public void setField3(String field3) {
      this.field3 = field3;
   }

   public void setField4(String field4) {
      this.field4 = field4;
   }

   public void setAffiliatedunitid(String affiliatedunitid) {
      this.affiliatedunitid = affiliatedunitid;
   }

   public void setField1(String field1) {
      this.field1 = field1;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSEvaluationGroupmberDO)) {
         return false;
      } else {
         AMSEvaluationGroupmberDO other = (AMSEvaluationGroupmberDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$name = this.getName();
            Object other$name = other.getName();
            if (this$name == null) {
               if (other$name != null) {
                  return false;
               }
            } else if (!this$name.equals(other$name)) {
               return false;
            }

            Object this$type = this.getType();
            Object other$type = other.getType();
            if (this$type == null) {
               if (other$type != null) {
                  return false;
               }
            } else if (!this$type.equals(other$type)) {
               return false;
            }

            Object this$parentid = this.getParentid();
            Object other$parentid = other.getParentid();
            if (this$parentid == null) {
               if (other$parentid != null) {
                  return false;
               }
            } else if (!this$parentid.equals(other$parentid)) {
               return false;
            }

            Object this$field5 = this.getField5();
            Object other$field5 = other.getField5();
            if (this$field5 == null) {
               if (other$field5 != null) {
                  return false;
               }
            } else if (!this$field5.equals(other$field5)) {
               return false;
            }

            Object this$persionid = this.getPersionid();
            Object other$persionid = other.getPersionid();
            if (this$persionid == null) {
               if (other$persionid != null) {
                  return false;
               }
            } else if (!this$persionid.equals(other$persionid)) {
               return false;
            }

            Object this$field2 = this.getField2();
            Object other$field2 = other.getField2();
            if (this$field2 == null) {
               if (other$field2 != null) {
                  return false;
               }
            } else if (!this$field2.equals(other$field2)) {
               return false;
            }

            Object this$field3 = this.getField3();
            Object other$field3 = other.getField3();
            if (this$field3 == null) {
               if (other$field3 != null) {
                  return false;
               }
            } else if (!this$field3.equals(other$field3)) {
               return false;
            }

            Object this$field4 = this.getField4();
            Object other$field4 = other.getField4();
            if (this$field4 == null) {
               if (other$field4 != null) {
                  return false;
               }
            } else if (!this$field4.equals(other$field4)) {
               return false;
            }

            Object this$affiliatedunitid = this.getAffiliatedunitid();
            Object other$affiliatedunitid = other.getAffiliatedunitid();
            if (this$affiliatedunitid == null) {
               if (other$affiliatedunitid != null) {
                  return false;
               }
            } else if (!this$affiliatedunitid.equals(other$affiliatedunitid)) {
               return false;
            }

            Object this$field1 = this.getField1();
            Object other$field1 = other.getField1();
            if (this$field1 == null) {
               if (other$field1 != null) {
                  return false;
               }
            } else if (!this$field1.equals(other$field1)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSEvaluationGroupmberDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $name = this.getName();
      result = result * 59 + ($name == null ? 43 : $name.hashCode());
      Object $type = this.getType();
      result = result * 59 + ($type == null ? 43 : $type.hashCode());
      Object $parentid = this.getParentid();
      result = result * 59 + ($parentid == null ? 43 : $parentid.hashCode());
      Object $field5 = this.getField5();
      result = result * 59 + ($field5 == null ? 43 : $field5.hashCode());
      Object $persionid = this.getPersionid();
      result = result * 59 + ($persionid == null ? 43 : $persionid.hashCode());
      Object $field2 = this.getField2();
      result = result * 59 + ($field2 == null ? 43 : $field2.hashCode());
      Object $field3 = this.getField3();
      result = result * 59 + ($field3 == null ? 43 : $field3.hashCode());
      Object $field4 = this.getField4();
      result = result * 59 + ($field4 == null ? 43 : $field4.hashCode());
      Object $affiliatedunitid = this.getAffiliatedunitid();
      result = result * 59 + ($affiliatedunitid == null ? 43 : $affiliatedunitid.hashCode());
      Object $field1 = this.getField1();
      result = result * 59 + ($field1 == null ? 43 : $field1.hashCode());
      return result;
   }

   public String toString() {
      return "AMSEvaluationGroupmberDO(id=" + this.getId() + ", name=" + this.getName() + ", type=" + this.getType() + ", parentid=" + this.getParentid() + ", field5=" + this.getField5() + ", persionid=" + this.getPersionid() + ", field2=" + this.getField2() + ", field3=" + this.getField3() + ", field4=" + this.getField4() + ", affiliatedunitid=" + this.getAffiliatedunitid() + ", field1=" + this.getField1() + ")";
   }

   public AMSEvaluationGroupmberDO(String id, String name, String type, String parentid, String field5, String persionid, String field2, String field3, String field4, String affiliatedunitid, String field1) {
      this.id = id;
      this.name = name;
      this.type = type;
      this.parentid = parentid;
      this.field5 = field5;
      this.persionid = persionid;
      this.field2 = field2;
      this.field3 = field3;
      this.field4 = field4;
      this.affiliatedunitid = affiliatedunitid;
      this.field1 = field1;
   }

   public AMSEvaluationGroupmberDO() {
   }
}
