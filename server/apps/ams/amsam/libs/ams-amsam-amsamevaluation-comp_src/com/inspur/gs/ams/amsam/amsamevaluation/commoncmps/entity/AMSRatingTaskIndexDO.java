package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSRATINGTASKINDEX"
)
public class AMSRatingTaskIndexDO {
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   @Column(
      name = "PARENTID"
   )
   private String parentid;
   @Column(
      name = "SCOREINSTRUCTIONS"
   )
   private String scoreinstructions;
   @Column(
      name = "EVALUATIONSCORE"
   )
   private String evaluationscore;
   @Column(
      name = "INDEXID"
   )
   private String indexid;
   @Column(
      name = "SCORE"
   )
   private String score;

   public String getId() {
      return this.id;
   }

   public String getParentid() {
      return this.parentid;
   }

   public String getScoreinstructions() {
      return this.scoreinstructions;
   }

   public String getEvaluationscore() {
      return this.evaluationscore;
   }

   public String getIndexid() {
      return this.indexid;
   }

   public String getScore() {
      return this.score;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setParentid(String parentid) {
      this.parentid = parentid;
   }

   public void setScoreinstructions(String scoreinstructions) {
      this.scoreinstructions = scoreinstructions;
   }

   public void setEvaluationscore(String evaluationscore) {
      this.evaluationscore = evaluationscore;
   }

   public void setIndexid(String indexid) {
      this.indexid = indexid;
   }

   public void setScore(String score) {
      this.score = score;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSRatingTaskIndexDO)) {
         return false;
      } else {
         AMSRatingTaskIndexDO other = (AMSRatingTaskIndexDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$parentid = this.getParentid();
            Object other$parentid = other.getParentid();
            if (this$parentid == null) {
               if (other$parentid != null) {
                  return false;
               }
            } else if (!this$parentid.equals(other$parentid)) {
               return false;
            }

            Object this$scoreinstructions = this.getScoreinstructions();
            Object other$scoreinstructions = other.getScoreinstructions();
            if (this$scoreinstructions == null) {
               if (other$scoreinstructions != null) {
                  return false;
               }
            } else if (!this$scoreinstructions.equals(other$scoreinstructions)) {
               return false;
            }

            Object this$evaluationscore = this.getEvaluationscore();
            Object other$evaluationscore = other.getEvaluationscore();
            if (this$evaluationscore == null) {
               if (other$evaluationscore != null) {
                  return false;
               }
            } else if (!this$evaluationscore.equals(other$evaluationscore)) {
               return false;
            }

            Object this$indexid = this.getIndexid();
            Object other$indexid = other.getIndexid();
            if (this$indexid == null) {
               if (other$indexid != null) {
                  return false;
               }
            } else if (!this$indexid.equals(other$indexid)) {
               return false;
            }

            Object this$score = this.getScore();
            Object other$score = other.getScore();
            if (this$score == null) {
               if (other$score != null) {
                  return false;
               }
            } else if (!this$score.equals(other$score)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSRatingTaskIndexDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $parentid = this.getParentid();
      result = result * 59 + ($parentid == null ? 43 : $parentid.hashCode());
      Object $scoreinstructions = this.getScoreinstructions();
      result = result * 59 + ($scoreinstructions == null ? 43 : $scoreinstructions.hashCode());
      Object $evaluationscore = this.getEvaluationscore();
      result = result * 59 + ($evaluationscore == null ? 43 : $evaluationscore.hashCode());
      Object $indexid = this.getIndexid();
      result = result * 59 + ($indexid == null ? 43 : $indexid.hashCode());
      Object $score = this.getScore();
      result = result * 59 + ($score == null ? 43 : $score.hashCode());
      return result;
   }

   public String toString() {
      return "AMSRatingTaskIndexDO(id=" + this.getId() + ", parentid=" + this.getParentid() + ", scoreinstructions=" + this.getScoreinstructions() + ", evaluationscore=" + this.getEvaluationscore() + ", indexid=" + this.getIndexid() + ", score=" + this.getScore() + ")";
   }

   public AMSRatingTaskIndexDO(String id, String parentid, String scoreinstructions, String evaluationscore, String indexid, String score) {
      this.id = id;
      this.parentid = parentid;
      this.scoreinstructions = scoreinstructions;
      this.evaluationscore = evaluationscore;
      this.indexid = indexid;
      this.score = score;
   }

   public AMSRatingTaskIndexDO() {
   }
}
