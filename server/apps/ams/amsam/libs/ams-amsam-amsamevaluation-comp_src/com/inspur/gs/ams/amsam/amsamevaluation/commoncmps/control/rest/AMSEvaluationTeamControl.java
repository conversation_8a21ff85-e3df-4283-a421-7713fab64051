package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest;

import com.inspur.gs.ams.amsbd.audititemcategory.amsResult;
import com.inspur.gs.ams.amsbd.audititemcategory.entity.AMSReferenceRelationDO;
import com.inspur.gs.ams.amsbd.audititemcategory.service.AMSReferenceRelationService.AMSReferenceRelationService;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Path("/amsEvaluationTeamControl")
@Produces({"application/json"})
@Consumes({"application/json"})
@RestController
@RequestMapping({"/ams/amsam/v1.0/amsamevaluation/amsEvaluationTeamControl"})
public class AMSEvaluationTeamControl {
   @Resource
   private AMSReferenceRelationService amsReferenceRelationService;

   @PostMapping({"/bfdelete"})
   @POST
   @Path("/bfdelete")
   public amsResult bfdelete(@RequestBody Map map) {
      List<AMSReferenceRelationDO> amsReferenceRelationDOList = this.amsReferenceRelationService.findReferenceRelationByBusinessBaseId(map.get("id").toString());
      if (amsReferenceRelationDOList.size() > 0) {
         Iterator var3 = amsReferenceRelationDOList.iterator();
         if (var3.hasNext()) {
            AMSReferenceRelationDO amsReferenceRelationDO = (AMSReferenceRelationDO)var3.next();
            return amsResult.failure("当前数据已经被" + amsReferenceRelationDO.getBusinessTableName() + "菜单引用请检查!");
         }
      }

      return amsResult.success();
   }
}
