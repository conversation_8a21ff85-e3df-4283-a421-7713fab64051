package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest;

import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.service.AMSDocumentPassAfterService.AMSDocumentPassAfterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RestController;

@EnableScheduling
@RestController
@EnableAsync
public class AMSTimerEvaluationDocumentControl {
   @Autowired
   private AMSDocumentPassAfterService amsDocumentPassAfterService;

   @Async
   @Scheduled(
      cron = "0 0 0 1 * ?"
   )
   public void monthCreate() {
      String creatType = "0";
      this.amsDocumentPassAfterService.creat(creatType);
   }

   @Async
   @Scheduled(
      cron = " 0 00 00 1 4,7,10,1 ?"
   )
   public void quarterCreate() {
      String creatType = "1";
      this.amsDocumentPassAfterService.creat(creatType);
   }

   @Async
   @Scheduled(
      cron = " 0 00 00 1 1 ?"
   )
   public void yearCreate() {
      String creatType = "2";
      this.amsDocumentPassAfterService.creat(creatType);
   }
}
