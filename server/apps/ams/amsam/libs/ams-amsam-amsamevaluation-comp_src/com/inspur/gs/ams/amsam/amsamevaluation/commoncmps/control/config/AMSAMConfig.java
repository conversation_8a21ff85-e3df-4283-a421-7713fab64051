package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.config;

import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest.AMSEvaluationDocumentControl;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest.AMSEvaluationGroupControl;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest.AMSEvaluationResultsControl;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest.AMSEvaluationTeamControl;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest.AMSRatingTaskControl;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest.AMSTimerEvaluationDocumentControl;
import io.iec.edp.caf.rest.RESTEndpoint;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EntityScan({"com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity"})
@EnableJpaRepositories({"com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.repository"})
@ComponentScan({"com.inspur.gs.ams.amsam.amsamevaluation"})
public class AMSAMConfig {
   @Bean
   public RESTEndpoint AMSAMRestEndpoint(AMSEvaluationDocumentControl amsEvaluationDocumentControl, AMSTimerEvaluationDocumentControl amsTimerEvaluationDocumentControl, AMSEvaluationResultsControl amsEvaluationResultsControl, AMSRatingTaskControl amsRatingTaskControl, AMSEvaluationGroupControl amsEvaluationGroupControl, AMSEvaluationTeamControl amsEvaluationTeamControl) {
      return new RESTEndpoint("/ams/amsam/v1.0/amsamevaluation", new Object[]{amsEvaluationDocumentControl, amsTimerEvaluationDocumentControl, amsEvaluationResultsControl, amsRatingTaskControl, amsEvaluationGroupControl, amsEvaluationTeamControl});
   }
}
