package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest;

import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.service.AMSRatingTaskService.AMSRatingTaskService;
import java.util.Map;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Path("/amsRatingTaskControl")
@Produces({"application/json"})
@Consumes({"application/json"})
@RestController
@RequestMapping({"/ams/amsam/v1.0/amsamevaluation/amsRatingTaskControl"})
public class AMSRatingTaskControl {
   @Autowired
   private AMSRatingTaskService amsRatingTaskService;

   @PostMapping({"/getRatingTask"})
   @POST
   @Path("/getRatingTask")
   public boolean getRatingTask(@RequestBody Map map) {
      return this.amsRatingTaskService.findAMSRatingTaskDOBYResultId((String)map.get("resultId"));
   }
}
