package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSDOCUMENTSOBJECT"
)
public class AMSDocumentsObjectDO {
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   @Column(
      name = "OBJECTID"
   )
   private String objectid;
   @Column(
      name = "DOCUMENTS"
   )
   private String documents;
   @Column(
      name = "PERSIONID"
   )
   private String persionid;
   @Column(
      name = "TYPE"
   )
   private String type;
   @Column(
      name = "AFFILIATEDUNIT"
   )
   private String affiliatedunit;
   @Column(
      name = "NAME"
   )
   private String name;

   public String getId() {
      return this.id;
   }

   public String getObjectid() {
      return this.objectid;
   }

   public String getDocuments() {
      return this.documents;
   }

   public String getPersionid() {
      return this.persionid;
   }

   public String getType() {
      return this.type;
   }

   public String getAffiliatedunit() {
      return this.affiliatedunit;
   }

   public String getName() {
      return this.name;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setObjectid(String objectid) {
      this.objectid = objectid;
   }

   public void setDocuments(String documents) {
      this.documents = documents;
   }

   public void setPersionid(String persionid) {
      this.persionid = persionid;
   }

   public void setType(String type) {
      this.type = type;
   }

   public void setAffiliatedunit(String affiliatedunit) {
      this.affiliatedunit = affiliatedunit;
   }

   public void setName(String name) {
      this.name = name;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSDocumentsObjectDO)) {
         return false;
      } else {
         AMSDocumentsObjectDO other = (AMSDocumentsObjectDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$objectid = this.getObjectid();
            Object other$objectid = other.getObjectid();
            if (this$objectid == null) {
               if (other$objectid != null) {
                  return false;
               }
            } else if (!this$objectid.equals(other$objectid)) {
               return false;
            }

            Object this$documents = this.getDocuments();
            Object other$documents = other.getDocuments();
            if (this$documents == null) {
               if (other$documents != null) {
                  return false;
               }
            } else if (!this$documents.equals(other$documents)) {
               return false;
            }

            Object this$persionid = this.getPersionid();
            Object other$persionid = other.getPersionid();
            if (this$persionid == null) {
               if (other$persionid != null) {
                  return false;
               }
            } else if (!this$persionid.equals(other$persionid)) {
               return false;
            }

            Object this$type = this.getType();
            Object other$type = other.getType();
            if (this$type == null) {
               if (other$type != null) {
                  return false;
               }
            } else if (!this$type.equals(other$type)) {
               return false;
            }

            Object this$affiliatedunit = this.getAffiliatedunit();
            Object other$affiliatedunit = other.getAffiliatedunit();
            if (this$affiliatedunit == null) {
               if (other$affiliatedunit != null) {
                  return false;
               }
            } else if (!this$affiliatedunit.equals(other$affiliatedunit)) {
               return false;
            }

            Object this$name = this.getName();
            Object other$name = other.getName();
            if (this$name == null) {
               if (other$name != null) {
                  return false;
               }
            } else if (!this$name.equals(other$name)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSDocumentsObjectDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $objectid = this.getObjectid();
      result = result * 59 + ($objectid == null ? 43 : $objectid.hashCode());
      Object $documents = this.getDocuments();
      result = result * 59 + ($documents == null ? 43 : $documents.hashCode());
      Object $persionid = this.getPersionid();
      result = result * 59 + ($persionid == null ? 43 : $persionid.hashCode());
      Object $type = this.getType();
      result = result * 59 + ($type == null ? 43 : $type.hashCode());
      Object $affiliatedunit = this.getAffiliatedunit();
      result = result * 59 + ($affiliatedunit == null ? 43 : $affiliatedunit.hashCode());
      Object $name = this.getName();
      result = result * 59 + ($name == null ? 43 : $name.hashCode());
      return result;
   }

   public String toString() {
      return "AMSDocumentsObjectDO(id=" + this.getId() + ", objectid=" + this.getObjectid() + ", documents=" + this.getDocuments() + ", persionid=" + this.getPersionid() + ", type=" + this.getType() + ", affiliatedunit=" + this.getAffiliatedunit() + ", name=" + this.getName() + ")";
   }

   public AMSDocumentsObjectDO(String id, String objectid, String documents, String persionid, String type, String affiliatedunit, String name) {
      this.id = id;
      this.objectid = objectid;
      this.documents = documents;
      this.persionid = persionid;
      this.type = type;
      this.affiliatedunit = affiliatedunit;
      this.name = name;
   }

   public AMSDocumentsObjectDO() {
   }
}
