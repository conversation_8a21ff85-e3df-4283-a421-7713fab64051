package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.inspur.edp.caf.db.dbaccess.DynamicResultRow;
import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.gs.ams.amsbd.audititemcategory.amsResult;
import com.inspur.gs.ams.amsbd.audititemcategory.entity.AMSReferenceRelationDO;
import com.inspur.gs.ams.amsbd.audititemcategory.service.AMSReferenceRelationService.AMSReferenceRelationService;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Path("/amsEvaluationGroupControl")
@Produces({"application/json"})
@Consumes({"application/json"})
@RestController
@RequestMapping({"/ams/amsam/v1.0/amsamevaluation/amsEvaluationGroupControl"})
public class AMSEvaluationGroupControl {
   private IBqlExecuter bqlExecuter;
   @Resource
   private AMSReferenceRelationService amsReferenceRelationService;

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   @PostMapping({"/bfdelete"})
   @POST
   @Path("/bfdelete")
   public amsResult bfdelete(@RequestBody Map map) {
      List<AMSReferenceRelationDO> amsReferenceRelationDOList = this.amsReferenceRelationService.findReferenceRelationByBusinessBaseId(map.get("id").toString());
      if (amsReferenceRelationDOList.size() > 0) {
         Iterator var3 = amsReferenceRelationDOList.iterator();
         if (var3.hasNext()) {
            AMSReferenceRelationDO amsReferenceRelationDO = (AMSReferenceRelationDO)var3.next();
            return amsResult.failure("当前数据已经被" + amsReferenceRelationDO.getBusinessTableName() + "菜单引用请检查!");
         }
      }

      return amsResult.success();
   }

   @PostMapping({"/sqlGetData"})
   @POST
   @Path("/sqlGetData")
   public amsResult sqlGetData(@RequestBody Map map) {
      ArrayList<String> planIDs = new ArrayList();
      String ID = (String)map.get("ID");
      String filter = (String)map.get("filter");
      filter = filter.replace("'", "");
      List<String> values = Arrays.asList(filter.split(","));
      Map<String, String> res = new HashMap();

      try {
         IDbParameter[] params = new IDbParameter[]{this.getBqlExecuter().makeInOutParam("ID", ID)};
         String selectSql = "select DATASET_JSON from BABAPDATASET where ID=:ID";
         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         List<DynamicResultRow> dataSqlResult = this.bqlExecuter.executeSelectStatement(selectSql, planIDs, params);
         ObjectMapper m = new ObjectMapper();
         JsonNode json = m.readTree(((DynamicResultRow)dataSqlResult.get(0)).get("DATASET_JSON").toString());
         String exeSql = json.get("query").asText();
         if (this.sqlCheck(exeSql)) {
            try {
               List<DynamicResultRow> dataSetResult = this.bqlExecuter.executeSelectStatement(exeSql, planIDs, new IDbParameter[0]);
               ObjectNode node = m.createObjectNode();
               ArrayNode an = node.putArray("Data");
               dataSetResult.forEach((element) -> {
                  if (!values.contains(element.get("id"))) {
                     an.add((JsonNode)m.convertValue(element.getValues(), JsonNode.class));
                  }

               });
               return amsResult.success(node);
            } catch (Exception var16) {
               res.put("info", "Sql查询错误。");
               return amsResult.failure(res);
            }
         } else {
            res.put("info", "Sql编写不规范。\nSql需有且仅有id,code,name属性。");
            return amsResult.failure(res);
         }
      } catch (Exception var17) {
         res.put("info", "数据集查询错误。");
         return amsResult.failure(res);
      }
   }

   public boolean sqlCheck(String sql) {
      if (sql == null) {
         return false;
      } else {
         String regix = "id\\s*,[^,]*code\\s*,[^,]*name\\s*from";
         Pattern pattern = Pattern.compile(regix, 2);
         Matcher mat = pattern.matcher(sql);
         return mat.find();
      }
   }
}
