package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSPLANINDEX"
)
public class AMSplanIndexDO {
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   @Column(
      name = "PLANID"
   )
   private String planid;
   @Column(
      name = "INDEXID"
   )
   private String indexid;
   @Column(
      name = "SCORE"
   )
   private String score;

   public String getId() {
      return this.id;
   }

   public String getPlanid() {
      return this.planid;
   }

   public String getIndexid() {
      return this.indexid;
   }

   public String getScore() {
      return this.score;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setPlanid(String planid) {
      this.planid = planid;
   }

   public void setIndexid(String indexid) {
      this.indexid = indexid;
   }

   public void setScore(String score) {
      this.score = score;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSplanIndexDO)) {
         return false;
      } else {
         AMSplanIndexDO other = (AMSplanIndexDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$planid = this.getPlanid();
            Object other$planid = other.getPlanid();
            if (this$planid == null) {
               if (other$planid != null) {
                  return false;
               }
            } else if (!this$planid.equals(other$planid)) {
               return false;
            }

            Object this$indexid = this.getIndexid();
            Object other$indexid = other.getIndexid();
            if (this$indexid == null) {
               if (other$indexid != null) {
                  return false;
               }
            } else if (!this$indexid.equals(other$indexid)) {
               return false;
            }

            Object this$score = this.getScore();
            Object other$score = other.getScore();
            if (this$score == null) {
               if (other$score != null) {
                  return false;
               }
            } else if (!this$score.equals(other$score)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSplanIndexDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $planid = this.getPlanid();
      result = result * 59 + ($planid == null ? 43 : $planid.hashCode());
      Object $indexid = this.getIndexid();
      result = result * 59 + ($indexid == null ? 43 : $indexid.hashCode());
      Object $score = this.getScore();
      result = result * 59 + ($score == null ? 43 : $score.hashCode());
      return result;
   }

   public String toString() {
      return "AMSplanIndexDO(id=" + this.getId() + ", planid=" + this.getPlanid() + ", indexid=" + this.getIndexid() + ", score=" + this.getScore() + ")";
   }

   public AMSplanIndexDO(String id, String planid, String indexid, String score) {
      this.id = id;
      this.planid = planid;
      this.indexid = indexid;
      this.score = score;
   }

   public AMSplanIndexDO() {
   }
}
