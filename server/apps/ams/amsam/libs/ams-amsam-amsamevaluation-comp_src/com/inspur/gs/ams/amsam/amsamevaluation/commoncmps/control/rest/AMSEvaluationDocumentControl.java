package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.control.rest;

import com.inspur.edp.caf.db.dbaccess.IDbParameter;
import com.inspur.edp.qdp.bql.api.IBqlExecuter;
import com.inspur.edp.qdp.bql.api.OptionType;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity.AMSEvaluateIndexDO;
import com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.service.AMSEvaluationDocumentService.AMSEvaluationDocumentService;
import com.inspur.gs.ams.amsbd.audititemcategory.amsResult;
import io.iec.edp.caf.runtime.config.BqlBeanUtil;
import java.util.ArrayList;
import java.util.Map;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Path("/amsEvaluationDocumentControl")
@Produces({"application/json"})
@Consumes({"application/json"})
@RestController
@RequestMapping({"/ams/amsam/v1.0/amsamevaluation/amsEvaluationDocumentControl"})
public class AMSEvaluationDocumentControl {
   private IBqlExecuter bqlExecuter;
   @Autowired
   private AMSEvaluationDocumentService amsEvaluationDocumentService;

   public IBqlExecuter getBqlExecuter() {
      if (this.bqlExecuter == null) {
         this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
      }

      return this.bqlExecuter;
   }

   @PostMapping({"/selectEvaluationIndex"})
   @POST
   @Path("/selectEvaluationIndex")
   public amsResult selectEvaluationIndex(@RequestBody Map map) {
      Map<String, Map<String, AMSEvaluateIndexDO>> newMap = this.amsEvaluationDocumentService.selectEvaluationIndex((String)map.get("evaluationPlanId"));
      return amsResult.success(newMap);
   }

   @PostMapping({"/selectTableExists"})
   @POST
   @Path("/selectTableExists")
   public amsResult selectTableExists() {
      try {
         ArrayList<String> planIDs = new ArrayList();
         String sqlselectTable = "SELECT ID FROM AMSSUPERVISORYSTAFFPOOL";
         if (this.bqlExecuter == null) {
            this.bqlExecuter = (IBqlExecuter)BqlBeanUtil.getAppCtx().getBean(IBqlExecuter.class);
         }

         this.bqlExecuter.getOptions().setOptionType(OptionType.ExecuteSql);
         this.bqlExecuter.executeSelectStatement(sqlselectTable, planIDs, new IDbParameter[0]);
         return amsResult.success();
      } catch (Exception e) {
         return amsResult.failure(e.getMessage());
      }
   }
}
