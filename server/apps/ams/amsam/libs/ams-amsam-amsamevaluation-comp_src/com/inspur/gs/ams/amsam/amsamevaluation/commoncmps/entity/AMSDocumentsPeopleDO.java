package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSDOCUMENTSPEOPLE"
)
public class AMSDocumentsPeopleDO {
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   @Column(
      name = "WEIGHTRATIO"
   )
   private String weightratio;
   @Column(
      name = "PEOPLEID"
   )
   private String peopleid;
   @Column(
      name = "AFFILIATEDUNITID"
   )
   private String affiliatedUnitId;
   @Column(
      name = "DOCUMENTS"
   )
   private String documents;

   public String getId() {
      return this.id;
   }

   public String getWeightratio() {
      return this.weightratio;
   }

   public String getPeopleid() {
      return this.peopleid;
   }

   public String getAffiliatedUnitId() {
      return this.affiliatedUnitId;
   }

   public String getDocuments() {
      return this.documents;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setWeightratio(String weightratio) {
      this.weightratio = weightratio;
   }

   public void setPeopleid(String peopleid) {
      this.peopleid = peopleid;
   }

   public void setAffiliatedUnitId(String affiliatedUnitId) {
      this.affiliatedUnitId = affiliatedUnitId;
   }

   public void setDocuments(String documents) {
      this.documents = documents;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSDocumentsPeopleDO)) {
         return false;
      } else {
         AMSDocumentsPeopleDO other = (AMSDocumentsPeopleDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$weightratio = this.getWeightratio();
            Object other$weightratio = other.getWeightratio();
            if (this$weightratio == null) {
               if (other$weightratio != null) {
                  return false;
               }
            } else if (!this$weightratio.equals(other$weightratio)) {
               return false;
            }

            Object this$peopleid = this.getPeopleid();
            Object other$peopleid = other.getPeopleid();
            if (this$peopleid == null) {
               if (other$peopleid != null) {
                  return false;
               }
            } else if (!this$peopleid.equals(other$peopleid)) {
               return false;
            }

            Object this$affiliatedUnitId = this.getAffiliatedUnitId();
            Object other$affiliatedUnitId = other.getAffiliatedUnitId();
            if (this$affiliatedUnitId == null) {
               if (other$affiliatedUnitId != null) {
                  return false;
               }
            } else if (!this$affiliatedUnitId.equals(other$affiliatedUnitId)) {
               return false;
            }

            Object this$documents = this.getDocuments();
            Object other$documents = other.getDocuments();
            if (this$documents == null) {
               if (other$documents != null) {
                  return false;
               }
            } else if (!this$documents.equals(other$documents)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSDocumentsPeopleDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $weightratio = this.getWeightratio();
      result = result * 59 + ($weightratio == null ? 43 : $weightratio.hashCode());
      Object $peopleid = this.getPeopleid();
      result = result * 59 + ($peopleid == null ? 43 : $peopleid.hashCode());
      Object $affiliatedUnitId = this.getAffiliatedUnitId();
      result = result * 59 + ($affiliatedUnitId == null ? 43 : $affiliatedUnitId.hashCode());
      Object $documents = this.getDocuments();
      result = result * 59 + ($documents == null ? 43 : $documents.hashCode());
      return result;
   }

   public String toString() {
      return "AMSDocumentsPeopleDO(id=" + this.getId() + ", weightratio=" + this.getWeightratio() + ", peopleid=" + this.getPeopleid() + ", affiliatedUnitId=" + this.getAffiliatedUnitId() + ", documents=" + this.getDocuments() + ")";
   }

   public AMSDocumentsPeopleDO(String id, String weightratio, String peopleid, String affiliatedUnitId, String documents) {
      this.id = id;
      this.weightratio = weightratio;
      this.peopleid = peopleid;
      this.affiliatedUnitId = affiliatedUnitId;
      this.documents = documents;
   }

   public AMSDocumentsPeopleDO() {
   }
}
