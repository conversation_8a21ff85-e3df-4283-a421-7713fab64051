package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSINDEXTYPE"
)
public class AMSIndexTypeDO {
   private String code;
   @Column(
      name = "NAME"
   )
   private String name;
   private String sign;
   private String status;
   private String remark;
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   private String unitid;
   private String creater_createdby;
   private Date creater_createdon;
   private String creater_lastchangedby;
   private Date creater_lastchangedon;
   private String field1;
   private String field2;
   private String field3;
   private String field4;
   private String field5;

   public String getCode() {
      return this.code;
   }

   public String getName() {
      return this.name;
   }

   public String getSign() {
      return this.sign;
   }

   public String getStatus() {
      return this.status;
   }

   public String getRemark() {
      return this.remark;
   }

   public String getId() {
      return this.id;
   }

   public String getUnitid() {
      return this.unitid;
   }

   public String getCreater_createdby() {
      return this.creater_createdby;
   }

   public Date getCreater_createdon() {
      return this.creater_createdon;
   }

   public String getCreater_lastchangedby() {
      return this.creater_lastchangedby;
   }

   public Date getCreater_lastchangedon() {
      return this.creater_lastchangedon;
   }

   public String getField1() {
      return this.field1;
   }

   public String getField2() {
      return this.field2;
   }

   public String getField3() {
      return this.field3;
   }

   public String getField4() {
      return this.field4;
   }

   public String getField5() {
      return this.field5;
   }

   public void setCode(String code) {
      this.code = code;
   }

   public void setName(String name) {
      this.name = name;
   }

   public void setSign(String sign) {
      this.sign = sign;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   public void setRemark(String remark) {
      this.remark = remark;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setUnitid(String unitid) {
      this.unitid = unitid;
   }

   public void setCreater_createdby(String creater_createdby) {
      this.creater_createdby = creater_createdby;
   }

   public void setCreater_createdon(Date creater_createdon) {
      this.creater_createdon = creater_createdon;
   }

   public void setCreater_lastchangedby(String creater_lastchangedby) {
      this.creater_lastchangedby = creater_lastchangedby;
   }

   public void setCreater_lastchangedon(Date creater_lastchangedon) {
      this.creater_lastchangedon = creater_lastchangedon;
   }

   public void setField1(String field1) {
      this.field1 = field1;
   }

   public void setField2(String field2) {
      this.field2 = field2;
   }

   public void setField3(String field3) {
      this.field3 = field3;
   }

   public void setField4(String field4) {
      this.field4 = field4;
   }

   public void setField5(String field5) {
      this.field5 = field5;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSIndexTypeDO)) {
         return false;
      } else {
         AMSIndexTypeDO other = (AMSIndexTypeDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$code = this.getCode();
            Object other$code = other.getCode();
            if (this$code == null) {
               if (other$code != null) {
                  return false;
               }
            } else if (!this$code.equals(other$code)) {
               return false;
            }

            Object this$name = this.getName();
            Object other$name = other.getName();
            if (this$name == null) {
               if (other$name != null) {
                  return false;
               }
            } else if (!this$name.equals(other$name)) {
               return false;
            }

            Object this$sign = this.getSign();
            Object other$sign = other.getSign();
            if (this$sign == null) {
               if (other$sign != null) {
                  return false;
               }
            } else if (!this$sign.equals(other$sign)) {
               return false;
            }

            Object this$status = this.getStatus();
            Object other$status = other.getStatus();
            if (this$status == null) {
               if (other$status != null) {
                  return false;
               }
            } else if (!this$status.equals(other$status)) {
               return false;
            }

            Object this$remark = this.getRemark();
            Object other$remark = other.getRemark();
            if (this$remark == null) {
               if (other$remark != null) {
                  return false;
               }
            } else if (!this$remark.equals(other$remark)) {
               return false;
            }

            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$unitid = this.getUnitid();
            Object other$unitid = other.getUnitid();
            if (this$unitid == null) {
               if (other$unitid != null) {
                  return false;
               }
            } else if (!this$unitid.equals(other$unitid)) {
               return false;
            }

            Object this$creater_createdby = this.getCreater_createdby();
            Object other$creater_createdby = other.getCreater_createdby();
            if (this$creater_createdby == null) {
               if (other$creater_createdby != null) {
                  return false;
               }
            } else if (!this$creater_createdby.equals(other$creater_createdby)) {
               return false;
            }

            Object this$creater_createdon = this.getCreater_createdon();
            Object other$creater_createdon = other.getCreater_createdon();
            if (this$creater_createdon == null) {
               if (other$creater_createdon != null) {
                  return false;
               }
            } else if (!this$creater_createdon.equals(other$creater_createdon)) {
               return false;
            }

            Object this$creater_lastchangedby = this.getCreater_lastchangedby();
            Object other$creater_lastchangedby = other.getCreater_lastchangedby();
            if (this$creater_lastchangedby == null) {
               if (other$creater_lastchangedby != null) {
                  return false;
               }
            } else if (!this$creater_lastchangedby.equals(other$creater_lastchangedby)) {
               return false;
            }

            Object this$creater_lastchangedon = this.getCreater_lastchangedon();
            Object other$creater_lastchangedon = other.getCreater_lastchangedon();
            if (this$creater_lastchangedon == null) {
               if (other$creater_lastchangedon != null) {
                  return false;
               }
            } else if (!this$creater_lastchangedon.equals(other$creater_lastchangedon)) {
               return false;
            }

            Object this$field1 = this.getField1();
            Object other$field1 = other.getField1();
            if (this$field1 == null) {
               if (other$field1 != null) {
                  return false;
               }
            } else if (!this$field1.equals(other$field1)) {
               return false;
            }

            Object this$field2 = this.getField2();
            Object other$field2 = other.getField2();
            if (this$field2 == null) {
               if (other$field2 != null) {
                  return false;
               }
            } else if (!this$field2.equals(other$field2)) {
               return false;
            }

            Object this$field3 = this.getField3();
            Object other$field3 = other.getField3();
            if (this$field3 == null) {
               if (other$field3 != null) {
                  return false;
               }
            } else if (!this$field3.equals(other$field3)) {
               return false;
            }

            Object this$field4 = this.getField4();
            Object other$field4 = other.getField4();
            if (this$field4 == null) {
               if (other$field4 != null) {
                  return false;
               }
            } else if (!this$field4.equals(other$field4)) {
               return false;
            }

            Object this$field5 = this.getField5();
            Object other$field5 = other.getField5();
            if (this$field5 == null) {
               if (other$field5 != null) {
                  return false;
               }
            } else if (!this$field5.equals(other$field5)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSIndexTypeDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $code = this.getCode();
      result = result * 59 + ($code == null ? 43 : $code.hashCode());
      Object $name = this.getName();
      result = result * 59 + ($name == null ? 43 : $name.hashCode());
      Object $sign = this.getSign();
      result = result * 59 + ($sign == null ? 43 : $sign.hashCode());
      Object $status = this.getStatus();
      result = result * 59 + ($status == null ? 43 : $status.hashCode());
      Object $remark = this.getRemark();
      result = result * 59 + ($remark == null ? 43 : $remark.hashCode());
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $unitid = this.getUnitid();
      result = result * 59 + ($unitid == null ? 43 : $unitid.hashCode());
      Object $creater_createdby = this.getCreater_createdby();
      result = result * 59 + ($creater_createdby == null ? 43 : $creater_createdby.hashCode());
      Object $creater_createdon = this.getCreater_createdon();
      result = result * 59 + ($creater_createdon == null ? 43 : $creater_createdon.hashCode());
      Object $creater_lastchangedby = this.getCreater_lastchangedby();
      result = result * 59 + ($creater_lastchangedby == null ? 43 : $creater_lastchangedby.hashCode());
      Object $creater_lastchangedon = this.getCreater_lastchangedon();
      result = result * 59 + ($creater_lastchangedon == null ? 43 : $creater_lastchangedon.hashCode());
      Object $field1 = this.getField1();
      result = result * 59 + ($field1 == null ? 43 : $field1.hashCode());
      Object $field2 = this.getField2();
      result = result * 59 + ($field2 == null ? 43 : $field2.hashCode());
      Object $field3 = this.getField3();
      result = result * 59 + ($field3 == null ? 43 : $field3.hashCode());
      Object $field4 = this.getField4();
      result = result * 59 + ($field4 == null ? 43 : $field4.hashCode());
      Object $field5 = this.getField5();
      result = result * 59 + ($field5 == null ? 43 : $field5.hashCode());
      return result;
   }

   public String toString() {
      return "AMSIndexTypeDO(code=" + this.getCode() + ", name=" + this.getName() + ", sign=" + this.getSign() + ", status=" + this.getStatus() + ", remark=" + this.getRemark() + ", id=" + this.getId() + ", unitid=" + this.getUnitid() + ", creater_createdby=" + this.getCreater_createdby() + ", creater_createdon=" + this.getCreater_createdon() + ", creater_lastchangedby=" + this.getCreater_lastchangedby() + ", creater_lastchangedon=" + this.getCreater_lastchangedon() + ", field1=" + this.getField1() + ", field2=" + this.getField2() + ", field3=" + this.getField3() + ", field4=" + this.getField4() + ", field5=" + this.getField5() + ")";
   }

   public AMSIndexTypeDO(String code, String name, String sign, String status, String remark, String id, String unitid, String creater_createdby, Date creater_createdon, String creater_lastchangedby, Date creater_lastchangedon, String field1, String field2, String field3, String field4, String field5) {
      this.code = code;
      this.name = name;
      this.sign = sign;
      this.status = status;
      this.remark = remark;
      this.id = id;
      this.unitid = unitid;
      this.creater_createdby = creater_createdby;
      this.creater_createdon = creater_createdon;
      this.creater_lastchangedby = creater_lastchangedby;
      this.creater_lastchangedon = creater_lastchangedon;
      this.field1 = field1;
      this.field2 = field2;
      this.field3 = field3;
      this.field4 = field4;
      this.field5 = field5;
   }

   public AMSIndexTypeDO() {
   }
}
