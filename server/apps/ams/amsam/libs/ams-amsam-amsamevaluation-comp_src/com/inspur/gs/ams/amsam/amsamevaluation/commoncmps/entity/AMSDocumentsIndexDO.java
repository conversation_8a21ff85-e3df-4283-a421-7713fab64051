package com.inspur.gs.ams.amsam.amsamevaluation.commoncmps.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(
   name = "AMSDOCUMENTSINDEX"
)
public class AMSDocumentsIndexDO {
   @Id
   @Column(
      name = "ID"
   )
   private String id;
   @Column(
      name = "SCORE"
   )
   private String score;
   @Column(
      name = "REMARK"
   )
   private String remark;
   @Column(
      name = "DOCUMENTS"
   )
   private String documents;
   @Column(
      name = "INDEXTYPEID"
   )
   private String indextypeid;
   @Column(
      name = "INDEXID"
   )
   private String indexid;

   public String getId() {
      return this.id;
   }

   public String getScore() {
      return this.score;
   }

   public String getRemark() {
      return this.remark;
   }

   public String getDocuments() {
      return this.documents;
   }

   public String getIndextypeid() {
      return this.indextypeid;
   }

   public String getIndexid() {
      return this.indexid;
   }

   public void setId(String id) {
      this.id = id;
   }

   public void setScore(String score) {
      this.score = score;
   }

   public void setRemark(String remark) {
      this.remark = remark;
   }

   public void setDocuments(String documents) {
      this.documents = documents;
   }

   public void setIndextypeid(String indextypeid) {
      this.indextypeid = indextypeid;
   }

   public void setIndexid(String indexid) {
      this.indexid = indexid;
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof AMSDocumentsIndexDO)) {
         return false;
      } else {
         AMSDocumentsIndexDO other = (AMSDocumentsIndexDO)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            Object this$id = this.getId();
            Object other$id = other.getId();
            if (this$id == null) {
               if (other$id != null) {
                  return false;
               }
            } else if (!this$id.equals(other$id)) {
               return false;
            }

            Object this$score = this.getScore();
            Object other$score = other.getScore();
            if (this$score == null) {
               if (other$score != null) {
                  return false;
               }
            } else if (!this$score.equals(other$score)) {
               return false;
            }

            Object this$remark = this.getRemark();
            Object other$remark = other.getRemark();
            if (this$remark == null) {
               if (other$remark != null) {
                  return false;
               }
            } else if (!this$remark.equals(other$remark)) {
               return false;
            }

            Object this$documents = this.getDocuments();
            Object other$documents = other.getDocuments();
            if (this$documents == null) {
               if (other$documents != null) {
                  return false;
               }
            } else if (!this$documents.equals(other$documents)) {
               return false;
            }

            Object this$indextypeid = this.getIndextypeid();
            Object other$indextypeid = other.getIndextypeid();
            if (this$indextypeid == null) {
               if (other$indextypeid != null) {
                  return false;
               }
            } else if (!this$indextypeid.equals(other$indextypeid)) {
               return false;
            }

            Object this$indexid = this.getIndexid();
            Object other$indexid = other.getIndexid();
            if (this$indexid == null) {
               if (other$indexid != null) {
                  return false;
               }
            } else if (!this$indexid.equals(other$indexid)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof AMSDocumentsIndexDO;
   }

   public int hashCode() {
      int PRIME = 59;
      int result = 1;
      Object $id = this.getId();
      result = result * 59 + ($id == null ? 43 : $id.hashCode());
      Object $score = this.getScore();
      result = result * 59 + ($score == null ? 43 : $score.hashCode());
      Object $remark = this.getRemark();
      result = result * 59 + ($remark == null ? 43 : $remark.hashCode());
      Object $documents = this.getDocuments();
      result = result * 59 + ($documents == null ? 43 : $documents.hashCode());
      Object $indextypeid = this.getIndextypeid();
      result = result * 59 + ($indextypeid == null ? 43 : $indextypeid.hashCode());
      Object $indexid = this.getIndexid();
      result = result * 59 + ($indexid == null ? 43 : $indexid.hashCode());
      return result;
   }

   public String toString() {
      return "AMSDocumentsIndexDO(id=" + this.getId() + ", score=" + this.getScore() + ", remark=" + this.getRemark() + ", documents=" + this.getDocuments() + ", indextypeid=" + this.getIndextypeid() + ", indexid=" + this.getIndexid() + ")";
   }

   public AMSDocumentsIndexDO(String id, String score, String remark, String documents, String indextypeid, String indexid) {
      this.id = id;
      this.score = score;
      this.remark = remark;
      this.documents = documents;
      this.indextypeid = indextypeid;
      this.indexid = indexid;
   }

   public AMSDocumentsIndexDO() {
   }
}
