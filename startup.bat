@echo off
setlocal
title iGIX Server

REM 获取当前操作系统类型
set SERVER_PATH=%~dp0
set CURRENT_DIR=%cd%

rem 检查目录是否存在
if exist "%SERVER_PATH%deploy" (
    @REM REM 执行 caf.exe
    %SERVER_PATH%\igix.bat start
) else (
    set EXECUTABLE=caf-server.bat"
    rem Check that target executable exists
    chcp 65001 > nul
    @REM call "caf-server.bat" run
    if exist "%EXECUTABLE%" (
        call "caf-server.bat" run %*
    ) else (
        if exist "%CURRENT_DIR%\bin\%EXECUTABLE%" (
            cd ".\bin"
            call "caf-server.bat" run %*
        ) else (
            echo Cannot find "%EXECUTABLE%"
            echo This file is needed to run this program
        )
    )
)
REM 添加一个换行
echo.

REM 保持控制台窗口打开，并允许用户输入命令
cmd /k
endlocal