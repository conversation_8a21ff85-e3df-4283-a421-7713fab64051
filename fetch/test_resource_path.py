#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试资源路径修复
"""

from pathlib import Path
from md_downloader import DownloadManager, Lo<PERSON>, Config

def test_specific_file():
    """测试特定文件的资源路径"""
    print("测试资源路径修复...")
    
    # 设置日志
    logger = Logger.setup_logger()
    
    # 创建下载目录
    Path("downloads").mkdir(exist_ok=True)
    
    # 创建下载管理器
    manager = DownloadManager()
    
    try:
        # 手动测试一个包含图片的文件
        from md_downloader import HTTPClient, FileDownloader, MetadataManager, ResourceExtractor
        
        http_client = HTTPClient()
        metadata_manager = MetadataManager()
        file_downloader = FileDownloader(http_client, metadata_manager)
        resource_extractor = ResourceExtractor(file_downloader)
        
        # 测试下载一个特定的MD文件
        test_url = "https://open.inspures.com/doc/assets/docs/tm/tm/zh-Hans/account management/account asked questions/Account-FAQ.md"
        local_path = Path("downloads/tm/tm/zh-Hans/account management/account asked questions/Account-FAQ.md")
        
        print(f"下载测试文件: {test_url}")
        if file_downloader.download_file(test_url, local_path, force=True):
            print(f"文件下载成功: {local_path}")
            
            # 测试资源提取
            print("提取资源文件...")
            resource_count = resource_extractor.download_resources(local_path, test_url)
            print(f"下载了 {resource_count} 个资源文件")
            
            # 检查目录结构
            print("\n目录结构:")
            for item in local_path.parent.rglob("*"):
                if item.is_file():
                    print(f"  {item}")
        
        http_client.close()
        metadata_manager.save_metadata()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_specific_file()
