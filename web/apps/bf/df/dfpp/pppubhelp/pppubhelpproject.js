/**
 * @author:蔺永建
 * @function:公共帮助-工程项目
 * @date:2025.06.30
 */
idp.event.bind("viewReady", function(e, context) {
    $('#input_409396').bind('keyup', function(event){
        if (event.keyCode === 13) {
            $('#button_529793').click();
        }
    });
    $('#input_312861').bind('keyup', function(event){
        if (event.keyCode === 13) {
            $('#button_529793').click();
        }
    });
    $("#layout_174697").appendTo("#col_164842");
    idp.event.register("grid_328683", 'selectRow', function() {
        var type = idp.control.get('radio_460195').getValue();
        if (type === 'cur') idp.func.refresh();
    });

    idp.control.get('radio_460195').setValue("cur");
    $("[name='radio_460195']").change(function(){
        idp.func.refresh();
    });
    idp.event.register("grid_main", "beforeGridFilter", function(e, filter) {
        var row = idp.control.get('grid_328683').getSelected();
        var type = idp.control.get('radio_460195').getValue();
        if (filter.length > 0) filter[filter.length - 1].Logic = " and ";
        if (row && type === 'cur') filter.push({
            "Left": " ",
            "Field": "bfprojectinfo.PROJECTTYPE",
            "Operate": '=',
            "IsExpress": false,
            "Value": row.ID,
            "Right": "",
            "Logic": " "
        });
        var code = idp.control.get('input_409396').getValue();
        if (code) {
            if (filter.length > 0) filter[filter.length - 1].Logic = " and ";
            filter.push({
                "Left": " ",
                "Field": "bfprojectinfo.CODE",
                "Operate": "like",
                "IsExpress": false,
                "Value": '%' + code + '%',
                "Right": "",
                "Logic": ""
            });
        }
        var name = idp.control.get('input_312861').getValue();
        if (name) {
            if (filter.length > 0) filter[filter.length - 1].Logic = " and ";
            filter.push({
                "Left": " ",
                "Field": "bfprojectinfo.NAME$LANGUAGE$",
                "Operate": "like",
                "IsExpress": false,
                "Value": '%' + name + '%',
                "Right": "",
                "Logic": ""
            });
        }
        return filter;
    });
    idp.func.refresh();
});
