window.compressionimage = (function(win,$){
	console.log("compressionimage");
	var content = {};
	content.compressFile =async function(fileName,base64,fileMaxSize){
		// return new Promise(async (resolve, reject) => {
			try{
				if(this.checkFileSize(base64,fileMaxSize)){
				  let fileType = fileName.split('.').pop().toLowerCase();
				  console.log("①file start compress");
				  //超过4M,需要进行压缩
				  let img =  await this.base64ToImg(base64,fileType);
				  const compressionRatio = this.getcompressionRatio(base64);
				  base64 = this.compressionImage(img, compressionRatio);
				  base64 = base64.split(',')[1]
				  console.log("①-file finish compress");
				}else{
				  console.log("①-not necessary compress",fileName);
				}	
				return base64;
			} catch (error) {
				throw error; // 失败时 reject
			}
		// });

	}
	//校验文件大于4M
	content.checkFileSize =function(str_base,fileMaxSize) {
		 //去掉等号
		 var equalIndex = str_base.indexOf('=');
		 if(str_base.indexOf('=')>0) {
		   str_base=str_base.substring(0, equalIndex);
		 }
		 const strlength = str_base.length;
		 console.log("str_base.length："+strlength);
		 const filesize_b = parseInt(strlength-(strlength/8)*2);
		 const filesize_M =  filesize_b/1024/1024
		 console.log("文件大小M："+filesize_M);
		 if(filesize_M > fileMaxSize){
		   return true;
		 }
		 else{
		   return false;
		 }
	  }
	  content.base64ToImg = function(base64,type) {
		console.log('进入base64ToImg',type);
		return new Promise((resolve, reject) => {
		  const img = new Image()
		  img.src = `data:image/${type};base64,` + base64
		  img.onload = function () {
			resolve(img)
		  }
		  img.onerror = function (e) {
			reject(e)
		  }
		})
	  }	
	  content.getcompressionRatio = function(str_base){
		var equalIndex = str_base.indexOf('=');
		if(str_base.indexOf('=')>0) {
		  str_base=str_base.substring(0, equalIndex);
		}
		const strlength = str_base.length;
		const filesize_b = parseInt(strlength-(strlength/8)*2);
		const filesize_M =  filesize_b/1024/1024;
		return 4/filesize_M;
	  }	
	  //压缩图片
	  content.compressionImage = function(img,compressionRatio){
		var canvas = document.createElement('canvas');
		var initSize = img.src.length;
		var width = img.width;
		var height = img.height;
		console.log("宽度："+width);
		console.log("高度："+height);
		console.log("传递的压缩比:"+compressionRatio);
		//如果图片大于四百万像素，计算压缩比并将大小压至400万以下
		var ratio;
		if ((ratio = width * height / 4000000) > 1) {
		  ratio = Math.sqrt(ratio);
		  width /= ratio;
		  height /= ratio;
		} else {
		  ratio = 1;
		}
		canvas.width = width;
		canvas.height = height;
		var ctx=canvas.getContext('2d');
		//铺底色
		ctx.fillStyle = "#fff";
		ctx.fillRect(0, 0, canvas.width, canvas.height);

		//如果图片像素大于100万则使用瓦片绘制
		var count;
		if ((count = width * height / 1000000) > 1) {
		  count = ~~(Math.sqrt(count) + 1); //计算要分成多少块瓦片

		  //计算每块瓦片的宽和高
		  var nw = ~~(width / count);
		  var nh = ~~(height / count);
		  var tCanvas = document.createElement('canvas');
		  tCanvas.width = nw;
		  tCanvas.height = nh;
		  var tctx = tCanvas.getContext('2d');
		  for (var i = 0; i < count; i++) {
			for (var j = 0; j < count; j++) {
			  tctx.drawImage(img, i * nw * ratio, j * nh * ratio, nw * ratio, nh * ratio, 0, 0, nw, nh);

			  ctx.drawImage(tCanvas, i * nw, j * nh, nw, nh);
			}
		  }
		} else {
		  ctx.drawImage(img, 0, 0, width, height);
		}
		ctx.drawImage(img, 0, 0, width, height);
		//进行最小压缩
		var ndata = canvas.toDataURL('image/jpeg', compressionRatio);

		console.log('压缩前：' + initSize);
		console.log('压缩后：' + ndata.length);
		console.log('压缩率：' + ~~(100 * (initSize - ndata.length) / initSize) + "%");

		tCanvas.width = tCanvas.height = canvas.width = canvas.height = 0;
		return ndata;
	  }
	return content;
})(window,$)