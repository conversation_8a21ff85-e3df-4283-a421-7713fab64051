const periodHelp = "lookup_150931";
const yearHelp = "lookup_247374";
const ledgerHelp = "lookup_981697";
const taskAbsHelp = "textarea_260925";

idp.event.bind("viewReady", function () {
    setDefaultValue();
})
function savePrintTask() {
    var defer = $.Deferred();

    var year = idp.control.get(yearHelp).getValue();
    var period = idp.control.get(periodHelp).getValue();
    var ledger = idp.control.get(ledgerHelp).getValue();
    var ledgerText = idp.control.get(ledgerHelp).getText();
    var taskAbs = idp.control.get(taskAbsHelp).getValue();
    if (!taskAbs) {
        taskAbs = idp.lang.get("Title_Year") + ": " + year + "/ " + idp.lang.get("Title_Period") + ": " + period + "/ " + idp.lang.get("Title_Ledger") + ledgerText; 
        if (taskAbs && taskAbs.length > 50) {
            taskAbs = taskAbs.substring(0, 45) + "...";
        }
    }
    var param = { year: year, period: period, ledger: ledger, taskAbs: taskAbs };
    idp.loading();
    idp.service
        .fetch("/api/fi/gl/v1.0/uniteprint/unitePrint/savePrintTask",
            param,
            true,
            "POST")
        .done(function (data) {
            idp.loaded();
            window.parent.canCloseDiag = true;
            defer.resolve(true);
        })
        .fail(function (data) {
            idp.loaded();
            defer.resolve(false);
        });
    return defer.promise();
}

// 获取年度和期间，赋值到帮助上
function setDefaultValue() {
    var checkData = window.parent.idp.store.get("FI_CheckPrintTask");
    if (checkData) {
        var ledgerName = checkData.value.ledgerName;
        var ledger = checkData.value.ledger;
        idp.control.get(ledgerHelp).setValue(ledger, ledgerName);
        var year = checkData.value.year;
        idp.control.get(yearHelp).setValue(year, year);
        var period = checkData.value.period;
        idp.control.get(periodHelp).setValue(period, period);

        idp.control.get(ledgerHelp).setDisabled();
        idp.control.get(yearHelp).setDisabled();
        idp.control.get(periodHelp).setDisabled();
        idp.control.get(taskAbsHelp).setDisabled();
    } else {
        // 根据js获取日期
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        if (month < 10) {
            month = "0" + month;
        }
        idp.control.get(yearHelp).setValue(year, year);
        idp.control.get(periodHelp).setValue(month, month);
    }
}