var basePath = "";
var canCloseDiag = false;

idp.event.bind("domReady", function () {
    setGridFilter();
})
function addTask() {
    openDilogDYRY = $.leeDialog.open(createDialogConfig({
        title: idp.lang.get("TITLE_AddPrintTask"),
        url: basePath + "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=a1775a89-b7f3-cfa5-1586-e8ff65f6e0a3&hidebar=true"
    }));
}

// 对话框配置工厂
function createDialogConfig(config) {
    return {
        name: 'PRINTTASKDIALOG',
        isHidden: false,
        showMax: true,
        width: 700,
        slide: false,
        height: 450,
        onclose: function () {
            idp.store.commit("FI_CheckPrintTask", "");
        },
        buttons: [
            {
                text: idp.lang.get("But_Cancel"),
                cls: 'lee-dialog-btn-cancel',
                onclick: function (item, dialog) {
                    dialog.close();
                }
            },
            {
                text: idp.lang.get("But_Confirm"),
                id: 'confirmset',
                cls: 'lee-btn-primary lee-dialog-btn-ok',
                onclick: function (item, dialog) {
                    document.getElementById("PRINTTASKDIALOG")
                        .contentWindow.savePrintTask()
                        .then(flag => {
                            if (flag && canCloseDiag) {
                                idp.uiview.refreshGrid("grid_main");
                                canCloseDiag = false;
                                dialog.close();
                            }
                        });
                }
            }
        ],
        ...config
    };
}


function editTask() {
    openDilogDYRY = $.leeDialog.open(createDialogConfig({
        title: idp.lang.get("TITLE_EditPrintTask"),
        url: basePath + "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=a1775a89-b7f3-cfa5-1586-e8ff65f6e0a3&hidebar=true"
    }));
}

/**
 *  删除打印任务
 * @returns 
 */
function deleteTask() {
    var row = idp.control.get("grid_main").getSelected();
    if (!row || !row.TASKID) {
        idp.warn(idp.lang.get("MSG_SelectPrintTask"));
        return;
    }
    idp.confirm(idp.lang.get("MSG_ConfirmDeletePrintTask"), function () {
        idp.service.fetch("/api/fi/gl/v1.0/uniteprint/unitePrint/deletePrintTask", { taskid: row.TASKID }, true, "POST")
            .done(function (data) {
                idp.tips(idp.lang.get("MSG_DeletePrintTaskSuccess"));
                idp.uiview.refreshGrid("grid_main");
            })
    }, function () {
        return false;
    });

}

/**
 *  检查打印任务(根据当前行数据，获取之前录入的信息)
 * @returns 

 */
function checkTask() {
    var row = idp.control.get("grid_main").getSelected();
    if (!row || !row.TASKID) {
        return;
    }
    var param = { taskid: row.TASKID };
    idp.service.fetch("/api/fi/gl/v1.0/uniteprint/unitePrint/checkPrintTask", param, true, "POST")
        .done(function (data) {
            openDilogDYRY = $.leeDialog.open(createDialogConfig({
                title: idp.lang.get("TITLE_CheckPrintTask"),
                url: basePath + "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=a1775a89-b7f3-cfa5-1586-e8ff65f6e0a3&hidebar=true"
            }));
        })
        .fail(function (data) {
        });
}




function getBasePath() {
    var paths = document.location.pathname.split("/");
    var appsIndex = paths.indexOf('apps');
    if (paths.length > 1 && appsIndex > 1) {
        for (var i = 0; i < appsIndex; i++) {
            if (!paths[i]) continue;
            basePath += "/" + paths[i];
        }
    }
}

function setGridFilter() {
    idp.event.register("grid_main", "beforeGridFilter", function (e, filter) {
        var filter = [];
        filter.push({
            Left: "",
            Field: "createuserid",
            Operate: "=",
            IsExpress: false,
            Value: idp.context.get("UserId"),
            Right: "",
            Logic: "AND",
        });

        return filter;
    })
}

function downloadFile(row) {

    var param = {
        ID: row.ID
    }
    idp.loading();
    idp.service.fetch("/api/fi/gl/v1.0/uniteprint/unitePrint/downloadFile", { paramMap: JSON.stringify(param) }
        , true, "post", "binary"
    ).done(function (data) {
        var file = new FileReader();
        file.onload = function (e) {
            var a = document.createElement('a');
            a.href = e.target.result;
            //设置文件名称
            a.download = idp.lang.get("Export_AccDocExport") + ".zip";
            if (navigator.userAgent.indexOf('Firefox') > -1) { //火狐
                document.body.appendChild(a);
                a.setAttribute("type", "hidden");
            }
            a.click();
            idp.loaded();
        }
        file.readAsDataURL(data);
    }).fail(function (data) {
        if (data.responseJSON && data.responseJSON.status == 600) {
            idp.error("文件已存在");
        } else {
            idp.error('导出失败');
        }
        idp.loaded();
    })
}