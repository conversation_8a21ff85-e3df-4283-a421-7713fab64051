function print(){
    var rowdata = idp.control.get("grid_main").selected;
    if (rowdata.length === 0) {
        return idp.error(idp.lang.get("alertinfo1"));//请选择一条数据
    }
    var dataMulti = "";
    for(var i = 0; i < rowdata.length; i++){
        if(i === rowdata.length-1){
            dataMulti = dataMulti + rowdata[i].ID;
        }else{
            dataMulti = dataMulti + rowdata[i].ID + ",";
        }
    }
    idp.print.startCloud("f3fe23eb-f0a1-8d43-e2cd-1ba05bc32832",dataMulti);
    return true;
}
function printCard(){
    var rowdata = idp.control.get("grid_main").selected;
    if (rowdata.length === 0) {
        return idp.error(idp.lang.get("alertinfo1"));//请选择一条数据
    }
    var dataMulti = "";
    var IDList = [];
    for(var i = 0; i < rowdata.length; i++){
        if(i === rowdata.length-1){
            dataMulti = dataMulti + rowdata[i].ID;
            IDList.push(rowdata[i].ID);
        }else{
            dataMulti = dataMulti + rowdata[i].ID + ",";
            IDList.push(rowdata[i].ID);
        }
    }
    var params = {};
    params.CZID = "GET_ILCONTRNOLIST";
    params.ID = IDList;
    //获取贷款申请单对应的合同编号
    var url = "/api/ihc/il/innerLoanIOUsService/v1.0/innerLoanIOUs/operation";
    idp.service.fetch(url, params, false, "POST")
        .then(function(result) {
            if (result.result){
                idp.print.startCloud("e42d7e49-484a-4ab0-9efb-35a4e0d4be8c",dataMulti);
                return true;
            }
        });
}
function queryFuncButtonAlign(toorbarid) {
    var buttonRight = false;
    var params = "AppId=TMFnd&keyId=TMFnd_QueryFuncButtonRight";
    var url = "/api/ihc/ihcfnd/v1.0/ihcfndapi/checkConfig?" + params;
    idp.service.fetch(url, null, false, "PUT").then(function (result) {
        idp.loaded();
        if (result && result.isSuccess) {
            buttonRight = true;
        } else {
            buttonRight = false;
        }
    }).fail(function () {
        idp.loaded();
    });
    if (buttonRight) {
        let idpobj = idp.control.get(toorbarid);
        // 右移按照顶部工具栏处理
        if(idpobj.options.items && idpobj.options.items.length > 0) {
            let newarr = [...idpobj.options.items];
            newarr.forEach((item) => {
                idp.control.get(`toolbar1`).addItem(item);
            });
            idp.control.get(`toolbar1`).options.items = [...idp.control.get(`toolbar1`).options.items, ...idpobj.options.items];
            idp.control.get(`toolbar1`).setFlex();
            newarr.forEach((item) => {
                idpobj.removeItem(item.id);
            });
            idpobj.options.items = new Array();
            idpobj.setFlex();
        }
        document.querySelector(`#${toorbarid}`).style = `border: none; padding: 0px`;
    } else {
        var leeToolbar = $('.toolbar.lee-toolbar');
        // 左移移掉标题
        $("body").addClass("nobar");
        leeToolbar[0].style = 'border: none;';
    }
}

idp.event.bind("domReady", function() {
    idp.event.register("input_621445", "beforeHelpFilter", function(e, g) {
        //根据资金组织过滤借款单位
        var TreasureOrgID = idp.control.get("input_569523").getValue();
        var filters = [];
        filters.push({
            "Left": "",
            "Field": "ID",
            "Operate": "in",
            "IsExpress": true,
            "Value": {
                sqlId:"NBDKSQLYS-NBDKDKSQDCX1",
                params:{
                    TreasureOrgID:TreasureOrgID
                }
            },
            "Right": "",
            "Logic": "",
            "FType": ""
        });
        return filters;
    });
    idp.event.bind("loadData", function(Data) {
        idp.control.get('toolbar_218740').setFlex();
        queryFuncButtonAlign(`toolbar_218740`);
    })
})