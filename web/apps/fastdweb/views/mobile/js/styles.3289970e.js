(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["styles"],{"020e":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("card-view",{attrs:{tab:t.tab}},[i("div",{staticClass:"f-wf-bar",attrs:{slot:"tab"},slot:"tab"},[i("vtoolbar",{attrs:{data:t.buttons,id:"workflowToolbar"}})],1)])},n=[],o=i("dfd8"),a=i("4232"),r=i("72fb"),l={components:{CardView:o["default"],vtoolbar:a["a"]},data:function(){return{tab:!0,buttons:[]}},mounted:function(){var t=this;r["a"].loadScriptArr(["/apps/fastdweb/views/mobile/extend/sp.js"],(function(e){window.IDP_EXTEND_WORKFLOWINIT&&window.IDP_EXTEND_WORKFLOWINIT(t)}))},methods:{setButton:function(t){this.buttons=t}}},c=l,h=(i("bb80"),i("2877")),d=Object(h["a"])(c,s,n,!1,null,null,null);e["default"]=d.exports},"03e3":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{attrs:{title:t.pageTitle},on:{backClick:t.backClick}},[i("div",{ref:"refLayout",staticClass:"f-form-list",attrs:{slot:"content"},slot:"content"},[i(t.currentView,{tag:"div"})],1)])},n=[],o=(i("4de4"),i("7db0"),i("4160"),i("c975"),i("d81d"),i("4e82"),i("159b"),i("d8ad"),i("365c")),a=i("c968"),r=i("39ea"),l=i("72fb"),c=i("7342"),h=i("3daa"),d=i("0771"),u=i("e082"),f={components:{FarrisBpage:a["a"]},mixins:[d["a"]],data:function(){return{currentView:null}},beforeRouteLeave:function(t,e,i){i()},activated:function(){},created:function(){},mounted:function(){this.matchResource(),this.custom=new h["a"],this.store=new c["a"],this.service=o["b"]},methods:{openCard:function(t,e,i,s,n){n=n||"",i=i||this.styleId,t=t||"",e=e||"";var o=s||{};o.type="refresh",o.status=e,this.$router.push({path:"/"+i+"/card?dataId="+t+"&status="+e+n,params:o})},matchResource:function(){if(window.IDP_DY_COMPONENT){var t=r["a"].getQuery("path"),e=window.IDP_DY_COMPONENT[t];e&&this.loadResource(e)}},loadResource:function(t){var e=this;l["a"].loadCssArr(t["links"]),l["a"].loadScriptArr(t["scripts"],(function(i){i&&(e.currentView=t["name"]),t.loaded=!0}),!1)},backClick:function(){this.$router.go(-1)},openWfViewCard:function(t,e,i){var s={};if(e&&i){i.forEach((function(t){s[t.code]=t.value})),s.taskId=t,s.task=e,s.dataId=s.dataid,s.taskCenterSSO=6;var n=s.styleid;"true"==s.runtime&&(n=s.styleid+"~"+s.fdim+"~"+s.sdim+"~"+s.ext),this.$router.push({path:"/"+n+"/recorddetail",params:s,query:s})}else if(t){var o={taskId:t,sourceId:null,processInstanceId:null,terminal:"app"},a=this;u["a"].getTaskEntityByPayload(o).then((function(i){e=i;var n=[];e.actions.forEach((function(t){var i=e.taskEntity.actions.find((function(e){return e.code===t.code}));if(i){var s=e.taskEntity.state.toLowerCase();t.scope.indexOf(s)>-1&&n.push(Object.assign({},i,t))}})),n=n.sort((function(t,e){return t.sortOrder>e.sortOrder?1:-1})),n=n.map((function(t){return t.preEvents=e.actionEvents.filter((function(e){return e.actionCode===t.code&&"Pre"===e.type})).sort((function(t,e){return t.sortOrder>e.sortOrder?1:-1})),t.postEvents=e.actionEvents.filter((function(e){return e.actionCode===t.code&&"Post"===e.type})).sort((function(t,e){return t.sortOrder>e.sortOrder?1:-1})),t})),e.taskEntity.actions=n.filter((function(t){return!0!==t.isHyperlinkAction}));var o=e.actions.filter((function(t){return!0===t.isHyperlinkAction}));"wf"==o[0].typeId?u["a"].getAppInfo(e.taskEntity.sourceId).then((function(i){i.parameters.forEach((function(t){s[t.code]=t.value})),s.formUrl=i.url,s.taskId=t,s.task=e.taskEntity,s.dataId=s.dataid,s.taskCenterSSO=6;var n=s.styleid;"true"==s.runtime&&(n=s.styleid+"~"+s.fdim+"~"+s.sdim+"~"+s.ext),a.$router.push({path:"/"+n+"/recorddetail",params:s,query:s})}),(function(t){console.log(t),alert("获取表单信息失败，请联系管理员")})):alert("获取表单信息失败，请联系管理员")}),(function(t){console.log(t),alert("获取表单信息失败，请联系管理员")}))}else alert("任务信息不完全")}}},p=f,m=i("2877"),g=Object(m["a"])(p,s,n,!1,null,null,null);e["default"]=g.exports},"0741":function(t,e,i){},"0934":function(t,e,i){},"09c2":function(t,e,i){},"0a2d":function(t,e,i){},"0b71":function(t,e,i){},"0b9a":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",[i("div",{staticClass:"f-filter-arrow",class:t.filterCls,on:{click:function(e){t.showPop=!0}}},[i("van-icon",{attrs:{name:"filter-o"}})],1)]),i("van-popup",{style:{width:"85%",height:"100%"},attrs:{"get-container":"body",position:"right"},on:{open:t.openPopup},model:{value:t.showPop,callback:function(e){t.showPop=e},expression:"showPop"}},[i("div",{staticClass:"f-filter-panel"},[i("div",{staticClass:"f-filter-panel-title"},[t._v(t._s(t.getLang("filter_advancedf")))]),i("div",{staticClass:"f-filter-panel-form"},t._l(t.fields,(function(e,s){return i("dycontrol",{key:s,ref:e.id,refInFor:!0,attrs:{ctx:t.ctx,styleId:t.styleId,type:t.controls[e.id].type,vprops:t.controls[e.id],value:t.filterModel[e.field]},on:{input:t.updateForm}})})),1),i("div",{staticClass:"f-list-filter-button"},[i("van-button",{attrs:{type:"info","native-type":"submit"},on:{click:t.handleConfirm}},[t._v(" "+t._s(t.getLang("filter_f"))+" ")]),i("van-button",{attrs:{type:"default","native-type":"submit"},on:{click:t.handelReset}},[t._v(" "+t._s(t.getLang("filter_reset"))+" ")])],1)])])],1)},n=[],o=(i("99af"),i("4160"),i("d3b7"),i("ac1f"),i("1276"),i("159b"),i("d8ad"),{name:"filterbar",props:["name","type","ctx","label","value","options","readonly","vprops","styleId"],inject:["rootview"],computed:{filterCls:function(){var t=this.getFilter();return t.length?"f-filter-hasfilter":""}},components:{dycontrol:function(){return Promise.resolve().then(i.bind(null,"efee"))}},activated:function(){console.log(this)},data:function(){return{showPop:!1,filterModel:{},controls:{},fields:[]}},mounted:function(){this.getCols()},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},updateForm:function(t,e){console.log(t),console.log(e),this.$set(this.filterModel,t,e)},getCols:function(){var t=this;if(this.rootview){var e,i=this.rootview.gridController.getGridIdByDsCode(this.vprops.queryds),s=null===(e=this.rootview.controls[i])||void 0===e?void 0:e.cols;s&&s.forEach((function(e){var s=i+"."+e.id;t.controls[s]=t.rootview.controls[s],"number"==t.controls[s].type?t.controls[s].type="numberrange":"date"==t.controls[s].type&&(t.controls[s].editor_date.range=!0,t.controls[s].editor_date.transfer=!0),t.fields.push({id:s,field:t.controls[s].field})}))}},openPopup:function(){var t=this;this.$nextTick((function(){return t.$emit("open")}))},handleConfirm:function(){console.log(this.getFilter());var t=this.getFilter();this.hasFilter=t.length>0,this.$emit("input",t),this.showPop=!1},handelReset:function(){for(var t in this.filterModel)this.$set(this.filterModel,t,"");this.handleConfirm()},setLookupValue:function(t,e){this.getLookupValue(t).currentValue=e},getSingleFilter:function(t,e){var i=this.controls[t],s=[],n=this.filterModel[e];if(!n)return[];switch(i.type){case"date":var o=n.split(" - ");s.push({Field:e,Operate:">=",Value:o[0]+" 00:00:00",Logic:"and"}),s.push({Field:e,Operate:"<=",Value:o[1]+" 23:59:59",Logic:"and"});break;case"numberrange":var a=n.split(" - ");a[0]&&s.push({Field:e,Operate:">=",Value:a[0],Logic:"and"}),a[1]&&s.push({Field:e,Operate:"<=",Value:a[1],Logic:"and"});break;case"radio":s.push({Field:e,Operate:"=",Value:n,Logic:"and"});break;case"dropdown":case"checklist":s.push({Field:e,Operate:"in",Value:n.split(","),Logic:"and"});break;case"lookup":var r=this.getLookupValue(t)||n;s.push({Field:e,Operate:i.editor_lookup&&i.editor_lookup.ismul?"in":"=",Value:i.editor_lookup&&i.editor_lookup.ismul?r.split(";"):r,Logic:"and"});break;case"input":s.push({Field:e,Operate:"like",Value:n,Logic:"and"});break}return s},getLookupValue:function(t){var e=this.$refs[t][0].$children[0];return e?e.currentValue:""},getFilter:function(){var t=[];for(var e in this.fields){var i=this.getSingleFilter(this.fields[e].id,this.fields[e].field);i.length&&(t=t.concat(i))}return t.length&&(t[0].Left="(",t[t.length-1].Right=")",t[t.length-1].Logic=""),t}},watch:{value:function(t){this.currentValue=t}}}),a=o,r=(i("461d"),i("f1d7"),i("2877")),l=Object(r["a"])(a,s,n,!1,null,"7e02bf58",null);e["a"]=l.exports},1300:function(t,e,i){"use strict";var s=i("ac5e"),n=i.n(s);n.a},"157a":function(t,e,i){},"16c2":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-login-wrap"},[t._m(0),i("div",{staticClass:"f-login-form"},[i("van-field",{attrs:{center:"",clearable:"",placeholder:t.placeHolderTel},scopedSlots:t._u([{key:"left-icon",fn:function(){},proxy:!0}]),model:{value:t.userTel,callback:function(e){t.userTel=e},expression:"userTel"}}),i("van-field",{attrs:{center:"",clearable:"",placeholder:t.placeHolderPwd,type:t.typeInput},scopedSlots:t._u([{key:"left-icon",fn:function(){},proxy:!0},{key:"button",fn:function(){return[t.isEmail?t._e():i("a",{staticClass:"f-login-send",attrs:{href:"javascript:void(0)"},on:{click:t.sendMessage}},[t._v(t._s(t.messageStatus))])]},proxy:!0}]),model:{value:t.userSms,callback:function(e){t.userSms=e},expression:"userSms"}}),i("div",{staticClass:"f-login-email",on:{click:t.changeFormat}},[t._v(t._s(t.logInFormat))]),i("van-button",{staticClass:"f-login-btn",attrs:{type:"primary",color:"#3a90ff",block:""},on:{click:t.login}},[t._v("登录")]),t._m(1)],1),i("div",{staticClass:"f-login-f0oter"}),i("van-action-sheet",{attrs:{actions:t.actions},on:{select:t.onSelect},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}})],1)},n=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-login-header"},[i("div",{staticClass:"f-login-title"},[t._v(" 登录 ")]),i("div",{staticClass:"f-login-info"},[t._v(" 欢迎来到 GS Cloud ")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-login-policy"},[t._v(" 点击登录按钮表示您同意并愿意遵守 "),i("a",{attrs:{href:"javascript:void(0)"}},[t._v("《用户协议》")]),t._v(" 和 "),i("a",{attrs:{href:"javascript:void(0)"}},[t._v("《隐私政策》")])])}],o=(i("c975"),i("4d63"),i("ac1f"),i("25f0"),i("466d"),i("841c"),i("4cfb")),a=i("d399"),r=(i("bc3a"),{components:{},data:function(){return{userTel:"",userSms:"",isSended:!1,isEmail:!1,actions:[],messageStatus:"发送验证码",total:60,placeHolderTel:"请输入手机号码",placeHolderPwd:"请输入短信验证码",logInFormat:"邮箱登录",typeInput:"number",phone:"",email:""}},beforeRouteLeave:function(t,e,i){i()},activated:function(){document.title="GS Cloud 企业数字化平台",""!==this.getQuery("error")&&a["a"].fail("绑定失败, 请联系管理员")},mounted:function(){},methods:{isWeiXin:function(){var t=navigator.userAgent.toLowerCase();return-1!=t.indexOf("micromessenger")},sendMessage:function(){var t=this;o["a"].sendMessage(t.userTel).then((function(e){var i=setInterval((function(){t.total--,t.messageStatus="重新获取("+t.total+"s)",t.total<0&&(window.clearInterval(i),t.messageStatus="重新发送验证码",t.total=60)}),1e3);a["a"].success("发送成功")})).catch((function(t){a["a"].fail("发送失败")}))},login:function(){var t=this,e={username:this.userTel,password:this.userSms};console.log(this.isEmail.valueOf()),o["a"].validate(e,this.isEmail.valueOf()).then((function(e){console.log(e),t.email=e.mail,t.phone=e.phone,500!==e.status?e.enterprises.length>1?(t.actions=e.enterprises,t.show=!0):1==e.enterprises.length?t.onSelect(e.enterprises[0]):a["a"].fail("未获取到租户列表"):a["a"].fail("账户或密码错误")})).catch((function(t){console.log(t),a["a"].fail("账户或密码错误")}))},onSelect:function(t){this.show=!1,this.tenantId=t.id;var e={email:this.email,phone:this.phone};null!==e.email&&void 0!==e.email||(e.email="");var i="/api/fastdweb/runtime/v1.0/wechat/dobinging",s={tenantId:this.tenantId,bindStrategy:!0,bindParam:"weChatImpl",phone:this.phone,email:this.email};this.postForm(i,s)},postForm:function(t,e){var i=document.createElement("form");for(var s in i.action=t,i.target="_self",i.method="post",i.style.display="none",e){var n=document.createElement("textarea");n.name=s,n.value=e[s],i.appendChild(n)}document.body.appendChild(i),i.submit()},changeFormat:function(){this.isEmail=!this.isEmail},getQuery:function(t){var e=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),i=window.location.search.substr(1).match(e);return null!=i?unescape(i[2]):window.location.hash&&(i=window.location.hash.substr(3).match(e),null!=i)?unescape(i[2]):""}},watch:{isEmail:function(t){t?(this.placeHolderTel="请输入邮箱",this.placeHolderPwd="请输入密码",this.logInFormat="手机验证码登录",this.typeInput="password"):(this.placeHolderTel="请输入手机号码",this.placeHolderPwd="请输入短信验证码",this.logInFormat="邮箱登录",this.typeInput="number"),this.userSms="",this.userTel=""}}}),l=r,c=(i("e0d9"),i("2877")),h=Object(c["a"])(l,s,n,!1,null,"6fecf61c",null);e["default"]=h.exports},"18a8":function(t,e,i){},"1ba7":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{"overflow-y":"auto"}},[i("ul",{staticClass:"tree"},t._l(t.datas,(function(e,s){return i("tree-node",{key:s,ref:"treeNode",refInFor:!0,attrs:{ttComp:t.ttComp,favor:t.favor,ttField:t.ttField,stField:t.stField,level:1,ids:t.ids,node:e,autoFold:t.autoFold,userJS:t.userJS,isMul:t.isMul,noCode:t.noCode,selectMemory:t.selectMemory,helpInput:t.helpInput,idField:t.idField,isTotal:t.isTotal,autoChild:t.autoChild,isList:t.isList,fields:t.fields,totalComp:t.totalComp,contentComp:t.contentComp,stComp:t.stComp,iconComp:t.iconComp,labelComp:t.labelComp,childOnly:t.childOnly,treeConfig:t.treeConfig},on:{favorClick:t.favorClick,treeNodeClick:t.treeNodeClick,beforeExpand:t.beforeExpand,autoCheck:t.autoCheck}})})),1),t._t("default")],2)},n=[],o=(i("99af"),i("4de4"),i("4160"),i("caad"),i("2532"),i("159b"),i("f382")),a={data:function(){return{ids:this.value,datas:[],labels:[]}},props:["nodes","value","isNeedChangData","treeConfig","stField","ttField","favor","autoFold","userJS","ttComp","isMul","noCode","helpInput","idField","isTotal","autoChild","selectMemory","isList","fields","stComp","totalComp","contentComp","labelComp","iconComp","childOnly"],components:{TreeNode:function(t){return Promise.all([i.e("styles"),i.e("chunk-2d2089ec")]).then(function(){var e=[i("8afe")];t.apply(null,e)}.bind(this)).catch(i.oe)}},watch:{nodes:function(t){this.datas=this.getDisplayData(t)}},created:function(){this.datas=this.getDisplayData(this.nodes)},methods:{beforeExpand:function(t){this.$emit("beforeExpand",t)},treeNodeClick:function(t){this.$emit("treeNodeClick",t)},autoCheck:function(t){this.$emit("autoCheck",t)},favorClick:function(t){this.$emit("favorClick",t)},addRoot:function(t){this.datas=this.datas.concat(t)},isCheckedAll:function(){if(!this.$refs.treeNode||!this.$refs.treeNode.length)return!1;for(var t=0;t<this.$refs.treeNode.length;t++)if(!this.$refs.treeNode[t].isCheckedAll())return!1;return!0},onCheckAll:function(t){var e,i=this,s=this.getCheckedId(),n=this.getNodesId();t?(e=this.childOnly?this.nodes.filter((function(t){return 1==t[i.treeConfig.isdetail]&&!s.includes(t[i.treeConfig.id])})):this.nodes.filter((function(t){return!s.includes(t[i.treeConfig.id])})),this.$route.params.helpData=this.$route.params.helpData.concat(e)):(e=this.$route.params.helpData.filter((function(t){return!n.includes(t[i.treeConfig.id])})),this.$route.params.helpData=e),this.$refs.treeNode.forEach((function(e){e.onCheckAll(t)}))},getNodesId:function(){var t=this,e=[];return this.nodes.forEach((function(i){e.push(i[t.treeConfig.id])})),e},getCheckedId:function(){var t=this,e=[];return this.$route.params.helpData.forEach((function(i){e.push(i[t.treeConfig.id])})),e},getDisplayData:function(t){var e=JSON.parse(JSON.stringify(t));e.forEach((function(t){t.isChoose=!1}));var i=[];return i="2"==this.treeConfig.treetype?o["a"].gradeToTree(e,this.treeConfig.grade,this.treeConfig.level,this.treeConfig.detail,this.treeConfig.format):o["a"].arrayToTree(e,this.treeConfig.id,this.treeConfig.pid),console.log(i),i}}},r=a,l=(i("1c42"),i("2877")),c=Object(l["a"])(r,s,n,!1,null,"19f21f5d",null);e["a"]=c.exports},"1bbb":function(t,e,i){"use strict";var s=i("e333"),n=i.n(s);n.a},"1be0":function(t,e,i){"use strict";var s=i("9964"),n=i.n(s);n.a},"1c05":function(t,e,i){},"1c42":function(t,e,i){"use strict";var s=i("0a2d"),n=i.n(s);n.a},"1c58":function(t,e,i){"use strict";var s=i("dc7e"),n=i.n(s);n.a},"1e0c":function(t,e,i){},2024:function(t,e,i){},"203b":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{attrs:{title:t.pageTitle,tab:"true"},on:{backClick:t.backClick}},[i("div",{staticClass:"f-flex-wrap",attrs:{slot:"content"},slot:"content"},[i("div",{staticClass:"f-wechat-top",on:{click:function(e){return t.openUserInfo()}}},[i("div",{staticClass:"f-wechat-avatar",on:{click:function(e){return t.openUserInfo()}}},[i("img",{attrs:{src:t.userImageBlob}})]),i("div",{staticClass:"f-wechat-user-info"},[i("div",{staticClass:"f-wechat-user-title"},[t._v(" "+t._s(t.userName)+" ")]),i("div",{staticClass:"f-wechat-user-info"},[t._v(" "+t._s(t.orgName)+" "),i("van-icon",{directives:[{name:"show",rawName:"v-show",value:t.isUsed,expression:"isUsed"}],attrs:{name:"setting-o",size:"20"},on:{click:function(e){return e.stopPropagation(),t.getTenantList()}}})],1)])]),i("van-notice-bar",{attrs:{"left-icon":"volume-o",text:"赋能智慧企业建设 浪潮发布新一代大型企业数字化平台 GSCloud"}}),t._l(t.groups,(function(e,s){return i("div",{key:s,staticStyle:{"margin-top":"10px"}},[i("div",{staticClass:"f-grid-title-block"},[t._v(t._s(s))]),i("van-grid",{staticClass:"f-grid-menu",attrs:{square:"",border:!1,clickable:""}},t._l(e,(function(e,s){return i("van-grid-item",{key:s,attrs:{icon:"photo-o",text:e[t.titleField]},on:{click:function(i){return t.onClick(e,s)}}},[e[t.srcField]?i("van-image",{attrs:{src:e[t.srcField]}}):t._e(),e[t.srcField]?i("div",{staticClass:"f-grid-menu-title",staticStyle:{"margin-top":"5px"}},[t._v(t._s(e[t.titleField]))]):t._e()],1)})),1)],1)})),i("div",{ref:"weChatDashBoard"}),i("van-action-sheet",{attrs:{actions:t.actions,description:"请选择切换租户","cancel-text":"退出登录"},on:{select:t.onSelect,cancel:t.logout},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}})],2),i("div",{attrs:{slot:"tab"},slot:"tab"},[i("van-tabbar",{staticClass:"f-wechat-tab",attrs:{route:""},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[i("van-tabbar-item",{attrs:{icon:"wap-home",to:"/wechat"}},[t._v("首页")]),i("div",{staticClass:"f-tab-scan"},[i("div",{staticClass:"f-round-corner"}),i("div",{staticClass:"f-scan-button",on:{click:function(e){return t.scanQRCode()}}},[i("van-icon",{attrs:{name:"scan"}})],1)]),i("div",{staticClass:"f-scan-title"},[t._v("扫一扫")]),i("van-tabbar-item",{attrs:{icon:"setting-o",to:"/guide"}},[t._v("应用")])],1)],1)])},n=[],o=(i("4de4"),i("c975"),i("4e82"),i("a434"),i("ac1f"),i("5319"),i("1276"),i("d399")),a=i("c968"),r=i("2e27"),l=i("c956"),c=i("4cfb"),h=(i("d8ad"),i("4901")),d=(i("3daa"),window.idp.Notify),u={components:{FarrisBpage:a["a"],GridMenu:r["a"]},data:function(){return{userName:"",orgName:"",phone:"",userImageBlob:"/platform/runtime/sys/web/assets/img/avatar-default.png",pageTitle:"GS Cloud 企业数字化平台",titleField:"Name",srcField:"Icon",groups:{},tenantId:"",actions:[],show:!1,isUsed:!0}},beforeRouteLeave:function(t,e,i){i()},activated:function(){document.title="GS Cloud 企业数字化平台"},mounted:function(){this.redirect(),document.title="GS Cloud 企业数字化平台",this.isWeiXin()&&h["a"].initConfig(),window.idpWechatViewReady&&window.idpWechatViewReady(),this.initWeChatgDiv()},methods:{isWeiXin:function(){var t=navigator.userAgent.toLowerCase();return-1!=t.indexOf("micromessenger")},scanQRCode:function(){window.WX_APP_Bridge&&window.WX_APP_Bridge.appScan&&window.WX_APP_Bridge.appScan()},initWeChatgDiv:function(){window.WX_APP_Bridge&&window.WX_APP_Bridge.appDashBoard&&window.WX_APP_Bridge.appDashBoard(this.$refs.weChatDashBoard)},openUserInfo:function(){window.location.href="/apps/fastdweb/views/mobile/index.html#/a285c09d-2816-71e3-6a07-9ce486573bc3/list"},onClick:function(t,e){if(-1!=t.Path.indexOf("idp:")){var i=t.Path.replace("idp:",""),s=i.split("/"),n=s[1].split("?"),o=s[1],a={};if(2==n.length){o=n[0];var r=n[1].split("&");for(var l in r){var c=r[l].split("=");a[c[0]]=c[1]}}this.openPage(s[0],o,a)}else-1!=t.Path.indexOf("url:")?window.location.href=t.Path.replace("url:",""):-1!=t.Path.indexOf("iframe:")&&this.openUrl(t.Name,t.Path.replace("iframe:",""))},formatData:function(t){t.sort((function(t,e){return t.SortOrder<e.SortOrder?-1:t.SortOrder>e.SortOrder?1:0}));var e=t.filter((function(t,e){return 2==t.Layer}));e.length>8&&e.splice(7,e.length-8);var i={"常用应用":e};this.groups=i},openUrl:function(t,e){this.$router.push({name:"iframe",path:"/iframe",params:{src:e,title:t}})},openPage:function(t,e,i,s){this.$router.push({name:t+e,path:"/"+t+"/"+e,params:s,query:i})},getChildren:function(t,e){return t.filter((function(t,i){return t.ParentId==e}))},redirect:function(){var t=this;o["a"].loading({duration:2e3,message:"加载中...",forbidClick:!0});var e=this;l["Service"].getMobileFunc().then((function(t){if(o["a"].clear(),"ok"==t.Code){var i=t.Data;e.formatData(i)}else d({type:"danger",message:t.Data.Message})})),c["a"].getUserInfo().then((function(e){console.log(e),e.userSetting&&e.userSetting.imgblob&&(t.userImageBlob=e.userSetting.imgblob),e.orgName&&(t.orgName=e.orgName),e.name&&(t.userName=e.name),e.mobilePhone&&(t.phone=e.mobilePhone),console.log(e)}))},openCard:function(t,e,i,s,n){i=i||this.styleId,t=t||"",e=e||"";var o=s||{};o.type="refresh",this.$router.replace({path:"/"+i+"/card?dataId="+t+"&status="+e+n,params:o})},getTenantList:function(){this.show=!1;var t=this;console.log(this.phone),l["Service"].getTenantList(this.phone).then((function(e){"ok"===e.Code?(t.show=!0,t.actions=e.Data):o["a"].fail("获取组合列表失败")}))},onSelect:function(t){var e="/api/fastdweb/runtime/v1.0/wechat/changeTenant",i={tenantId:t.id};this.postForm(e,i)},postForm:function(t,e){var i=document.createElement("form");for(var s in i.action=t,i.target="_self",i.method="get",i.style.display="none",e){var n=document.createElement("textarea");n.name=s,n.value=e[s],i.appendChild(n)}document.body.appendChild(i),i.submit()},logout:function(){l["Service"].logout(this.phone).then((function(t){"ok"==t.Code?window.location.href="/api/runtime/sys/v1.0/routerRedirect?language=zh-CHS&callType=1&bindStrategy=true&bindParam=weChatImpl&callParam=%252fapps%252ffastdweb%252fviews%252fmobile%252fredirect.html%253fhash%253dwechat":d({type:"danger",message:t.Data.Message})}))},backClick:function(){window.backClick&&window.backClick()}}},f=u,p=(i("1be0"),i("2877")),m=Object(p["a"])(f,s,n,!1,null,null,null);e["default"]=m.exports},"20a8":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-action-btn",on:{click:t.openClick}},[t.icon?t._e():i("div",{staticClass:"f-action-name"},[i("span",{staticStyle:{margin:"0 auto"}},[t._v(t._s(t.name))])]),t.icon?i("van-icon",{staticClass:"f-action-vanticon",attrs:{name:t.icon}}):t._e()],1)},n=[],o=i("e2e1"),a={data:function(){return{}},mounted:function(){Object(o["a"])(this.$el)},props:["icon","name"],computed:{},methods:{openClick:function(){this.$emit("click")}}},r=a,l=(i("d3b7d"),i("2877")),c=Object(l["a"])(r,s,n,!1,null,null,null);e["a"]=c.exports},2395:function(t,e,i){},2419:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{attrs:{title:t.pageTitle},on:{backClick:t.backClick}},[i("div",{staticClass:"f-list-wrap",attrs:{slot:"content"},slot:"content"},[t.isForiegn?i("van-tabs",{attrs:{color:"#388fff"},on:{click:t.onTabClick},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[i("van-tab",{attrs:{title:t.getLang("city_domestic")}}),i("van-tab",{attrs:{title:t.getLang("city_abroad")}})],1):t._e(),i("van-search",{attrs:{placeholder:t.getLang("city_search")},on:{input:t.onSearch},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),i("div",{staticClass:"f-list-warp-main"},[t.isQueryMode?t._e():i("div",{staticClass:"f-group-title"},[t._v(t._s(t.getLang("city_hot")))]),t.isQueryMode?t._e():i("div",{staticClass:"f-btn-list",staticStyle:{padding:"10px"}},t._l(t.hotList,(function(e,s){return i("div",{key:s,staticClass:"f-btn-check",on:{click:function(i){return t.onConfirm(e,s)}}},[t._v(t._s(e[t.showField]))])})),0),t.isQueryMode?t._e():i("van-index-bar",{attrs:{"index-list":t.indexList}},[t._l(t.indexList,(function(e,s){return[i("van-index-anchor",{key:s,attrs:{index:e}},[t._v(t._s(e))]),t._l(t.indexMap[e],(function(e,s){return i("van-cell",{key:s,attrs:{cx:e,title:e[t.showField]},on:{click:function(i){return t.onConfirm(e,s)}}})}))]}))],2),t.isQueryMode?i("div",t._l(t.searchList,(function(e,s){return i("van-cell",{key:s,attrs:{title:e[t.showField]},on:{click:function(i){return t.onConfirm(e,s)}}})})),1):t._e()],1)],1)])},n=[],o=(i("99af"),i("4de4"),i("4160"),i("4e82"),i("b64b"),i("d3b7"),i("3ca3"),i("159b"),i("ddb0"),i("96cf"),i("1da1")),a=i("b982"),r=i("c968"),l=i("62e4"),c=i("1ba7"),h=null,d={components:{dylistitem:a["a"],FarrisBpage:r["a"],treelist:c["a"]},props:{styleId:{type:String,required:!1},controlId:{type:String,required:!1},filter:{type:Array,required:!1},title:{type:String,required:!1},isMul:{type:Boolean,required:!1},isAsync:{type:Boolean,required:!1}},data:function(){return{indexList:[],searchList:[],indexMap:{},hotList:[],tabColor:"#388fff",pageTitle:"",active:0,show:!1,list:[],loading:!1,listConfig:null,finished:!1,value:"",refreshing:!1,pageIndex:0,pageSize:1e4,totalCount:0,filterArr:[],isQueryMode:!1,isTree:!1,cityConfig:{field:"",showField:"",city:{filter:[],api:"",mainFilter:[]},group:{filter:[],api:""}},showField:"",treeConfig:{treetype:"",grade:"",level:"",isdetail:"",id:"",pid:"",rootValue:"",format:"",name:""},fields:{title:"",subtitle:"",label:""},isForiegn:!1}},beforeRouteLeave:function(t,e,i){this.confirm&&(t.query.returnValue={controlId:this.controlId,data:this.confirmData}),t.query.back=!0,t.query.fromHelp=!0,i()},activated:function(){this.activeParams()},mounted:function(){this.activeParams(),this.init()},watch:{active:function(){this.showView()}},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},activeParams:function(){this.controlId=this.$route.params.controlId,this.filter=this.$route.params.filter,this.title=this.$route.params.title,this.styleId=this.$route.params.styleId,this.pageTitle=this.$route.params.title,this.async=this.$route.params.async,this.favor=this.$route.params.favor,this.formId=this.$route.params.formId,this.cityConfig=this.$route.params.cityConfig,this.isForiegn=this.$route.params.isForiegn,this.showField=this.cityConfig.showField,h=new l["a"](this.styleId)},getHotCity:function(){return h.getListPage(this.getQueryParam(!0))},getALLCity:function(){return h.getListPage(this.getQueryParam())},getMainCity:function(){return h.getListPage(this.getQueryParam(!1,!0))},getQueryParam:function(t,e){return{sqlId:this.styleId,fields:this.getFilter(t,e),orders:this.getOrder(),count:!0,bizOpId:"",bizId:"",page:this.pageIndex,pagesize:this.pageSize}},onSearch:function(){var t=this;this.value?this.getALLCity().then((function(e){"ok"==e.Code&&(t.searchList=e.Data.Rows),t.isQueryMode=!0})):this.showView()},getFilter:function(t,e){var i=[],s=[],n=[],o=JSON.parse(JSON.stringify(this.filter));if(this.isForiegn&&(0===this.active?(n=JSON.parse(JSON.stringify(this.cityConfig.city.domesticFilter)),n.length>0&&(n[0].Left+="(",n[n.length-1].Right+=")",n[n.length-1].Logic="and")):(n=JSON.parse(JSON.stringify(this.cityConfig.city.foreignFilter)),n.length>0&&(n[0].Left+="(",n[n.length-1].Right+=")",n[n.length-1].Logic="and")),o=o.concat(n)),t)return i=JSON.parse(JSON.stringify(this.cityConfig.city.filter)),o.concat(i);if(e)return this.cityConfig.city.mainFilter&&(s=JSON.parse(JSON.stringify(this.cityConfig.city.mainFilter))),o.concat(s);if(this.value)for(var a in this.filterArr){var r="",l="";0==a&&(r="("),a==this.filterArr.length-1&&(l=")"),i.push({Left:r,Field:this.filterArr[a],Operate:" like ",IsExpress:!1,Value:"%"+this.value+"%",Right:l,Logic:a<this.filterArr.length-1?" or ":""})}return o.length>0&&i.length>0&&(o[o.length-1].Logic=" and "),o.concat(i)},getOrder:function(){return[]},showView:function(){var t=[],e=this;t.push(this.getMainCity()),t.push(this.getHotCity()),Promise.all(t).then((function(t){var i=t[0],s=t[1];if(e.indexMap={},"ok"==s.Code&&(e.hotList=s.Data.Rows),"ok"==i.Code){e.list=i.Data.Rows;var n=e.cityConfig.field;if(n)for(var o in e.list){var a=e.list[o];if(a[n]){var r=a[n].substring(0,1).toUpperCase();e.indexMap[r]=e.indexMap[r]||[],e.indexMap[r].push(e.list[o])}}}e.indexList=Object.keys(e.indexMap).sort(),e.isQueryMode=!1})).catch((function(t){console.log("oh no",t)}))},init:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(){var e=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.listConfig){t.next=3;break}return t.next=3,h.getListInfo(this.styleId).then((function(t){var i=t.Data;e.listConfig=t.Data,i.TreeType&&(e.isTree=!0,e.treeConfig={treetype:i.TreeType,grade:i.WbsCol,level:i.RankCol,isdetail:i.LeafCol,id:i.TreeIdCol,pid:i.ParentCol,rootValue:i.RootVal,format:i.PcolExp,name:i.NodeCol}),e.pageTitle||(e.pageTitle=e.listConfig.MC);var s=[];e.listConfig.Cols.forEach((function(t){"1"==t.IsShow&&s.push(t.Field)})),s.length&&(e.fields.title=s[0],s.length>1&&(e.fields.subtitle=s[1]),s.length>2&&(e.fields.label=s[2])),e.filterArr=s,console.log(e.fields)}));case 3:this.showView();case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getBizInfo:function(){var t={bizId:"",bizOpId:""};return t.isTotal=this.isTotal,t},onConfirm:function(t,e){this.confirm=!0,this.confirmData=t,this.$router.go(-1)},backClick:function(){this.$router.go(-1)},onTabClick:function(){console.log(this.active)}}},u=d,f=(i("57c5"),i("2877")),p=Object(f["a"])(u,s,n,!1,null,null,null);e["default"]=p.exports},"24cb":function(t,e,i){"use strict";var s=i("9579"),n=i.n(s);n.a},2501:function(t,e,i){"use strict";var s=i("aded"),n=i.n(s);n.a},"26c6":function(t,e,i){},2771:function(t,e,i){"use strict";var s=i("51d0"),n=i.n(s);n.a},2870:function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"button",attrs:{id:"CircleMenu"}},[i("div",{class:t.type,attrs:{animate:t.animate}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.MaskToggle,expression:"MaskToggle"}],staticClass:"oy-mask-white",on:{click:t.toggle}}),i("div",{staticClass:"oy-menu-group",class:{open:t.open}},[t.number>1?i("button",{ref:t.btnMain,staticClass:"oy-menu-btn btn-toggle ",class:{"oy-menu-btn-Circle":t.circle},on:{click:t.toggle}},[i("i",{staticClass:"icon-bars"}),t._t("item_btn")],2):t._e(),1==t.number?i("drag",{class:{"oy-menu-btn-Circle":t.circle},attrs:{icon:t.icon,name:t.name},on:{click:t.toggle}}):t._e(),i("div",{staticClass:"btn-list"},[i("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.floatBtn.item1.name,expression:"floatBtn.item1.name != ''"}],staticClass:"oy-menu-item oy-menu-item_1 ",class:t.AnimateClass,on:{click:function(e){return t.handelItemClick(0)}}},[t._t("item_1")],2),i("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.floatBtn.item2.name,expression:"floatBtn.item2.name != ''"}],staticClass:"oy-menu-item oy-menu-item_2 ",class:t.AnimateClass,on:{click:function(e){return t.handelItemClick(1)}}},[t._t("item_2")],2),i("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.floatBtn.item3.name,expression:"floatBtn.item3.name != ''"}],staticClass:"oy-menu-item oy-menu-item_3 ",class:t.AnimateClass,on:{click:function(e){return t.handelItemClick(2)}}},[t._t("item_3")],2),i("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.floatBtn.item4.name,expression:"floatBtn.item4.name != ''"}],staticClass:"oy-menu-item oy-menu-item_4 ",class:t.AnimateClass,on:{click:function(e){return t.handelItemClick(3)}}},[t._t("item_4")],2),i("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.floatBtn.item5.name,expression:"floatBtn.item5.name != ''"}],staticClass:"oy-menu-item oy-menu-item_5 ",class:t.AnimateClass,on:{click:function(e){return t.handelItemClick(4)}}},[t._t("item_5")],2),i("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.floatBtn.item6.name,expression:"floatBtn.item6.name != ''"}],staticClass:"oy-menu-item oy-menu-item_6 ",class:t.AnimateClass,on:{click:function(e){return t.handelItemClick(5)}}},[t._t("item_6")],2),i("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.floatBtn.item7.name,expression:"floatBtn.item7.name != ''"}],staticClass:"oy-menu-item oy-menu-item_7 ",class:t.AnimateClass,on:{click:function(e){return t.handelItemClick(6)}}},[t._t("item_7")],2),i("div",{directives:[{name:"show",rawName:"v-show",value:""!=t.floatBtn.item8.name,expression:"floatBtn.item8.name != ''"}],staticClass:"oy-menu-item oy-menu-item_8 ",class:t.AnimateClass,on:{click:function(e){return t.handelItemClick(7)}}},[t._t("item_8")],2)])],1)])])},n=[],o=(i("a9e3"),i("e2e1")),a=i("20a8"),r={name:"CirleMenu",components:{drag:a["a"]},props:{type:{type:String,required:!0},number:{type:Number,required:!0},icon:String,name:String,animate:String,mask:String,circle:Boolean,btn:Boolean,colors:Array,floatBtn:Array},data:function(){return{btnMain:"btnMain",open:!1,toggleAnimate:!1,MaskToggle:!1,BtnColor:"",Item1Color:"",Item2Color:"",Item3Color:"",Item4Color:"",Item5Color:"",Item6Color:"",Item7Color:"",Item8Color:""}},mounted:function(){1==this.number?(this.name=this.floatBtn.item1.name,this.icon=this.floatBtn.item1.icon):Object(o["a"])(this.$refs.button)},methods:{handelItemClick:function(t){this.$emit("btnClick",t)},toggleStatus:function(t){t!=this.open&&1!=this.number&&(this.open=!this.open,this.toggleAnimate=!this.toggleAnimate,this.MaskToggle=!this.MaskToggle)},toggle:function(t){t!=this.open&&(1!=this.number?(this.open=!this.open,this.toggleAnimate=!this.toggleAnimate,this.MaskToggle=!this.MaskToggle):this.handelItemClick(0))}},computed:{AnimateClass:function(){return this.toggleAnimate?this.animate:""}}},l=r,c=(i("4d48"),i("2877")),h=Object(c["a"])(l,s,n,!1,null,null,null);e["a"]=h.exports},"29c5":function(t,e,i){},"2b42":function(t,e,i){},"2b6b":function(t,e,i){"use strict";var s=i("f1e3"),n=i.n(s);n.a},"2e27":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.isGroup?t._e():i("van-grid",{staticClass:"f-grid-simple",attrs:{square:!t.textHeight,border:!1,clickable:""}},t._l(t.list,(function(e,s){return i("van-grid-item",{key:s,ref:"GI_"+s,refInFor:!0,attrs:{icon:t.getIcon(e),text:e[t.vprops.titleField]},on:{click:function(i){return t.onClick(e,s)}}},[i("div",{attrs:{slot:"icon"},slot:"icon"},[e[t.srcField]?i("van-image",{attrs:{src:e[t.srcField]}}):t._e(),e[t.srcField]?t._e():i("div",{staticClass:"oy-menu-item"},[i("van-icon",{staticClass:"oy-menu-icon",attrs:{name:t.getIcon(e)}})],1)],1),i("div",{ref:"GITitle_"+s,refInFor:!0,staticClass:"f-grid-menu-title",style:"margin-top:5px;height:"+t.textHeight,attrs:{slot:"text"},slot:"text"},[t._v(t._s(e[t.vprops.titleField]))])])})),1),t.isGroup?i("div",t._l(t.groups,(function(e,s){return i("div",{key:s},[t.isGroup?i("div",{staticClass:"f-grid-title"},[t._v(t._s(s))]):t._e(),i("van-grid",{staticClass:"f-grid-simple",attrs:{square:!t.textHeight,border:!1,clickable:""}},t._l(e,(function(e,s){return i("van-grid-item",{key:s,attrs:{icon:t.getIcon(e),text:e[t.vprops.titleField]},on:{click:function(i){return t.onClick(e,s)}}},[i("div",{attrs:{slot:"icon"},slot:"icon"},[e[t.srcField]?i("van-image",{attrs:{src:e[t.srcField]}}):t._e(),e[t.srcField]?t._e():i("div",{staticClass:"oy-menu-item"},[i("van-icon",{staticClass:"oy-menu-icon",attrs:{name:t.getIcon(t.jItem)}})],1)],1),i("div",{ref:"GITitle_"+s,refInFor:!0,staticClass:"f-grid-menu-title",style:"margin-top:5px;height:"+t.textHeight,attrs:{slot:"text"},slot:"text"},[t._v(t._s(e[t.vprops.titleField]))])])})),1)],1)})),0):t._e()],1)},n=[],o=(i("a9e3"),i("d8ad")),a=i("62e4"),r=null,l={name:"vinput",props:["name","type","options","readonly","vprops","styleId","callbackGetFilter"],components:{},inject:["viewTag"],activated:function(){console.log(this)},data:function(){return{isGroup:!1,srcField:"MBIMGSRC",list:[],groups:{},textHeight:""}},mounted:function(){r=new a["a"](this.vprops.dscode),this.initView()},methods:{emitListeners:function(t,e){var i=this.vprops.id,s="gridmenu";return o["a"].$emit("component-change",t,s,i,e,this.styleId,this.viewTag?this.viewTag():void 0)},initView:function(){this.vprops.groupField&&(this.isGroup=!0),this.vprops.textHeight&&!isNaN(Number(this.vprops.textHeight))?this.textHeight=String(this.vprops.textHeight)+"px":this.textHeight="",this.refresh()},refresh:function(){var t=this;r.getListPage(this.getQueryParam()).then((function(e){"ok"==e.Code&&(t.list=e.Data.Rows,t.formatData(t.list))}))},formatData:function(t){var e={},i=this.vprops.groupField;for(var s in t){var n=t[s][i];e[n]=e[n]||[],e[n].push(t[s])}this.groups=e},getFilter:function(){var t=this.callbackGetFilter?this.callbackGetFilter(this.vprops.dscode):[],e=this.emitListeners("beforeGridMenuFilter",[t]);return e||t},getIcon:function(t){var e="home-o";return e=this.emitListeners("getGridIcon",[this,t])||e,e},getQueryParam:function(){return{sqlId:this.vprops.dscode,fields:this.getFilter(),orders:[],count:!0,bizOpId:"",bizId:""}},getData:function(){return this.list},onClick:function(t,e){o["a"].$emit("component-change","click","gridmenu",this.vprops.id,[this.vprops,t,e],this.styleId,this.viewTag?this.viewTag():void 0)}},watch:{value:function(t){this.currentValue=t}}},c=l,h=(i("3c1c"),i("31f4"),i("2877")),d=Object(h["a"])(c,s,n,!1,null,null,null);e["a"]=d.exports},"2f04":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.showDialog,expression:"showDialog"}]},[i("van-overlay",{attrs:{show:t.show,"z-index":"2000"}}),i("div",{staticClass:"f-dialog",class:{"f-dialog-leave":!t.show},attrs:{role:"dialog","aria-labelledby":"标题",tabindex:"0"},on:{click:function(t){t.stopPropagation()}}},[i("div",{staticClass:"f-dialog-container"},[t.imgsrc?i("div",{staticClass:"f-dialog-img--container"},[i("img",{staticClass:"f-dialog-img",class:{"f-dialog-icon":"icon"==t.imgtype},attrs:{src:t.imgsrc}})]):t._e(),i("div",{staticClass:"f-dialog-header",class:{"f-dialog-header--withoutmes":!t.message}},[t._v(t._s(t.title))]),t.extend?i(t.extend,{ref:"ext",tag:"div"}):t._e(),t.message?i("div",{staticClass:"f-dialog-content"},[i("div",[i("div",{staticClass:"f-dialog-message"},[t._v(t._s(t.message))])])]):t._e(),i("vtoolbar",{directives:[{name:"show",rawName:"v-show",value:t.btns&&t.btns.length&&t.btns.length>0,expression:"btns && btns.length && btns.length > 0"}],attrs:{styleId:t.styleId,data:t.btns,column:t.btns.length>2},on:{buttonClick:t.btnClick}})],1)])],1)},n=[],o=(i("fb6a"),i("53ca")),a=i("a026"),r=i("4232"),l={components:{vtoolbar:r["a"]},data:function(){return{show:!1,title:"标题",message:"",imgsrc:"",btns:[],extend:null,showDialog:!1,imgtype:""}},props:{ctx:{type:Object,require:!1}},mounted:function(){},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},toggle:function(t){this.show=void 0===t?!this.show:t},open:function(){if(!this.show){var t=Array.prototype.slice.call(arguments),e=t[0],i=t[1],s=t[2],n=t[3];this.message=i||"",this.title=e||"",this.btns=s||[],this.imgsrc="",this.imgtype="",this.extend=null,n&&"object"==Object(o["a"])(n)&&(n.img&&"object"==Object(o["a"])(n.img)&&(this.imgsrc=n.img.src||"",this.imgtype=n.img.type||""),n.extend&&"object"==Object(o["a"])(n.extend)&&(this.extend=a["a"].extend(n.extend))),this.toggle(!0)}},close:function(){},btnClick:function(t){if(t.click&&"function"==typeof t.click){var e=[];this.$refs.ext&&this.$refs.ext.length&&e.push(this.$refs.ext[0]);var i=t.click.call(window,e);if(!1===i)return}this.show=!1},emitListeners:function(t,e){var i,s;null===(i=this.ctx)||void 0===i||null===(s=i.event)||void 0===s||s.emitGlobal(t,e)}},watch:{show:function(t){var e=this;t?this.showDialog=!0:setTimeout((function(){e.showDialog=!1}),300)}}},c=l,h=(i("883a"),i("2877")),d=Object(h["a"])(c,s,n,!1,null,null,null);e["a"]=d.exports},"31f4":function(t,e,i){"use strict";var s=i("7b83"),n=i.n(s);n.a},"33de":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t._l(t.layouts,(function(e){return t._l(e.childs,(function(s,n){return i("div",{key:n,attrs:{name:e.index}},["grid"==t.controls[s].type?i("dylistview",{attrs:{styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}):t._e(),"grid"!=t.controls[s].type?i("dycontrol",{attrs:{styleId:t.styleId,readonly:t.readonly,type:t.controls[s].type,vprops:t.controls[s],value:t.formItem[t.controls[s].field]},on:{input:t.updateForm}}):t._e()],1)}))}))],2)},n=[],o=i("efee"),a=i("3616"),r={name:"dylayout",components:{dycontrol:o["default"],dylistview:a["a"]},provide:function(){return{viewTag:this.getViewTag}},props:{styleId:{type:String,required:!1},title:{type:String,required:!1},layouts:{type:Object,required:!1},controls:{type:Object,required:!1},formItem:{type:Object,required:!1},listItems:{type:Object,required:!1},readonly:{type:Boolean,required:!1}},data:function(){return{activeNames:[0,1]}},methods:{getViewTag:function(){return this.viewTag},updateForm:function(){console.log(arguments)}}},l=r,c=(i("ef77"),i("2877")),h=Object(c["a"])(l,s,n,!1,null,null,null);e["a"]=h.exports},3616:function(t,e,i){"use strict";var s,n,o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.gridTitle?i("div",{staticClass:"f-panel-title f-panel-grid",class:{"f-panel-hidden":!t.show},on:{click:t.toggleHeader}},[t.iconurl?t._e():i("span",{staticClass:"f-panel-header"},[t._v(" "+t._s(t.gridTitle)+" ")]),t.iconurl?i("span",{staticClass:"f-panel-header f-panel-header-hasicon"},[i("img",{attrs:{src:t.iconurl}}),t._v(" "+t._s(t.gridTitle)+" ")]):t._e(),0!=t.vprops.fold?i("span",{staticClass:"f-panel-arrow"},[i("van-icon",{attrs:{name:"arrow"}})],1):t._e()]):t._e(),t.isTree?t._e():i("van-pull-refresh",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}],attrs:{"success-text":t.getLang("list_loadsuccess"),disabled:t.noPullRefresh||t.processing},on:{refresh:t.onRefresh},model:{value:t.processing,callback:function(e){t.processing=e},expression:"processing"}},[i("van-list",{directives:[{name:"show",rawName:"v-show",value:!t.isFavorTab,expression:"!isFavorTab"}],staticStyle:{"min-height":"50px"},attrs:{finished:t.finished,"finished-text":t.finishText},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[t.group?i("van-collapse",{model:{value:t.activeNames,callback:function(e){t.activeNames=e},expression:"activeNames"}},t._l(t.groupedList,(function(e,s){return i("van-collapse-item",{key:s,attrs:{name:s}},[i("template",{slot:"title"},[i("dylistgrouptitle",{attrs:{index:s,name:t.nameList[s],groupComp:t.groupComp}})],1),i("kajiang"),t._l(e,(function(e,s){return i("van-swipe-cell",{key:s,class:t.getWidthClass(),attrs:{name:s,"before-close":t.beforeClose}},[i("dylistitem",{attrs:{favor:t.favor,enableIcon:t.enableIcon,keyword:t.keyword,highlight:t.highlight,highlightStyle:t.highlightStyle,isMul:t.isMul,isLink:!0,row:e,totalComp:t.totalComp,contentComp:t.contentComp,stComp:t.stComp,ttComp:t.ttComp,iconComp:t.iconComp,labelComp:t.labelComp,subtitle:e[t.fields.subtitle],title:e[t.fields.title],label:e[t.fields.label],content:e[t.fields.content],icon:e[t.fields.icon],rowIndex:s},on:{favorClick:function(i){return t.favorClick(e)},click:function(i){return t.onRowClick(e,s)},checkRow:t.onCheckRow}}),t.vprops.swipe?i("template",{slot:"right"},t._l(t.buttonList,(function(n,o){return i("van-button",{directives:[{name:"show",rawName:"v-show",value:!n.hide&&t.showButton(n,e),expression:"!button.hide&&showButton(button, item)"}],key:o,staticStyle:{height:"100%"},attrs:{square:"",type:t.transforButtonFormat(n.style),text:n.name,icon:n.icon},on:{click:function(i){return t.swipeClick(n.id,o,e,s)}}})})),1):t._e()],2)}))],2)})),1):t._l(t.list,(function(e,s){return i("van-swipe-cell",{key:s,class:t.getWidthClass(),attrs:{name:s,"before-close":t.beforeClose}},[i("dylistitem",{attrs:{favor:t.favor,enableIcon:t.enableIcon,keyword:t.keyword,highlight:t.highlight,highlightStyle:t.highlightStyle,isMul:t.isMul,isLink:!0,row:e,totalComp:t.totalComp,contentComp:t.contentComp,stComp:t.stComp,ttComp:t.ttComp,iconComp:t.iconComp,labelComp:t.labelComp,subtitle:e[t.fields.subtitle],title:e[t.fields.title],label:e[t.fields.label],content:e[t.fields.content],icon:e[t.fields.icon],rowIndex:s},on:{favorClick:function(i){return t.favorClick(e)},click:function(i){return t.onRowClick(e,s)},checkRow:t.onCheckRow}}),t.vprops.swipe?i("template",{slot:"right"},t._l(t.buttonList,(function(n,o){return i("van-button",{directives:[{name:"show",rawName:"v-show",value:!n.hide&&t.showButton(n,e),expression:"!button.hide&&showButton(button, item)"}],key:o,staticStyle:{height:"100%"},attrs:{square:"",type:t.transforButtonFormat(n.style),text:n.name,icon:n.icon},on:{click:function(i){return t.swipeClick(n.id,o,e,s)}}})})),1):t._e()],2)})),t.vprops.showSum?i("div",{staticClass:"f-list-sum"},[i("div",{staticStyle:{margin:"6px"}},[t._v(t._s(t.getLang("lookup_sum")))]),t._l(t.sumArr,(function(e,s){return i("div",{key:s,staticClass:"f-list-sum-item",attrs:{name:s}},[t._v(" "+t._s(e.text)+" "),i("span",[t._v(t._s(e.value))])])}))],2):t._e(),t.bottomTpl?i(t.bottomView,{tag:"component"}):t._e()],2),i("div",{directives:[{name:"show",rawName:"v-show",value:t.isFavorTab,expression:"isFavorTab"}],staticClass:"f-list-warp-main"},[[t._l(t.favorList,(function(e,s){return i("dylistitem",{key:s,attrs:{ttComp:t.ttComp,enableIcon:t.enableIcon,keyword:t.keyword,highlight:t.highlight,highlightStyle:t.highlightStyle,row:e,remove:!0,isLink:!1,subtitle:e[t.fields.subtitle],rowData:e,title:e[t.fields.title],label:e[t.fields.label],isMul:t.isMul,rowIndex:s},on:{removeClick:t.onRemoveFavorItem,click:function(i){return t.onConfirm(e,s)},checkRow:t.onCheckRow}})})),0!=t.favorList.length||t.processing||t.loadingFavor?t._e():i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])]),t.processing||t.loadingFavor?i("div",{staticStyle:{"min-height":"50px"}}):t._e()]],2),0!=t.list.length||this.loading||this.processing||t.isFavorTab?t._e():i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])])],1),t.isTree?i("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}]},[t.loading||t.processing||t.loadingFavor?i("div",{staticStyle:{display:"block","text-align":"center"}},[i("van-loading",{attrs:{size:"24px"}},[t._v(t._s(t.getLang("lookup_loading")))])],1):t._e(),i("treelist",{directives:[{name:"show",rawName:"v-show",value:!t.isFavorTab,expression:"!isFavorTab"}],ref:"treeList",attrs:{ttComp:t.ttComp,favor:t.favor,treeConfig:t.treeConfig,ttField:t.fields.title,stField:t.fields.subtitle,nodes:t.list,autoFold:t.autoFold,userJS:"1",selectMemory:t.selectMemory,isMul:t.isMul,noCode:t.noCode,idField:t.listConfig.KeyCol,helpInput:t.helpInput,isTotal:t.isTotal,autoChild:t.autoChild,isList:t.isList,fields:t.fields,totalComp:t.totalComp,contentComp:t.contentComp,stComp:t.stComp,iconComp:t.iconComp,labelComp:t.labelComp,childOnly:t.childOnly},on:{favorClick:t.favorClick,beforeExpand:t.beforeExpand,treeNodeClick:t.treeNodeClick,autoCheck:t.autoCheck}},[t.showMore?i("a",{staticClass:"f-list-more-btn",on:{click:t.loadMoreRoot}},[t._v(t._s(t.getLang("lookup_loadMore")))]):t._e()]),i("div",{directives:[{name:"show",rawName:"v-show",value:t.isFavorTab,expression:"isFavorTab"}],staticClass:"f-list-warp-main"},[[t._l(t.favorList,(function(e,s){return i("dylistitem",{key:s,attrs:{ttComp:t.ttComp,keyword:t.keyword,highlight:t.highlight,highlightStyle:t.highlightStyle,enableIcon:t.enableIcon,row:e,remove:!0,isLink:!1,subtitle:e[t.fields.subtitle],rowData:e,title:e[t.fields.title],label:e[t.fields.label],isMul:t.isMul,rowIndex:s,childOnly:t.childOnly,isDetail:"0"!=e[t.treeConfig.isdetail]&&!0},on:{removeClick:t.onRemoveFavorItem,click:function(i){return t.onConfirm(e,s)},checkRow:t.onCheckRow}})})),0!=t.favorList.length||t.loadingFavor?t._e():i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])])]],2),0!=t.list.length||this.loading||this.processing||t.isFavorTab||t.loadingFavor?t._e():i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])])],1):t._e()],1)},a=[],r=(i("99af"),i("4de4"),i("4160"),i("c975"),i("d81d"),i("a434"),i("a9e3"),i("b64b"),i("d3b7"),i("159b"),i("5530")),l=(i("96cf"),i("1da1")),c=i("b982"),h={name:"dylistgrouptitle",props:["name","index","groupComp"],data:function(){return{}},methods:{getTitle:function(){var t=this.$createElement,e=this.groupComp;return t(e,{attrs:{name:this.name,index:this.index}})}},mounted:function(){},render:function(t){return this.groupComp?t("div",[this.getTitle()]):t("div",["分组：",this.name," "])}},d=h,u=i("2877"),f=Object(u["a"])(d,s,n,!1,null,null,null),p=f.exports,m=i("d8ad"),g=i("62e4"),v=i("a026"),b=i("bab6"),w=i("39ea"),y=i("1ba7"),C=null,k={name:"dylistview",props:["styleId","gridId","type","title","controls","vprops","filters","row","callbackGetFilter","getFilterKeyword","hashDs","favor","isFavorTab","isLookupMul","lookupAsync","lookupTotal","lookupChildonly","lookupAutoFold","isEmpty","isLookup","iconurl","gridTitle","selectMemory","bizid","bizopid","clearFilter"],components:{dylistitem:c["a"],dylistgrouptitle:p,treelist:y["a"]},directives:{longTouch:b["a"]},created:function(){C=new g["a"](this.styleId)},mounted:function(){if(this.hashDs[this.vprops.dscode]?this.dsId=this.vprops.dscode:this.dsId=this.styleId,console.log(this.vprops),this.fields.title=this.vprops.ttField,this.fields.subtitle=this.vprops.stField,this.fields.label=this.vprops.tagField,this.fields.content=this.vprops.desField,this.fields.icon=this.vprops.iconField,this.bottomTpl=this.vprops.bottomTpl,this.noPullRefresh=this.vprops.noPullRefresh,this.vprops.iconField&&(this.enableIcon=!0),this.vprops.pager?this.pageSize=this.vprops.pagesize||20:this.pageSize=100,this.group=this.vprops.group||"",this.autoExpand=this.vprops.autoExpand||!1,""!=this.group&&(this.pageSize=1e4),this.vprops.noFinishText&&(this.noFinishText=!0),this.vprops.highlight?(this.highlight=!0,this.highlightStyle=this.vprops.highlightStyle):(this.highlight=!1,this.highlightStyle=""),this.isMul=this.vprops.checkbox,this.emptyFlag=this.isEmpty||this.vprops.isEmpty||!1,this.isMul=this.isLookup&&this.isLookupMul||this.isMul,this.isMul&&this.$emit("setLookupMul"),this.buildDynamicComponent(),m["a"].$emit("component-change","beforeGridInit","grid",this.vprops.id,[this.vprops,this.gridHooks],this.styleId),this.vprops.buttonList||(this.vprops.buttonList=[]),this.vprops.swipe&&0==this.vprops.buttonList.length){var t={custom:!0,id:"baritem_delete",name:this.getLang("baritem_delete"),script:this.vprops.onSwipeClick,style:"4"};this.buttonList.push(t)}else this.buttonList=this.vprops.buttonList;this.vprops.autoFold&&this.gridTitle&&(this.show=!1),this.langCode=window.idp.lang.getLang().id,this.autoFold=this.lookupAutoFold||this.vprops.treeAutoFold||!1},data:function(){return{show:!0,dsId:"",list:[],isMul:!1,loading:!1,sumArr:[],listConfig:null,loadingData:!1,finished:!1,emptyFlag:!1,value:"",refreshing:!1,pageIndex:0,pageSize:20,totalCount:0,stComp:null,ttComp:null,iconComp:null,labelComp:null,contentComp:null,groupComp:null,groupTitle:null,fields:{title:"",label:"",content:"",icon:""},gridHooks:{customDataService:null,onAfterShowData:null},buttonList:[],favorList:[],enableIcon:!1,noFinishText:!1,bottomView:null,showBottom:!0,bottomTpl:"",activeNames:[],group:"",nameList:[],groupedList:[],isTree:!1,treeConfig:{treetype:"",grade:"",level:"",isdetail:"",id:"",pid:"",rootValue:"",format:"",name:""},async:!1,isList:!0,checkedNodes:[],childOnly:!1,isTotal:!1,langCode:"zh-CHS",noPullRefresh:!1,keyword:"",highlight:!1,highlightStyle:"",finishText:"没有更多数据！",autoFold:!1,loadingFavor:!1,latestGetData:0,latestGetFavor:0,debounceTimer:null}},activated:function(){this.isEmpty&&(this.emptyFlag=!0),this.initTreeConfig()},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},toggleHeader:function(){0!=this.vprops.fold&&(this.show=!this.show)},getSumDisplay:function(){if(this.vprops.showSum){if(!this.sumFieldArr){var t=this.vprops.cols,e=[];for(var i in t){var s=t[i].id,n=this.vprops.id+"."+s,o=this.controls[n];o&&o.issum&&e.push({field:o.field,label:o.label,precision:2,thousand:!1})}this.sumFieldArr=e}var a=[];for(var r in this.sumFieldArr)a.push({text:this.sumFieldArr[r].label,value:this.getSumResult(this.sumFieldArr[r].field)});this.sumArr=a}},getSumResult:function(t){var e=0;for(var i in this.list)this.list[i][t]&&(e=window.accAdd(e,this.list[i][t]));var s=this.vprops.id+"."+t;return this.controls[s]&&this.controls[s].fparams&&(this.controls[s].fparams.thousand?e=w["a"].currency(e,this.controls[s].fparams.decimal):this.controls[s].fparams.decimal&&(e=window.NumberRound(e,Number(this.controls[s].fparams.decimal)))),e},buildDynamicComponent:function(){var t={stTpl:"stComp",ttTpl:"ttComp",iconTpl:"iconComp",tagTpl:"labelComp",desTpl:"contentComp",totalTpl:"totalComp"};for(var e in t)this.buildSingleComponent(e,t[e],["row","rowindex","checked"]);this.buildSingleComponent("groupTpl","groupComp",["name","index"]);var i=["bottomTpl"],s=["bottomView"];for(var n in i){var o=i[n];this.vprops[o]&&(this[s[n]]=this.buildSingleComponent(o,s[n],["data"]))}},buildSingleComponent:function(t,e,i){if(this.vprops[t]&&this.vprops[t].template){var s={};if(this.vprops[t].mixin){var n=new Function("return "+this.vprops[t].mixin+";");s=n()}console.log("底部组件："+e),this[e]=v["a"].extend({mixins:[s],props:i,template:this.vprops[t].template,data:function(){return{}}})}return this[e]},loadUIView:function(){var t=this;C.getListInfo(this.dsId).then((function(e){t.listConfig=e.Data,console.log(t.fields)}))},beforeExpand:function(t){var e=this;return Object(l["a"])(regeneratorRuntime.mark((function i(){var s,n;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(s=e,!e.async){i.next=8;break}if(!(1==t.loaded||t.children&&t.children.length>0)){i.next=4;break}return i.abrupt("return");case 4:return i.next=6,e.getQueryParam();case 6:n=i.sent,C.getChildrenTree(n.sqlId,t,n.fields,e.getBizInfo()).then((function(i){for(var n in e.formatFavorData(i.Data),i.Data)i.Data[n].isAsync=!0,"0"==i.Data[n][e.treeConfig.isdetail]&&(i.Data[n].children=[],i.Data[n].hasAync=!0,i.Data[n].close=!0),s.isTotal&&i.Data[n]["com.inspur.fastdweb.hasAuth"]&&(i.Data[n].hasAuth=!0),t.children.push(i.Data[n]);t.loaded=!0}));case 8:case"end":return i.stop()}}),i)})))()},formatFavorData:function(t){if(this.favor)for(var e in t)for(var i in t[e]["isFavor"]=!1,this.favorList)t[e][this.listConfig.KeyCol]==this.favorList[i][this.listConfig.KeyCol]&&(t[e]["isFavor"]=!0);return t},getBizInfo:function(){var t={bizId:this.bizid||this.vprops.bizid||"",bizOpId:this.bizopid||this.vprops.bizopid||""};return t.isTotal=this.isTotal,t},initTreeConfig:function(){this.listConfig&&(this.treeConfig.treetype=this.listConfig.TreeType,this.treeConfig.grade=this.listConfig.WbsCol,this.treeConfig.level=this.listConfig.RankCol,this.treeConfig.isdetail=this.listConfig.LeafCol,this.treeConfig.id=this.listConfig.TreeIdCol,this.treeConfig.pid=this.listConfig.ParentCol,this.treeConfig.rootValue=this.listConfig.RootVal,this.treeConfig.format=this.listConfig.PcolExp,this.treeConfig.name=this.listConfig.NodeCol),this.async=this.vprops.async||this.lookupAsync,this.isTotal=this.vprops.isTotal||this.lookupTotal,this.childOnly=this.vprops.childOnly||this.lookupChildonly},loadRootData:function(t){var e=this;return Object(l["a"])(regeneratorRuntime.mark((function i(){var s,n;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return s=e,e.list=[],e.favor&&(e.loadingFavor=!0),i.next=5,e.getQueryParam();case 5:n=i.sent,C.getRootTree(n.sqlId,1,n.fields,e.getBizInfo()).then((function(i){if(t==e.latestGetData&&(e.latestGetData=0,"ok"==i.Code&&i.Data)){for(var n in i.Data)"0"==i.Data[n][e.treeConfig.isdetail]&&(i.Data[n].children=[],i.Data[n].hasAync=!0,i.Data[n].close=!0),s.isTotal&&i.Data[n]["com.inspur.fastdweb.hasAuth"]&&(i.Data[n].hasAuth=!0);e.loading=!1,e.favor?e.loadFavorList().then((function(){e.list=e.formatFavorData(i.Data),e.loadingFavor=!1})):e.list=i.Data}}));case 7:case"end":return i.stop()}}),i)})))()},getQueryParam:function(){var t=this;return Object(l["a"])(regeneratorRuntime.mark((function e(){var i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getFilter();case 2:return i=e.sent,t.isTree&&(t.pageSize=1e4),console.log(i),e.abrupt("return",{sqlId:t.dsId,fields:i,orders:t.getOrder(),count:!0,bizOpId:t.bizopid||t.vprops.bizopid||"",bizId:t.bizid||t.vprops.bizid||"",page:t.pageIndex,pagesize:t.pageSize});case 6:case"end":return e.stop()}}),e)})))()},getFilter:function(){var t=this;return Object(l["a"])(regeneratorRuntime.mark((function e(){var i,s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.callbackGetFilter){e.next=13;break}if(i=t.callbackGetFilter(t.vprops.id),t.keyword=t.getFilterKeyword?t.getFilterKeyword(t.vprops.id):"",!i.then){e.next=10;break}return e.next=6,i;case 6:return s=e.sent,e.abrupt("return",s);case 10:return e.abrupt("return",i);case 11:e.next=14;break;case 13:return e.abrupt("return",t.filters);case 14:case"end":return e.stop()}}),e)})))()},getOrder:function(){return[]},openFilter:function(){this.show=!0},initListViewTpl:function(){},beforeClose:function(t){var e=t.position,i=t.instance,s=t.name;switch(console.log(arguments),e){case"left":case"cell":case"outside":i.close();break;case"right":this.vprops.swipe&&!this.vprops.onSwipeClick&&m["a"].$emit("component-change","swipeclick","grid",this.vprops.id,[this.vprops,this.list[s],s],this.styleId),i.close();break}},setMulState:function(t){this.isMul=t},getCheckedRows:function(){if(this.isTree)return this.checkedNodes;console.log(this.list);var t=[];for(var e in this.list)this.list[e]["isChecked"]&&t.push(this.list[e]);return t},refresh:function(t){this.onRefresh(t)},onLoad:function(){var t=Object(l["a"])(regeneratorRuntime.mark((function t(){var e,i,s,n=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=Date.now(),this.latestGetData=e,this,this.listConfig){t.next=6;break}return t.next=6,C.getListInfo(this.dsId).then((function(t){n.listConfig=t.Data,n.isTree=n.vprops.istree,n.isTree&&n.initTreeConfig();var e=[];n.listConfig.Cols.forEach((function(t){"1"==t.IsShow&&e.push(t.Field)}))}));case 6:if(!this.emptyFlag){t.next=16;break}return this.latestGetData=0,this.refreshing&&(this.list=[],this.refreshing=!1),this.clearFilter&&this.clearFilter(null===(i=this.vprops)||void 0===i?void 0:i.id),this.favor&&this.loadFavorList(),this.finishText="",this.finished=!0,this.loading=!1,this.emptyFlag=!1,t.abrupt("return");case 16:if(!this.isTree||!this.async){t.next=27;break}if(this.favorList=[],!this.callbackGetFilter||!this.callbackGetFilter(this.vprops.id).length){t.next=24;break}return t.next=21,this.loadFilter(e);case 21:return t.abrupt("return",t.sent);case 24:return t.next=26,this.loadRootData(e);case 26:return t.abrupt("return",t.sent);case 27:return this.refreshing&&(this.list=[],this.refreshing=!1),this.pageIndex++,console.log(this.pageIndex),this.loadingData=!0,t.next=33,this.getQueryParam();case 33:return s=t.sent,t.next=36,this.getGridPageData(s).then((function(t){if("ok"==t.Code&&e==n.latestGetData){n.latestGetData=0;var i=n;n.totalCount=t.Data.Total;var s=t.Data.Rows;n.selectMemory&&Object.keys(n.selectMemory).length&&s.forEach((function(t){i.selectMemory[t[i.listConfig.KeyCol]]?t["isChecked"]=!0:t["isChecked"]=!1})),n.favor?n.loadFavorList().then((function(e){var s=i.formatFavorData(t.Data.Rows);t.ftoken?(i.list=s,i.group&&i.initGroupedList()):i.list=i.list.concat(s),i.afterLoadData()})):(t.ftoken?(i.list=t.Data.Rows,i.group&&i.initGroupedList()):i.list=i.list.concat(s),i.afterLoadData())}else"ok"!=t.Code&&m["a"].$emit("component-change","getListPageError","grid",n.vprops.id,[n.vprops,t],n.styleId)}));case 36:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),afterLoadData:function(){this.loading=!1,this.loadingData=!1;var t=this;this.totalCount<=this.pageSize*this.pageIndex&&(this.finished=!0),this.$nextTick((function(){t.finishText=t.getFinishText()})),m["a"].$emit("component-change","afterLoadData","grid",this.vprops.id,[this.vprops,this.list],this.styleId)},loadFilter:function(t){var e=this;return Object(l["a"])(regeneratorRuntime.mark((function i(){var s,n;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return e.list=[],s=e,e.favor&&(e.loadingFavor=!0),i.next=5,e.getQueryParam();case 5:n=i.sent,C.searchTree(n.sqlId,n.fields,e.getBizInfo()).then((function(i){if(t==e.latestGetData){if(e.latestGetData=0,i.Data)for(var n in e.showMore=!1,e.favor?e.loadFavorList().then((function(){e.list=e.formatFavorData(i.Data),e.loadingFavor=!1})):e.list=i.Data,i.Data)s.isTotal&&i.Data[n]["com.inspur.fastdweb.hasAuth"]&&(i.Data[n].hasAuth=!0);e.loading=!1}}));case 7:case"end":return i.stop()}}),i)})))()},initGroupedList:function(){var t=this;this.group&&(this.nameList=[],this.groupedList=[],this.list.forEach((function(e){-1==t.nameList.indexOf(e[t.group]||"")&&t.nameList.push(e[t.group]||"")})),this.nameList.forEach((function(e){t.groupedList.push(t.list.filter((function(i){return i[t.group]==e||""==e&&!i[t.group]})))})),this.autoExpand&&(this.activeNames=Object.keys(Object(r["a"])({},this.groupedList)).map((function(t){return Number(t)}))))},getGridPageData:function(t){return this.gridHooks.customDataService?this.gridHooks.customDataService(t):C.getListPageMerge(t)},onConfirm:function(t,e){m["a"].$emit("component-change","rowclick","grid",this.vprops.id,[this.vprops,t,e],this.styleId)},onRowClick:function(t,e){m["a"].$emit("component-change","rowclick","grid",this.vprops.id,[this.vprops,t,e],this.styleId)},treeNodeClick:function(t,e){this.isTotal&&!t.hasAuth||(this.isMul&&(t.isChoose=!t.isChoose,this.onCheckRow(t,t.isChoose),t.isChoose?this.checkedNodes.push(t):-1!=this.checkedNodes.indexOf(t)&&this.checkedNodes.splice(this.checkedNodes.indexOf(t),1)),m["a"].$emit("component-change","rowclick","grid",this.vprops.id,[this.vprops,t,e],this.styleId))},onLongTouch:function(t,e){var i=this;return function(){m["a"].$emit("component-change","longClick","grid",i.vprops.id,[i.vprops,t,e],this.styleId)}},onCheckRow:function(t,e){m["a"].$emit("component-change","checkrow","grid",this.vprops.id,[this.vprops,t,e],this.styleId)},onRefresh:function(t){var e=this;return Object(l["a"])(regeneratorRuntime.mark((function i(){return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:clearTimeout(e.debounceTimer),void 0!==t&&"number"==typeof t||(t=400),e.debounceTimer=setTimeout(Object(l["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.refreshing=!0,m["a"].$emit("component-change","refresh","grid",e.vprops.id,[e.vprops,e.gridHooks],e.styleId),e.pageIndex=0,e.finished=!1,e.loading=!0,t.next=7,e.onLoad();case 7:case"end":return t.stop()}}),t)}))),t);case 3:case"end":return i.stop()}}),i)})))()},getData:function(){return this.list},getFinishText:function(){var t=!!this.$el&&this.$el.parentElement.parentElement.parentElement;return 0==this.list.length||this.noFinishText||this.vprops.noFillNoFinish&&t&&t.scrollHeight<=t.clientHeight?"":this.getLang("lookup_nomoreData")},swipeClick:function(t,e,i,s){console.log(e),m["a"].$emit("component-change","swipeButtonClick","grid",this.vprops.id,[t,e,i,this,s],this.styleId)},transforButtonFormat:function(t){switch(t){case"2":case"7":return"info";case"6":return"warning";case"4":return"danger";case"3":case"5":return"primary"}},favorClick:function(t){var e=this;this.$emit("favorClick",this.row);var i=1==t.isFavor?"removeFavor":"addFavor";t.isFavor=!t.isFavor,C[i](t[this.listConfig.KeyCol],this.$route.params.formId,this.styleId).then((function(i){"ok"==i.Code&&(console.log(t.isFavor+"    test"),e.loadFavorList())}))},onRemoveFavorItem:function(t){this.onRemoveFavor(t,!0)},onRemoveFavor:function(t,e){var i=this;C.removeFavor(t[this.listConfig.KeyCol],this.$route.params.formId,this.styleId).then((function(s){"ok"==s.Code&&(t.isFavor=!t.isFavor,e?(i.list=[],i.showView()):i.loadFavorList())}))},loadFavorList:function(){var t=this;return Object(l["a"])(regeneratorRuntime.mark((function e(){var i,s,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getQueryParam();case 2:return i=e.sent,s=t,console.log("loadFavorList"),n=new Promise((function(t,e){var n=Date.now();s.latestGetFavor=n,C.getFavor(s.$route.params.formId,i).then((function(e){s.latestGetFavor==n&&(s.latestGetFavor=0,"ok"==e.Code&&(s.favorList=e.Data),t(!0))}))})),e.abrupt("return",n);case 7:case"end":return e.stop()}}),e)})))()},showView:function(t){var e=this;this.favor?this.loadFavorList().then((function(i){e.favorList.length>0&&!t&&(e.isFavorTab=!0,e.active=1),e.refresh()})):this.refresh()},showButton:function(t,e){console.log("展示滑动按钮");var i=m["a"].$emit("component-change","rowButtonShow","grid",this.vprops.id,[t,e],this.styleId);return!1!==i||i},getWidthClass:function(){return"2"==this.vprops.colType?"f-list-twospan":"3"==this.vprops.colType?"f-list-threespan":void 0}},watch:{listItems:function(){},isLookupMul:function(t){this.isMul=this.vprops.checkbox,t&&(this.isMul=t),this.isMul&&this.$emit("setLookupMul")},list:{deep:!0,handler:function(){this.getSumDisplay(),this.initGroupedList()}},styleId:function(){C.setSuPath(this.styleId)}},computed:{processing:function(){return this.latestGetData||this.latestGetFavor}}},x=k,I=(i("6c75"),Object(u["a"])(x,o,a,!1,null,"72f2cae9",null));e["a"]=I.exports},"379c":function(t,e,i){},"3a4e":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("van-field",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],class:{"f-input-block":t.isBlock},attrs:{name:"checkbox",label:t.label,readonly:t.disabled,required:t.options.required,"error-message":t.errorMessage,"error-message-align":"right"},scopedSlots:t._u([{key:"input",fn:function(){return[t.isBtn?t._e():i("van-radio-group",{staticClass:"f-input-radios",attrs:{direction:"horizontal",disabled:t.disabled},model:{value:t.currentValue,callback:function(e){t.currentValue=e},expression:"currentValue"}},t._l(t.data,(function(e,s){return i("van-radio",{key:s,class:{"f-input-checked":t.currentValue==e.name},staticStyle:{margin:"5px"},attrs:{name:e.name},on:{click:t.onRadioCheck}},[t._v(t._s(e.text))])})),1),t.isBtn?i("check-button",{attrs:{data:t.data,disabled:t.disabled},on:{input:t.onRadioChange},model:{value:t.currentValue,callback:function(e){t.currentValue=e},expression:"currentValue"}}):t._e()]},proxy:!0},{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0}])})},n=[],o=(i("4de4"),i("7db0"),i("4160"),i("b64b"),i("ac1f"),i("1276"),i("159b"),i("27ae")),a=i("c0bb"),r=i("d8ad"),l=i("b914"),c=i("c956"),h=i("74a5"),d=null,u={name:"vcheckbox",props:["name","type","label","value","options","readonly","styleId","ctx","gridEditParm"],inject:["viewTag"],components:{checkButton:l["a"],popLabel:h["a"]},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick}},created:function(){d=new c["b"](this.styleId)},mounted:function(){var t=this;this.initRadioOptions(),this.disabled=this.readonly,this.note=this.options.note;var e=this;r["a"].$on("component-change",(function(i,s,n,o,a,r){if(a&&t.styleId!=a)return null;if(t.viewTag&&r!=t.viewTag())return null;if("afterEndEdit"==i||"valueChange"==i){var l=o[3],c=o[0];"valueChange"==i&&(l=o[2],c=o[0]),e.filterDeps[l+c]&&e.setAyncSelectOptions()}"beforeRowEdit"==i&&e.gridEditParm&&n==e.gridEditParm.id&&Object.keys(e.filterDeps).length>0&&e.setAyncSelectOptions()}))},activated:function(){},data:function(){return{filterDeps:{},isBlock:!1,isBtn:!1,currentValue:this.value,data:[],disabled:!1,visible:!0,show:!0,errorMessage:"",showPopover:!1,note:""}},methods:{handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},emitListeners:function(t,e){var i=this.options.id,s=this.options.type;return this.gridEditParm&&(i=this.gridEditParm.id,s="grid",e.push(this.gridEditParm.field),e.push(this.gridEditParm.rowindex),e.push(this.gridEditParm.record)),r["a"].$emit("component-change",t,s,i,e,this.styleId,this.viewTag?this.viewTag():void 0)},setDisabled:function(t){this.readonly||(this.disabled=t)},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},setRequired:function(t){this.options.required=t,this.$forceUpdate()},initRadioOptions:function(){this.data=[];var t=this.options.editor_radio;if((t.isBtn||window.IDP_CUSTOM_ALLCHECKBUTTON)&&(this.isBtn=!0),t.isBlock&&(this.isBlock=!0),"str"==t.datatype){var e=[],i=o["Base64"].decode(t.json),s=i.split(";");for(var n in s){var r=s[n].split(",");e.push({name:r[0],text:r[1],checked:!1})}this.data=e}else if(t.filter)this.setFilterDeps(t.filter),this.setAyncSelectOptions();else{var l=a["a"].getSelectOpitons(this.styleId,this.options.id);this.refreshOptions(l)}},refreshOptions:function(t){this.data=[];var e=this.options.editor_radio;for(var i in t){var s=t[i];s["name"]=s[e.key],s["text"]=s[e.value],s["checked"]=!1,this.data.push(s)}},setFilterDeps:function(t){var e=this;this.filterDeps={},t.forEach((function(t){var i=e.ctx.params.getMatchFields(t.Value);i.length&&i.forEach((function(t){var i=t.table+t.field;e.filterDeps[i]||(e.filterDeps[i]=!0)}))}))},setAyncSelectOptions:function(){var t=this,e=this,i=this.options.editor_radio,s=i.filter,n=[],o=!1,a=0,r=null,l="";this.gridEditParm&&(o=!0,a=this.gridEditParm.rowindex,r=this.gridEditParm.record,l=this.gridEditParm.table),s&&s.forEach((function(t,i){var s=e.ctx.params.parser(t.Value,o,a,r,l);n.push({Left:t.Left,Field:t.Field,Operate:t.Operate,IsExpress:t.IsExpress,Value:s,Right:t.Right,Logic:t.Logic})}));var c={styleId:this.styleId,formType:"2",controlId:this.options.id,sqlWhere:{fields:n}};d.getColSetData(c).then((function(e){"ok"==e.Code&&t.refreshOptions(e.Data)}))},onRadioCheck:function(t){this.onRadioChange(this.currentValue)},onRadioChange:function(t){this.$emit("input",this.name,t,this);var e=this.findRowByValue(t);e&&(this.updateMapInfo(e),this.emitListeners("selected",[this,[e],t,this.data]))},findRowByValue:function(t){this.options.editor_radio;return this.data.find((function(e){return e["name"]==t}))},updateMapInfo:function(t){var e=this.options.editor_radio;if(e.returncols&&e.setcols){var i=e.returncols.split(";"),s=e.setcols.split(";");for(var n in i)this.$emit("input",s[n],t[i[n]],this)}},setErrorMessage:function(t){this.errorMessage=t},clearValue:function(){this.currentValue="",this.$emit("input",this.name,this.currentValue,this)}},watch:{options:function(){this.initRadioOptions(),this.disabled=this.readonly,this.visible=!0},value:function(t){"number"==typeof t&&(t=String(t)),this.currentValue=t,this.setErrorMessage("")},styleId:function(){d.setSuPath(this.styleId)}}},f=u,p=(i("ad75"),i("2877")),m=Object(p["a"])(f,s,n,!1,null,null,null);e["a"]=m.exports},"3c1c":function(t,e,i){"use strict";var s=i("d7f5"),n=i.n(s);n.a},"3c3a":function(t,e,i){"use strict";var s=i("7421"),n=i.n(s);n.a},"3dfd":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{attrs:{id:"app"}},[i("div",{staticClass:"f-main-wrapper"},[i("keep-alive",[t.$route.meta.keepAlive?i("router-view",{key:t.$route.meta.key}):t._e()],1),t.$route.meta.keepAlive?t._e():i("router-view")],1)])},n=[],o=i("d399"),a={components:{},data:function(){return{}},mounted:function(){console.log(this)},methods:{onClickLeft:function(){},sorry:function(){Object(o["a"])("暂无后续逻辑~")}}},r=a,l=(i("7c55"),i("2877")),c=Object(l["a"])(r,s,n,!1,null,null,null);e["a"]=c.exports},4045:function(t,e,i){},4232:function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.visible?i("div",{staticClass:"f-toolbar",class:{"f-toolbar-grid":t.isGrid,"f-toolbar-column":t.column}},[t._l(t.data,(function(e,s){return[e.hide?t._e():i("div",{key:s,staticClass:"f-toolbar-item",class:t.getBtnClass(e),on:{click:function(i){return t.onClick(e,s)}}},[i("span",{staticClass:"icon"},[i("van-icon",{attrs:{name:e.icon}})],1),i("span",{staticClass:"text"},[t._v(t._s(e.name))])])]}))],2):t._e()},n=[],o=(i("caad"),i("2532"),i("d8ad")),a={name:"buttonBar",props:["data","id","isGrid","gridId","styleId","column"],inject:["viewTag"],data:function(){return{visible:!0}},methods:{onClick:function(t,e){if(t.clickFunc&&t.clickFunc(),this.$emit("buttonClick",t,e),!t.disabled){var i=this.id;this.isGrid&&o["a"].$emit("component-change","toolbarClick","grid",this.gridId,[t,e,i],this.styleId,this.viewTag?this.viewTag():void 0),o["a"].$emit("component-change","click","toolbar",this.id,[t,e,i],this.styleId,this.viewTag?this.viewTag():void 0),console.log(arguments)}},cannotEdit:function(t,e){return 0!=e},isToolbar:function(){return!0},getBtnClass:function(t){var e="";if(t.disabled&&(e+=" f-toolbar-disabled "),t.type)switch(e+="dialogBtn",t.type){case"warn":e+=" warn ";break;case"primary":e+=" guide ";break;case"highlight":e+=" guide highlight ";break;default:break}switch(t.style){case"2":e+=" primary ";break;case"7":e+=" primary highlight ";break;case"6":e+=" vertical ";break;case"3":e+=" success ";break;case"4":e+=" danger ";break;case"5":e+=" info ";break;default:break}return e},setDisabled:function(t,e){if(void 0!==e&&null!==e)this.data[e].disabled=t;else for(var i in this.data)this.data[i].disabled=t;this.$forceUpdate()},toggleBtns:function(t,e){for(var i in this.data)t.includes(this.data[i].id)&&(this.data[i].hide=!e);this.$forceUpdate()},setVisible:function(t,e){"boolean"==typeof t?this.visible=t:(e&&(this.visible=!0),this.data[t].hide=!e),this.$forceUpdate()}},computed:{},mounted:function(){}},r=a,l=(i("1bbb"),i("2877")),c=Object(l["a"])(r,s,n,!1,null,null,null);e["a"]=c.exports},"444e":function(t,e,i){"use strict";var s=i("be4b"),n=i.n(s);n.a},"44d5":function(t,e,i){"use strict";var s=i("69f8"),n=i.n(s);n.a},"44de":function(t,e,i){},4521:function(t,e,i){"use strict";var s=i("5496"),n=i.n(s);n.a},"461d":function(t,e,i){"use strict";var s=i("5916"),n=i.n(s);n.a},"4af5":function(t,e,i){},"4b72":function(t,e,i){},"4bef":function(t,e,i){"use strict";var s=i("1e0c"),n=i.n(s);n.a},"4c60":function(t,e,i){},"4d48":function(t,e,i){"use strict";var s=i("725d"),n=i.n(s);n.a},"4e92":function(t,e,i){"use strict";var s=i("0741"),n=i.n(s);n.a},"4f4c":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{attrs:{title:t.pageTitle},on:{backClick:t.backClick}},[i("div",{staticClass:"f-flex-wrap f-form-card",class:t.pageCls,attrs:{slot:"content"},slot:"content"},[i("div",{ref:"colContainer",staticClass:"f-flex-col"},t._l(t.layouts,(function(e){return i("div",{key:e,attrs:{id:e}},[i("dycontrol",{ref:"dypanel",refInFor:!0,attrs:{ctx:t.ctx,styleId:t.styleId,gridEditParm:t.gridEditParm,type:t.controls[e].type,vprops:t.controls[e],row:t.rowData,value:t.rowData[t.controls[e].field],bizOps:t.bizOps},on:{input:t.updateForm}})],1)})),0),"view"==t.actionState||t.readonly?t._e():i("div",{staticClass:"f-flex-col f-flex-fixed"},[i("div",{staticStyle:{display:"block"}},[i("div",{staticClass:"f-toolbar"},[i("a",{staticClass:"f-toolbar-item  vertical",on:{click:t.quit}},[t._v(t._s(t.getLang("detail_quit")))]),i("a",{staticClass:"f-toolbar-item  primary highlight ",staticStyle:{margin:"10px"},on:{click:t.saveAndQuit}},[t._v(t._s(t.getLang("detail_save")))])])])]),i("vdialog",{ref:"dialog",attrs:{ctx:t.ctx}}),"float"==t.floatBtn.type&&t.vprops.floatBtn?i("circle-menu",{ref:"menu",staticClass:"f-float-btn",style:t.styleId,attrs:{icon:t.currentFloatBtn.item1.icon,name:t.currentFloatBtn.item1.name,type:"top",number:t.currentFloatBtn.btnLength,circle:"","float-btn":t.currentFloatBtn},on:{btnClick:t.handleMenuClick}},[i("a",{attrs:{slot:"item_1"},slot:"item_1"},[t.currentFloatBtn.item1.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item1.name)+" ")]),t.currentFloatBtn.item1.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item1.icon}}):t._e()],1),i("a",{attrs:{slot:"item_2"},slot:"item_2"},[t.currentFloatBtn.item2.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item2.name)+" ")]),t.currentFloatBtn.item2.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item2.icon}}):t._e()],1),i("a",{attrs:{slot:"item_3"},slot:"item_3"},[t.currentFloatBtn.item3.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item3.name)+" ")]),t.currentFloatBtn.item3.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item3.icon}}):t._e()],1),i("a",{attrs:{slot:"item_4"},slot:"item_4"},[t.currentFloatBtn.item4.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item4.name)+" ")]),t.currentFloatBtn.item4.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item4.icon}}):t._e()],1),i("a",{attrs:{slot:"item_5"},slot:"item_5"},[t.currentFloatBtn.item5.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item5.name)+" ")]),t.currentFloatBtn.item5.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item5.icon}}):t._e()],1),i("a",{attrs:{slot:"item_6"},slot:"item_6"},[t.currentFloatBtn.item6.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item6.name)+" ")]),t.currentFloatBtn.item6.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item6.icon}}):t._e()],1),i("a",{attrs:{slot:"item_7"},slot:"item_7"},[t.currentFloatBtn.item7.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item7.name)+" ")]),t.currentFloatBtn.item7.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item7.icon}}):t._e()],1),i("a",{attrs:{slot:"item_8"},slot:"item_8"},[t.currentFloatBtn.item8.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item8.name)+" ")]),t.currentFloatBtn.item8.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item8.icon}}):t._e()],1)]):t._e(),"action"==t.floatBtn.type&&t.vprops.floatBtn?i("action-menu",{staticClass:"f-float-btn",attrs:{actions:t.floatBtn.actions.filter((function(t){return!t.hide})),styleId:t.styleId}}):t._e()],1)])},n=[],o=(i("a15b"),i("d81d"),i("2241")),a=i("d399"),r=i("d362"),l=i("efee"),c=i("c968"),h=i("c956"),d=(i("810a"),i("c0bb"),i("154e")),u=(i("e8ff"),i("d8ad")),f=i("0771"),p=i("3daa"),m=i("2870"),g=i("8c1b"),v=i("2f04"),b=window.idp.Notify,w=null,y={components:{dypanel:r["a"],dycontrol:l["default"],FarrisBpage:c["a"],CircleMenu:m["a"],ActionMenu:g["a"],vdialog:v["a"]},provide:function(){return{viewTag:this.getViewTag}},mixins:[f["a"]],data:function(){return{block:!1,ctx:{},gridEditParm:{},pageTitle:"",styleId:"",gridId:"",actionState:"",rowIndex:-1,tableName:"",dataId:"",mainId:"",status:"add",vprops:{},rowData:{},layouts:[],controls:{},readonly:!1,viewTag:void 0,currentFloatBtn:{type:"",btnLength:8,item1:{name:"",icon:"",click:"",hide:!1},item2:{name:"",icon:"",click:"",hide:!1},item3:{name:"",icon:"",click:"",hide:!1},item4:{name:"",icon:"",click:"",hide:!1},item5:{name:"",icon:"",click:"",hide:!1},item6:{name:"",icon:"",click:"",hide:!1},item7:{name:"",icon:"",click:"",hide:!1},item8:{name:"",icon:"",click:"",hide:!1},actions:[]},floatBtn:{type:"",btnLength:8,item1:{name:"",icon:"",click:"",id:"",hide:!1},item2:{name:"",icon:"",click:"",id:"",hide:!1},item3:{name:"",icon:"",click:"",id:"",hide:!1},item4:{name:"",icon:"",click:"",id:"",hide:!1},item5:{name:"",icon:"",click:"",id:"",hide:!1},item6:{name:"",icon:"",click:"",id:"",hide:!1},item7:{name:"",icon:"",click:"",id:"",hide:!1},item8:{name:"",icon:"",click:"",id:"",hide:!1},actions:[]},bizOps:[]}},activated:function(){this.$route.query.returnValue||this.$route.query.fromHelp||this.$route.query.back||this.loadUIComponent(),this.$route.query.fromHelp&&this.scrollRef&&(this.scrollRef.scrollTop=this.scroll,this.triggerGridEditEvent("","detailActive")),this.$route.params&&this.$route.params.hasOwnProperty("viewTag")&&(this.viewTag=this.$route.params.viewTag)},beforeRouteLeave:function(t,e,i){window.parent["cancelTaskCenterPopstate"]&&window.parent["cancelTaskCenterPopstate"](!0),this.confirm&&(t.query.detailReturn={table:this.vprops.table,row:JSON.parse(JSON.stringify(this.rowData)),index:this.rowIndex}),t.query.back=!0,this.scrollRef&&(this.scroll=this.scrollRef.scrollTop),i()},computed:{pageCls:function(){var t=[];return(this.block||"en"==window.idp.lang.getLang().id)&&t.push("f-flex-block"),t}},mounted:function(){this.event=new p["b"](this.controls)},watch:{floatBtn:{deep:!0,handler:function(t){this.updateCurFloatBtn()}}},methods:{getViewTag:function(){return this.viewTag},backClick:function(){this.$router.go(-1)},getLang:function(t,e){return window.idp.lang.get(t,e)},toggleMenu:function(t){this.$refs.menu.toggle(t)},loadUIComponent:function(){console.log(this.$route.params);var t=this;this.confirm=!1,this.styleId=this.$route.params.styleId,this.controls=this.$route.params.controls,this.vprops=this.$route.params.vprops,this.pageTitle=this.vprops.title,this.dataId=this.$route.params.dataId,this.gridId=this.$route.params.gridId,this.status=this.$route.params.status,this.rowIndex=this.$route.params.rowIndex,this.parent=this.$route.params.parent,this.listData=this.$route.params.listData,this.formItem=this.$route.params.formItem,this.readonly=this.vprops.readonly,w=new h["b"](this.styleId),this.parent&&(this.store=this.parent.store),this.actionState=this.$route.params.actionState,this.ctx=this.parent.ctx,(this.parent&&this.parent.page&&this.parent.page.block||"en"==window.idp.lang.getLang().id)&&(this.block=!0),this.rowData=JSON.parse(JSON.stringify(this.$route.params.rowData)),this.rowData["__rowindex"]=this.rowIndex,this.gridEditParm={id:this.gridId,table:this.vprops.table,rowindex:this.rowIndex,record:this.rowData},t.regFloatBtn(!0),t.updateCurFloatBtn(),w.getCustomConfig(this.styleId).then((function(e){"ok"==e.Code&&e.Data.bizOps&&(t.bizOps=e.Data.bizOps.map((function(t){return t.id}))),t.updateCurFloatBtn()})),this.initExtend(),this.initStatus()},initStatus:function(){var t=this;this.$nextTick((function(){for(var e in t.layouts){var i=t.control.get(t.layouts[e]);i&&i.setDisabled&&i.setDisabled("view"==t.actionState)}t.readonly&&t.setUIReadOnly(!0),console.log("aaaa"),t.express.triggerTableOne(t.vprops.table,t.rowData,t.rowIndex,t.status),t.triggerGridEditEvent("","beforeRowEdit")}))},setUIReadOnly:function(t){for(var e in this.layouts){var i=this.control.get(this.layouts[e]);i&&i.setDisabled&&i.setDisabled(t)}},triggerGridEditEvent:function(t,e){if(this.gridEditParm){var i=this.gridEditParm.id,s="grid",n=[];n.push(t),n.push(this.rowIndex),n.push(this.gridEditParm.record),n.push(this.gridEditParm.table);var o=u["a"].$emit("component-change",e,s,i,n,this.styleId,this.viewTag);return o}},initExtend:function(){var t=this,e=this;this.layouts=[];var i=this.vprops.cols;for(var s in i)this.layouts.push(this.gridId+"."+i[s].id);this.control=new d["a"](this.$refs,"detail"),this.validate=this.$route.params.validate,this.mainFields=this.$route.params.mainFields,this.$nextTick((function(){t.scrollRef=t.$refs.colContainer})),this.express=this.$route.params.express,this.express.changeExpressContext({setValue:function(t,i,s,n,o){console.log(e),console.log(arguments),t.srctable==e.vprops.table&&o==e.rowIndex&&e.setValue(t.srcfield,i)},setReadOnly:function(t,i,s,n,o){if(console.log(e.control),console.log(arguments),t.ctrlid){var a=e.control.get(t.ctrlid+"."+t.srcfield);a&&a.setDisabled&&a.setDisabled(i)}},setRequired:function(t,i,s,n,o){if(t.ctrlid){var a=e.control.get(t.ctrlid+"."+t.srcfield);a&&a.setRequired&&a.setRequired(i)}},setHide:function(t,i,s,n,o){if(console.log(e.control),console.log(arguments),t.ctrlid){var a=e.control.get(t.ctrlid+"."+t.srcfield);a&&a.setVisible&&a.setVisible(i)}},getRow:function(){return e.rowData}})},setValue:function(t,e){this.rowData[t]=e},quit:function(){this.confirm=!1,this.$router.go(-1)},saveAndQuit:function(){var t=this,e=!0;e=t.triggerGridEditEvent("","beforeSaveDetail"),0!=e&&this.validate.validateGridRow(this.rowData,this.vprops.table,this.mainFields,(function(e,i,s,n,a){console.log(arguments),e?(t.rowData.__validate=!1,t.rowData.__validateMessage="",t.triggerGridEditEvent("","afterSaveDetail"),t.confirm=!0,t.$router.go(-1)):o["a"].alert({message:s.join("\n")}).then((function(){}))}))},updateForm:function(t,e,i,s,n){this.setValue(t,e),n||(this.express.trigger(this.vprops.table,t,null,this.gridId,this.rowIndex,this.rowData),this.triggerGridEditEvent(t,"afterEndEdit"))},loading:function(t){var e=this;a["a"].loading({duration:8e3,message:t||e.getLang("detail_loading"),forbidClick:!0})},loaded:function(){a["a"].clear()},tips:function(t){b({type:"success",message:t})},warn:function(t){b({type:"warning",message:t})},info:function(t){b({type:"primary",message:t})},error:function(t){b({type:"danger",message:t})},confirm:function(t,e,i,s){var n=s||{},a=n.title||this.getLang("confirm"),r=n.confirmButtonText||this.getLang("confirm"),l=n.cancelButtonText||this.getLang("cancel");o["a"].confirm({title:a,message:t,confirmButtonText:r,cancelButtonText:l}).then((function(){e&&e(self)})).catch((function(){i&&i(self)}))},alert:function(t,e){o["a"].alert({message:t}).then((function(){e&&e(self)}))}}},C=y,k=(i("4521"),i("2877")),x=Object(k["a"])(C,s,n,!1,null,null,null);e["default"]=x.exports},"51d0":function(t,e,i){},5496:function(t,e,i){},"568f":function(t,e,i){},"57c5":function(t,e,i){"use strict";var s=i("09c2"),n=i.n(s);n.a},5916:function(t,e,i){},"5c9c":function(t,e,i){"use strict";var s=i("4b72"),n=i.n(s);n.a},"61a1":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{attrs:{title:t.pageTitle},on:{backClick:t.backClick}},[i("div",{staticClass:"f-list-wrap",attrs:{slot:"content"},slot:"content"},[t.favor||t.frequent?i("van-tabs",{attrs:{color:t.tabColor,duration:t.tabDuration},on:{click:t.onTabClick},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[t.frequent?i("van-tab",{attrs:{title:t.getLang("lookup_frequent"),name:"frequent"}}):t._e(),i("van-tab",{attrs:{title:t.pageTitle,name:"data"}}),t.favor?i("van-tab",{attrs:{title:t.getLang("lookup_favor"),name:"favor"}}):t._e()],1):t._e(),"data"==t.curTab?i("van-search",{attrs:{placeholder:t.calcTip()},on:{input:t.onSearch},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}):t._e(),t.localSearch&&t.localRes.length&&!t.frequent?i("div",t._l(t.localRes,(function(e,s){return i("van-tag",{key:s,attrs:{type:"primary",size:"medium",closeable:""},on:{click:function(i){return t.confirmSelect(e)},close:function(e){return t.delCache(s)}}},[t._v(t._s(e[t.fields.title]))])})),1):t._e(),t.group?i("van-dropdown-menu",{directives:[{name:"show",rawName:"v-show",value:"data"==t.curTab,expression:"curTab == 'data'"}],attrs:{"active-color":"#1989fa"}},[i("van-dropdown-item",{ref:"pathTree",attrs:{title:t.calcPath()}},[i("treelist",{ref:"treeList",attrs:{treeConfig:t.treeGroupConfig,ttField:t.pathFields.title,stField:t.pathFields.subtitle,nodes:t.pathList,autoFold:"true",userJS:"1",idField:t.pathConfig?t.pathConfig.KeyCol:""},on:{treeNodeClick:t.pathNodeClick}})],1)],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:"data"==t.curTab,expression:"curTab == 'data'"}],staticClass:"f-list-warp-main"},[t.group?i("van-list",{staticClass:"f-list-pathnodechild"},t._l(t.curPathNode.children,(function(e,s){return i("dylistitem",{key:s,attrs:{ttComp:t.ttComp,row:e,isLink:!1,subtitle:e[t.pathFields.subtitle],rowData:e,title:e[t.pathFields.title],label:e[t.pathFields.label]},on:{click:t.pathNodeClick}})})),1):t._e(),t.isTree?t._e():i("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.refreshing,callback:function(e){t.refreshing=e},expression:"refreshing"}},[i("van-list",{attrs:{finished:t.finished},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,(function(e,s){return i("dylistitem",{key:s,ref:"row",refInFor:!0,attrs:{ttComp:t.ttComp,favor:t.favor,row:e,isLink:!1,subtitle:t.gettpl(e,"subtitle"),rowData:e,title:t.gettpl(e,"title"),label:t.gettpl(e,"label"),userid:t.gettpl(e,"userid"),isMul:t.isMul},on:{favorClick:t.favorClick,click:function(i){return t.onConfirm(e,s)},checkRow:function(i){t.checkRow(e,e.isChecked),t.onConfirmIsMul(e,s)}}})})),1)],1),t.isTree?[i("div",[t.loading?i("div",{staticStyle:{display:"block","text-align":"center"}},[i("van-loading",{attrs:{size:"24px"}},[t._v(t._s(t.getLang("lookup_loading")))])],1):t._e(),i("treelist",{ref:"treeList",attrs:{ttComp:t.ttComp,favor:t.favor,treeConfig:t.treeConfig,ttField:t.fields.title,stField:t.fields.subtitle,nodes:t.list,autoFold:t.autoFold,userJS:t.userJS,selectMemory:t.selectMemory,isMul:t.isMul,noCode:t.noCode,idField:t.idField,helpInput:t.helpInput,isTotal:t.isTotal,autoChild:t.autoChild,childOnly:t.childonly},on:{favorClick:t.favorClick,beforeExpand:t.beforeExpand,treeNodeClick:t.treeNodeClick,autoCheck:t.autoCheck}},[t.showMore?i("a",{staticClass:"f-list-more-btn",on:{click:t.loadMoreRoot}},[t._v(t._s(t.getLang("lookup_loadMore")))]):t._e()])],1)]:t._e(),0!=t.list.length||t.loading?t._e():i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])])],2),i("div",{directives:[{name:"show",rawName:"v-show",value:"favor"==t.curTab,expression:"curTab == 'favor'"}],staticClass:"f-list-warp-main"},[[t._l(t.favorList,(function(e,s){return i("dylistitem",{key:s,ref:"favorRow",refInFor:!0,attrs:{ttComp:t.ttComp,row:e,remove:!0,isLink:!1,subtitle:t.gettpl(e,"subtitle"),rowData:e,title:t.gettpl(e,"title"),label:t.gettpl(e,"label"),isMul:t.isMul,userid:t.gettpl(e,"userid")},on:{removeClick:t.onRemoveFavorItem,click:function(i){return t.onConfirm(e,s)},checkRow:function(i){return t.onConfirmIsMul(e,s)}}})})),0==t.favorList.length?i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])]):t._e()]],2),i("div",{directives:[{name:"show",rawName:"v-show",value:"frequent"==t.curTab,expression:"curTab == 'frequent'"}],staticClass:"f-list-warp-main"},[[t._l(t.localRes,(function(e,s){return i("dylistitem",{key:s,ref:"frequentRow",refInFor:!0,attrs:{ttComp:t.ttComp,row:e,remove:!0,isLink:!1,subtitle:t.gettpl(e,"subtitle"),rowData:e,title:t.gettpl(e,"title"),label:t.gettpl(e,"label"),isMul:t.isMul,userid:t.gettpl(e,"userid")},on:{removeClick:function(e){return t.delCache(s)},click:function(i){return t.onConfirm(e,s)},checkRow:function(i){return t.onConfirmIsMul(e,s)}}})})),0==t.localRes.length?i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])]):t._e()]],2),t.isMul?i("div",{staticClass:"f-flex-col f-flex-fixed"},[i("van-notice-bar",{staticClass:"checkedLengthBar",scopedSlots:t._u([t.selectAll?{key:"left-icon",fn:function(){return[i("checkbox",{staticStyle:{"margin-right":"5px"},attrs:{value:t.checkedAll},on:{input:function(e){return t.onCheckAll()}}})]},proxy:!0}:null,t.group?{key:"right-icon",fn:function(){return[i("van-icon",{directives:[{name:"show",rawName:"v-show",value:"data"==t.curTab,expression:"curTab == 'data'"}],attrs:{name:"arrow",color:"#1989fa"},on:{click:t.showCheckedUser}})]},proxy:!0}:null],null,!0)},[t._v(" "+t._s(t.getLang(t.groupInfo.user?"lookup_userCount":"lookup_selectedCount",{0:t.checkedLength()}))+" ")]),i("div",{staticStyle:{display:"block"}},[i("div",{staticClass:"f-toolbar"},[i("a",{staticClass:"f-toolbar-item  vertical",on:{click:t.quit}},[t._v(t._s(t.getLang("lookup_quit")))]),i("a",{staticClass:"f-toolbar-item  primary highlight ",staticStyle:{margin:"10px"},on:{click:t.saveAndQuit}},[t._v(t._s(t.getLang("lookup_save")))])])])],1):t._e(),t.group?i("van-popup",{style:{width:"85%",height:"100%"},attrs:{"get-container":"body",position:"right"},model:{value:t.showChecked,callback:function(e){t.showChecked=e},expression:"showChecked"}},[i("div",{staticClass:"f-filter-panel"},[t.groupInfo.user?i("div",{staticClass:"f-filter-panel-title"},[t._v(t._s(t.getLang("lookup_checkedUser")))]):i("div",{staticClass:"f-filter-panel-title"},[t._v(t._s(t.getLang("lookup_checked")))]),i("div",{staticClass:"f-filter-panel-form"},[t._l(t.getMemoryCheckedRows(),(function(e,s){return i("dylistitem",{key:s,attrs:{ttComp:t.ttComp,row:e,remove:!0,isLink:!1,subtitle:t.gettpl(e,"subtitle"),rowData:e,title:t.gettpl(e,"title"),label:t.gettpl(e,"label"),userid:t.gettpl(e,"userid")},on:{removeClick:t.onRemoveCheckedRow}})})),t.getMemoryCheckedRows().length?t._e():i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])])],2),i("div",{staticClass:"f-list-filter-button"},[i("van-button",{attrs:{type:"info","native-type":"submit"},on:{click:t.clearAll}},[t._v(" "+t._s(t.getLang("lookup_clear"))+" ")])],1)])]):t._e(),i("vdialog",{ref:"dialog",attrs:{ctx:t.ctx}})],1)])},n=[],o=(i("99af"),i("4de4"),i("7db0"),i("c740"),i("4160"),i("c975"),i("a15b"),i("d81d"),i("fb6a"),i("a434"),i("d3b7"),i("07ac"),i("e25e"),i("3ca3"),i("159b"),i("ddb0"),i("96cf"),i("1da1")),a=i("b982"),r=i("c968"),l=i("62e4"),c=i("1ba7"),h=i("d399"),d=i("f382"),u=i("d761"),f=i("2f04"),p=null,m=window.idp.Notify,g={components:{dylistitem:a["a"],FarrisBpage:r["a"],treelist:c["a"],checkbox:u["a"],vdialog:f["a"]},props:{styleId:{type:String,required:!1},controlId:{type:String,required:!1},filter:{type:Array,required:!1},title:{type:String,required:!1},isMul:{type:Boolean,required:!1},isAsync:{type:Boolean,required:!1}},data:function(){return{isTotal:!1,tabColor:"#388fff",pageTitle:"",active:"data",show:!1,showMore:!1,curTab:["data"],list:[],favorList:[],loading:!1,listConfig:null,loadingData:!1,finished:!1,value:"",refreshing:!1,pageIndex:0,pageSize:50,totalCount:0,filterArr:[],isTree:!1,searching:!1,ttComp:null,helpData:[],treeConfig:{treetype:"",grade:"",level:"",isdetail:"",id:"",pid:"",rootValue:"",format:"",name:""},fields:{title:"",subtitle:"",label:""},selectMemory:{},selectLength:0,memoryData:[],checkedRows:[],checkedFavorRows:[],fieldTypes:[],helpInfo:{},expand:"1",pathConfig:{},treeGroupConfig:{},pathList:[],groupInfo:{},listInfo:{},listFields:{},pathFields:{},curPathNode:"",showChecked:!1,checkedAll:!1,langCode:"zh-CHS",ignoreCase:!1,localSearch:!1,localRes:[],tabDuration:.3,bizid:"",bizopid:"",ctx:{}}},beforeRouteLeave:function(t,e,i){window.parent["cancelTaskCenterPopstate"]&&window.parent["cancelTaskCenterPopstate"](!0),this.confirm&&(t.query.returnValue={controlId:this.controlId,data:this.confirmData}),t.query.back=!0,t.query.fromHelp=!0,i()},activated:function(){this.activeParams()},mounted:function(){this.activeParams(),p=new l["a"](this.styleId),this.langCode=window.idp.lang.getLang().id},methods:{load:function(t){h["a"].loading({duration:25e3,message:t||this.getLang("card_loading"),forbidClick:!0})},loaded:function(){h["a"].clear()},getLang:function(t,e){return window.idp.lang.get(t,e)},activeParams:function(){if(this.controlId=this.$route.params.controlId,this.filter=this.$route.params.filter,this.title=this.$route.params.title,this.styleId=this.$route.params.styleId,this.realStyleId=this.$route.params.realStyleId||this.$route.params.styleId,this.value=this.$route.params.value,this.runtime=this.$route.params.runtime,this.group=this.$route.params.group,void 0!=this.$route.params.title&&(this.pageTitle=this.$route.params.title),this.$route.params.valiInfo&&(this.selectMemory=this.$route.params.valiInfo),this.async=this.$route.params.async,this.favor=this.$route.params.favor,this.formId=this.$route.params.formId,this.childonly=this.$route.params.childonly,this.calcChildOnly=this.$route.params.calcChildOnly,this.onBeforeConfirm=this.$route.params.onBeforeConfirm,this.selectAll=this.$route.params.selectAll,this.autoFold=this.$route.params.autoFold,this.userJS=this.$route.params.userJS,this.ttComp=this.$route.params.ttComp,this.isMul=this.$route.params.isMul,this.noCode=this.$route.params.noCode,this.helpData=this.$route.params.helpData,this.idField=this.$route.params.idField,this.helpInput=this.$route.params.helpInput,this.isTotal=this.$route.params.isTotal,this.autoChild=this.$route.params.autoChild,this.putChangeFilter=this.$route.params.putChangeFilter,this.notOpenFavor=this.$route.params.notOpenFavor,this.ignoreCase=this.$route.params.isIgnoreCase||!1,this.checkremember=this.$route.params.checkremember||this.group,this.localSearch=!this.isMul&&this.$route.params.localSearch,this.frequent=!this.isMul&&this.localSearch&&this.$route.params.frequent,this.rootPager=this.$route.params.rootPager,this.store=this.$route.params.store,this.pageSize=this.$route.params.pager&&this.$route.params.pageSize||50,this.bizid=this.$route.params.bizid||"",this.bizopid=this.$route.params.bizopid||"",this.ctx=this.$route.params.ctx||"",this.rootPager&&(this.showMore=!0),!this.isMul&&this.localSearch){var t=localStorage.getItem(this.styleId+this.formId+window.idp.context.get("UserId"));this.localRes=t?JSON.parse(t):[]}else this.localRes=[];this.userJS&&(this.userJS=parseInt(this.userJS))},beforeExpand:function(t){var e=this,i=this;if(this.async){if(1==t.loaded||t.children&&t.children.length>0)return;var s=this.getQueryParam();p.getChildrenTree(s.sqlId,t,s.fields,this.getBizInfo()).then((function(s){for(var n in e.formatFavorData(s.Data),s.Data)s.Data[n].isAsync=!0,"0"==s.Data[n][e.treeConfig.isdetail]&&(s.Data[n].children=[],s.Data[n].hasAync=!0,s.Data[n].close=!0),i.isTotal&&s.Data[n]["com.inspur.fastdweb.hasAuth"]&&(s.Data[n].hasAuth=!0),t.children.push(s.Data[n]);t.loaded=!0}))}},delCache:function(t){var e=window.idp.context.get("UserId")||"";this.localRes.splice(t,1),localStorage.setItem(this.styleId+this.formId+e,JSON.stringify(this.localRes))},getRemoteSingleDeferred:function(t,e){var i=this,s=this.getQueryParam(),n=new Promise((function(n,o){p.getChildrenTree(s.sqlId,t,i.filter,e).then((function(t){n(0==t.Data.length)}))}));return n},onCheckAll:function(){var t=this,e=this,i=!this.checkedAll;"favor"==this.curTab?this.favorList.forEach((function(e,s){t.$refs.favorRow[s].onClick(i)})):this.isTree?(this.$refs.treeList.onCheckAll(i),this.helpData=this.$route.params.helpData,this.checkremember&&(this.selectMemory={},this.$refs.treeList.getCheckedId().forEach((function(t){e.selectMemory[t]=!0})))):this.list.forEach((function(e,s){t.$refs.row[s].onClick(i)}))},getRemoteChildOnly:function(t){var e=this;return new Promise((function(i,s){var n=e.getBizInfo(),o=[];for(var a in t)o.push(e.getRemoteSingleDeferred(t[a],n));Promise.all(o).then((function(t){for(var n in t)if(0==t[n])return e.error(e.getLang("lookup_childOnlyWarn")),void s();i(!0)})).catch((function(t){s(),console.log(t)}))}))},pathNodeClick:function(t){this.value="",this.curPathNode=t,this.pageIndex=0,this.refreshing=!0,this.$refs["pathTree"].toggle(!1),this.onLoad(!1,!0)},calcTip:function(){var t,e=this.getLang("lookup_inputKeyword");if(this.group&&this.listInfo){var i=window.idp.lang.getLang().id;switch(i){case"en":t=this.listInfo.placeholder_EN;break;case"zh-CHT":t=this.listInfo.placeholder_CHT;break;default:t=this.listInfo.placeholder_CHS}t=t||this.listInfo.placeholder}return t||e},calcPath:function(){var t=this;if(!this.curPathNode||!this.groupInfo.map||!this.curPathNode[this.groupInfo.map.source]){var e,i=window.idp.lang.getLang().id;switch(i){case"en":e=this.groupInfo.placeholder_EN;break;case"zh-CHT":e=this.groupInfo.placeholder_CHT;break;default:e=this.groupInfo.placeholder_CHS}return e||this.groupInfo.placeholder}var s=d["a"].getGradePath(this.curPathNode[this.pathConfig.WbsCol],this.pathConfig.PcolExp),n=[];return s.forEach((function(e){var i=t.pathList.find((function(i){return i[t.pathConfig.WbsCol]==e}));i&&n.push(i[t.groupInfo.tpl.title])})),n.join(" > ")},gettpl:function(t,e){return this.group?t[this.listFields?this.listFields[e]:this.fields[e]]:t[this.fields[e]]},showCheckedUser:function(){this.showChecked=!0},treeNodeClick:function(t){var e=this,i=this;if(!this.isTotal||t.hasAuth){if(this.isMul)if(this.childonly){if("0"==t[this.treeConfig.isdetail])return void this.error(this.getLang("lookup_childOnlyTip"));t.isChoose=!t.isChoose,this.checkremember&&!this.isTotal&&(this.selectMemory[t[this.treeConfig.id]]=t.isChoose)}else t.isChoose=!t.isChoose,this.checkremember&&!this.isTotal&&(this.selectMemory[t[this.treeConfig.id]]=t.isChoose);else{if(this.calcChildOnly)return void this.getRemoteChildOnly([t]).then((function(i){i&&e.confirmSelect(t)}));if(this.childonly&&"0"==t[this.treeConfig.isdetail])return void this.error(this.getLang("lookup_childOnlyTip"));this.confirmSelect(t)}this.$nextTick((function(){i.helpData=i.$route.params.helpData}))}},confirmSelect:function(t){if(!this.onBeforeConfirm||0!=this.onBeforeConfirm(Array.isArray(t)?t:[t])){this.confirm=!0,this.confirmData=t;var e=this,i=window.idp.context.get("UserId")||"",s=localStorage.getItem(this.styleId+this.formId+i),n=[];if(s&&(n=JSON.parse(s)),Array.isArray(t)){var o=function(i){var s=n.findIndex((function(s){return s[e.idField]==t[i][e.idField]}));-1!=s&&n.splice(s,1),n.unshift(t[i])};for(var a in t)o(a)}else if(t[e.idField]){var r=n.findIndex((function(i){return i[e.idField]==t[e.idField]}));-1!=r&&n.splice(r,1),n.unshift(t)}n.length>5&&(n=n.slice(0,5)),localStorage.setItem(this.styleId+this.formId+i,JSON.stringify(n)),this.$router.go(-1)}},autoCheck:function(t){this.checkremember&&this.autoChild&&!this.isTotal&&(this.selectMemory[t[this.idField]]=t.isChoose)},onTabClick:function(t){this.curTab=t;console.log(arguments)},favorClick:function(t){var e=this;if(t.hasAuth||!this.isTotal){var i=1==t.isFavor?"removeFavor":"addFavor";p[i](t[this.listConfig.KeyCol],this.formId,this.styleId).then((function(i){"ok"==i.Code&&(t.isFavor=!t.isFavor,e.loadFavorList())}))}},onRemoveFavorItem:function(t){this.onRemoveFavor(t,!0)},onRemoveCheckedRow:function(t){var e=this;this.memoryData.splice(this.memoryData.findIndex((function(i){return i[e.listConfig.KeyCol]==t[e.listConfig.KeyCol]})),1),this.selectMemory[t[this.listConfig.KeyCol]]=!1,this.onRefresh(),this.calcSelectLength()},clearAll:function(){this.memoryData=[],this.selectMemory={},this.onRefresh(),this.selectLength=0,this.showChecked=!1},onRemoveFavor:function(t,e){var i=this;p.removeFavor(t[this.listConfig.KeyCol],this.formId,this.styleId).then((function(s){"ok"==s.Code&&(t.isFavor=!t.isFavor,i.checkedFavorRows.splice(i.checkedFavorRows.indexOf(t),1),e?(i.list=[],i.checkedFavorRows=[],i.curPathNode={},i.showView()):i.loadFavorList())}))},loadUIView:function(){var t=this;p.getListInfo(this.realStyleId).then((function(e){t.listConfig=e.Data,console.log(t.fields)}))},loadListData:function(){},calcSelectLength:function(){return this.selectLength=Object.values(this.selectMemory).filter((function(t){return t})).length,this.calcCheckedAll(),this.selectLength},getQueryParam:function(t,e,i){if(t)return{sqlId:this.groupInfo.sqlid,fields:[],orders:[],count:!0,bizOpId:"",bizId:"",page:1,pagesize:3e3};this.selectAll&&(this.pageSize=1e3),this.isTree&&(this.pageSize=1e4);var s=this.styleId;return i&&(s=this.groupInfo.default),{sqlId:s,fields:this.getFilter(e),orders:this.getOrder(),count:!0,bizId:this.bizid||"",bizOpId:this.bizopid||"",page:this.pageIndex,pagesize:this.pageSize}},onSearch:function(){var t=this;this.curPathNode={},this.throttle((function(){t.refreshData(!0)}),400,400)(this)},getFilter:function(t){var e=[],i=JSON.parse(JSON.stringify(this.filter)),s=[];for(var n in this.fieldTypes)-1!="int;tinyint;smallint;bigint;decimal;float;double;date;datetime".indexOf(this.fieldTypes[n])&&""!=this.fieldTypes[n]||s.push(this.filterArr[n]);if(this.value)for(var o in this.group&&this.listInfo&&(s=this.listInfo.fields),s){var a="",r="";0==o&&(a="("),o==s.length-1&&(r=")"),e.push({Left:a,Field:s[o],Operate:" like ",IsExpress:!1,Value:"%"+this.value+"%",Right:r,Logic:o<s.length-1?" or ":"",IgnoreCase:this.ignoreCase||!1})}else t&&this.group&&this.groupInfo.map&&this.curPathNode[this.groupInfo.map.source]&&e.push({Left:"(",Field:this.groupInfo.map.target,Operate:" leftlike ",IsExpress:!1,Value:this.curPathNode[this.groupInfo.map.source],Right:")",Logic:o<s.length-1?" or ":"",IgnoreCase:this.ignoreCase||!1});return i.length>0&&e.length>0&&(i[i.length-1].Logic=" and "),i.concat(e)},getOrder:function(){var t=[];if(this.helpInfo&&this.helpInfo.mobileSort&&this.helpInfo.mobileSort.length)for(var e in this.helpInfo.mobileSort){var i=this.helpInfo.mobileSort[e];t.push({Field:i.field,Order:i.type})}return t},showView:function(t,e){var i=this,s=this;this.refreshing&&(this.list=[],this.refreshing=!1),this.favor&&1==this.pageIndex?this.loadFavorList().then((function(n){i.tabDuration=0,i.favorList.length>0&&!t&&!i.notOpenFavor&&!e?(i.curTab="favor",i.active="favor"):!t&&!e&&i.frequent&&i.localRes.length>0&&(i.curTab="frequent",i.active="frequent"),i.$nextTick((function(){s.tabDuration=.3})),i.loadPathTree(),i.loadDataList(t,e)})):(this.tabDuration=0,!t&&!e&&this.frequent&&this.localRes.length>0&&(this.curTab="frequent",this.active="frequent"),this.$nextTick((function(){s.tabDuration=.3})),this.loadPathTree(),this.loadDataList(t,e))},loadFavorList:function(){var t=this.getQueryParam(),e=this;this.checkedFavorRows=[];var i=new Promise((function(i,s){p.getFavor(e.formId,t).then((function(t){"ok"==t.Code&&(e.favorList=t.Data),i(!0)}))}));return i},loadDataList:function(t,e){this.async?this.loadRootData(t,e):this.loadMoreData(t,e)},loadPathTree:function(t){var e=this;!this.pathList.length&&this.group&&this.helpInfo&&this.groupInfo&&p.getListPage(this.getQueryParam(!0)).then((function(t){"ok"==t.Code?(t.Data.Rows.forEach((function(t){t["isChecked"]=!1})),e.pathList=[],e.pathList=e.pathList.concat(t.Data.Rows),e.loading=!1):e.loading=!1}))},onLoad:function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(e,i){var s=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.listConfig){t.next=9;break}return t.next=3,p.getListInfo(this.realStyleId).then((function(t){var e=t.Data,i=s;if(s.listConfig=t.Data,e.TreeType&&(s.isTree=!0,s.treeConfig={treetype:e.TreeType,grade:e.WbsCol,level:e.RankCol,isdetail:e.LeafCol,id:e.TreeIdCol,pid:e.ParentCol,rootValue:e.RootVal,format:e.PcolExp,name:e.NodeCol}),e.HelpInfo){s.helpInfo=JSON.parse(e.HelpInfo),s.group&&(s.groupInfo=s.helpInfo.group,s.listInfo=s.helpInfo.list,s.listFields.title=s.listInfo.tpl.title,s.listFields.subtitle=s.listInfo.tpl.subtitle,s.listFields.label=s.listInfo.tpl.prefix,s.listFields.userid=s.listInfo.tpl.userid);var n=window.idp.lang.getLang().id;switch(n){case"en":s.pageTitle=s.pageTitle||s.helpInfo.title_EN||"";break;case"zh-CHT":s.pageTitle=s.pageTitle||s.helpInfo.title_CHT||"";break;default:s.pageTitle=s.pageTitle||s.helpInfo.title_CHS||""}s.pageTitle=s.pageTitle||s.helpInfo.title||""}s.pageTitle=s.pageTitle||s.listConfig.MC||s.getLang("lookup_defaultPageTitle");var o=[],a=[];if(s.helpInfo&&s.helpInfo.mobileFields&&s.helpInfo.mobileFields.length>0)for(var r in s.helpInfo.mobileFields)o.push(s.helpInfo.mobileFields[r]),a.push(s.listConfig.Cols.find((function(t){return t.Field==i.helpInfo.mobileFields[r]})).FieldType||"");else s.listConfig.Cols.forEach((function(t){"1"==t.IsShow&&(o.push(t.Field),a.push(t.FieldType))}));o.length&&(s.fields.title=o[0],o.length>1&&(s.fields.subtitle=o[1]),o.length>2&&(s.fields.label=o[2])),s.filterArr=o,s.fieldTypes=a}));case 3:if(!this.group||!this.groupInfo){t.next=9;break}return t.next=6,p.getListInfo(this.groupInfo.sqlid).then((function(t){var e=t.Data;s.pathConfig=t.Data,e.TreeType&&(s.treeGroupConfig={treetype:e.TreeType,grade:e.WbsCol,level:e.RankCol,isdetail:e.LeafCol,id:e.TreeIdCol,pid:e.ParentCol,rootValue:e.RootVal,format:e.PcolExp,name:e.NodeCol}),s.pathFields.title=s.groupInfo.tpl.title,s.pathFields.subtitle=s.groupInfo.tpl.subtitle,s.pathFields.label=s.groupInfo.tpl.prefix,s.pathFields.userid=s.groupInfo.tpl.userid}));case 6:if(!this.groupInfo.default){t.next=9;break}return t.next=9,p.getListPage(this.getQueryParam(!1,!1,!0)).then((function(t){"ok"==t.Code?(s.curPathNode=t.Data.Rows[0],i=!0):console.error("默认数据源查询失败")}));case 9:if(!this.loadingData){t.next=11;break}return t.abrupt("return");case 11:this.pageIndex++,console.log(this.pageIndex),this.loadingData=!0,this.showView(e,i);case 15:case"end":return t.stop()}}),t,this)})));function e(e,i){return t.apply(this,arguments)}return e}(),getBizInfo:function(){var t={bizId:this.bizid||"",bizOpId:this.bizopid||""};return t.isTotal=this.isTotal,t},throttle:function(t,e,i){var s=this,n=this.timer,o=(new Date).getTime();return function(){var a=s,r=arguments,l=(new Date).getTime();clearTimeout(n),l-o>=i?(t.apply(a,r),o=l):(console.log(e),s.timer=setTimeout((function(){t.apply(a,r)}),e))}},debounce:function(t,e){var i=null,s=e||200;return function(){var e=arguments,n=this;clearTimeout(i),i=setTimeout((function(){t.apply(n,e)}),s)}},refreshData:function(){this.checkremember||(this.selectLength=0),this.async?this.value?this.loadFilter():this.loadRootData():(this.refreshing=!0,this.loading=!0,this.onRefresh())},loadFilter:function(){var t=this,e=this,i=this.getQueryParam();this.load(this.getLang("lookup_searching")),p.searchTree(i.sqlId,i.fields,this.getBizInfo()).then((function(i){if(t.loaded(),i.Data){for(var s in t.showMore=!1,t.formatFavorData(i.Data),t.list=i.Data,i.Data)e.isTotal&&i.Data[s]["com.inspur.fastdweb.hasAuth"]&&(i.Data[s].hasAuth=!0);t.$nextTick((function(){t.checkedAll=!!t.$refs.treeList&&t.$refs.treeList.isCheckedAll()}))}}))},loadRootPager:function(t){var e=this.getQueryParam(),i=e.fields;i.length>0&&(i[i.length-1].Logic="and"),this.treeConfig.level?i.push({Field:this.treeConfig.level,Operate:"=",Value:1}):this.treeConfig.pid&&(i.push({Field:this.treeConfig.pid,Operate:"isnull",Left:"(",Logic:"or"}),i.push({Field:this.treeConfig.pid,Operate:"=",Value:"",Right:")"}));var s=this.getBizInfo(),n={sqlId:e.sqlId,orders:[],fields:i,page:t,pageSize:50,bizId:s.bizId,bizOpId:s.bizOpId,count:!1};return p.getListPage(n)},loadMoreRoot:function(){var t=this;this.loadRootPager(this.rootPage+1).then((function(e){t.rootPage+=1,t.dealTreeData(e.Data.Rows),t.$refs.treeList.addRoot(e.Data.Rows),e.Data.Rows.length||(t.showMore=!1,t.error(t.getLang("lookup_nomoreData")))}))},dealTreeData:function(t){for(var e in t)"0"==t[e][this.treeConfig.isdetail]&&(t[e].children=[],t[e].hasAync=!0,t[e].close=!0);this.formatFavorData(t)},loadRootData:function(){var t=this,e=this;if(this.rootPager)this.rootPage=1,this.loadRootPager(this.rootPage).then((function(e){t.dealTreeData(e.Data.Rows),t.list=e.Data.Rows,t.showMore=!!e.Data.Rows.length,t.loading=!1}));else{var i=this.getQueryParam();this.load(this.getLang("card_loading")),p.getRootTree(i.sqlId,1,i.fields,this.getBizInfo()).then((function(i){if(t.loaded(),"ok"==i.Code&&i.Data){for(var s in i.Data)"0"==i.Data[s][t.treeConfig.isdetail]&&(i.Data[s].children=[],i.Data[s].hasAync=!0,i.Data[s].close=!0),e.isTotal&&i.Data[s]["com.inspur.fastdweb.hasAuth"]&&(i.Data[s].hasAuth=!0);t.checkremember||(t.$route.params.helpData=[],t.helpData=[]),t.loading=!1,t.formatFavorData(i.Data),t.list=i.Data}}))}},formatFavorData:function(t){if(this.favor)for(var e in t)for(var i in t[e]["isFavor"]=!1,this.favorList)if(t[e][this.listConfig.KeyCol]==this.favorList[i][this.listConfig.KeyCol]){t[e]["isFavor"]=!0;break}},loadMoreData:function(t,e){var i=this;p.getListPage(this.getQueryParam(!1,e)).then((function(e){"ok"==e.Code?(i.totalCount=e.Data.Total,i.formatFavorData(e.Data.Rows),i.checkremember||(i.$route.params.helpData=[],i.helpData=[]),e.Data.Rows.forEach((function(t){i.selectMemory[t[i.listConfig.KeyCol]]?(t["isChecked"]=!0,i.$route.params.valiInfo&&(i.checkremember?i.memoryData.filter((function(e){return e[i.listConfig.KeyCol]==t[i.listConfig.KeyCol]})).length||i.memoryData.push(t):i.isTree||-1!=i.checkedRows.indexOf(t)?i.isTree&&-1==i.$route.params.helpData.indexOf(t)&&(i.$route.params.helpData.push(t),i.helpData=i.$route.params.helpData):i.checkedRows.push(t))):t["isChecked"]=!1})),t&&(i.list=[]),i.list=i.list.concat(e.Data.Rows),i.loading=!1,i.loadingData=!1,i.totalCount<=i.pageSize*i.pageIndex&&(i.finished=!0)):(i.loading=!1,i.loadingData=!1),i.calcSelectLength()}))},error:function(t){m({type:"primary",message:t})},onConfirm:function(t,e){this.isMul||(this.childonly&&"0"==t[this.treeConfig.isdetail]?this.error(this.getLang("lookup_childOnlyTip")):this.confirmSelect(t))},onConfirmIsMul:function(t,e){if(this.isMul){if(this.childonly&&"0"==t[this.treeConfig.isdetail])return t["isChecked"]=!1,void this.error(this.getLang("lookup_childOnlyTip"));if("favor"!=this.curTab){if(t["isChecked"])this.checkedRows.push(t);else for(var i in this.checkedRows)if(this.checkedRows[i]==t){this.checkedRows.splice(i,1);break}}else if(t["isChecked"])this.checkedFavorRows.push(t);else for(var s in this.checkedFavorRows)if(this.checkedFavorRows[s]==t){this.checkedFavorRows.splice(s,1);break}}},backClick:function(){this.putChangeFilter?this.onConfirm([]):this.$router.go(-1)},onRefresh:function(){this.pageIndex=0,this.finished=!1,this.checkedRows=[],this.checkedFavorRows=[],this.curPathNode={},this.onLoad(!0)},quit:function(){this.confirm=!1,this.putChangeFilter&&(this.confirm=!0,this.confirmData=[]),this.$router.go(-1)},saveAndQuit:function(){var t=this;this.isTree?this.confirmData=this.$route.params.helpData:this.confirmData=this.checkedRows,"favor"==this.curTab&&(this.confirmData=this.checkedFavorRows),!this.checkremember||this.autoChild||this.isTotal||this.isTree||"data"!=this.curTab||(this.confirmData=this.getMemoryCheckedRows()),this.calcChildOnly?this.getRemoteChildOnly(this.confirmData).then((function(e){e&&t.confirmSelect(t.confirmData)})):this.confirmSelect(this.confirmData)},checkRow:function(t,e){var i=this;!this.checkremember||this.autoChild||this.isTotal||(e?(this.selectMemory[t[this.listConfig.KeyCol]]=!0,-1==this.memoryData.findIndex((function(e){return e[i.listConfig.KeyCol]==t[i.listConfig.KeyCol]}))&&this.memoryData.push(t)):(this.selectMemory[t[this.listConfig.KeyCol]]=!1,this.memoryData.splice(this.memoryData.findIndex((function(e){return e[i.listConfig.KeyCol]==t[i.listConfig.KeyCol]})),1))),this.calcSelectLength()},getMemoryCheckedRows:function(){var t=[];for(var e in this.memoryData)this.selectMemory[this.memoryData[e][this.listConfig.KeyCol]]&&t.push(this.memoryData[e]);return t},calcCheckedAll:function(){if("favor"==this.curTab)if(this.checkedAll=!0,this.$refs.favorRow&&this.$refs.favorRow.length){for(var t=0;t<this.$refs.favorRow.length;t++)if(!this.$refs.favorRow[t].row.isChecked){this.checkedAll=!1;break}}else this.checkedAll=!1;else if(this.isTree)this.checkedAll=!(!this.$refs.treeList||!this.$refs.treeList.isCheckedAll)&&this.$refs.treeList.isCheckedAll();else if(this.checkedAll=!0,this.$refs.row&&this.$refs.row.length){for(var e=0;e<this.$refs.row.length;e++)if(!this.$refs.row[e].row.isChecked){this.checkedAll=!1;break}}else this.checkedAll=!1},checkedLength:function(){return this.calcCheckedAll(),"favor"==this.curTab?this.checkedFavorRows.length:this.isTree?this.helpData.length:this.checkremember?this.selectLength:this.checkedRows.length}},watch:{loading:function(t){var e=this;t||this.$nextTick((function(){e.calcCheckedAll()}))},pageTitle:{immediate:!0,handler:function(t){window.document.title=t}}}},v=g,b=(i("7a20"),i("2877")),w=Object(b["a"])(v,s,n,!1,null,null,null);e["default"]=w.exports},"64ac":function(t,e,i){},6596:function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],staticClass:"f-split-title",class:{"f-split-panel-fold":t.isFold},on:{click:function(e){return e.stopPropagation(),t.onClick()}}},[t._v(" "+t._s(t.showTitle)+" "),i("van-icon",{attrs:{name:"arrow-up"}})],1)},n=[],o=(i("c740"),{name:"splitTitle",props:["title","styleId","ctx","parent","id","options"],components:{},data:function(){return{isFold:!1,visible:!0,show:!0}},mounted:function(){this.isFold=!0},activated:function(){this.$route.query.back||(this.isFold=!0)},methods:{setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t,this.isFold=!!t},onClick:function(){this.isFold=!this.isFold},getLang:function(t,e){return window.idp.lang.get(t,e)}},computed:{showTitle:function(){for(var t=this,e=0,i=this.parent.childs,s=i.findIndex((function(e,i){return e==t.id})),n=this.options.foldIndex||0,o=s-1;o>n;o--){var a=i[o],r=this.ctx.control.get(a);r&&r.options&&r.options.foldShow||(r&&r.options&&r.options.express&&""!==r.options.express.hide.express||r&&r.setVisible&&(e+=1))}return this.isFold?this.getLang("title_showMore",{0:e}):this.getLang("title_fold")}},watch:{isFold:function(t){var e=this;console.log(this.parent);for(var i=this.parent.childs,s=i.findIndex((function(t,i){return t==e.id})),n=this.options.foldIndex||0,o=s-1;o>n;o--){var a=i[o],r=this.ctx.control.get(a);r&&r.options&&r.options.foldShow||(r&&r.options&&r.options.express&&""!==r.options.express.hide.express||r&&r.setVisible&&this.ctx.control.get(a).setVisible(t,!0))}}}}),a=o,r=(i("2b6b"),i("2877")),l=Object(r["a"])(a,s,n,!1,null,null,null);e["a"]=l.exports},"67c8":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-sucess-Container"},[i("div",{staticStyle:{width:"20%"}},[i("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",version:"1.1",viewBox:"0 0 70 70",preserveAspectRatio:"xMinYMin meet"}},[i("circle",{attrs:{cx:"35",cy:"35",r:"35",fill:"#51BD78"}}),i("polyline",{staticStyle:{fill:"none",stroke:"#FFFFFF","stroke-width":"4"},attrs:{points:"16,32 30,48, 52,21","stroke-linecap":"round"}})])]),i("div",[i("span",{staticClass:"f-sucess-Tips"},[t._v(t._s(t.message))])]),i("div",{staticClass:"f-sucess-Button"},[i("button",{staticClass:"f-sucess-Continue",on:{click:function(e){return t.Continue()}}},[t._v("返回首页")])]),i("div",{staticClass:"f-sucess-Button"},[i("button",{staticClass:"f-sucess-Exit",on:{click:function(e){return t.Exit()}}},[t._v("退出应用")])])])},n=[],o={data:function(){return{message:""}},activated:function(){this.activeParams()},mounted:function(){this.activeParams()},methods:{activeParams:function(){this.message=this.$route.params.message},Continue:function(){this.$router.go(-2)},Exit:function(){imp.iWindow.close()}}},a=o,r=(i("e9a1"),i("2877")),l=Object(r["a"])(a,s,n,!1,null,null,null);e["default"]=l.exports},6829:function(t,e,i){},"693a":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-filter-warp"},[t.showLeft&&t.isTree?i("van-dropdown-menu",{staticClass:"f-filter-left",attrs:{"close-on-click-outside":!t.panelSearch}},[i("van-dropdown-item",{ref:"item",on:{open:t.openEvent,close:t.closeEvent},scopedSlots:t._u([{key:"title",fn:function(){return[i("van-icon",{attrs:{name:"exchange"}})]},proxy:!0}],null,!1,127811271)},[i("van-tree-select",{attrs:{height:t.height,items:t.panelSearch?t.curTreeList:t.treeList,"active-id":t.activeTreeId,"main-active-index":t.activeTreeIndex},on:{"click-item":t.onSelectChange,"update:activeId":function(e){t.activeTreeId=e},"update:active-id":function(e){t.activeTreeId=e},"update:mainActiveIndex":function(e){t.activeTreeIndex=e},"update:main-active-index":function(e){t.activeTreeIndex=e}}})],1)],1):t._e(),t.showLeft&&!t.isTree?i("van-dropdown-menu",{staticClass:"f-filter-left",attrs:{"close-on-click-outside":!t.panelSearch}},[i("van-dropdown-item",{ref:"item",attrs:{options:t.curSingleList},on:{change:t.onSelectChange,open:t.openEvent,close:t.closeEvent},scopedSlots:t._u([{key:"title",fn:function(){return[i("van-icon",{attrs:{name:"exchange"}})]},proxy:!0}],null,!1,127811271),model:{value:t.singleValue,callback:function(e){t.singleValue=e},expression:"singleValue"}})],1):t._e(),i("van-search",{attrs:{placeholder:t.getPlaceholder(),shape:"round","input-align":"center"},on:{input:t.onInputEvent},scopedSlots:t._u([t.iconView?{key:"left-icon",fn:function(){return[i(t.iconView,{tag:"div"})]},proxy:!0}:null],null,!0),model:{value:t.currentValue,callback:function(e){t.currentValue=e},expression:"currentValue"}}),t.showRight?i(t.searchView,{tag:"div"}):t._e(),t.showFilter?i("div",[i("filterbar",{ref:"filterbar",attrs:{styleId:t.styleId,vprops:t.vprops},on:{input:t.handleFilterChange,open:t.filterPanelOpen}})],1):t._e()],1)},n=[],o=(i("99af"),i("4de4"),i("4160"),i("caad"),i("c975"),i("d81d"),i("fb6a"),i("4ec9"),i("d3b7"),i("ac1f"),i("2532"),i("3ca3"),i("841c"),i("159b"),i("ddb0"),i("d8ad")),a=i("0b9a"),r=i("a026"),l={name:"vinput",props:["name","type","label","value","options","readonly","vprops","styleId"],inject:["viewTag"],components:{filterbar:a["a"]},activated:function(){console.log(this)},data:function(){return{autoFilter:!0,isTree:!1,singleValue:"",showLeft:!1,height:280,showRight:!1,showFilter:!1,singleList:[],curSingleList:[],treeList:[],curTreeList:[],moreFilters:[],activeTreeId:"",activeTreeIndex:0,currentValue:"",recordValue:"",placeholder:"请输入搜索关键词",searchView:null,iconView:null,panelSearch:!1,panelSearchTip:"",searchPanelOpen:!1,isMul:!1,treeMap:new Map}},mounted:function(){this.vprops&&this.vprops.placeholder?this.placeholder=this.vprops.placeholder:this.placeholder=this.getLang("lookup_inputKeyword"),this.searchView=this.buildSingleComp("searchTpl","searchView",["value","text"]),this.searchView&&(this.showRight=!0),this.buildSingleComp("iconTpl","iconView",[]),this.vprops&&this.vprops.filter&&(this.showFilter=!0)},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},handleFilterChange:function(t){console.log(t),this.moreFilters=t,this.onInputEvent(this.currentValue,!0)},onInputEvent:function(t,e){var i=this;!this.searchPanelOpen||e?(this.$emit("input",this.name,t),o["a"].$emit("component-change","serach","serachbar",this.vprops.id,[t],this.styleId,this.viewTag?this.viewTag():void 0)):this.isTree?(this.curTreeList=[],t?this.treeList.forEach((function(e){var s=e.children.filter((function(e){return e.text.indexOf(t)>-1}));s&&s.length&&i.curTreeList.push({children:s.slice(0),text:e.text})})):this.curTreeList=this.treeList,this.activeTreeIndex=0):(this.curSingleList=[],this.curSingleList=t?this.singleList.filter((function(e){return e.text.indexOf(t)>-1})):this.singleList)},onSelectChange:function(t){if(this.isTree)if(!this.isMul&&this.activeTreeId.length>1){var e=[];e.push(t.id),this.activeTreeId=e}else if(t.single){var i=this.treeMap.get(t.id);if(i){var s=this.activeTreeId.length,n=this.treeList.filter((function(t){return t.text==i}))[0].children.map((function(t){return t.id})),o=this.activeTreeId.filter((function(t){return!n.includes(t)})),a=o.length;s>a&&o.push(t.id),this.activeTreeId=o}}this.onInputEvent(this.currentValue,!0)},getFilterValue:function(){return this.isTree?this.activeTreeId:this.singleValue},setShowLeft:function(t){t?$(".f-form-list > .f-flex-col").css("-webkit-overflow-scrolling","unset"):$(".f-form-list > .f-flex-col").css("-webkit-overflow-scrolling","touch"),this.showLeft=t},setSource:function(t,e){this.showLeft=!0,this.isTree=!1,this.singleValue="",this.singleList=t,this.curSingleList=this.singleList,e&&e.search&&(this.panelSearch=!0,e.searchTip&&(this.panelSearchTip=e.searchTip))},setTreeSource:function(t,e,i){var s=this;this.treeMap.clear(),this.showLeft=!0,this.isTree=!0,this.activeTreeId=[],this.isMul=e,t&&t.length&&t.forEach((function(t){t.children&&t.children.length&&t.children.forEach((function(e){e.single=t.single,s.treeMap.set(e.id,t.text)}))})),this.treeList=t,i?i.search&&(this.panelSearch=!0,i.searchTip&&(this.panelSearchTip=i.searchTip)):(this.panelSearch=!1,this.panelSearchTip="")},getPlaceholder:function(){return this.searchPanelOpen&&this.panelSearch&&this.panelSearchTip?this.panelSearchTip:this.placeholder},setDropDownSource:function(){},openEvent:function(){this.panelSearch&&(this.searchPanelOpen=!0,this.recordValue=this.currentValue,this.currentValue="",this.curTreeList=this.treeList,this.curSingleList=this.singleList),$(".f-form-list > .f-flex-col").css("-webkit-overflow-scrolling","unset")},closeEvent:function(){this.panelSearch&&(this.searchPanelOpen=!1,this.currentValue=this.recordValue),$(".f-form-list > .f-flex-col").css("-webkit-overflow-scrolling","touch"),this.isTree&&o["a"].$emit("component-change","searchPanelClose","serachbar",this.vprops.id,[this.vprops.id,this.activeTreeId],this.styleId,this.viewTag?this.viewTag():void 0)},filterPanelOpen:function(){o["a"].$emit("component-change","filterPanelOpen","serachbar",this.vprops.id,[this.vprops.id,this],this.styleId,this.viewTag?this.viewTag():void 0)},getCtrls:function(){if(this.$refs.filterbar)return this.$refs.filterbar.$refs},getCtrl:function(t){if(this.$refs.filterbar)return this.$refs.filterbar.$refs[t]?this.$refs.filterbar.$refs[t][0]:void 0},getFilter:function(){var t=[],e=this.vprops.filters;if(this.currentValue&&e&&e.length)for(var i in e){var s={Left:"",Field:e[i].value,Operate:e[i].oper||"like",IsExpress:!1,Value:this.currentValue,Right:"",Logic:"or"};0==i&&(s.Left="("),i==e.length-1&&(s.Right=")",s.Logic=""),t.push(s)}return t.length>0&&this.moreFilters.length>0&&(t[t.length-1].Logic="and"),t.concat(this.moreFilters)},buildSingleComp:function(t,e,i){var s=this.vprops;if(s[t]&&s[t].template){var n={};if(s[t].mixin){var o=new Function("return "+s[t].mixin+";");n=o()}return this[e]=r["a"].extend({mixins:[n],props:i,template:s[t].template,data:function(){return{}},methods:{}}),this[e]}}},watch:{value:function(t){this.currentValue=t}}},c=l,h=(i("d5ae"),i("2877")),d=Object(h["a"])(c,s,n,!1,null,null,null);e["a"]=d.exports},"69f8":function(t,e,i){},"6abd":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},n=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"fm-page fm-page-hasbg fm-cmp-lookup",attrs:{id:"wf-modal-window"}})])}],o=(i("4de4"),{components:{},props:[],data:function(){return{envent:{},btnid:"",taskEntity:{},actions:[]}},created:function(){},mounted:function(){console.log(this.$route),this.envent=this.$route.query.event,this.btnid=this.$route.query.btnid,this.taskEntity=this.$route.query.taskEntity,this.actions=this.$route.query.actions,this.initContent(this.envent,this.btnid);var t=this;window["closeActionView"]=function(){document.getElementById("wf-modal-window").classList.remove("fm-state-open"),document.getElementById("wf-modal-window").innerHTML="",t.$router.go(-2)},window.history.pushState(null,null,document.URL),window.addEventListener("popstate",this.dealModalPopstate)},destroyed:function(){window.removeEventListener("popstate",this.dealModalPopstate)},beforeRouteLeave:function(t,e,i){t.query.back=!0,i()},methods:{initContent:function(t,e){t.stopPropagation();var i=this.actions.filter((function(t){return t.code==e}));window["actionConfig"]=i[0],window["taskEntity"]=this.taskEntity;var s=document.getElementById("wf-modal-window"),n="/platform/runtime/wf/webapp/mobiletaskcenter/index.html#/actionview?action=".concat(e),o='<iframe style="width: 100%;height: 100%;border-width: 0" src="'+n+'"></iframe>';s.innerHTML=o,document.getElementById("wf-modal-window").classList.add("fm-state-open")},dealModalPopstate:function(){if(console.log("modalmodal"),window["IDPBack"]&&"function"==typeof window["IDPBack"]){var t=window["IDPBack"]();t?this.$router.go(-1):(console.log("modal open, router hold on"),window.history.pushState(null,null,document.URL))}}}}),a=o,r=i("2877"),l=Object(r["a"])(a,s,n,!1,null,null,null);e["default"]=l.exports},"6baf":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{ref:"pageContainer",attrs:{title:t.pageTitle,rightCmp:t.navbarRCmp},on:{backClick:t.backClick}},[i("div",{ref:"refLayout",staticClass:"f-form-list",class:{"f-flex-wrap":t.page.flex},attrs:{slot:"content"},slot:"content"},[t.favor?i("van-tabs",{attrs:{color:t.tabColor},on:{click:t.onTabClick},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[i("van-tab",{attrs:{title:t.getLang("list_data")}}),i("van-tab",{attrs:{title:t.getLang("list_favor")}})],1):t._e(),t._l(t.container,(function(e,s){return i("div",{key:s,ref:"refCols",refInFor:!0,staticClass:"f-flex-col",class:{"f-flex-fixed":e.fixed},on:{scroll:t.scrollEvent}},["9"==e.type||e.pop?t._e():i("div",{attrs:{id:e.id}},[t._l(e.childs,(function(e){return t._l(e.childs,(function(s,n){return i("div",{key:n,attrs:{name:e.index,id:s}},["searchbar"==t.controls[s].type?i("searchbar",{ref:s,refInFor:!0,attrs:{styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}):t._e(),"grid"==t.controls[s].type?i("dylistview",{key:"normalList"+t.lookupRefresh,ref:s,refInFor:!0,attrs:{hashDs:t.hashDs,callbackGetFilter:t.getGridFilter,getFilterKeyword:t.getFilterKeyword,clearFilter:t.clearFilter,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s],favor:t.favor,isFavorTab:t.isFavorTab,isLookupMul:t.isLookupMul,lookupAsync:t.lookupAsync,lookupChildonly:t.lookupChildonly,lookupTotal:t.lookupTotal,isEmpty:t.isEmpty,isLookup:t.isLookup,selectMemory:t.lookup?t.lookup.selectMemory:void 0,bizid:t.bizid,bizopid:t.bizopid,lookupAutoFold:t.lookupAutoFold},on:{favorClick:t.favorClick,setLookupMul:t.setLookupMul}}):t._e(),"grid"!=t.controls[s].type&&"searchbar"!=t.controls[s].type?i("dycontrol",{ref:s,refInFor:!0,attrs:{ctx:t.ctx,styleId:t.styleId,type:t.controls[s].type,vprops:t.controls[s],value:t.formItem[t.controls[s].field],callbackGetFilter:t.getFilterDataSource,bizOps:t.bizOps},on:{input:t.updateForm}}):t._e()],1)}))}))],2),"9"==e.type&&e.childs&&e.childs.length?i("van-tabs",{ref:e.id,refInFor:!0,attrs:{color:t.tabColor},on:{click:t.changeTab},model:{value:t.tabsActive[e.id],callback:function(i){t.$set(t.tabsActive,e.id,i)},expression:"tabsActive[item.id]"}},t._l(e.childs,(function(e,s){return i("van-tab",{key:s,attrs:{title:e.label}},t._l(e.childs,(function(s,n){return i("div",{key:n,attrs:{name:e.index,id:s}},["searchbar"==t.controls[s].type?i("searchbar",{ref:s,refInFor:!0,attrs:{styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}):t._e(),"grid"==t.controls[s].type?i("dylistview",{key:"tabList"+t.lookupRefresh,ref:s,refInFor:!0,attrs:{hashDs:t.hashDs,callbackGetFilter:t.getGridFilter,getFilterKeyword:t.getFilterKeyword,clearFilter:t.clearFilter,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s],isLookupMul:t.isLookupMul,lookupAsync:t.lookupAsync,lookupChildonly:t.lookupChildonly,lookupTotal:t.lookupTotal,isEmpty:t.isEmpty,isLookup:t.isLookup,bizid:t.bizid,bizopid:t.bizopid,lookupAutoFold:t.lookupAutoFold},on:{favorClick:t.favorClick,setLookupMul:t.setLookupMul}}):t._e(),"grid"!=t.controls[s].type&&"searchbar"!=t.controls[s].type?i("dycontrol",{ref:s,refInFor:!0,attrs:{styleId:t.styleId,type:t.controls[s].type,vprops:t.controls[s],value:t.formItem[t.controls[s].field],callbackGetFilter:t.getFilterDataSource,bizOps:t.bizOps},on:{input:t.updateForm}}):t._e()],1)})),0)})),1):t._e()],1)})),t._l(t.popupConfig,(function(e,s){return i("div",{key:s},[i("van-popup",{style:e.style,attrs:{position:e.position,"lazy-render":t.lazyRender,"get-container":"body"},model:{value:e.show,callback:function(i){t.$set(e,"show",i)},expression:"item.show"}},["9"!=e.type?i("div",[t._l(e.childs,(function(e){return t._l(e.childs,(function(s,n){return i("div",{key:n,attrs:{name:e.index}},["searchbar"==t.controls[s].type?i("searchbar",{ref:s,refInFor:!0,attrs:{styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}):t._e(),"grid"==t.controls[s].type?i("dylistview",{key:"popupList"+t.lookupRefresh,ref:s,refInFor:!0,attrs:{hashDs:t.hashDs,callbackGetFilter:t.getGridFilter,getFilterKeyword:t.getFilterKeyword,clearFilter:t.clearFilter,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s],isLookupMul:t.isLookupMul,lookupAsync:t.lookupAsync,lookupChildonly:t.lookupChildonly,lookupTotal:t.lookupTotal,isLookup:t.isLookup},on:{setLookupMul:t.setLookupMul}}):t._e(),"grid"!=t.controls[s].type&&"searchbar"!=t.controls[s].type?i("dycontrol",{ref:s,refInFor:!0,attrs:{ctx:t.ctx,styleId:t.styleId,type:t.controls[s].type,vprops:t.controls[s],value:t.formItem[t.controls[s].field],callbackGetFilter:t.getFilterDataSource,bizOps:t.bizOps},on:{input:t.updateForm}}):t._e()],1)}))}))],2):t._e()])],1)})),i("vdialog",{ref:"dialog",attrs:{ctx:t.ctx}}),"float"==t.floatBtn.type&&t.showFloatIcon?i("circle-menu",{ref:"menu",staticClass:"f-float-btn",style:t.styleId,attrs:{icon:t.currentFloatBtn.item1.icon,name:t.currentFloatBtn.item1.name,type:"top",number:t.currentFloatBtn.btnLength,circle:"","float-btn":t.currentFloatBtn},on:{btnClick:t.handleMenuClick}},[i("a",{attrs:{slot:"item_1"},slot:"item_1"},[t.currentFloatBtn.item1.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item1.name)+" ")]),t.currentFloatBtn.item1.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item1.icon}}):t._e()],1),i("a",{attrs:{slot:"item_2"},slot:"item_2"},[t.currentFloatBtn.item2.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item2.name)+" ")]),t.currentFloatBtn.item2.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item2.icon}}):t._e()],1),i("a",{attrs:{slot:"item_3"},slot:"item_3"},[t.currentFloatBtn.item3.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item3.name)+" ")]),t.currentFloatBtn.item3.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item3.icon}}):t._e()],1),i("a",{attrs:{slot:"item_4"},slot:"item_4"},[t.currentFloatBtn.item4.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item4.name)+" ")]),t.currentFloatBtn.item4.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item4.icon}}):t._e()],1),i("a",{attrs:{slot:"item_5"},slot:"item_5"},[t.currentFloatBtn.item5.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item5.name)+" ")]),t.currentFloatBtn.item5.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item5.icon}}):t._e()],1),i("a",{attrs:{slot:"item_6"},slot:"item_6"},[t.currentFloatBtn.item6.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item6.name)+" ")]),t.currentFloatBtn.item6.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item6.icon}}):t._e()],1),i("a",{attrs:{slot:"item_7"},slot:"item_7"},[t.currentFloatBtn.item7.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item7.name)+" ")]),t.currentFloatBtn.item7.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item7.icon}}):t._e()],1),i("a",{attrs:{slot:"item_8"},slot:"item_8"},[t.currentFloatBtn.item8.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item8.name)+" ")]),t.currentFloatBtn.item8.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item8.icon}}):t._e()],1)]):t._e(),"action"==t.floatBtn.type&&t.showFloatIcon?i("action-menu",{staticClass:"f-float-btn",attrs:{actions:t.floatBtn.actions.filter((function(t){return!t.hide})),styleId:t.styleId}}):t._e(),i("van-action-sheet",{attrs:{round:!1,actions:t.actions,"cancel-text":t.getLang("list_cancel"),"close-on-click-action":""},on:{cancel:t.onActionCancel,select:t.onActionConfirm},model:{value:t.actionShow,callback:function(e){t.actionShow=e},expression:"actionShow"}}),i("Workflow",{ref:"refWork",attrs:{styleId:t.styleId,ctx:t.ctx}}),t.isLookup&&t.isLookupMul?i("div",{staticClass:"f-flex-col f-flex-fixed"},[i("van-notice-bar",{staticClass:"checkedLengthBar"},[t._v(" "+t._s(t.getLang("list_selectedCount",{0:t.checkedLength()}))+" ")]),i("div",{staticStyle:{display:"block"}},[i("div",{staticClass:"f-toolbar"},[i("a",{staticClass:"f-toolbar-item  vertical",on:{click:t.quit}},[t._v(" "+t._s(t.getLang("list_quit")))]),i("a",{staticClass:"f-toolbar-item  primary highlight ",staticStyle:{margin:"10px"},on:{click:t.saveAndQuit}},[t._v(" "+t._s(t.getLang("list_confirm")))])])])],1):t._e()],2)])},n=[],o=(i("a4d3"),i("e01a"),i("d28b"),i("99af"),i("4de4"),i("7db0"),i("4160"),i("c975"),i("d81d"),i("4e82"),i("a434"),i("b64b"),i("d3b7"),i("ac1f"),i("8a79"),i("3ca3"),i("1276"),i("159b"),i("ddb0"),i("5530")),a=i("3c69"),r=i("d399"),l=i("91f4"),c=i.n(l),h=i("c6e7"),d=i.n(h),u=i("d8ad"),f=i("33de"),p=i("efee"),m=i("693a"),g=i("0b9a"),v=i("3616"),b=i("c956"),w=i("1b34"),y=i("810a"),C=i("c0bb"),k=i("154e"),x=i("3daa"),I=i("2870"),_=i("8c1b"),T=i("c968"),F=i("74b9"),L=i("0771"),S=i("7342"),D=(i("39ea"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("van-action-sheet",{attrs:{actions:t.wfProcess,description:"请选择提交的流程"},on:{select:t.confirmSelectWF},model:{value:t.wfChoose,callback:function(e){t.wfChoose=e},expression:"wfChoose"}}),i("van-popup",{attrs:{"get-container":"body"},model:{value:t.wfUserChoose,callback:function(e){t.wfUserChoose=e},expression:"wfUserChoose"}},[i("div",{staticStyle:{padding:"30px"}},[i("div",{staticStyle:{"padding-bottom":"10px","font-size":"18px"}},[t._v("请选择候选人")]),i("van-checkbox-group",{model:{value:t.resultUser,callback:function(e){t.resultUser=e},expression:"resultUser"}},t._l(t.wfUser,(function(e,s){return i("div",{key:s,staticStyle:{"padding-bottom":"6px"}},[i("div",{staticStyle:{padding:"6px"}},[i("van-checkbox",{attrs:{name:e.Id}},[t._v(t._s(e.Name))])],1)])})),0),i("van-button",{attrs:{type:"default",block:""},on:{click:t.confirmSubmit}},[t._v("确定")])],1)])],1)}),E=[],$=(i("fb6a"),{}),P=window.idp.Notify,O={props:["styleId","ctx"],components:{},data:function(){return{wfChoose:!1,wfProcess:[],wfUserChoose:!1,wfUser:[],resultUser:[],extData:{}}},mounted:function(){this.styleId&&($=new b["b"](this.styleId)),this.wfConfig={Cancel:"0",DfIdField:null,DfStateField:null,DwField:null,Id:"",Pass:"",Submit:"",UnPass:"",styleId:this.styleId},this.ctx.event=this.ctx.event,this.currentRow={},this.extData={}},watch:{styleId:function(){this.styleId&&($=new b["b"](this.styleId)),this.wfConfig.styleId=this.styleId}},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},loading:function(t){r["a"].loading({duration:8e3,message:t||this.getLang("card_loading"),forbidClick:!0})},loaded:function(){r["a"].clear()},setWfConfig:function(t){this.wfConfig=t},init:function(t){var e=this;return new Promise((function(i,s){$.getWorkFlowConfig(t).then((function(n){"ok"==n.Code&&n.Data?(e.wfConfig=n.Data,e.wfConfig.styleId=t,i(e.wfConfig)):(s(),console.warn("尚未配置审批流"))}))}))},setCurrentRow:function(t){this.currentRow=t},getRowsInfo:function(t){var e=[],i=[],s=[],n=!0,o=!1,a=void 0;try{for(var r,l=t[Symbol.iterator]();!(n=(r=l.next()).done);n=!0){var c=r.value;e.push(c[this.wfConfig.DwField]||""),i.push(c[this.wfConfig.Id]||""),s.push(c[this.wfConfig.DfIdField]||"")}}catch(h){o=!0,a=h}finally{try{n||null==l.return||l.return()}finally{if(o)throw a}}return{companyIdArr:e,dataIdArr:i,lastWfIdArr:s}},getMulWorkFlow:function(t){var e=this;this.isMul=!0,this.currentRows=t;var i=this;this.loading(i.getLang("wf_submit"));var s=this.getRowsInfo(t);$.getWorkItem(this.wfConfig.styleId,s.companyIdArr.shift(),s.dataIdArr.shift(),this.extData).then((function(t){e.loaded(),"ok"==t.Code&&t.Data?1==t.Data||"string"==typeof t.Data||t.Data instanceof Array&&1==t.Data.length?Promise.all($.getWorkItems(e.wfConfig.styleId,s.companyIdArr,s.dataIdArr,e.extData)).then((function(t){P({type:"success",message:i.getLang("card_submitSuccess")}),e.ctx.event.emitGlobal("submitMulSuccess",[e.ctx,t]),console.log(t),e.isMul=!1})).catch((function(t){console.error(t),e.isMul=!1})):t.Data.assigneeInfoList?e.showSelectUserNew(t.Data.procInstanceId,t.Data.assigneeInfoList):t.Data.length>1?e.showWFSelect(t.Data):1==t.Data.length?e.submit(t.Data[0].Id):P({type:"danger",message:"未能获取到提交分配信息"}):P({type:"danger",message:t.Message})}))},getWorkFlow:function(){var t=this,e=this,i="";this.currentRow[this.wfConfig.DwField]&&(i=this.currentRow[this.wfConfig.DwField]||"");var s=this.currentRow[this.wfConfig.Id];this.loading(e.getLang("wf_submit")),$.getWorkItem(this.wfConfig.styleId,i,s,this.extData).then((function(i){t.loaded(),"ok"==i.Code&&i.Data?1==i.Data||"string"==typeof i.Data||i.Data instanceof Array&&1==i.Data.length?(P({type:"success",message:e.getLang("card_submitSuccess")}),t.ctx.event.emitGlobal("submitSuccess",[t.ctx])):i.Data.assigneeInfoList?t.showSelectUserNew(i.Data.procInstanceId,i.Data.assigneeInfoList):i.Data.length>1?t.showWFSelect(i.Data):1==i.Data.length?t.submit(i.Data[0].Id):P({type:"danger",message:"为获取到提交分配信息"}):P({type:"danger",message:i.Message})}))},showWFSelect:function(t){var e=[];for(var i in t){var s={name:t[i]["Name"],id:t[i]["Id"]};e.push(s)}this.wfProcess=e,this.wfChoose=!0},confirmSelectWF:function(t){this.wfChoose=!1,this.submit(t.id)},submit:function(t){var e=this,i=this.currentRow[this.wfConfig.Id],s=this.currentRow[this.wfConfig.DfIdField],n=[i,s],o={},a=this.ctx.event.emitGlobal("beforeWfSubmit",n),r=this;if(a&&(i=a),this.loading(r.getLang("card_submiting")),this.isMul){o=this.getRowsInfo(this.currentRows);var l=[];$.startWorkFlow(this.wfConfig.styleId,o.dataIdArr[0],t,o.lastWfIdArr[0],this.extData).then((function(i){if(e.loaded(),"ok"==i.Code)if(i.Data.assigneeInfoList)e.showSelectUserNew(i.Data.procInstanceId,i.Data.assigneeInfoList);else{for(var s in e.currentRows.shift(),o=e.getRowsInfo(e.currentRows),e.currentRows)l.push($.startWorkFlow(e.wfConfig.styleId,o.dataIdArr[s],t,o.lastWfIdArr[s],e.extData));Promise.all(l).then((function(t){P({type:"success",message:r.getLang("card_submitSccess")}),e.ctx.event.emitGlobal("submitMulSuccess",[e.ctx]),e.isMul=!1,console.log(t)})).catch((function(t){console.error(t)}))}else P({type:"danger",message:i.Message})}))}else $.startWorkFlow(this.wfConfig.styleId,i,t,s,this.extData).then((function(t){e.loaded(),"ok"==t.Code?t.Data.assigneeInfoList?e.showSelectUserNew(t.Data.procInstanceId,t.Data.assigneeInfoList):(P({type:"success",message:r.getLang("card_submitSccess")}),e.ctx.event.emitGlobal("submitSuccess",[e.ctx])):P({type:"danger",message:t.Message})}))},showSelectUserNew:function(t,e){var i=this;this.currentProcessInstanceId=t,this.wfUser=[],e.forEach((function(t,e){t.assigneeUsers&&t.assigneeUsers.forEach((function(e,s){e.activityInstId=t.activityInstId,i.wfUser.push(e)}))})),this.wfUserChoose=!0},confirmSubmit:function(){var t=this,e=this;if(this.resultUser.length){var i={};this.resultUser.forEach((function(t){var s=e.wfUser.find((function(e){return e.Id==t}));s&&(i[s.activityInstId]=i[s.activityInstId]||[],i[s.activityInstId].push(t))}));var s=[];for(var n in i)s.push({activityInstId:n,assigneeIds:i[n]});var o=this.wfConfig,a=this.currentRow[o.Id],r=this.currentRow[o.DfIdField],l={},c=this.currentProcessInstanceId;if(this.loading(e.getLang("card_submiting")),this.isMul){var h=[],d=[];l=this.getRowsInfo(this.currentRows.slice().splice(1)),h.push(c),Promise.all($.getWorkItems(o.styleId,l.companyIdArr,l.dataIdArr,e.extData)).then((function(i){for(var n in i.forEach((function(t){return h.push(t.Data.procInstanceId)})),l=t.getRowsInfo(t.currentRows),t.currentRows)d.push($.startWorkFlowUserCloud(o.styleId,l.dataIdArr[n],h[n],l.lastWfIdArr[n],s,h[n],e.extData));Promise.all(d).then((function(t){e.loaded(),e.wfUserChoose=!1,P({type:"success",message:e.getLang("card_submitSuccess")}),e.ctx.event.emitGlobal("submitMulSuccess",[e.ctx]),console.log(t),e.isMul=!1})).catch((function(t){console.error(t),e.isMul=!1}))}))}else $.startWorkFlowUserCloud(o.styleId,a,c,r,s,c,this.extData).then((function(i){t.loaded(),"ok"==i.Code?(t.wfUserChoose=!1,P({type:"success",message:e.getLang("card_submitSuccess")}),t.ctx.event.emitGlobal("submitSuccess",[t.ctx])):P({type:"danger",message:i.Message})}))}else P({type:"danger",message:e.getLang("card_submiting")})},cancelSubmit:function(t){var e=this,i=this,s=t[this.wfConfig.Id],n=t[this.wfConfig.DfIdField];this.loading(i.getLang("wf_canceling")),$.cancelSubmit(this.wfConfig.styleId,s,n).then((function(t){e.loaded(),"ok"==t.Code&&1==t.Data?(P({type:"success",message:i.getLang("wf_cancelsuccess")}),e.ctx.event.emitGlobal("cancelSumbitSuccess")):P({type:"danger",message:i.getLang("wf_cancelfailed")})}))}}},B=O,R=i("2877"),M=Object(R["a"])(B,D,E,!1,null,null,null),V=M.exports,A=i("e082"),N=(i("72fb"),i("2f04")),q=window.idp.Notify,z={},U={},j=null,H={},W={components:{dylayout:f["a"],dycontrol:p["default"],dylistview:v["a"],searchbar:m["a"],CircleMenu:I["a"],ActionMenu:_["a"],FarrisBpage:T["a"],Workflow:V,filterbar:g["a"],vdialog:N["a"]},provide:function(){return{rootview:this,viewTag:this.getViewTag}},mixins:[L["a"]],data:function(){return{isCustom:!0,isLookup:!1,lookup:{confirm:!1,confirmData:[],checkedRows:[],checkedFavorRows:[]},version:"",lazyRender:!1,tabColor:"#388fff",name:"list",styleId:"",pageTitle:"",hashDs:{},listConfig:{},formItem:{},listItems:{},schema:[],page:{},controls:{},uiConfig:{},container:{},layouts:[],actions:[],actionShow:!1,currentFloatBtn:{type:"",btnLength:8,item1:{name:"",icon:"",click:"",hide:!1},item2:{name:"",icon:"",click:"",hide:!1},item3:{name:"",icon:"",click:"",hide:!1},item4:{name:"",icon:"",click:"",hide:!1},item5:{name:"",icon:"",click:"",hide:!1},item6:{name:"",icon:"",click:"",hide:!1},item7:{name:"",icon:"",click:"",hide:!1},item8:{name:"",icon:"",click:"",hide:!1},actions:[]},floatBtn:{type:"",btnLength:8,item1:{name:"",icon:"",click:"",id:"",hide:!1},item2:{name:"",icon:"",click:"",id:"",hide:!1},item3:{name:"",icon:"",click:"",id:"",hide:!1},item4:{name:"",icon:"",click:"",id:"",hide:!1},item5:{name:"",icon:"",click:"",id:"",hide:!1},item6:{name:"",icon:"",click:"",id:"",hide:!1},item7:{name:"",icon:"",click:"",id:"",hide:!1},item8:{name:"",icon:"",click:"",id:"",hide:!1},actions:[]},favor:!1,isFavorTab:!1,isLookupMul:!1,lookupAsync:!1,lookupChildonly:!1,lookupTotal:!1,isEmpty:!1,showFloatIcon:!1,langResources:{},tabsInfo:{},tabsActive:{},ctx:{},bizOps:[],bizid:"",bizopid:"",lookupRefresh:0,lookupAutoFold:!1,navbarRCmp:null}},activated:function(){this.scrollRef&&(this.scrollRef.scrollTop=this.scroll),(this.pageTitle||this.page.title)&&(document.title=this.pageTitle||this.page.title),this.activeLookupParams(),this.isLookup&&(this.active=0,this.isFavorTab=!1,this.favor=this.lookup.favor||!1,this.isLookupMul=this.lookup.isMul||!1,this.lookupAsync=this.lookup.async||!1,this.lookupTotal=this.lookup.isTotal||!1,this.lookupChildonly=this.lookup.childonly||!1,this.isEmpty=this.lookup.isEmpty||!1,this.bizid=this.lookup.bizid||"",this.bizopid=this.lookup.bizopid||"",this.lookupAutoFold=this.lookup.autoFold||!1,this.$route.params.lookup&&(this.lookupRefresh+=1)),""!==this.$route.query.workItemId&&window.parent["taskCenterLoadingClose"]&&window.parent["taskCenterLoadingClose"](!0)},created:function(){var t=this;u["a"].$on("component-change",(function(e,i,s,n,o){return o&&t.styleId!=o?null:(console.log(t.event),console.log(t.styleId+o),t.event.emit(e,i,s,n))}))},beforeRouteEnter:function(t,e,i){z[t.name]||U[t.name]||(U[t.name]=!0,z[t.name]=e.name),H.query=JSON.parse(JSON.stringify(e.query)),H.fullpath=e.fullPath,H.name=e.name,console.log(arguments),i((function(i){t&&t.name&&t.name.endsWith("list")&&e&&e.name&&"wffirstview"==e.name&&(console.log("beforeRouteEnter-next-holdiframe-refresh"),i.gridController&&i.gridController.refresh())}))},beforeRouteLeave:function(t,e,i){var s=!0;this.event&&(s=this.event.emitGlobal("beforeRouteLeave",[t,e,i]),t.name&&t.name===z[e.name]&&(s=this.event.emitGlobal("pageBack",[this.name,this.styleId]))),0!=s?(this.clearWatermark(!0),this.$route.query&&this.$route.query.wfviewpagedeep&&window.parent["cancelTaskCenterPopstate"]&&window.parent["cancelTaskCenterPopstate"](!0),console.log(arguments),this.scrollRef&&(this.scroll=this.scrollRef.scrollTop),this.isLookup&&(this.lookup.confirm&&(t.query.returnValue={controlId:this.lookup.controlId,data:this.lookup.confirmData}),t.query.back=!0,t.query.fromHelp=!0,this.lookup.confirm=!1,this.lookup.confirmData=[],this.lookup.checkedRows=[],this.lookup.checkedFavorRows=[],this.lookup.selectMemory={},this.lookup.ignoreCase=!1,this.lookup.checkremember=!1),i()):i(!1)},mounted:function(){var t=this.$route.path,e=t.split("/");3==e.length&&(this.styleId=this.$route.meta.styleId,this.pathName=e[2]),j=new b["b"](this.styleId),this.service=j,"en"==window.idp.lang.getLang().id?a["a"].use("en-US",c.a):"zh-CHT"==window.idp.lang.getLang().id&&a["a"].use("zh-TW",d.a),this.activeLookupParams(),this.loadUIComponent()},methods:{getViewTag:function(){return this.viewTag},initLangResources:function(){this.langResources=window.idp.lang.initLangResources(this.styleId)||{},this.pageTitle=this.langResources[this.name+"_title"]?this.langResources[this.name+"_title"]:this.pageTitle},getLang:function(t,e){return window.idp.lang.get(t,e)},activeResurces:function(){var t=this;this.moduleManager=new F["a"](this.styleId,this.version),this.moduleManager.execModule([this.code+"/list"],(function(e,i){e&&e.init(t),i&&i.init(t),t.initView((function(){t.event.emit("viewReady","all","all",[t]),t.event.emit("pageActive","all","all",[t]),t.showFloatIcon=!0}))}),this.isCustom,this.customCode)},setLookupMul:function(){this.isLookupMul=!0},changeTab:function(t,e){this.event.emitGlobal("tabChange",[t,e])},regEvents:function(){var t=this,e=this;for(var i in this.context=w["a"],this.event=new x["b"](this.uiConfig.controls),this.view=this,this.uiConfig.container)for(var s in this.uiConfig.container[i].childs){var n=this.uiConfig.container[i].childs[s];for(var o in this.uiConfig.container[i].childs[s].childs){var a=this.uiConfig.container[i].childs[s].childs[o],r=this.uiConfig.controls[a];switch(r.type){case"toolbar":this.regToolbar(a,!1,r);break;case"searchbar":n.isqry&&(this.gridController.pushQry(n.queryds,n),this.regSearchBar(a,n),r.queryds=n.queryds);break;case"grid":console.log(this.$refs),this.gridController.add(a,r,this),this.isLookup?this.regLookup(a):this.regGrid(a);break;case"tab":this.regTab(a);break;case"gridmenu":this.gridController.add(a,r,this),this.regGridMenu(a);break;default:break}}}e.regFloatBtn(),e.updateCurFloatBtn(),this.control=new k["a"](this.$refs,"list"),j.getCustomConfig(this.styleId).then((function(i){for(var s in"ok"==i.Code&&i.Data.bizOps&&(e.bizOps=i.Data.bizOps.map((function(t){return t.id}))),e.updateCurFloatBtn(),t.uiConfig.container)for(var n in t.uiConfig.container[s].childs){t.uiConfig.container[s].childs[n];for(var o in t.uiConfig.container[s].childs[n].childs){var a=t.uiConfig.container[s].childs[n].childs[o],r=t.uiConfig.controls[a];switch(r.type){case"toolbar":t.initBtnHidden(a,!1,r,!0);break;default:break}}}}))},regSearchBar:function(t,e){var i=this;this.event.on("serach","serachbar",t,(function(){i.gridController.refreshDs(e.queryds)})),this.event.on("searchPanelClose","serachbar",t,(function(t,e){i.event.emitGlobal("searchPanelClose",[t,e,i.styleId])})),this.event.on("filterPanelOpen","serachbar",t,(function(t,e){i.event.emitGlobal("filterPanelOpen",[t,e,i.styleId])}))},activeLookupParams:function(){var t=this.$route.params.lookup;t&&(this.isLookup=!0,this.lookup.controlId=this.$route.params.controlId,this.lookup.filter=this.$route.params.filter,this.lookup.async=this.$route.params.async,this.lookup.favor=this.$route.params.favor,this.lookup.formId=this.$route.params.formId,this.lookup.autoChild=this.$route.params.autoChild,this.lookup.isTotal=this.$route.params.isTotal,this.lookup.childonly=this.$route.params.childonly,this.lookup.isMul=this.$route.params.isMul,this.lookup.isEmpty=this.$route.params.empty,this.lookup.checkremember=this.$route.params.checkremember,this.lookup.selectMemory=this.$route.params.valiInfo||[],this.lookup.ignoreCase=this.$route.params.isIgnoreCase||!1,this.lookup.bizopid=this.$route.params.bizopid||"",this.lookup.bizid=this.$route.params.bizid||"",this.lookup.autoFold=this.$route.params.autoFold||!1)},regLookup:function(t){var e=this,i=this.lookup;this.event.on("refresh","grid",t,(function(){e.lookup.checkedFavorRows=[],e.lookup.checkremember||(e.lookup.checkedRows=[],e.lookup.selectMemory={})})),this.event.on("rowclick","grid",t,(function(t,i,s){e.isLookupMul||(e.lookup.confirm=!0,e.lookup.confirmData=i,e.$router.go(-1))})),this.event.on("checkrow","grid",t,(function(t,s,n){i.childonly,e.isLookup&&e.isLookupMul&&(e.isFavorTab?-1==e.lookup.checkedFavorRows.indexOf(s)&&n?e.lookup.checkedFavorRows.push(s):e.lookup.checkedFavorRows.splice(e.lookup.checkedFavorRows.indexOf(s),1):-1==e.lookup.checkedRows.indexOf(s)&&n?(e.lookup.checkedRows.push(s),e.lookup.selectMemory[s[e.listConfig.KeyCol]]=!0):(e.lookup.checkedRows.splice(e.lookup.checkedRows.indexOf(s),1),e.lookup.selectMemory[s[e.listConfig.KeyCol]]=!1))})),this.regSwipe(t)},favorClick:function(){console.log("触发了favorclick"),this.lookup.checkedFavorRows=[]},saveAndQuit:function(){this.isFavorTab?this.lookup.confirmData=this.lookup.checkedFavorRows:this.lookup.confirmData=this.lookup.checkedRows,this.lookup.confirm=!0,this.$router.go(-1)},quit:function(){this.$router.go(-1)},getGridFilter:function(t){return this.getGridFilterExpress(t)},getFilterKeyword:function(t){var e=this.uiConfig.controls[t].dscode;return this.gridController.getFilterKeyword(e)},clearFilter:function(t){var e,i=null===(e=this.uiConfig.controls[t])||void 0===e?void 0:e.dscode;return this.gridController.clearFilter(i)},getGridFilterExpress:function(t){var e=this.uiConfig.controls[t].dscode,i=[],s=[];this.isLookup&&this.lookup&&this.lookup.filter&&this.lookup.filter.length&&(i=this.lookup.filter,i[i.length-1]["Logic"]=i[i.length-1]["Logic"]||"and");var n=this.gridController.getGridFilter(this.uiConfig.controls[t].filter);s=s.concat(this.getFilterDataSource(e)),s.length>0&&(s[s.length-1].Logic="",n.length>0&&(n[n.length-1].Logic="and")),i=i.concat(n).concat(s),this.isLookup&&this.lookup&&this.lookup.ignoreCase&&i.length&&i.forEach((function(t){t.IgnoreCase=!0}));var o=this.event.emit("beforeGridFilter","grid",t,[i]);return o||i},getFilterDataSource:function(t){return this.gridController.getFilterDataSource(t)},setGridFilter:function(t,e){this.$refs[t].filter=e},scrollTab:function(t,e){var i,s=this.container[t].childs.find((function(t,s){if(t.id==e)return i=s,!0}));this.$refs[t][0].scrollTo(i),this.changeTab(i,s.label)},loadUIComponent:function(){var t=this,e=this.styleId,i={styleId:this.styleId,formType:"2",ControlId:"",fields:[]},s=[],n=!1,o=C["a"].hasServerSelectStore(e);o&&s.push(j.getColSetData(i)),C["a"].getListConfig(e)&&(n=!0),n||s.push(j.getListConfig(e)),Promise.all(s).then((function(i){if(o){var s=i[0].Data;C["a"].setSelectOpitons(e,s)}var a=n?C["a"].getListConfig(e):i[o?1:0].Data;C["a"].setListConfig(e,a),t.version=C["a"].getVersion(e),t.listConfig=a;var l=y["a"].getMobiConfig("list",C["a"].getFormInfo(e));t.page=l.config,t.page.NavbarTmpl&&(t.navbarRCmp=JSON.stringify(t.page.NavbarTmpl)),t.code=l.code||e,t.customCode=l.customCode,t.uiConfig=l;var c=l.basic.dsList;if(c)for(var h in c)t.hashDs[c[h].info.SqlId]=c[h];t.gridController=new x["c"](t),t.custom=new x["a"],t.store=new S["a"],t.from=H,t.initPopups(),t.pageTitle=t.page.title,t.initLangResources(),t.regEvents(),t.activeResurces(),t.uistate="loaded",document.title=t.pageTitle,console.log(t.container),r["a"].clear(),console.log(t.$refs),t.ctx={event:t.event,params:t.params,express:t.express,control:t.control,data:t.dataManager,modelController:t.modelController,store:t.store,langResources:t.langResources}}))},initView:function(t){var e=this;this.initLang(),this.initTabsInfo(),this.popupConfig=this.uiConfig.popups,this.$nextTick((function(){var i=0,s=!1;for(var n in e.container){if(!e.container[n].fixed){e.scrollRef=e.$refs.refCols[i],s=!0;break}i++}s||(e.scrollRef=e.$refs.refLayout.parentNode),t&&t()}))},deleteData:function(t){var e=this,i=this,s={id:t,modId:"",styleId:this.styleId,treeParentId:""};j.deleteData(s).then((function(t){e.loaded(),"ok"==t.Code?(console.log(t),e.event.emitGlobal("afterRowDelete",[s]),e.gridController.refresh(),q({type:"success",message:i.getLang("list_deleteCompleted")})):"lockdata"==t.Code?r["a"].fail(i.getLang("err_lockdata",{0:t.Message})):"wfCheck"==t.Code?r["a"].fail(i.getLang("err_wfcheck")):r["a"].fail(t.Message)}))},editCard:function(t){var e=this.listConfig["KeyCol"],i=t[e];this.openCard(i,"edit")},editCardWithLock:function(t){var e=this.listConfig["KeyCol"],i=t[e];this.openCard(i,"editwithlock")},viewCard:function(t){var e=this.listConfig["KeyCol"],i=t[e];this.openCard(i,"view")},addCard:function(){this.openCard("","add")},openCard:function(t,e,i,s,n){n=n||"",i=i||this.styleId,t=t||"",e=e||"";var o=s||{};o.type="refresh",o.status=e,this.$route.query&&this.$route.query.wfviewlist&&(n+="&wfviewpagedeep=true");var a={dataId:t,status:e};n&&n.split("&").filter((function(t){return t})).forEach((function(t){a[t.split("=")[0]]=t.split("=")[1]})),this.$router.push({path:"/"+i+"/card?dataId="+t+"&status="+e+n,params:o})},openWfViewCard:function(t,e,i){var s={};if(e&&i){i.forEach((function(t){s[t.code]=t.value})),s.taskId=t,s.task=e,s.dataId=s.dataid,s.taskCenterSSO=6;var n=s.styleid;"true"==s.runtime&&(n=s.styleid+"~"+s.fdim+"~"+s.sdim+"~"+s.ext),this.$router.push({path:"/"+n+"/recorddetail",params:s,query:s})}else if(t){var o={taskId:t,sourceId:null,processInstanceId:null,terminal:"app"},a=this;A["a"].getTaskEntityByPayload(o).then((function(i){e=i;var n=[];e.actions.forEach((function(t){var i=e.taskEntity.actions.find((function(e){return e.code===t.code}));if(i){var s=e.taskEntity.state.toLowerCase();t.scope.indexOf(s)>-1&&n.push(Object.assign({},i,t))}})),n=n.sort((function(t,e){return t.sortOrder>e.sortOrder?1:-1})),n=n.map((function(t){return t.preEvents=e.actionEvents.filter((function(e){return e.actionCode===t.code&&"Pre"===e.type})).sort((function(t,e){return t.sortOrder>e.sortOrder?1:-1})),t.postEvents=e.actionEvents.filter((function(e){return e.actionCode===t.code&&"Post"===e.type})).sort((function(t,e){return t.sortOrder>e.sortOrder?1:-1})),t})),e.taskEntity.actions=n.filter((function(t){return!0!==t.isHyperlinkAction}));var o=e.actions.filter((function(t){return!0===t.isHyperlinkAction}));"wf"==o[0].typeId?A["a"].getAppInfo(e.taskEntity.sourceId).then((function(i){i.parameters.forEach((function(t){s[t.code]=t.value})),s.formUrl=i.url,s.taskId=t,s.task=e.taskEntity,s.dataId=s.dataid,s.taskCenterSSO=6;var n=s.styleid;"true"==s.runtime&&(n=s.styleid+"~"+s.fdim+"~"+s.sdim+"~"+s.ext),a.$router.push({path:"/"+n+"/recorddetail",params:s,query:s})}),(function(t){console.log(t),alert(a.getLang("list_notFoundFormConfig"))})):alert(a.getLang("list_notFoundFormConfig"))}),(function(t){console.log(t),alert(a.getLang("list_notFoundFormConfig"))}))}else alert(this.getLang("list_noTaskId"))},updateForm:function(t,e){this.formItem[t]=e},showMenu:function(t){this.actions=[],this.actions=t,this.actionShow=!0},onActionCancel:function(){},onActionConfirm:function(t,e){var i=this.actions[e],s=i.click;s(this,i)},scrollEvent:function(){console.log("scrolling")},onTabClick:function(t){this.isFavorTab=1==t,console.log(arguments)},initTabsInfo:function(){var t=this,e=Object.keys(this.container).filter((function(e){return t.container[e].id=t.container[e].id||e,"9"==t.container[e].type}));e.forEach((function(e){t.tabsInfo[e]=Object(o["a"])({},t.container[e])}))},setTabHide:function(t,e,i){this.tabsInfo[t].childs.find((function(t){return t.id==e})).hide=i,this.updateCurTabs(t)},updateCurTabs:function(t){this.container[t].childs=[];var e=!0,i=!1,s=void 0;try{for(var n,o=this.tabsInfo[t].childs[Symbol.iterator]();!(e=(n=o.next()).done);e=!0){var a=n.value;a.hide||this.container[t].childs.push(a)}}catch(r){i=!0,s=r}finally{try{e||null==o.return||o.return()}finally{if(i)throw s}}return this.container[t]},submit:function(t,e){var i=this;e=e||this.styleId,this._wfConfigLoaded=this._wfConfigLoaded||{},this._wfConfigLoaded[e]?(this.$refs.refWork.setCurrentRow(t),this.$refs.refWork.getWorkFlow()):this.$refs.refWork.init(e).then((function(s){i._wfConfigLoaded[e]=!0,i.$refs.refWork.setCurrentRow(t),i.$refs.refWork.getWorkFlow()}))},startMul:function(t,e){var i=this;e=e||this.styleId,this._wfConfigLoaded=this._wfConfigLoaded||{},this._wfConfigLoaded[e]?this.$refs.refWork.getMulWorkFlow(t):this.$refs.refWork.init(e).then((function(s){i._wfConfigLoaded[e]=!0,i.$refs.refWork.getMulWorkFlow(t)}))},cancelSubmit:function(t,e){var i=this;e=e||this.styleId,this._wfConfigLoaded=this._wfConfigLoaded||{},this._wfConfigLoaded[e]?this.$refs.refWork.cancelSubmit(t):this.$refs.refWork.init(e).then((function(s){i._wfConfigLoaded[e]=!0,i.$refs.refWork.cancelSubmit(t)}))},checkedLength:function(){return this.isFavorTab?this.lookup.checkedFavorRows.length:this.lookup.checkedRows.length}},watch:{floatBtn:{deep:!0,handler:function(t){"float"==t.type&&this.updateCurFloatBtn()}},pageTitle:{immediate:!0,handler:function(t){window.document.title=t}}}},G=W,J=(i("97d5"),Object(R["a"])(G,s,n,!1,null,null,null));e["default"]=J.exports},"6c75":function(t,e,i){"use strict";var s=i("4c60"),n=i.n(s);n.a},"725d":function(t,e,i){},7421:function(t,e,i){},"74a5":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("span",[t.showLink||t.note?i("span",{on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.stopClick(e)},touch:function(e){return e.stopPropagation(),e.preventDefault(),t.stopClick(e)}}},[t.note?i("van-popover",{ref:"notePop",attrs:{trigger:"click",theme:"dark",offset:[-8,8],placement:t.getPlaceMment},scopedSlots:t._u([{key:"reference",fn:function(){return[i("van-icon",{staticStyle:{color:"#000","margin-right":"4px"},attrs:{name:"question-o",size:"16"}}),t.showLink?i("span",[i("a",{attrs:{href:"javascript:void(0)"},domProps:{innerHTML:t._s(t.label)},on:{click:t.handleLinkClick}})]):t._e(),t.showLink?t._e():i("span",{domProps:{innerHTML:t._s(t.label)}})]},proxy:!0}],null,!1,2940743508),model:{value:t.showPopover,callback:function(e){t.showPopover=e},expression:"showPopover"}},[i("div",[i("div",{staticClass:"f-note-show"},[t._v(" "+t._s(t.note)+" ")])])]):t._e(),t.showLink&&!t.note?i("span",[i("a",{attrs:{href:"javascript:void(0)"},domProps:{innerHTML:t._s(t.label)},on:{click:t.handleLinkClick}})]):t._e()],1):i("span",{domProps:{innerHTML:t._s(t.label)}})])},n=[],o={name:"popLabel",props:["note","showLink","label"],data:function(){return{showPopover:!1,getPlaceMment:"top-start"}},updated:function(){if(this.$refs.notePop&&this.$refs.notePop.$el){var t=this.$refs.notePop.$el.getBoundingClientRect().top,e=window.innerHeight;this.getPlaceMment=t/e<1/3?"bottom-start":"top-start"}},methods:{handleLinkClick:function(){this.$emit("linkclick")},stopClick:function(){}}},a=o,r=i("2877"),l=Object(r["a"])(a,s,n,!1,null,null,null);e["a"]=l.exports},"75bd":function(t,e,i){"use strict";var s=i("b808"),n=i.n(s);n.a},"777d":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{attrs:{title:t.pageTitle,tab:"true"},on:{backClick:t.backClick}},[i("div",{staticClass:"f-flex-wrap",attrs:{slot:"content"},slot:"content"},t._l(t.groups,(function(e,s){return i("div",{key:s},[i("div",{staticClass:"f-grid-title"},[t._v(t._s(s))]),i("van-grid",{staticClass:"f-grid-menu",attrs:{square:"",border:!1,clickable:""}},t._l(e,(function(e,s){return i("van-grid-item",{key:s,attrs:{icon:"photo-o",text:e[t.titleField]},on:{click:function(i){return t.onClick(e,s)}}},[e[t.srcField]?i("van-image",{attrs:{src:e[t.srcField]}}):t._e(),e[t.srcField]?i("div",{staticClass:"f-grid-menu-title",staticStyle:{"margin-top":"5px"}},[t._v(t._s(e[t.titleField]))]):t._e()],1)})),1)],1)})),0),i("div",{attrs:{slot:"tab"},slot:"tab"},[i("van-tabbar",{staticClass:"f-wechat-tab",attrs:{route:""},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[i("van-tabbar-item",{attrs:{icon:"home-o",to:"/wechat"}},[t._v("首页")]),i("div",{staticClass:"f-tab-scan"},[i("div",{staticClass:"f-round-corner"}),i("div",{staticClass:"f-scan-button",on:{click:function(e){return t.scanQRCode()}}},[i("van-icon",{attrs:{name:"scan"}})],1)]),i("div",{staticClass:"f-scan-title"},[t._v("扫一扫")]),i("van-tabbar-item",{attrs:{icon:"setting",to:"/guide"}},[t._v("应用")])],1)],1)])},n=[],o=(i("4de4"),i("c975"),i("4e82"),i("ac1f"),i("5319"),i("1276"),i("d399")),a=i("f564"),r=i("c968"),l=i("2e27"),c=i("365c"),h=(i("d8ad"),i("4901")),d={components:{FarrisBpage:r["a"],GridMenu:l["a"]},data:function(){return{pageTitle:"GS Cloud 企业数字化平台",titleField:"Name",srcField:"Icon",groups:{}}},beforeRouteLeave:function(t,e,i){i()},activated:function(){document.title="GS Cloud 企业数字化平台"},mounted:function(){this.redirect(),document.title="GS Cloud 企业数字化平台",this.isWeiXin()&&h["a"].initConfig(),window.idpViewReady&&window.idpViewReady()},methods:{isWeiXin:function(){var t=navigator.userAgent.toLowerCase();return-1!=t.indexOf("micromessenger")},scanQRCode:function(){window.WX_APP_Bridge&&window.WX_APP_Bridge.appScan&&window.WX_APP_Bridge.appScan()},onClick:function(t,e){if(-1!=t.Path.indexOf("idp:")){var i=t.Path.replace("idp:",""),s=i.split("/"),n=s[1].split("?"),o=s[1],a={};if(2==n.length){o=n[0];var r=n[1].split("&");for(var l in r){var c=r[l].split("=");a[c[0]]=c[1]}}this.openPage(s[0],o,a)}else-1!=t.Path.indexOf("url:")?window.location.href=t.Path.replace("url:",""):-1!=t.Path.indexOf("iframe:")&&this.openUrl(t.Name,t.Path.replace("iframe:",""))},formatData:function(t){t.sort((function(t,e){return t.SortOrder<e.SortOrder?-1:t.SortOrder>e.SortOrder?1:0}));var e=t.filter((function(t,e){return 1==t.Layer})),i={};for(var s in e){var n=e[s].Name;i[n]=this.getChildren(t,e[s].Id)}this.groups=i},openUrl:function(t,e){this.$router.push({name:"iframe",path:"/iframe",params:{src:e,title:t}})},openPage:function(t,e,i,s){this.$router.push({name:t+e,path:"/"+t+"/"+e,params:s,query:i})},getChildren:function(t,e){return t.filter((function(t,i){return t.ParentId==e}))},redirect:function(){o["a"].loading({duration:2e3,message:"加载中...",forbidClick:!0});var t=this;c["b"].getMobileFunc().then((function(e){if(o["a"].clear(),"ok"==e.Code){var i=e.Data;t.formatData(i)}else Object(a["a"])({type:"danger",message:e.Data.Message})}))},openCard:function(t,e,i,s,n){i=i||this.styleId,t=t||"",e=e||"";var o=s||{};o.type="refresh",this.$router.replace({path:"/"+i+"/card?dataId="+t+"&status="+e+n,params:o})},backClick:function(){window.backClick&&window.backClick()}}},u=d,f=(i("eec9"),i("2877")),p=Object(f["a"])(u,s,n,!1,null,null,null);e["default"]=p.exports},"77bf":function(t,e,i){"use strict";var s=i("b9a9"),n=i.n(s);n.a},"7a20":function(t,e,i){"use strict";var s=i("44de"),n=i.n(s);n.a},"7b83":function(t,e,i){},"7c55":function(t,e,i){"use strict";var s=i("2395"),n=i.n(s);n.a},"7f4f":function(t,e,i){},8006:function(t,e,i){},"81d6":function(t,e,i){},"83e9":function(t,e,i){},"84c7":function(t,e,i){"use strict";var s=i("cbd9"),n=i.n(s);n.a},"85d9":function(t,e,i){"use strict";var s=i("379c"),n=i.n(s);n.a},"883a":function(t,e,i){"use strict";var s=i("9740"),n=i.n(s);n.a},"89ef":function(t,e,i){},"8afe":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("li",{staticClass:"treeList"},[i("div",[t.isList?t._e():i("van-cell",{style:t.style,attrs:{center:"",clickable:""},on:{click:function(e){return t.treeNodeClick(t.node)}},scopedSlots:t._u([{key:"title",fn:function(){return[t.ttComp?i(t.ttComp,{tag:"component",attrs:{row:t.node}}):t._e(),t.ttComp||t.noCode?t._e():[i("div",{staticClass:"f-tree-label"},[t._v(t._s(t.node[t.ttField]))])]]},proxy:!0},{key:"label",fn:function(){return[t.ttComp?t._e():[i("div",{staticClass:"f-tree-label"},[t._v(t._s(t.node[t.stField]))])]]},proxy:!0},{key:"right-icon",fn:function(){return[t.node.close&&t.showLink?i("van-icon",{staticClass:"f-tree-link f-tree-open",staticStyle:{"line-height":"inherit"},attrs:{name:"arrow"},on:{click:function(e){return e.stopPropagation(),t.changeStatus(t.node)}}}):t._e(),!t.node.close&&t.showLink?i("van-icon",{staticClass:"f-tree-link f-tree-close",staticStyle:{"line-height":"inherit"},attrs:{name:"arrow-down"},on:{click:function(e){return e.stopPropagation(),t.changeStatus(t.node)}}}):t._e(),t.showLink?t._e():i("div",{staticClass:"f-tree-link"})]},proxy:!0},t.favor||t.isMul?{key:"icon",fn:function(){return[i("div",[!t.isMul||!t.node.hasAuth&&t.isTotal||!t.isDetail&&t.childOnly?t._e():i("checkbox",{staticStyle:{position:"absolute",left:"10px",bottom:"40%"},attrs:{value:t.node.isChoose},on:{input:function(e){return t.onCheckRow(t.node)}}}),!t.node.hasAuth&&t.isTotal&&t.isMul?i("van-radio",{staticStyle:{position:"absolute",left:"10px",bottom:"40%"},attrs:{name:"1",disabled:"",shape:"square","icon-size":"16px"}}):t._e(),!t.node.isFavor&&t.favor?i("van-icon",{staticClass:"f-favor-icon",attrs:{name:"star-o",color:t.getColor()},on:{click:function(e){return e.stopPropagation(),t.favorClick(t.node)}}}):t._e(),t.node.isFavor&&t.favor?i("van-icon",{staticClass:"f-favor-icon",attrs:{name:"star"},on:{click:function(e){return e.stopPropagation(),t.favorClick(t.node)}}}):t._e()],1)]},proxy:!0}:null],null,!0)}),t.isList?i("dylistitem",{style:t.style,attrs:{favor:t.favor,enableIcon:!!t.fields.icon,isMul:t.isMul,isLink:!0,row:t.node,totalComp:t.totalComp,contentComp:t.contentComp,stComp:t.stComp,ttComp:t.ttComp,iconComp:t.iconComp,labelComp:t.labelComp,subtitle:t.node[t.fields.subtitle],title:t.node[t.fields.title],label:t.node[t.fields.label],content:t.node[t.fields.content],icon:t.node[t.fields.icon],rowIndex:t.index,isTree:t.isTree,level:t.level,isDetail:t.isDetail,childOnly:t.childOnly},on:{favorClick:function(e){return t.favorClick(t.node)},click:function(e){return t.clickListNode(t.node)},checkRow:function(e){return t.checkListNode(t.node)}}},[[t.node.close&&t.showLink?i("van-icon",{staticClass:"f-tree-link f-tree-open",staticStyle:{"line-height":"inherit"},attrs:{name:"arrow"},on:{click:function(e){return e.stopPropagation(),t.changeStatus(t.node)}}}):t._e(),!t.node.close&&t.showLink?i("van-icon",{staticClass:"f-tree-link f-tree-close",staticStyle:{"line-height":"inherit"},attrs:{name:"arrow-down"},on:{click:function(e){return e.stopPropagation(),t.changeStatus(t.node)}}}):t._e(),t.showLink?t._e():i("div",{staticClass:"f-tree-link"})]],2):t._e()],1),t._l(t.node.children,(function(e,s){return i("tree-node",{directives:[{name:"show",rawName:"v-show",value:!t.node.close,expression:"!node.close"}],key:s,ref:"childNode",refInFor:!0,attrs:{ttComp:t.ttComp,favor:t.favor,stField:t.stField,ttField:t.ttField,level:t.level+1,node:e,autoFold:t.autoFold,userJS:t.userJS,isMul:t.isMul,noCode:t.noCode,helpInput:t.helpInput,idField:t.idField,isTotal:t.isTotal,autoChild:t.autoChild,selectMemory:t.selectMemory,isList:t.isList,fields:t.fields,totalComp:t.totalComp,contentComp:t.contentComp,stComp:t.stComp,iconComp:t.iconComp,labelComp:t.labelComp,treeConfig:t.treeConfig,childOnly:t.childOnly},on:{favorClick:t.favorClick,beforeExpand:t.beforeExpand,treeNodeClick:t.childNodeClick,getParentNode:t.getParentNode}})}))],2)},n=[],o=(i("a623"),i("4de4"),i("4160"),i("a434"),i("159b"),i("d761")),a=i("b982"),r={data:function(){return{style:{paddingLeft:"",borderBottom:"1px solid #ebedf0",background:"#ffffff"},vals:[],ignoreChange:!1,isTree:!0,simple:!1}},props:["node","ids","level","ttField","favor","stField","autoFold","userJS","ttComp","isMul","noCode","helpInput","idField","isTotal","autoChild","selectMemory","treeConfig","isList","fields","stComp","totalComp","contentComp","labelComp","iconComp","childOnly"],mounted:function(){this.node.hasAync||(this.autoFold&&this.level>=this.userJS?this.$set(this.node,"close",!0):this.$set(this.node,"close",!1)),!this.node.hasAuth&&this.isTotal&&(this.style.background="#d3d3d53d"),this.$set(this.node,"isChecked",!1),this.$set(this.node,"ignoreChange",!1),this.isMul&&this.selectMemory&&this.selectMemory[this.node[this.treeConfig.id]]?this.$set(this.node,"isChoose",!0):this.$set(this.node,"isChoose",!1),this.node.isAsync&&this.autoChild&&this.getParent()&&this.$set(this.node,"isChoose",this.getParent().isChoose),this.treeConfig&&(this.isDetail="0"!=this.node[this.treeConfig.isdetail]&&!0)},methods:{treeNodeClick:function(t,e){void 0!=e&&e==!!t.isChoose||(this.isMul&&!this.isDetail&&this.childOnly?this.changeStatus(t):this.$emit("treeNodeClick",t))},simpleCheck:function(t){this.node.isChoose!=t&&(this.simple=!0,this.node.isChoose=t)},onCheckAll:function(t){this.childOnly&&1!=this.node[this.treeConfig.isdetail]||this.simpleCheck(t),this.$refs.childNode&&this.$refs.childNode.length&&this.$refs.childNode.forEach((function(e){e.onCheckAll(t)}))},isCheckedAll:function(){if(!this.node.isChoose&&(!this.childOnly||1==this.node[this.treeConfig.isdetail]))return!1;for(var t=0;t<(this.$refs.childNode?this.$refs.childNode.length:0);t++)if(!this.$refs.childNode[t].isCheckedAll())return!1;return!0},clickListNode:function(t){this.isMul||this.treeNodeClick(t)},checkListNode:function(t){this.isMul&&this.treeNodeClick(t)},childNodeClick:function(t){this.$emit("treeNodeClick",t)},favorClick:function(t){this.$emit("favorClick",t)},beforeExpand:function(t){this.$emit("beforeExpand",t)},checkValue:function(t){console.log(t)},changeStatus:function(t){t&&(t.close?(t.close=!1,this.$emit("beforeExpand",t)):(t.close=!0,this.$emit("beforeCollapse",t)))},setChildrensRole:function(t,e){var i=this;t.children&&t.children.length>0&&t.children.forEach((function(t,s){t.isChecked=e,t.children&&i.setChildrensRole(t,e)}))},nodeClick:function(t){t.isChecked=!t.isChecked,this.setChildrensRole(t,t.isChecked)},onCheckRow:function(t){this.$emit("treeNodeClick",t)},getColor:function(){return!this.node.hasAuth&&this.isTotal?"#ebedf0":"rgb(255, 193, 7)"},getParentNode:function(t){t(this.node)},getParent:function(){var t;return this.$emit("getParentNode",(function(e){t=e})),t},checkParent:function(t){var e=t.isChoose,i=t.children.every((function(t){return t.isChoose}));t.isChoose=!!i,e==t.isChoose&&(this.$emit("autoCheck",t),t.checkParent=!1)}},watch:{node:function(){this.$set(this.node,"isChecked",!1)},"node.isChoose":function(t){if(this.simple)this.simple=!1;else{var e=this,i=null;if(t){if(this.autoChild&&this.node.children&&this.node.children.length){var s=this.node.children.every((function(t){return t.isChoose})),n=this.node.children.every((function(t){return!t.isChoose}));if(!s&&!n)return this.node.isChoose=!1,void(this.node.isChecked=!1)}if(void 0!==this.$route.params.helpData){var o=this.$route.params.helpData.filter((function(t){return t[e.treeConfig.id]==e.node[e.treeConfig.id]}));0===o.length&&this.$route.params.helpData.push(this.node)}}else if(!t){for(var a in this.$route.params.helpData)this.$route.params.helpData[a][this.treeConfig.id]==this.node[this.treeConfig.id]&&(i=a);i&&this.$route.params.helpData.splice(i,1)}if(this.autoChild){if(this.node.children&&!this.node.checkParent)for(var r in this.node.children)this.node.children[r].checkChild=!0,this.node.children[r].isChoose=t,this.$emit("autoCheck",this.node.children[r]);this.getParent()&&!this.node.checkChild&&t!=this.getParent().isChoose&&(this.getParent().checkParent=!0,this.checkParent(this.getParent()))}this.node.checkParent=!1,this.node.checkChild=!1,this.node.isChecked=t}}},computed:{showLink:function(){return this.node.children&&this.node.children.length>0||this.node.hasAync}},created:function(){this.level||(this.level=1),this.isMul&&!this.isList?this.style.paddingLeft=20*(this.level-1)+31+"px":this.style.paddingLeft=20*(this.level-1)+10+"px"},components:{TreeNode:function(t){return Promise.resolve().then(function(){var e=[i("8afe")];t.apply(null,e)}.bind(this)).catch(i.oe)},checkbox:o["a"],dylistitem:a["a"]}},l=r,c=(i("eb4b"),i("2877")),h=Object(c["a"])(l,s,n,!1,null,null,null);e["default"]=h.exports},"8b69":function(t,e,i){"use strict";var s=i("81d6"),n=i.n(s);n.a},"8c1b":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("drag",{attrs:{icon:t.icon},on:{click:t.openAction}}),i("van-action-sheet",{attrs:{round:!1,actions:t.actions,"cancel-text":t.getLang("actions_cancel"),"close-on-click-action":""},on:{cancel:t.onCancel,select:t.onSelect},model:{value:t.actionShow,callback:function(e){t.actionShow=e},expression:"actionShow"}})],1)},n=[],o=i("d8ad"),a=i("20a8"),r={name:"actionMenu",props:["actions","styleId"],components:{drag:a["a"]},inject:["viewTag"],data:function(){return{actionShow:!1,icon:""}},mounted:function(){1==this.actions.length?this.icon=this.actions[0].icon:this.icon="plus"},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},onCancel:function(t){console.log(arguments)},onSelect:function(t,e){o["a"].$emit("component-change","click","floatbtn","floatbtn",[t,e],this.styleId,this.viewTag?this.viewTag():void 0),this.toggle(),console.log(arguments)},openAction:function(){1!=this.actions.length?this.actionShow=!0:this.onSelect(this.actions[0],0)},toggle:function(){this.open=!this.open,this.toggleAnimate=!this.toggleAnimate,this.MaskToggle=!this.MaskToggle}},computed:{}},l=r,c=i("2877"),h=Object(c["a"])(l,s,n,!1,null,null,null);e["a"]=h.exports},9145:function(t,e,i){},9188:function(t,e,i){},"93ae":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"fm-page"},[i("div",{staticClass:"f-wrap-fm-page",staticStyle:{flex:"1"}},[i("card-view",{attrs:{viewTag:"recorddetail"}})],1),i("div",{staticClass:"fm-page-footer",staticStyle:{position:"absolute",width:"100%",bottom:"0"}},[i("div",{staticClass:"fm-cmp-actionbar"},[t.approveBtns.length>0&&t.btnlength<=t.threshold?i("div",{staticClass:"fm-cmp-actionbar-container",staticStyle:{height:"54px"},style:t.actionFlex},t._l(t.approveBtns,(function(e){return i("div",{key:e.id,staticStyle:{flex:"1","text-align":"center",position:"relative"}},["Pass"==e.id||"pass"==e.id||"Resubmit"==e.id?i("div",[i("button",{staticClass:"wf-btn wf-btn-text-info pass-bar",attrs:{type:"button"},on:{click:function(i){!e.submitDisable&&t.actionbarItemClick2Content(i,e.id)}}},[t._v(" "+t._s(e.text)+" ")])]):t._e(),"Back"==e.id||"back"==e.id||"Obsolete"==e.id?i("div",[i("button",{staticClass:"wf-btn wf-btn-text-info back-bar",attrs:{type:"button"},on:{click:function(i){!e.submitDisable&&t.actionbarItemClick2Content(i,e.id)}}},[t._v(" "+t._s(e.text)+" ")])]):t._e(),"Pass"!=e.id&&"pass"!=e.id&&"Back"!=e.id&&"back"!=e.id&&"otherAction"!=e.id&&"Resubmit"!=e.id&&"Obsolete"!=e.id?i("div",{staticClass:"actionbar-container--item",on:{click:function(i){!e.submitDisable&&t.actionbarItemClick2Content(i,e.id)}}},[e.icon?i("span",{staticClass:"fm-icon actionbar-container--icon",class:e.icon},[e.tcount?i("div",{staticClass:"wf-transverse-badge"},[t._v(t._s(e.tcount))]):t._e()]):t._e(),i("span",{staticClass:"actionbar-container--text",class:e.id,staticStyle:{"margin-top":"4px"}},[t._v(t._s(e.text))])]):t._e()])})),0):t._e(),t.btnlength>t.threshold?i("div",{staticClass:"fm-cmp-actionbar-container",staticStyle:{height:"54px"},style:t.actionFlex},[t._l(t.hBtns,(function(e){return i("div",{key:e.id,staticStyle:{flex:"1","text-align":"center",position:"relative"}},["Pass"==e.id||"pass"==e.id||"Resubmit"==e.id?i("div",[i("button",{staticClass:"wf-btn wf-btn-text-info pass-bar",attrs:{type:"button"},on:{click:function(i){!e.submitDisable&&t.actionbarItemClick2Content(i,e.id)}}},[t._v(" "+t._s(e.text)+" ")])]):t._e(),"Back"==e.id||"back"==e.id||"Obsolete"==e.id?i("div",[i("button",{staticClass:"wf-btn wf-btn-text-info back-bar",attrs:{type:"button"},on:{click:function(i){!e.submitDisable&&t.actionbarItemClick2Content(i,e.id)}}},[t._v(" "+t._s(e.text)+" ")])]):t._e(),"otherAction"==e.id&&t.isUseActionView?i("div",{staticClass:"actionbar-container--item",on:{click:function(e){return t.showOrHideDropdownHandler()}}},[i("span",{staticClass:"fm-icon actionbar-container--icon wf-icon-more"}),i("span",{staticClass:"actionbar-container--text",staticStyle:{"margin-top":"4px"}},[t._v(t._s(t.getLang("more")))])]):t._e(),"Pass"!=e.id&&"pass"!=e.id&&"Back"!=e.id&&"back"!=e.id&&"otherAction"!=e.id&&"Resubmit"!=e.id&&"Obsolete"!=e.id?i("div",{staticClass:"actionbar-container--item",on:{click:function(i){!e.submitDisable&&t.actionbarItemClick2Content(i,e.id)}}},[e.icon?i("span",{staticClass:"fm-icon actionbar-container--icon",class:e.icon},[e.tcount?i("div",{staticClass:"wf-transverse-badge"},[t._v(t._s(e.tcount))]):t._e()]):t._e(),i("span",{staticClass:"actionbar-container--text",class:e.id,staticStyle:{"margin-top":"4px"}},[t._v(t._s(e.text))])]):t._e()])})),t.isUseActionView?t._e():i("div",{staticClass:"actionbar-container--item",on:{click:function(e){return t.showOrHideDropdownHandler()}}},[i("span",{staticClass:"fm-icon  wf-icon-more actionbar-container--icon"}),i("span",{staticClass:"actionbar-container--text",staticStyle:{"margin-top":"4px"}},[t._v(t._s(t.getLang("more")))])]),t.showDropdown?i("div",[i("div",{staticClass:"wf-actionbar-container--dropdown",style:t.dropdownPosition},t._l(t.vBtns,(function(e){return i("div",{key:e.id,staticClass:"actionbar-container--item-left",class:{"actionbar-container--item-noclick":e.submitDisable,Pass:"pass"==e.id||"Pass"==e.id},on:{click:function(i){return t.actionbarItemClick2Content(i,e.id)}}},[e.icon?i("span",{staticClass:"fm-icon actionbar-container--icon",class:e.icon,staticStyle:{margin:"0 12px 0 0","font-size":"15px",color:"#606467"}}):t._e(),i("span",{staticClass:"actionbar-container--text"},[t._v(t._s(e.text))])])})),0)]):t._e()],2):t._e()])])])},n=[],o=(i("4de4"),i("c740"),i("4160"),i("d81d"),i("fb6a"),i("4e82"),i("a434"),i("159b"),i("e082")),a=i("dfd8"),r={components:{CardView:a["default"]},data:function(){return{taskEntity:[],actions:null,events:null,formUrl:null,approveBtns:[],payload:{taskId:null,sourceId:null,processInstanceId:null,terminal:"app"},task:null,showDropdown:!1,btnlength:0,hBtns:[],vBtns:[],threshold:4,isUseActionView:!1,actionFlex:{},dropdownPosition:{}}},mounted:function(){console.log(this.$route),window["closeActionView"]=function(){document.getElementById("wf-modal-window").classList.remove("fm-state-open"),document.getElementById("wf-modal-window").innerHTML=""}},activated:function(){this.$route.query.back||this.initTask()},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},initTask:function(){this.payload.taskId=this.$route.query.taskId,this.formUrl=this.$route.query.formUrl,this.taskEntity=this.$route.query.task,this.actions=this.taskEntity.actions,this.events=this.taskEntity.actionEvents,this.getAppTaskConfig()},getTaskEntityByPayload:function(){var t=this;this.payload.taskId=this.$route.query.taskId,o["a"].getTaskEntityByPayload(this.payload).then((function(e){t.taskEntity=e.taskEntity,t.actions=e.actions,t.events=e.actionEvents,t.initByTaskEntity(),t.getAppTaskConfig()}),(function(t){console.log(t)}))},initByTaskEntity:function(){var t=this.actions.filter((function(t){return!0===t.isHyperlinkAction}));"wf"==t[0].typeId&&this.getFormUrl(t[0])},getFormUrl:function(t){var e=this;"wf"==t.typeId&&o["a"].getAppInfo(this.taskEntity.sourceId).then((function(t){e.formUrl=t.data.url,console.log(e.formUrl)}),(function(t){console.log(t)}))},getAppTaskConfig:function(){var t=this,e=this.taskEntity.actions.map((function(e){if("Focus"==e.code)return null;var i=t.actions.filter((function(t){return t.code===e.code&&!0!==t.isHyperlinkAction}));return i&&0!=i.length?{id:e.code,text:t.actions.filter((function(t){return t.code===e.code}))[0].name,icon:o["a"].getIcon(e.code),submitDisable:!1,sortOrder:null===i[0].sortOrder?999:i[0].sortOrder,actionId:e.id}:null}));e=e.filter((function(t){return null!==t})).sort((function(t,e){return t.sortOrder>e.sortOrder?1:t.sortOrder==e.sortOrder&&"Pass"==e.id?1:-1})),this.setAction(e),console.log(this.approveBtns)},setAction:function(t){var e=t.findIndex((function(t){return"Back"==t.id||"back"==t.id||"Obsolete"==t.id}));if(e>-1){var i=t[e];t.splice(e,1),t.unshift(i),this.isUseActionView=!0}var s=t.findIndex((function(t){return"Pass"==t.id||"pass"==t.id||"Resubmit"==t.id}));if(s>-1){var n=t[s];t.splice(s,1),t.unshift(n),this.isUseActionView=!0}this.isUseActionView?(this.actionFlex={"flex-direction":"row-reverse"},this.dropdownPosition={left:"8px"}):this.dropdownPosition={right:"12px",left:"auto"},this.approveBtns=t,this.btnlength=this.approveBtns.length,this.btnlength>this.threshold?(this.hBtns=this.approveBtns.slice(0,3),this.isUseActionView&&this.hBtns.splice(2,0,{id:"otherAction"}),this.vBtns=this.approveBtns.slice(3)):(this.hBtns=[],this.vBtns=[]);var a=this;o["a"].getExtendActionConfig(this.approveBtns.map((function(t){return t.actionId})),this.taskEntity).then((function(t){var e=t;e&&e.length&&(e.forEach((function(t){var e=a.approveBtns.findIndex((function(e){return e.actionId==t.actionId}));e>-1&&(a.approveBtns[e].tcount=t.tcount)})),a.$forceUpdate())}))},showOrHideDropdownHandler:function(){this.showDropdown=!this.showDropdown},actionbarItemClick:function(t,e){t.stopPropagation();var i=this.actions.filter((function(t){return t.code==e}));window["actionConfig"]=i[0],window["taskEntity"]=this.taskEntity;var s=document.getElementById("wf-modal-window"),n="/platform/runtime/wf/webapp/mobiletaskcenter/index.html#/actionview?action=".concat(e),o='<iframe style="width: 100%;height: 100%;border-width: 0" src="'+n+'"></iframe>';s.innerHTML=o,document.getElementById("wf-modal-window").classList.add("fm-state-open")},actionbarItemClick2Content:function(t,e){var i=this;window["WfActionBarItemClickExtendFunc"]?window["WfActionBarItemClickExtendFunc"](t,e,this.payload,this.taskEntity,this.actions,this.events).then((function(s){s||i.action2Content(t,e)})):this.action2Content(t,e)},action2Content:function(t,e){var i={event:t,btnid:e,taskEntity:this.taskEntity,actions:this.actions,taskCenterSSO:6};this.$router.push({name:"actioncontent",path:"/actioncontent",params:i,query:i})},goback:function(){history.go(-1)}}},l=r,c=(i("b1ca"),i("2877")),h=Object(c["a"])(l,s,n,!1,null,null,null);e["default"]=h.exports},"93f5":function(t,e,i){"use strict";var s=i("568f"),n=i.n(s);n.a},9579:function(t,e,i){},9740:function(t,e,i){},"97d5":function(t,e,i){"use strict";var s=i("acb6"),n=i.n(s);n.a},9820:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{attrs:{title:t.pageTitle}},[i("div",{staticClass:"f-list-wrap",attrs:{slot:"content"},slot:"content"},[i("van-search",{attrs:{placeholder:"请输入搜索关键词"},on:{input:t.onSearch},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),i("div",{staticClass:"f-list-warp-main"},[i("treelist",{ref:"treeList",attrs:{nodes:t.data},model:{value:t.ids,callback:function(e){t.ids=e},expression:"ids"}})],1)],1)])},n=[],o=i("1ba7"),a=i("c968"),r=(i("62e4"),{components:{treelist:o["a"],FarrisBpage:a["a"]},props:{},data:function(){return{ids:[],data:[{id:"01",name:"浪潮",close:!1,isChecked:!1,children:[{id:"0101",name:"浪潮国际",close:!1,isChecked:!1,children:[{id:"010101",name:"GS产品部",isChecked:!1},{id:"010102",name:"PS产品部",isChecked:!1},{id:"010101",name:"GS产品部",isChecked:!1},{id:"010102",name:"PS产品部",isChecked:!1},{id:"010101",name:"GS产品部",isChecked:!1},{id:"010102",name:"PS产品部",isChecked:!1},{id:"010101",name:"GS产品部",isChecked:!1},{id:"010102",name:"PS产品部",isChecked:!1}]},{id:"0102",name:"浪潮信息",close:!1,isChecked:!1,children:[{id:"010101",name:"GS产品部",isChecked:!1},{id:"010102",name:"PS产品部",isChecked:!1},{id:"010101",name:"GS产品部",isChecked:!1},{id:"010102",name:"PS产品部",isChecked:!1},{id:"010101",name:"GS产品部",isChecked:!1},{id:"010102",name:"PS产品部",isChecked:!1},{id:"010101",name:"GS产品部",isChecked:!1},{id:"010102",name:"PS产品部",isChecked:!1}]}]}],pageTitle:""}},activated:function(){},mounted:function(){},methods:{}}),l=r,c=(i("a1d7"),i("2877")),h=Object(c["a"])(l,s,n,!1,null,null,null);e["default"]=h.exports},9964:function(t,e,i){},9976:function(t,e,i){},a1d7:function(t,e,i){"use strict";var s=i("d762"),n=i.n(s);n.a},aa7a:function(t,e,i){"use strict";var s=i("cedb"),n=i.n(s);n.a},abcc:function(t,e,i){},ac5c:function(t,e,i){"use strict";var s=i("9145"),n=i.n(s);n.a},ac5e:function(t,e,i){},acb6:function(t,e,i){},ad4c:function(t,e,i){},ad75:function(t,e,i){"use strict";var s=i("6829"),n=i.n(s);n.a},aded:function(t,e,i){},aed2:function(t,e,i){"use strict";var s=i("8006"),n=i.n(s);n.a},afd6:function(t,e,i){"use strict";var s=i("89ef"),n=i.n(s);n.a},b19d:function(t,e,i){"use strict";var s=i("ad4c"),n=i.n(s);n.a},b1ca:function(t,e,i){"use strict";var s=i("2024"),n=i.n(s);n.a},b616:function(t,e,i){"use strict";var s=i("9188"),n=i.n(s);n.a},b808:function(t,e,i){},b914:function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-checkbox-list",class:t.cls},t._l(t.data,(function(e,s){return i("div",{key:s,staticClass:"f-checkbox-wrap",attrs:{name:e.name}},[i("div",{staticStyle:{"margin-right":"10px"}},[i("div",{staticClass:"f-checkbox-button",class:{checked:1==e.checked,disabled:t.disabled},on:{click:function(i){return i.stopPropagation(),t.checkItem(e,s)}}},[t._v(" "+t._s(e.text)+" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.checked&&t.cls.includes("f-checkbox-newstyle"),expression:"item.checked && cls.includes('f-checkbox-newstyle')"}],staticClass:"f-checkbox-sub"},[i("van-icon",{staticClass:"f-checkbox-icon",attrs:{name:"success"}})],1)])])])})),0)},n=[],o=(i("caad"),i("a9e3"),i("2532"),{data:function(){return{isMul:!1,checkValue:!1}},props:["value","data","disabled"],mounted:function(){this.value,this.initOptions()},computed:{cls:function(){var t="f-checkbox-newstyle";return this.isMul&&(t+=" f-checkbox-multiple"),t}},methods:{initOptions:function(){for(var t in this.value instanceof Array&&(this.isMul=!0),this.hashIndex={},this.data){var e=this.data[t]["name"];this.isMul?this.value.includes(e)?this.data[t].checked=!0:this.data[t].checked=!1:this.value==e?this.data[t].checked=!0:this.data[t].checked=!1}},getValue:function(){var t=[];for(var e in this.data)1==this.data[e]["checked"]&&t.push(this.data[e]["name"]);return this.isMul?t:t[0]},onChangeValue:function(){var t=this.getValue();this.$emit("input",t)},checkItem:function(t,e){if(!this.disabled){if(this.data[e].checked=!t.checked,!this.isMul)for(var i in this.data)Number(e)!=i&&(this.data[i].checked=!1);this.onChangeValue()}}},watch:{value:function(t){this.initOptions()},data:function(t){this.initOptions()}}}),a=o,r=(i("8b69"),i("b616"),i("2877")),l=Object(r["a"])(a,s,n,!1,null,null,null);e["a"]=l.exports},b982:function(t,e,i){"use strict";i("99af"),i("caad"),i("4d63"),i("ac1f"),i("25f0"),i("2532"),i("5319");var s,n,o=i("2909"),a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.userid?i("div",{staticClass:"avatar"},[i("img",{staticStyle:{width:"45px",height:"45px","border-radius":"100%"},attrs:{src:"/api/fastdweb/runtime/v1.0/Common/getUserAvatar?userId="+this.userid},on:{error:t.getDefaultImg}})]):i("div",{staticClass:"f-avatar"},[t._v(" "+t._s(t.title)+" ")])])},r=[],l={name:"vavatar",props:["name","src","title","userid"],components:{},data:function(){return{errorImg:"/platform/runtime/sys/web/assets/img/avatar-default.png"}},mounted:function(){},methods:{getDefaultImg:function(t){var e=t.srcElement;e.src=this.errorImg,e.onerror=null}},watch:{}},c=l,h=(i("84c7"),i("2877")),d=Object(h["a"])(c,a,r,!1,null,"c0cae566",null),u=d.exports,f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i(t.dynamicTemplate(),{tag:"component"})],1)},p=[],m=i("a026"),g={name:"dyhtml",components:{},props:["title"],data:function(){return{}},methods:{dynamicTemplate:function(){return m["a"].compile("<div>12341234{{title}}asdfasdf</div>")}}},v=g,b=Object(h["a"])(v,f,p,!1,null,null,null),w=b.exports,y=i("d761"),C={name:"dylistitem",props:["label","title","subtitle","icon","enableIcon","content","value","rowData","isLink","stComp","totalComp","ttComp","contentComp","labelComp","iconComp","row","remove","favor","isMul","rowIndex","isTree","style","level","isDetail","childOnly","userid","keyword","highlight","highlightStyle"],data:function(){return{tpl:"<b>asdfasdf</b>",tpl1:"{{title}}111",hightitle:"<b>"+this.title+"</b>"}},components:{vavatar:u,checkbox:y["a"],dyhtml:w},methods:{hasContent:function(){return!(!this.stComp&&void 0===this.subtitle&&!this.content)},keywordHighLight:function(){this.highlight&&this.keyword&&this.traverseNodes(this.$el,this.doHighlight)},traverseNodes:function(t,e){if(t.classList&&Object(o["a"])(t.classList).includes("search_highlight"))e&&e(t);else for(var i=t.childNodes,s=0;s<i.length;s++)this.traverseNodes(i[s],e)},doHighlight:function(t){var e=this.keyword,i=new RegExp(e,"ig"),s=this.highlightStyle||"color:#66a4fb";t.innerHTML=t.innerHTML.replace(i,(function(t){return'<span style="'.concat(s,'">').concat(t,"</span>")}))},getTotal:function(){var t=this.$createElement,e=this.totalComp;return t(e,{attrs:{row:this.row,rowindex:this.rowIndex,checked:this.row.isChecked}})},getContent:function(){var t=this.$createElement;if(this.stComp){var e=this.stComp;return t(e,{attrs:{row:this.row,rowindex:this.rowIndex}})}return t("div",[t("div",{class:"search_highlight"},[this.subtitle]),t("div",{class:"search_highlight"},[this.content])])},getTag:function(){var t=this.$createElement;return t("div",[this.tag])},getRightIcon:function(){var t=this,e=this.$createElement;return this.remove?e("van-icon",{on:{click:function(e){e.stopPropagation(),t.removeClick()}},attrs:{name:"cross"}}):this.isTree?this.$slots.default:void 0},getCheckBox:function(){var t=this.$createElement;return!this.isMul||!this.isDetail&&this.childOnly?null:t(y["a"],{style:{marginRight:"10px"},attrs:{value:this.row["isChecked"]},on:{input:this.onCheckRow}})},getFavorCheck:function(t){var e=this,i=this.$createElement;return i("van-icon",{style:{lineHeight:"inherit",color:"#FFC107",marginRight:"10px"},on:{click:function(t){t.stopPropagation(),e.favorClick()}},attrs:{name:t}})},getIcon:function(){var t=this.$createElement;if(this.favor)return void 0==this.userid?t("div",[" ",this.getCheckBox(),this.getFavorCheck(this.row["isFavor"]?"star":"star-o")]):t("div",{style:"display:flex;align-items: center;"},[" ",this.getCheckBox(),this.getFavorCheck(this.row["isFavor"]?"star":"star-o"),t(u,{attrs:{userid:this.userid}})]);if(this.iconComp){var e=this.iconComp;return t(e,{attrs:{row:this.row,rowindex:this.rowIndex}})}return this.icon||this.enableIcon?t("div",{style:"display:flex;align-items: center;"},[this.getCheckBox(),t(u,{attrs:{title:this.icon}})]):this.userid?t("div",{style:"display:flex;align-items: center;"},[this.getCheckBox(),t(u,{attrs:{userid:this.userid}})]):this.getCheckBox()},getTitle:function(){var t=this.$createElement;if(this.ttComp){var e=this.ttComp;return t(e,{attrs:{row:this.row,rowindex:this.rowIndex}})}return t("div",{class:"search_highlight"},[this.title])},getLabel:function(){var t=this.$createElement;if(this.labelComp){var e=this.labelComp;return t(e,{attrs:{row:this.row,rowindex:this.rowIndex}})}return this.label?t("div",{class:"search_highlight"},[this.label]):null},onClick:function(t){if(!this.isMul)return!this.isDetail&&this.childOnly||this.$emit("click",this.rowData),this.onCheckRow();var e=!!this.row["isChecked"];void 0!=t&&t==e||(!this.isDetail&&this.childOnly?this.onCheckRow(e):(this.$emit("click",this.rowData),this.onCheckRow(e)))},removeClick:function(){this.$emit("removeClick",this.row)},favorClick:function(){this.$emit("favorClick",this.row),this.$forceUpdate()},onCheckRow:function(t){this.isDetail||!this.childOnly?(this.$set(this.row,"isChecked",!t),this.$emit("checkRow",this.row,!t),this.$forceUpdate()):window.idp.error(window.idp.lang.get("lookup_childOnlyTip"))}},mounted:function(){void 0==this.row.isFavor&&this.$set(this.row,"isFavor",!1),this.keywordHighLight()},render:function(t){return this.totalComp?t("div",{on:{click:this.onClick}},[this.getTotal()," "]):t("div",[t("van-cell",{on:{click:this.onClick},attrs:{clickable:!0,"is-link":this.isLink,center:!0,border:!1},scopedSlots:{"right-icon":this.getRightIcon,icon:this.getIcon,title:this.getTitle,label:this.hasContent()?this.getContent:null,default:this.label?this.getLabel:null,extra:null}})])}},k=C,x=Object(h["a"])(k,s,n,!1,null,null,null);e["a"]=x.exports},b9a9:function(t,e,i){},baf5:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{ref:"pageContainer",attrs:{title:t.pageTitle,rightCmp:t.navbarRCmp},on:{backClick:t.backClick}},[i("div",{class:{"f-flex-wrap":t.page.flex},attrs:{slot:"content"},slot:"content"},[t._l(t.container,(function(e,s){return i("div",{key:s,staticClass:"f-flex-col",class:{"f-flex-fixed":e.fixed}},["9"!=e.type?i("div",[t._l(e.childs,(function(e){return t._l(e.childs,(function(s,n){return i("div",{key:n,attrs:{name:e.index,id:s}},["searchbar"==t.controls[s].type?i("searchbar",{ref:s,refInFor:!0,attrs:{styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}):t._e(),"grid"==t.controls[s].type?i("dylistview",{ref:s,refInFor:!0,attrs:{hashDs:t.hashDs,callbackGetFilter:t.getGridFilter,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}):t._e(),"grid"!=t.controls[s].type&&"searchbar"!=t.controls[s].type?i("dycontrol",{ref:s,refInFor:!0,attrs:{styleId:t.styleId,type:t.controls[s].type,vprops:t.controls[s],value:t.formItem[t.controls[s].field],callbackGetFilter:t.getFilterDataSource,bizOps:t.bizOps},on:{input:t.updateForm}}):t._e()],1)}))}))],2):t._e(),"9"==e.type&&e.childs&&e.childs.length?i("van-tabs",{ref:e.id,refInFor:!0,attrs:{color:t.tabColor},on:{click:t.changeTab},model:{value:t.tabsActive[e.id],callback:function(i){t.$set(t.tabsActive,e.id,i)},expression:"tabsActive[item.id]"}},t._l(e.childs,(function(e,s){return i("van-tab",{key:s,attrs:{title:e.label}},t._l(e.childs,(function(s,n){return i("div",{key:n,attrs:{name:e.index,id:s}},["searchbar"==t.controls[s].type?i("searchbar",{ref:s,refInFor:!0,attrs:{styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}):t._e(),"grid"==t.controls[s].type?i("dylistview",{ref:s,refInFor:!0,attrs:{hashDs:t.hashDs,callbackGetFilter:t.getGridFilter,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}):t._e(),"grid"!=t.controls[s].type&&"searchbar"!=t.controls[s].type?i("dycontrol",{ref:s,refInFor:!0,attrs:{styleId:t.styleId,type:t.controls[s].type,vprops:t.controls[s],value:t.formItem[t.controls[s].field],callbackGetFilter:t.getFilterDataSource,bizOps:t.bizOps},on:{input:t.updateForm}}):t._e()],1)})),0)})),1):t._e()],1)})),i("vdialog",{ref:"dialog",attrs:{ctx:t.ctx}}),"float"==t.floatBtn.type?i("circle-menu",{ref:"menu",staticClass:"f-float-btn",attrs:{type:"top",number:t.currentFloatBtn.btnLength,circle:"",floatBtn:t.currentFloatBtn,styleId:t.styleId,name:t.currentFloatBtn.item1.name,icon:t.currentFloatBtn.item1.icon},on:{btnClick:t.handleMenuClick}},[i("a",{attrs:{slot:"item_1"},slot:"item_1"},[t._v(t._s(t.currentFloatBtn.item1.name))]),i("a",{attrs:{slot:"item_2"},slot:"item_2"},[t._v(t._s(t.currentFloatBtn.item2.name))]),i("a",{attrs:{slot:"item_3"},slot:"item_3"},[t._v(t._s(t.currentFloatBtn.item3.name))]),i("a",{attrs:{slot:"item_4"},slot:"item_4"},[t._v(t._s(t.currentFloatBtn.item4.name))]),i("a",{attrs:{slot:"item_5"},slot:"item_5"},[t._v(t._s(t.currentFloatBtn.item5.name))]),i("a",{attrs:{slot:"item_6"},slot:"item_6"},[t._v(t._s(t.currentFloatBtn.item6.name))]),i("a",{attrs:{slot:"item_7"},slot:"item_7"},[t._v(t._s(t.currentFloatBtn.item7.name))]),i("a",{attrs:{slot:"item_8"},slot:"item_8"},[t._v(t._s(t.currentFloatBtn.item8.name))])]):t._e(),"action"==t.floatBtn.type?i("action-menu",{staticClass:"f-float-btn",attrs:{actions:t.floatBtn.actions.filter((function(t){return!t.hide})),styleId:t.styleId}}):t._e(),i("van-action-sheet",{attrs:{round:!1,actions:t.actions,"cancel-text":t.getLang("custom_cancel"),"close-on-click-action":""},on:{cancel:t.onActionCancel,select:t.onActionConfirm},model:{value:t.actionShow,callback:function(e){t.actionShow=e},expression:"actionShow"}})],2)])},n=[],o=(i("a4d3"),i("e01a"),i("d28b"),i("99af"),i("4de4"),i("7db0"),i("4160"),i("d81d"),i("b64b"),i("d3b7"),i("ac1f"),i("3ca3"),i("1276"),i("159b"),i("ddb0"),i("5530")),a=i("3c69"),r=i("d399"),l=i("91f4"),c=i.n(l),h=i("c6e7"),d=i.n(h),u=i("d8ad"),f=i("33de"),p=i("efee"),m=i("693a"),g=i("3616"),v=i("c956"),b=i("810a"),w=i("c0bb"),y=i("7342"),C=i("154e"),k=(i("e8ff"),i("3daa")),x=i("2870"),I=i("8c1b"),_=i("c968"),T=i("74b9"),F=i("0771"),L=(i("72fb"),i("2f04")),S=null,D={components:{dylayout:f["a"],dycontrol:p["default"],dylistview:g["a"],searchbar:m["a"],CircleMenu:x["a"],ActionMenu:I["a"],FarrisBpage:_["a"],vdialog:L["a"]},provide:function(){return{viewTag:this.getViewTag}},mixins:[F["a"]],data:function(){return{version:"",tabColor:"#388fff",styleId:"",pathName:"",pageTitle:"",listConfig:{},formItem:{},listItems:{},schema:[],page:{},controls:{},uiConfig:{},container:{},layouts:[],actions:[],actionShow:!1,hashDs:{},currentFloatBtn:{type:"",btnLength:8,item1:{name:"",icon:"",click:"",hide:!1},item2:{name:"",icon:"",click:"",hide:!1},item3:{name:"",icon:"",click:"",hide:!1},item4:{name:"",icon:"",click:"",hide:!1},item5:{name:"",icon:"",click:"",hide:!1},item6:{name:"",icon:"",click:"",hide:!1},item7:{name:"",icon:"",click:"",hide:!1},item8:{name:"",icon:"",click:"",hide:!1},actions:[]},floatBtn:{type:"",btnLength:8,item1:{name:"",icon:"",click:"",id:"",hide:!1},item2:{name:"",icon:"",click:"",id:"",hide:!1},item3:{name:"",icon:"",click:"",id:"",hide:!1},item4:{name:"",icon:"",click:"",id:"",hide:!1},item5:{name:"",icon:"",click:"",id:"",hide:!1},item6:{name:"",icon:"",click:"",id:"",hide:!1},item7:{name:"",icon:"",click:"",id:"",hide:!1},item8:{name:"",icon:"",click:"",id:"",hide:!1},actions:[]},langResources:{},tabsInfo:{},tabsActive:{},isCustom:!0,bizOps:[],ctx:{},name:"",navbarRCmp:""}},created:function(){var t=this;u["a"].$on("component-change",(function(e,i,s,n,o){return o&&t.styleId!=o?null:(console.log(t.event),console.log(t.styleId+o),t.event.emit(e,i,s,n))}))},activated:function(){this.pageTitle&&(document.title=this.pageTitle)},beforeRouteEnter:function(t,e,i){console.log(arguments),i()},beforeRouteLeave:function(t,e,i){var s=!0;this.event&&(s=this.event.emitGlobal("pageBack",[this.name,this.styleId]),this.event.emitGlobal("beforeRouteLeave",[t,e,i])),0!=s?(this.clearWatermark(),this.$route.query&&this.$route.query.wfviewpagedeep&&window.parent["cancelTaskCenterPopstate"]&&window.parent["cancelTaskCenterPopstate"](!0),i()):i(!1)},mounted:function(){console.log(this.$route);var t=this.$route.path,e=t.split("/");3==e.length&&(this.styleId=this.$route.meta.styleId,this.pathName=e[2]),S=new v["b"](this.styleId),this.service=S,"en"==window.idp.lang.getLang().id?a["a"].use("en-US",c.a):"zh-CHT"==window.idp.lang.getLang().id&&a["a"].use("zh-TW",d.a),this.loadUIComponent()},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},initLangResources:function(){this.langResources=window.idp.lang.initLangResources(this.styleId)||{},this.pageTitle=this.langResources[this.name+"_title"]?this.langResources[this.name+"_title"]:this.pageTitle},getViewTag:function(){return this.viewTag},activeResurces:function(){var t=this;this.moduleManager=new T["a"](this.styleId,this.version),this.moduleManager.execModule([this.code+"/"+this.pathName],(function(e,i){console.log(arguments),e&&e.init(t),i&&i.init(t),t.initView((function(){t.event.emit("viewReady","all","all",[t]),t.event.emit("pageActive","all","all",[t])}))}),this.isCustom,this.customCode)},handleMenuClick:function(t){var e=this.currentFloatBtn.actions[t].script,i="return (function(view,idp){"+e+"})(view,idp)",s=new Function("view","idp",i),n={func:this};s(this,n)},regEvents:function(){var t=this,e=this;for(var i in this.serachBarController=null,this.toolbarController=null,this.inputEditorContorller=null,this.view=this,this.uiConfig.container){var s=function(){var s=t.uiConfig.container[i].childs[n];for(o in t.uiConfig.container[i].childs[n].childs){var a=t.uiConfig.container[i].childs[n].childs[o],r=t.uiConfig.controls[a];switch(r.type){case"toolbar":t.regToolbar(a,!1,r);break;case"searchbar":s.isqry&&(t.gridController.pushQry(s.queryds,s),t.event.on("serach","serachbar",a,(function(){e.gridController.refreshDs(s.queryds)})));break;case"grid":console.log(t.$refs),t.gridController.add(a,r,t),t.regGrid(a);break;case"tab":t.regTab(a);break;case"gridmenu":t.gridController.add(a,r,t),t.regGridMenu(a);break;default:break}}};for(var n in this.uiConfig.container[i].childs){var o;s()}}e.regFloatBtn(),e.updateCurFloatBtn(),this.control=new C["a"](this.$refs,"list"),S.getCustomConfig(this.styleId).then((function(i){for(var s in"ok"==i.Code&&i.Data.bizOps&&(e.bizOps=i.Data.bizOps.map((function(t){return t.id}))),t.uiConfig.container)for(var n in t.uiConfig.container[s].childs){t.uiConfig.container[s].childs[n];for(var o in t.uiConfig.container[s].childs[n].childs){var a=t.uiConfig.container[s].childs[n].childs[o],r=t.uiConfig.controls[a];switch(r.type){case"toolbar":t.initBtnHidden(a,!1,r,!0);break;default:break}}}e.updateCurFloatBtn()}))},getGridFilter:function(t){return this.getGridFilterExpress(t)},getGridFilterExpress:function(t){var e=this.uiConfig.controls[t].dscode,i=[],s=[],n=this.gridController.getGridFilter(this.uiConfig.controls[t].filter);s=s.concat(this.getFilterDataSource(e)),s.length>0&&(s[s.length-1].Logic="",n.length>0&&(n[n.length-1].Logic="and")),i=i.concat(n).concat(s);var o=this.event.emit("beforeGridFilter","grid",t,[i]);return o||i},getFilterDataSource:function(t){return this.gridController.getFilterDataSource(t)},setGridFilter:function(t,e){this.$refs[t].filter=e},loadUIComponent:function(){var t=this,e=this.styleId,i={styleId:this.styleId,formType:"2",ControlId:"",fields:[]},s=[];s.push(S.getColSetData(i)),Promise.all(s).then((function(i){var s=i[0].Data;w["a"].setSelectOpitons(e,s);var n=b["a"].getMobiConfig(t.pathName,w["a"].getFormInfo(e));t.version=w["a"].getVersion(e),t.uiConfig=n,t.page=n.config,t.page.NavbarTmpl&&(t.navbarRCmp=JSON.stringify(t.page.NavbarTmpl)),t.name=t.page.id||"",t.code=n.code||e,t.basic=n.basic,t.customCode=n.customCode;var o=n.basic.dsList;if(o)for(var a in o)t.hashDs[o[a].info.SqlId]=o[a];t.uistate="loaded",console.log(t.hashDs),t.store=new y["a"],t.gridController=new k["c"](t),t.event=new k["b"](t.uiConfig.controls),t.pageTitle=t.page.title,t.initLangResources(),t.regEvents(),t.activeResurces(),console.log(t.container),r["a"].clear(),console.log(t.$refs),t.ctx={event:t.event,params:t.params,express:t.express,control:t.control,data:t.dataManager,modelController:t.modelController,store:t.store,langResources:t.langResources}}))},initView:function(t){this.initLang(),this.initTabsInfo(),this.$nextTick((function(){t&&t()}))},initTabsInfo:function(){var t=this,e=Object.keys(this.container).filter((function(e){return t.container[e].id=t.container[e].id||e,"9"==t.container[e].type}));e.forEach((function(e){t.tabsInfo[e]=Object(o["a"])({},t.container[e])}))},setTabHide:function(t,e,i){this.tabsInfo[t].childs.find((function(t){return t.id==e})).hide=i,this.updateCurTabs(t)},updateCurTabs:function(t){this.container[t].childs=[];var e=!0,i=!1,s=void 0;try{for(var n,o=this.tabsInfo[t].childs[Symbol.iterator]();!(e=(n=o.next()).done);e=!0){var a=n.value;a.hide||this.container[t].childs.push(a)}}catch(r){i=!0,s=r}finally{try{e||null==o.return||o.return()}finally{if(i)throw s}}return this.container[t]},scrollTab:function(t,e){var i,s=this.container[t].childs.find((function(t,s){if(t.id==e)return i=s,!0}));this.$refs[t][0].scrollTo(i),this.changeTab(i,s.label)},updateForm:function(t,e){this.formItem[t]=e,this.$forceUpdate(),this.event.emitGlobal("dataChange",[t,e])},changeTab:function(t,e){this.event.emitGlobal("tabChange",[t,e])}},watch:{floatBtn:{deep:!0,handler:function(t){"float"==t.type&&this.updateCurFloatBtn()}},pageTitle:{immediate:!0,handler:function(t){window.document.title=t}}}},E=D,$=(i("f243"),i("2877")),P=Object($["a"])(E,s,n,!1,null,null,null);e["default"]=P.exports},bb80:function(t,e,i){"use strict";var s=i("dd74"),n=i.n(s);n.a},bc6d:function(t,e,i){"use strict";var s=i("f320"),n=i.n(s);n.a},bdeb:function(t,e,i){"use strict";var s=i("ceb5"),n=i.n(s);n.a},be4b:function(t,e,i){},be65:function(t,e,i){"use strict";var s=i("2b42"),n=i.n(s);n.a},c844:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div")},n=[],o={components:{},data:function(){return{styleId:"",dataId:""}},activated:function(){},mounted:function(){},methods:{}},a=o,r=i("2877"),l=Object(r["a"])(a,s,n,!1,null,null,null);e["default"]=l.exports},c968:function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.showBar?i("van-nav-bar",{attrs:{title:t.title,fixed:"","left-arrow":""},on:{"click-left":t.onClickLeft,"click-right":t.onClickRight},scopedSlots:t._u([t.right?{key:"right",fn:function(){return[i(t.right,{ref:"rightCmp",tag:"div"})]},proxy:!0}:null],null,!0)}):t._e(),i("div",{class:t.getContentClass(),style:t.getStyle(),attrs:{id:"f-main-bpage"}},[t._t("content")],2),t._t("tab")],2)},n=[],o=(i("c975"),i("a026")),a={props:["title","tab","page","rightCmp"],components:{},data:function(){return{showBar:!0,right:null}},mounted:function(){console.log(this),(this.isWeiXin()||this.$route&&this.$route.query&&"true"==this.$route.query.hidebar||window.IDP_EXTEND_NOBAR)&&(this.showBar=!1)},methods:{isWeiXin:function(){var t=navigator.userAgent.toLowerCase();return-1!=t.indexOf("micromessenger")},getContentClass:function(){var t=this.showBar?"f-main-content":"f-main-content-nobar";return this.tab&&(t+=" f-main-content-tab"),t},getStyle:function(){return this.$route.path.indexOf("recorddetail")>-1?"bottom: 49px":""},onClickLeft:function(){this.$emit("backClick")},onClickRight:function(){},sorry:function(){},buildSingleComponent:function(t,e){if(t&&t.template){var i={};if(t.mixin){var s=new Function("return "+t.mixin+";");i=s()}return this[e]=o["a"].extend({mixins:[i],template:t.template,data:function(){return{}},methods:{}}),this[e]}}},watch:{rightCmp:{immediate:!0,handler:function(t){if(t&&"string"==typeof t)try{this.buildSingleComponent(JSON.parse(t),"right")}catch(e){console.warn(e),this.right=null}else this.right=null}}}},r=a,l=(i("aa7a"),i("2877")),c=Object(l["a"])(r,s,n,!1,null,null,null);e["a"]=c.exports},cbd9:function(t,e,i){},ceb5:function(t,e,i){},cedb:function(t,e,i){},d362:function(t,e,i){"use strict";var s,n,o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.layoutShow,expression:"layoutShow"}],attrs:{id:t.id}},["9"!==t.info.type?i("div",{ref:"container",class:t.cls},[t._l(t.layouts,(function(e){return[t.isPageHeader||!t.isView||t.info.fixed||t.info.noRemark?t._e():i("formdesc",{key:e.id+"_desc",staticClass:"f-panel-desc",attrs:{row:t.formItem,options:t.page.remark}}),e.showtitle?i("div",{key:e.id,staticClass:"f-panel-title"},[t.customIconSrc?i("img",{attrs:{src:t.customIconSrc}}):t._e(),i("span",{staticClass:"f-panel-header"},[t._v(t._s(e.title))])]):t._e(),t.isCard?i("div",{key:e.id+"_card",staticClass:"f-panel-card"},t._l(e.childs,(function(s,n){return i("div",{key:n,attrs:{id:s}},[t.isMainControl(s)?i("dycontrol",{ref:s,refInFor:!0,attrs:{ctx:t.ctx,parent:e,styleId:t.styleId,readonly:t.readonly,type:t.controls[s].type,vprops:t.controls[s],row:t.getRow(s),value:t.getValue(s),listItems:t.listItems,isView:t.isView,bizOps:t.bizOps},on:{tabClick:t.tabClick,input:t.updateForm}}):t._e()],1)})),0):t._e(),t._l(e.childs,(function(s,n){return i("div",{key:n,attrs:{id:s}},["grid"!=t.controls[s].type||t.controls[s].table?t._e():i("dylistview",{ref:s,refInFor:!0,attrs:{hashDs:t.hashDs,callbackGetFilter:t.callbackGetFilter,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s],gridTitle:t.controls[s].title,iconurl:t.controls[s].iconurl}}),"grid"==t.controls[s].type&&t.controls[s].table?i("dycardlist",{ref:s,refInFor:!0,attrs:{ctx:t.ctx,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s],listItems:t.listItems}}):t._e(),t.isDetailControl(s)?i("dycontrol",{ref:s,refInFor:!0,class:t.aloneCtrlCls,attrs:{ctx:t.ctx,parent:e,styleId:t.styleId,readonly:t.readonly,type:t.controls[s].type,vprops:t.controls[s],row:t.getRow(s),value:t.getValue(s),listItems:t.listItems,isView:t.isView,bizOps:t.bizOps},on:{tabClick:t.tabClick,input:t.updateForm}}):t._e()],1)}))]}))],2):t._e(),"9"==t.info.type&&t.curLayouts&&t.curLayouts.length?i("van-tabs",{ref:"container",attrs:{color:t.tabColor,"lazy-render":!1},on:{click:t.changeTab},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},t._l(t.curLayouts,(function(e,s){return i("van-tab",{key:s,attrs:{title:e.label}},t._l(e.childs,(function(s){return i("div",{key:s,attrs:{id:s}},["grid"!=t.controls[s].type||t.controls[s].table?t._e():i("dylistview",{ref:s,refInFor:!0,attrs:{hashDs:t.hashDs,callbackGetFilter:t.callbackGetFilter,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s]}}),"grid"==t.controls[s].type&&t.controls[s].table?i("dycardlist",{ref:s,refInFor:!0,attrs:{ctx:t.ctx,styleId:t.styleId,type:t.controls[s].type,controls:t.controls,vprops:t.controls[s],listItems:t.listItems}}):t._e(),"grid"!=t.controls[s].type?i("dycontrol",{ref:s,refInFor:!0,attrs:{ctx:t.ctx,parent:e,styleId:t.styleId,readonly:t.readonly,type:t.controls[s].type,vprops:t.controls[s],row:t.getRow(s),value:t.getValue(s),listItems:t.listItems,bizOps:t.bizOps},on:{tabClick:t.tabClick,input:t.updateForm}}):t._e()],1)})),0)})),1):t._e()],1)},a=[],r=(i("a4d3"),i("e01a"),i("d28b"),i("7db0"),i("d3b7"),i("3ca3"),i("ddb0"),i("efee")),l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.showView,expression:"visible&&showView"}],staticClass:"f-panel-detail"},[t.vprops.title?i("div",{staticClass:"f-panel-title f-panel-grid",class:{"f-panel-hidden":!t.show},on:{click:t.toggleHeader}},[t.vprops.iconurl||t.customIconSrc||t.leftView?t._e():i("span",{staticClass:"f-panel-header"},[t._v(" "+t._s(t.vprops.title)+" ")]),!t.vprops.iconurl&&!t.customIconSrc||t.leftView?t._e():i("span",{staticClass:"f-panel-header f-panel-header-hasicon"},[t.vprops.iconurl?i("img",{attrs:{src:t.vprops.iconurl}}):i("img",{attrs:{src:t.customIconSrc}}),t._v(" "+t._s(t.vprops.title)+" ")]),i(t.leftView,{tag:"div",staticClass:"f-panel-header f-panel-header-hasicon",attrs:{data:t.list}}),i(t.rightView,{tag:"div",attrs:{data:t.list}}),0!=t.vprops.fold?i("span",{staticClass:"f-panel-arrow"},[i("van-icon",{attrs:{name:"arrow"}})],1):t._e()],1):t._e(),t.topToolBar?i("vtoolbar",{directives:[{name:"show",rawName:"v-show",value:t.show&&t.topToolBar,expression:"show && topToolBar"}],ref:t.toolBarId,attrs:{styleId:t.styleId,data:t.vprops.btns,id:t.toolBarId,isGrid:!0,gridId:t.vprops.id}}):t._e(),t._l(t.list,(function(e,s){return i("van-swipe-cell",{directives:[{name:"show",rawName:"v-show",value:t.show&&!e.hide,expression:"show&&!item.hide"}],key:s,attrs:{name:s,"before-close":t.beforeClose,disabled:t.readonly||t.disabled}},[t.flatStyle?t._e():i("dylistitem",{attrs:{enableIcon:t.enableIcon,totalComp:t.totalComp,row:e,contentComp:t.contentComp,stComp:t.stComp,ttComp:t.ttComp,iconComp:t.iconComp,labelComp:t.labelComp,subtitle:e[t.fields.subtitle],rowData:e,title:e[t.fields.title],label:e[t.fields.label],content:e[t.fields.content],icon:e[t.fields.icon],rowIndex:s},on:{click:function(i){return t.editRow(e,s)}}}),t.flatStyle?i("dydetailpanel",{ref:"detail_"+s,refInFor:!0,attrs:{styleId:t.styleId,controls:t.controls,formItem:t.formItem,listItems:t.listItems,isCard:!0,isView:!0,readonly:!0,index:s,rowdata:e,flatCols:t.flatCols,gridId:t.vprops.id,flatFold:t.flatFold,flatFoldIndex:t.flatFoldIndex,isLast:s==t.list.length-1,abovetoolbar:t.aboveToolbar},on:{click:function(i){return t.editRow(e,s)}}}):t._e(),t.vprops.swipe?i("template",{slot:"right"},t._l(t.buttonList,(function(n,o){return i("van-button",{directives:[{name:"show",rawName:"v-show",value:!n.hide&&t.showButton(n,e),expression:"!button.hide&&showButton(button, item)"}],key:o,staticStyle:{height:"100%"},attrs:{square:"",type:t.transforButtonFormat(n.style),text:n.name,icon:n.icon},on:{click:function(i){return t.swipeClick(n.id,o,e,s)}}})})),1):t._e(),e.__validate?i("div",{staticClass:"f-error-message"},[t._v(t._s(e.__validateMessage))]):t._e()],2)})),t.vprops.showSum?i("div",{staticClass:"f-list-sum"},[i("div",{staticStyle:{margin:"6px"}},[t._v(t._s(t.getLang("lookup_sum")))]),t._l(t.sumArr,(function(e,s){return i("div",{key:s,staticClass:"f-list-sum-item",attrs:{name:s}},[t._v(" "+t._s(e.text)+" "),i("span",[t._v(t._s(e.value))])])}))],2):t._e(),t.topToolBar?t._e():i("vtoolbar",{directives:[{name:"show",rawName:"v-show",value:t.show&&!t.topToolBar,expression:"show && !topToolBar"}],ref:t.toolBarId,attrs:{styleId:t.styleId,data:t.vprops.btns,id:t.toolBarId,isGrid:!0,gridId:t.vprops.id}}),i(t.bottomView,{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}],tag:"div",attrs:{data:t.list}})],2)},c=[],h=(i("a9e3"),i("ac1f"),i("1276"),i("b982")),d=i("d8ad"),u=i("4232"),f=(i("62e4"),i("a026")),p=i("bab6"),m=i("c0bb"),g=i("27ae"),v=i("39ea"),b=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-detail-container",class:{"f-detail-container-isLast":t.isLast&&!t.abovetoolbar}},[i("div",{staticClass:"f-detail-panel",on:{click:t.onClick}},[t._l(t.displayCols,(function(e,s){return i("div",{key:s,attrs:{id:e}},[i("dycontrol",{ref:e,refInFor:!0,attrs:{styleId:t.styleId,readonly:t.readonly,type:t.controls[e]?t.controls[e].type:"input",vprops:t.controls[e]||t.extCtrls[e],row:t.rowdata,value:t.getValue(e),listItems:t.listItems,isView:t.isView},on:{input:t.updateDetail}})],1)})),t.flatFold?i("vtitle",{ref:"detailSplit",attrs:{id:"detailSplit",parent:t.parent,ctx:t.ctx,options:t.splitCtrl}}):t._e()],2)])},w=[],y=(i("4de4"),i("d81d"),i("fb6a"),i("5319"),i("2ca0"),i("6596")),C={name:"dydetailpanel",components:{dycontrol:r["default"],vtitle:y["a"]},props:["styleId","controls","formItem","listItems","isCard","isView","readonly","index","rowdata","flatCols","gridId","flatFold","flatFoldIndex","isLast","abovetoolbar"],inject:["viewTag"],data:function(){return{ctx:{},parent:{childs:[]},splitCtrl:{id:"detailSplit",type:"title",foldIndex:2},extCtrls:{}}},mounted:function(){var t=this,e=this;if(this.flatFold){this.ctx.control={get:e.getCtrl};var i=this.flatCols.map((function(e){return t.gridId+"."+e.id}));i.push("detailSplit"),this.parent.childs=i,this.splitCtrl.foldIndex=this.flatFoldIndex}},computed:{displayCols:function(){var t=this,e=this;this.$nextTick((function(){e.setPanelReadOnly(!0)}));var i=this.flatCols.map((function(i){var s=t.gridId+"."+i.id;if(!e.controls[s]&&!e.extCtrls[s]){var n={field:i.id,id:s,label:i.name.replace(/\(\w+\)/g,""),type:"input",visible:!0};e.extCtrls[s]=n}return s}));return i=i.filter((function(t){return!e.controls[t]||"upload"!=e.controls[t].type})),i}},methods:{updateDetail:function(t,e,i,s,n){this.$emit("updateDetail",t,e,i,s,n),console.log(arguments)},getValue:function(t){return this.controls[t]&&this.controls[t].field?this.rowdata[this.controls[t].field]:t.startsWith(this.gridId+".")?this.rowdata[t.slice(this.gridId.length+1)]:void 0},getCtrl:function(t){return this.$refs[t]&&this.$refs[t][0].$children?this.$refs[t][0].$children[0]:{}},setPanelReadOnly:function(t){var e=!0,i=!1,s=void 0;try{for(var n,o=this.displayCols[Symbol.iterator]();!(e=(n=o.next()).done);e=!0){var a=n.value;this.getCtrl(a)&&this.getCtrl(a).setDisabled&&this.getCtrl(a).setDisabled(t)}}catch(r){i=!0,s=r}finally{try{e||null==o.return||o.return()}finally{if(i)throw s}}},onClick:function(){this.$emit("click",this.rowData)}}},k=C,x=(i("ed53"),i("2877")),I=Object(x["a"])(k,b,w,!1,null,null,null),_=I.exports,T={name:"dycardlist",props:["styleId","gridId","type","title","controls","vprops","listItems","formItem"],inject:["viewTag"],components:{dylistitem:h["a"],vtoolbar:u["a"],dydetailpanel:_},directives:{longTouch:p["a"]},mounted:function(){if(this.customIconSrc=window.IDP_CUSTOM_HEADER_ICON,this.fields.title=this.vprops.ttField,this.fields.subtitle=this.vprops.stField,this.fields.label=this.vprops.tagField,this.fields.content=this.vprops.desField,this.fields.icon=this.vprops.iconField,this.toolBarId=this.vprops.id+"_toolbar",this.topToolBar=this.vprops.topToolBar||!1,this.vprops.iconField&&(this.enableIcon=!0),this.updateView(),this.buildDynamicComponent(),this.vprops.hide&&(this.visible=!1),this.vprops.autoFold&&(this.show=!1),this.vprops.buttonList||(this.vprops.buttonList=[]),this.vprops.swipe&&0==this.vprops.buttonList.length){var t={custom:!0,id:"baritem_delete",name:this.getLang("baritem_delete"),script:this.vprops.onSwipeClick,style:"4"};this.buttonList.push(t)}else this.buttonList=this.vprops.buttonList;this.flatStyle=this.vprops.flatStyle||!1,this.flatCols=this.vprops.flatCols||[],this.flatFold=this.vprops.flatFold||!1,this.flatFoldIndex=this.vprops.flatFoldIndex||2;var e=this;this.$nextTick((function(){e.vprops.readonly&&(e.readonly=e.vprops.readonly,e.disabled=e.vprops.readonly,e.setToolBarDisabled(e.readonly))}))},data:function(){return{leftView:null,rightView:null,bottomView:null,showView:!0,visible:!0,list:[],sumArr:[],toolBarId:"",topToolBar:!1,loading:!1,listConfig:null,loadingData:!1,finished:!1,value:"",refreshing:!1,pageIndex:0,pageSize:20,totalCount:0,stComp:null,totalComp:null,ttComp:null,iconComp:null,labelComp:null,contentComp:null,hashFieldCtrl:{},show:!0,enableIcon:!1,readonly:!1,disabled:!1,fields:{title:"",label:"",content:"",icon:""},buttonList:[],customIconSrc:"",flatStyle:!1,flatFoldIndex:2,flatFold:!1,flatCols:[]}},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},setVisible:function(t){this.visible=!t},setShow:function(t){this.showView=t},setDisabled:function(t){console.log(this.$refs),this.readonly=t,this.setToolBarDisabled(t)},clearValue:function(){var t=this.vprops.table;this.listItems[t]&&this.listItems[t].length&&(this.listItems[t]=[])},toggleHeader:function(){0!=this.vprops.fold&&(this.show=!this.show)},buildDynamicComponent:function(){var t={stTpl:"stComp",ttTpl:"ttComp",iconTpl:"iconComp",tagTpl:"labelComp",desTpl:"contentComp",totalTpl:"totalComp"};for(var e in t)this.buildSingleComponent(e,t[e],["row","rowindex"]);var i=["rightTpl","leftTpl","bottomTpl"],s=["rightView","leftView","bottomView"];for(var n in i){var o=i[n];this.vprops[o]&&(this[s[n]]=this.buildSingleComponent(o,s[n],["data"]))}},buildSingleComponent:function(t,e,i){var s=this;if(this.vprops[t]&&this.vprops[t].template){var n={};if(this.vprops[t].mixin){var o=new Function("return "+this.vprops[t].mixin+";");n=o()}return this[e]=f["a"].extend({mixins:[n],props:i,template:this.vprops[t].template,data:function(){return{}},methods:{getText:function(t){return s.getMapData(t,this.row)},dateFormat:function(t){},thousand:function(t){}}}),this[e]}},getMapData:function(t,e){var i=this.getDropDownCtrl(t);if(i){var s=this.getDropDownOpitons(i),n="";for(var o in s)s[o].name==e[t]&&(n=s[o].text);return n}},getSumDisplay:function(){if(this.vprops.showSum){if(!this.sumFieldArr){var t=this.vprops.cols,e=[];for(var i in t){var s=t[i].id,n=this.vprops.id+"."+s,o=this.controls[n];o&&o.issum&&e.push({field:o.field,label:o.label,precision:2,thousand:!1,hideIf0:o.hideIf0})}this.sumFieldArr=e}var a=[];for(var r in this.sumFieldArr)this.sumFieldArr[r].hideIf0&&!Number(this.getSumResult(this.sumFieldArr[r].field))||a.push({text:this.sumFieldArr[r].label,value:this.getSumResult(this.sumFieldArr[r].field)});this.sumArr=a}},getSumResult:function(t){var e=0;for(var i in this.list)this.list[i][t]&&(e=window.accAdd(e,this.list[i][t]));var s=this.vprops.id+"."+t;return this.controls[s]&&this.controls[s].fparams&&(this.controls[s].fparams.thousand?e=v["a"].currency(e,this.controls[s].fparams.decimal):this.controls[s].fparams.decimal&&(e=window.NumberRound(e,Number(this.controls[s].fparams.decimal)))),e},getDropDownCtrl:function(t){if(this.hashFieldCtrl[t])return this.hashFieldCtrl[t];var e=this.vprops.id,i=this.vprops.cols;for(var s in i){var n=i[s].id,o=e+"."+n;if(this.controls[o]&&this.controls[o].field==t){this.hashFieldCtrl[t]=this.controls[o];break}}return this.hashFieldCtrl[t]},getDropDownOpitons:function(t){var e=t["editor_"+t.type],i=[];if("str"==e.datatype){var s=[],n=g["Base64"].decode(e.json),o=n.split(";");for(var a in o){var r=o[a].split(",");s.push({name:r[0],text:r[1]})}i=s}else{var l=m["a"].getSelectOpitons(this.styleId,this.ctrl.id);for(var c in l)i.push({name:l[c][e.key],text:l[c][e.value]})}return i},updateView:function(){var t=this.vprops.table;for(var e in this.list=this.listItems[t]||[],this.list)void 0==this.list[e].__validate&&(this.$set(this.list[e],"__validate",!1),this.list[e].__validate=!1),void 0==this.list[e].__validateMessage&&this.$set(this.list[e],"__validateMessage","");this.getSumDisplay()},beforeClose:function(t){var e=t.position,i=t.instance,s=t.name;switch(console.log(arguments),e){case"left":case"cell":case"outside":i.close();break;case"right":this.vprops.swipe&&!this.vprops.onSwipeClick&&d["a"].$emit("component-change","swipeclick","grid",this.vprops.id,[this.vprops,this.list[s],s],this.styleId,this.viewTag?this.viewTag():void 0),i.close();break}},addDetail:function(){this.openDetail({},"add",-1)},onLongTouch:function(t,e){var i=this;return function(){d["a"].$emit("component-change","longClick","grid",i.vprops.id,[i.vprops,t,e],this.styleId,this.viewTag?this.viewTag():void 0)}},editRow:function(t,e){console.log(t);var i=d["a"].$emit("component-change","defineRowClick","grid",this.vprops.id,[this.vprops,t,e],this.styleId,this.viewTag?this.viewTag():void 0);i||d["a"].$emit("component-change","rowclick","grid",this.vprops.id,[this.vprops,t,e],this.styleId,this.viewTag?this.viewTag():void 0)},openDetail:function(t,e,i){this.$router.push({name:"detail",path:"/detail",params:{gridId:this.vprops.id,styleId:this.styleId,dataId:"",rowIndex:i,status:e,rowData:t,controls:this.controls,vprops:this.vprops,viewTag:this.viewTag?this.viewTag():void 0}}).catch((function(t){return t}))},setToolBarDisabled:function(t){this.disabled?this.$refs[this.toolBarId].setDisabled(!0):this.$refs[this.toolBarId].setDisabled(t),console.log(this.$refs)},setToolBarVisible:function(t){this.$refs[this.toolBarId].setVisible(t),console.log(this.$refs)},swipeClick:function(t,e,i,s){console.log(e),d["a"].$emit("component-change","swipeButtonClick","grid",this.vprops.id,[t,e,i,this,s],this.styleId,this.viewTag?this.viewTag():void 0)},transforButtonFormat:function(t){switch(t){case"2":case"7":return"info";case"6":return"warning";case"4":return"danger";case"3":case"5":return"primary"}},clearValidateMessage:function(){for(var t in this.list)this.list[t].__validate=!1,this.list[t].__validateMessage=""},showButton:function(t,e){console.log("展示滑动按钮");var i=d["a"].$emit("component-change","rowButtonShow","grid",this.vprops.id,[t,e],this.styleId,this.viewTag?this.viewTag():void 0);return!1!==i||i}},watch:{list:{deep:!0,handler:function(){this.getSumDisplay()}},listItems:{immediate:!0,deep:!0,handler:function(t){this.updateView()}}},computed:{aboveToolbar:function(){var t=this.$refs[this.toolBarId];return t&&t.visible&&this.show&&!this.topToolBar}}},F=T,L=(i("1300"),Object(x["a"])(F,l,c,!1,null,null,null)),S=L.exports,D=i("3616"),E=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"f-form-title"},[i("div",{staticClass:"avatar"},[i("img",{staticStyle:{width:"40px",height:"40px","border-radius":"100%"},attrs:{src:t.src},on:{error:t.getDefaultImg}})]),i("div",{staticClass:"desc"},[i("div",{staticClass:"title"},[i("div",{staticClass:"left"},[t._v(t._s(t.username)+t._s(t.title))]),i("div",{staticClass:"right"},[i("span",[t._v(t._s(t.currency))]),t._v(t._s(t.money))])]),i("div",{staticClass:"subtitle"},[i("div",{staticClass:"left"},[t._v(t._s(t.subtitle))]),i("div",{staticClass:"right"},[t._v(t._s(t.info))])])])])])},$=[],P=(i("b680"),i("25f0"),{props:["options","row"],components:{},activated:function(){},data:function(){return{src:"",username:"",title:"",subtitle:"",money:"",info:"",currency:"",errorImg:"/platform/runtime/sys/web/assets/img/avatar-default.png"}},methods:{updateView:function(){var t=this.options.fields;if(this.info=this.options.info,this.currency=this.options.currency||"￥",t&&(this.src="/api/fastdweb/runtime/v1.0/Common/getUserAvatar?userId="+this.row[t.userid],this.username=this.row[t.name],this.title=this.row[t.title],this.subtitle=this.row[t.subtitle],t.money))if(this.row[t.money]){var e=Number(this.row[t.money]);if(this.options.thousand){var i=String(e).split(".");void 0===i[1]||null===i[1]?this.money=this.toThousands(i[0]):this.money=this.toThousands(i[0])+"."+i[1]}else this.money=String(e.toFixed(2))}else this.money=0},getDefaultImg:function(t){var e=t.srcElement;e.src=this.errorImg,e.onerror=null},toThousands:function(t){t=(t||0).toString();var e="",i=!1;t<0&&(i=!0),t=t.replace("-","");while(t.length>3)e=","+t.slice(-3)+e,t=t.slice(0,t.length-3);return t&&(e=t+e),i&&(e="-"+e),e}},watch:{row:{deep:!0,handler:function(){this.updateView()}}}}),O=P,B=(i("fff5"),Object(x["a"])(O,E,$,!1,null,null,null)),R=B.exports,M={name:"vinput",props:["options","row"],components:{formuser:R},activated:function(){},data:function(){return{}},render:function(t){if(this.options=this.options||{fields:{userid:"",title:"",subtitle:"",username:"",money:""},info:"xxx"},this.options&&this.options.tmpl&&this.options.tmpl.template&&this.options.isCustom){var e={};if(this.options.tmpl.mixin){var i=new Function("return "+this.options.tmpl.mixin+";");e=i()}var s=f["a"].extend({mixins:[e],props:["row"],template:this.options.tmpl.template,data:function(){return{}},methods:{}});return t(s,{attrs:{row:this.row}})}return t(R,{attrs:{row:this.row,options:this.options}})},methods:{getView:function(){}},watch:{row:{deep:!0,handler:function(){}}}},V=M,A=(i("93f5"),Object(x["a"])(V,s,n,!1,null,null,null)),N=A.exports,q={name:"dypanel",components:{dycontrol:r["default"],dycardlist:S,dylistview:D["a"],formdesc:N},props:{styleId:{type:String,required:!1},title:{type:String,required:!1},layouts:{type:Array,required:!1},page:{type:Object,required:!1},info:{type:Object,required:!1},controls:{type:Object,required:!1},formItem:{type:Object,required:!1},listItems:{type:Object,required:!1},isCard:{type:Boolean,required:!1},isView:{type:Boolean,required:!1},isPageHeader:{type:Boolean,required:!1},readonly:{type:Boolean,required:!1},ctx:{type:Object,required:!1},hashDs:{type:Object,required:!1},callbackGetFilter:{type:Object,required:!1},id:{type:String,required:!1},bizOps:{type:Array,required:!1}},inject:["viewTag"],data:function(){return{cls:"",tabColor:"#388fff",activeNames:[0,1],layoutShow:!0,curLayouts:[],active:"",customIconSrc:""}},computed:{aloneCtrlCls:function(){return!this.info.fixed&&(this.isView||this.isCard||this.isPageHeader)?"newline-control":""}},mounted:function(){this.customIconSrc=window.IDP_CUSTOM_HEADER_ICON,this.updateCls()},methods:{isDetailControl:function(t){return!("vue"!=this.controls[t].type||!this.controls[t].isNewLine)||(!this.isCard&&"grid"!=this.controls[t].type||"toolbar"==this.controls[t].type||(!("upload"!=this.controls[t].type||!this.controls[t].editor_upload.isNewLine)||"wf"==this.controls[t].type))},isMainControl:function(t){return"grid"!=this.controls[t].type&&"toolbar"!=this.controls[t].type&&"vue"!=this.controls[t].type&&"upload"!=this.controls[t].type&&"wf"!=this.controls[t].type||("vue"==this.controls[t].type&&!this.controls[t].isNewLine||"upload"==this.controls[t].type&&!this.controls[t].editor_upload.isNewLine)},updateCurLayouts:function(){if("9"===this.info.type){this.curLayouts=[];var t=!0,e=!1,i=void 0;try{for(var s,n=this.layouts[Symbol.iterator]();!(t=(s=n.next()).done);t=!0){var o=s.value;o.hide||this.curLayouts.push(o)}}catch(a){e=!0,i=a}finally{try{t||null==n.return||n.return()}finally{if(e)throw i}}return this.curLayouts}},scrollTab:function(t){var e,i=this.layouts.find((function(i,s){if(i.id==t)return e=s,!0}));this.$refs.container.scrollTo(e),this.changeTab(e,i.label)},updateCls:function(){this.cls="",this.info.fixed||(this.isView?this.cls="f-flex-view":this.isCard?this.cls="f-flex-card":this.cls=""),window.IDP_CUSTOM_HEADER_ICON&&(this.cls=this.cls?this.cls+" f-header-cicon":"f-header-cicon")},updateForm:function(t,e,i,s,n){this.$emit("updateForm",t,e,i,s,n),console.log(arguments)},tabClick:function(){this.$emit("tabClick"),console.log(arguments)},getRow:function(t){return this.isDetailCtrl(t)?this.listItems[this.controls[t].table][0]:this.formItem},isDetailCtrl:function(t){return this.$parent.$parent.modelController&&this.controls[t].table&&this.controls[t].table!=this.$parent.$parent.modelController.maintable},getValue:function(t){return this.isDetailCtrl(t)?this.listItems[this.controls[t].table]&&this.listItems[this.controls[t].table].length>0?this.listItems[this.controls[t].table][0][this.controls[t].field]:"":this.formItem[this.controls[t].field]},changeTab:function(t,e){this.$emit("changeTab",t,e),console.log(arguments),this.updateTextArea(this.curLayouts[t].childs)},updateTextArea:function(t){var e=this;if(t&&t.length)for(var i=function(){var i=e.ctx.control.get(t[s]);if(i.calcTextOverFlow){if(i.isTextOverFlow)return"continue";e.$nextTick((function(){i.calcTextOverFlow()}))}},s=0;s<t.length;s++)i()},setVisible:function(t){this.layoutShow=t}},watch:{isView:function(){this.updateCls()},isCard:function(){this.updateCls()},layouts:{immediate:!0,handler:function(t){this.updateCurLayouts()}}}},z=q,U=(i("44d5"),Object(x["a"])(z,o,a,!1,null,null,null));e["a"]=U.exports},d3b7d:function(t,e,i){"use strict";var s=i("e7e7"),n=i.n(s);n.a},d5ae:function(t,e,i){"use strict";var s=i("e9fc"),n=i.n(s);n.a},d5c3:function(t,e,i){"use strict";var s=i("0b71"),n=i.n(s);n.a},d761:function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-checkbox",class:{chk:t.checkstate},on:{click:function(e){return e.stopPropagation(),t.check()}}})},n=[],o={data:function(){return{checkValue:!1}},props:{value:{type:[Boolean,String]}},mounted:function(){this.value,this.checkValue=this.value},computed:{checkstate:function(){return this.checkValue}},watch:{value:function(){this.checkValue=this.value}},methods:{check:function(){this.$emit("input",this.checkValue)}}},a=o,r=(i("3c3a"),i("2877")),l=Object(r["a"])(a,s,n,!1,null,null,null);e["a"]=l.exports},d762:function(t,e,i){},d7f5:function(t,e,i){},d8ec:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{attrs:{title:t.pageTitle},on:{backClick:t.backClick}},[i("div",{staticClass:"f-flex-iframe",attrs:{slot:"content"},slot:"content"},[i("iframe",{staticStyle:{height:"100%",width:"100%"},attrs:{src:t.src},on:{load:t.onLoad}})])])},n=[],o=(i("c975"),i("d8ad"),i("c968")),a={components:{FarrisBpage:o["a"]},data:function(){return{pageTitle:"",src:"",rlen:history.length,wfAfterSubmit:!1,controlId:""}},beforeRouteLeave:function(t,e,i){window.parent["cancelTaskCenterPopstate"]&&window.parent["cancelTaskCenterPopstate"](!0),-1!=this.src.indexOf("/platform/runtime/wf/webapp/mobile-submit/index.html#/")&&this.wfAfterSubmit&&(t.query.wfAfterSubmit=!0,this.wfAfterSubmit=!1),this.controlId&&(t.query.returnValue={controlId:this.controlId}),t.query.back=!0,i()},activated:function(){this.activePage()},created:function(){},mounted:function(){window.addEventListener("popstate",(function(){console.log(arguments)}),!1),this.activePage()},methods:{activePage:function(){this.src=this.$route.params.src,this.pageTitle=this.$route.params.title,this.controlId=this.$route.params.controlId},onLoad:function(){},backClick:function(){this.$router.go(-1)},goBack:function(t){this.wfAfterSubmit=t||!1,this.backClick()}}},r=a,l=(i("fca3"),i("2877")),c=Object(l["a"])(r,s,n,!1,null,null,null);e["default"]=c.exports},dc7e:function(t,e,i){},dd74:function(t,e,i){},dfd8:function(t,e,i){"use strict";i.r(e);var s,n,o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("farris-bpage",{ref:"pageContainer",attrs:{tab:t.tab,title:t.pageTitle,page:t.page,rightCmp:t.navbarRCmp},on:{backClick:t.backClick}},[i("div",{ref:"refLayout",staticClass:"f-form-card",class:t.pageCls,attrs:{slot:"content"},slot:"content"},[t.enableStep?i("div",{staticClass:"f-step-bar"},[i("van-steps",{attrs:{active:t.stepIndex},on:{"click-step":t.clickStep}},t._l(t.stepData,(function(e,s){return i("van-step",{key:s,scopedSlots:t._u([{key:"active-icon",fn:function(){return[i("div",{staticClass:"f-step-active-contanier"},[i("div",{staticClass:"f-step-active"},[t._v(t._s(s+1))])])]},proxy:!0},{key:"inactive-icon",fn:function(){return[i("div",{staticClass:"f-step-contanier"},[i("div",{staticClass:"f-step-normal"},[t._v(t._s(s+1))])])]},proxy:!0}],null,!0)},[t._v(" "+t._s(e.title)+" ")])})),1)],1):t._e(),t._l(t.container,(function(e,s,n){return i("div",{key:s,ref:"refCols",refInFor:!0,staticClass:"f-flex-col",class:{"f-flex-fixed":e.fixed}},[t.useHeader&&0===n?i("dyheader",{directives:[{name:"show",rawName:"v-show",value:t.isPageHeader,expression:"isPageHeader"}],ref:"pageHeader",refInFor:!0,attrs:{page:t.page,ctx:t.ctx,row:t.formItem}}):t._e(),e.pop?t._e():i("dypanel",{ref:"dypanel",refInFor:!0,attrs:{id:e.id,page:t.page,isPageHeader:t.isPageHeader,isView:t.isView,isCard:t.isCard,hashDs:t.hashDs,callbackGetFilter:t.getGridFilter,ctx:t.ctx,styleId:t.styleId,controls:t.controls,layouts:e.childs,formItem:t.formItem,listItems:t.listItems,readonly:!1,info:e,bizOps:t.bizOps},on:{updateForm:t.updateForm,changeTab:t.changeTab}})],1)})),t.showStepBar&&t.enableStep?i("div",[i("vtoolbar",{attrs:{styleId:t.styleId,data:t.setpBtns},on:{buttonClick:t.stepButtonClick}})],1):t._e(),t._l(t.popupConfig,(function(e,s){return i("div",{key:s},[i("van-popup",{style:e.style,attrs:{position:e.position,closeable:e.closeable,"close-on-click-overlay":e.closeOnClickOverlay,"lazy-render":t.lazyRender,round:e.round},model:{value:e.show,callback:function(i){t.$set(e,"show",i)},expression:"item.show"}},[i("dypanel",{ref:"dypanel",refInFor:!0,attrs:{ctx:t.ctx,styleId:t.styleId,controls:t.controls,layouts:e.childs,formItem:t.formItem,listItems:t.listItems,readonly:!1,info:e},on:{updateForm:t.updateForm}})],1)],1)})),i("vdialog",{ref:"dialog",attrs:{ctx:t.ctx}}),i("div",[i("van-action-sheet",{attrs:{actions:t.wfProcess,description:t.getLang("card_wfChoose")},on:{select:t.selectWf},model:{value:t.wfChoose,callback:function(e){t.wfChoose=e},expression:"wfChoose"}}),i("van-popup",{model:{value:t.wfUserChoose,callback:function(e){t.wfUserChoose=e},expression:"wfUserChoose"}},[i("div",{staticStyle:{padding:"30px"}},[i("div",{staticStyle:{"padding-bottom":"10px","font-size":"18px"}},[t._v(t._s(t.getLang("card_userChoose")))]),i("van-checkbox-group",{model:{value:t.resultUser,callback:function(e){t.resultUser=e},expression:"resultUser"}},t._l(t.wfUser,(function(e,s){return i("div",{key:s,staticStyle:{"padding-bottom":"6px"}},[i("div",{staticStyle:{padding:"6px"}},[i("van-checkbox",{attrs:{name:e.Id}},[t._v(t._s(e.Name))])],1)])})),0),i("van-button",{attrs:{type:"default",block:""},on:{click:t.confirmSubmitUserForCloud}},[t._v(t._s(t.getLang("card_confirm")))])],1)])],1),"float"==t.floatBtn.type&&t.showFloatIcon?i("circle-menu",{ref:"menu",staticClass:"f-float-btn",attrs:{styleId:t.styleId,icon:t.currentFloatBtn.item1.icon,name:t.currentFloatBtn.item1.name,type:"top",number:t.currentFloatBtn.btnLength,circle:"",floatBtn:t.currentFloatBtn},on:{btnClick:t.handleMenuClick}},[i("a",{attrs:{slot:"item_1"},slot:"item_1"},[t.currentFloatBtn.item1.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item1.name)+" ")]),t.currentFloatBtn.item1.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item1.icon}}):t._e()],1),i("a",{attrs:{slot:"item_2"},slot:"item_2"},[t.currentFloatBtn.item2.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item2.name)+" ")]),t.currentFloatBtn.item2.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item2.icon}}):t._e()],1),i("a",{attrs:{slot:"item_3"},slot:"item_3"},[t.currentFloatBtn.item3.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item3.name)+" ")]),t.currentFloatBtn.item3.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item3.icon}}):t._e()],1),i("a",{attrs:{slot:"item_4"},slot:"item_4"},[t.currentFloatBtn.item4.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item4.name)+" ")]),t.currentFloatBtn.item4.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item4.icon}}):t._e()],1),i("a",{attrs:{slot:"item_5"},slot:"item_5"},[t.currentFloatBtn.item5.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item5.name)+" ")]),t.currentFloatBtn.item5.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item5.icon}}):t._e()],1),i("a",{attrs:{slot:"item_6"},slot:"item_6"},[t.currentFloatBtn.item6.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item6.name)+" ")]),t.currentFloatBtn.item6.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item6.icon}}):t._e()],1),i("a",{attrs:{slot:"item_7"},slot:"item_7"},[t.currentFloatBtn.item7.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item7.name)+" ")]),t.currentFloatBtn.item7.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item7.icon}}):t._e()],1),i("a",{attrs:{slot:"item_8"},slot:"item_8"},[t.currentFloatBtn.item8.icon?t._e():i("span",[t._v(t._s(t.currentFloatBtn.item8.name)+" ")]),t.currentFloatBtn.item8.icon?i("van-icon",{staticClass:"f-circle-vanticon",attrs:{name:t.currentFloatBtn.item8.icon}}):t._e()],1)]):t._e(),"action"==t.floatBtn.type?i("action-menu",{staticClass:"f-float-btn",attrs:{styleId:t.styleId,actions:t.floatBtn.actions.filter((function(t){return!t.hide}))}}):t._e()],2),i("div",{attrs:{slot:"tab"},slot:"tab"},[t._t("tab")],2)])},a=[],r=(i("a4d3"),i("e01a"),i("d28b"),i("99af"),i("4de4"),i("7db0"),i("4160"),i("caad"),i("c975"),i("d81d"),i("a434"),i("b64b"),i("d3b7"),i("ac1f"),i("2532"),i("3ca3"),i("1276"),i("2ca0"),i("159b"),i("ddb0"),i("96cf"),i("1da1")),l=i("4232"),c=i("3c69"),h=i("2241"),d=i("d399"),u=i("91f4"),f=i.n(u),p=i("c6e7"),m=i.n(p),g=i("d362"),v=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"f-form-header",class:t.hBottomCls},[t.onlyRenderComps?i("div",{staticClass:"f-header-info"},t._l(t.hcomponents,(function(e,s){return i("div",{key:s},[e.type==t.COMP_TYPE.remark_subject&&e.display?i("hremark-subject",{staticClass:"f-header-remark",attrs:{row:t.row,remark:e}}):t._e(),e.type==t.COMP_TYPE.vue&&e.display?i("hremark-vue",{staticClass:"f-header-remark",attrs:{row:t.row,options:e}}):t._e(),e.type==t.COMP_TYPE.board&&e.display?i("hboard",{ref:e.id,refInFor:!0,attrs:{vprops:e,ctx:t.ctx}}):t._e()],1)})),0):t._e(),t.useIndexBar?i("div",[i("hindexbar",{ref:"indexBar",attrs:{data:t.barItems}})],1):t._e()])},b=[],w=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"f-remark-subject"},[t._v(t._s(t.reamrk_doc_title))]),i("div",{staticClass:"f-remark-subtitle"},[i("img",{staticClass:"user-avatar",attrs:{src:t.src},on:{error:t.getDefaultImg}}),i("div",{staticClass:"user-name"},[i("span",[t._v(t._s(t.user_name)+t._s(t.user_deptname?" | "+t.user_deptname:""))])]),i("div",{staticClass:"doc-date"},[t._v(t._s(t.doc_date))])])])},y=[],C=(i("466d"),i("c466")),k={props:{row:{type:Object,required:!0},remark:{type:Object,required:!0}},data:function(){return{src:"",BASE_USER_SRC:"/api/fastdweb/runtime/v1.0/Common/getUserAvatar?userId=",errorImg:"/platform/runtime/sys/web/assets/img/avatar-default.png",reamrk_doc_title:"",doc_date:"",user_name:"",user_deptname:""}},methods:{updateView:function(){this.initRemark(),this.row[this.remark.fields.sysuser_id]&&(this.src=this.BASE_USER_SRC+this.row[this.remark.fields.sysuser_id]),this.user_name=this.row[this.remark.fields.user_name],this.user_deptname=this.row[this.remark.fields.user_deptname];var t=this.row[this.remark.fields.doc_subject],e=this.remark.fields.doc_title||"",i=e.length;e.match(/\{Lang.(\w+)\}/)&&i>7&&(e=window.idp.lang.get(e.substring(6,i-1))),t&&e&&(this.reamrk_doc_title=window.idp.lang.get("reamrk_doc_title",[t,e]));var s=this.row[this.remark.fields.doc_date];s&&this.remark.fields.doc_date_format?this.doc_date=C["a"].dateFormat(s,this.remark.fields.doc_date_format):this.doc_date=s},initRemark:function(){this.src=this.errorImg,this.user_name="",this.user_deptname="",this.reamrk_doc_title="",this.doc_date=""},getDefaultImg:function(t){var e=t.srcElement;e.src=this.errorImg,e.onerror=null}},watch:{row:{deep:!0,handler:function(){this.updateView()}}}},x=k,I=(i("75bd"),i("2877")),_=Object(I["a"])(x,w,y,!1,null,"8f35f0bc",null),T=_.exports,F=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible,expression:"visible"}],class:t.calcClass(),staticStyle:{display:"flex",padding:"10px 0 0 0"}},t._l(t.data,(function(e,s){return i("div",{key:s,staticClass:"f-index-case",class:t.calcCaseClass(s),attrs:{id:"index_"+s}},[i("div",[i("span",{staticClass:"f-index-title"},[t._v(t._s(t.getLabel(e)+": "))]),i("span",{staticClass:"f-index-val",style:"color:"+e.color||!1},[t._v(t._s(e.value))]),i("span",{staticClass:"f-index-currency"},[t._v(t._s(e.currency))]),i("span",{staticClass:"f-index-label"},[t._v(t._s(t.getLabel(e)))])])])})),0)},L=[],S=(i("fb6a"),i("a9e3"),{name:"hboard",props:["vprops","ctx"],data:function(){return{visible:!0,data:[]}},mounted:function(){this.vprops.data&&this.vprops.data.length?this.initData(!0):this.visible=!1},methods:{setVal:function(t,e){var i=Number(t.slice(4)),s=this.data[i];if(e){if(s.thousand){e+="";var n=e.split("."),o=2==n.length?n[1].length:0;e=window.idp.utils.currency(e,o)}s.value=e}else s.value=s.empty},setCurrency:function(t,e){var i=Number(t.slice(9));this.data[i].currency=e},initData:function(t){this.data=[];var e=this;this.vprops.data.forEach((function(i,s){i.value=i.value||i.empty||"",t&&(i.express&&e.ctx.express.addExpress(e.vprops.id,"val_"+s,i.express,e.vprops.id,!1,"",!0,!0),i.currencyExpress&&e.ctx.express.addExpress(e.vprops.id,"currency_"+s,i.currencyExpress,e.vprops.id,!1,"",!0,!0)),e.data.push(JSON.parse(JSON.stringify(i)))})),this.$forceUpdate()},calcClass:function(){var t=this.data&&this.data.length?this.data.length:0;switch(t){case 1:return"f-index-single";case 2:return"f-index-double";case 3:return"f-index-trible";default:return""}},calcCaseClass:function(t){return 0==t?"f-index-first":t==this.data.length-1?"f-index-last":""},getLabel:function(t){var e=t.label||"",i=e.length;return e.match(/\{Lang.(\w+)\}/)&&i>7?window.idp.lang.get(e.substring(6,i-1)):e}}}),D=S,E=(i("77bf"),Object(I["a"])(D,F,L,!1,null,null,null)),$=E.exports,P=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("vsticky",{ref:"sticky",attrs:{offsetTop:t.offsetTop},on:{change:t.fixedChange,scroll:t.scrollChange}},[i("div",{staticClass:"f-header-indexbar"},[t._l(t.data,(function(e,s){return i("div",{key:s,ref:e.id,refInFor:!0,staticClass:"baritem",class:{active:s==t.curItemIndex},on:{click:function(i){return t.clickIndexBar(e,s)}}},[i("span",[t._v(t._s(e.name))])])})),i("div",{ref:"bottomline",staticClass:"f-header-indexbar-bottomline"})],2)])],1)},O=[],B=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{style:t.holderHeight},[i("div",{ref:"sticky",staticClass:"f-bpage-sticky",class:{fixed:t.fixed},style:t.stickyStyle},[t._t("default")],2)])},R=[],M=i("1172"),V={props:{offsetTop:{type:[Number,String],default:0}},data:function(){return{fixed:!1,height:0,scroller:null}},beforeDestroy:function(){this.scroller&&this.onScroll&&this.scroller.removeEventListener("scroll",this.onScroll)},mounted:function(){this.scroller=Object(M["a"])(this.$el),this.scroller.addEventListener("scroll",this.onScroll)},computed:{holderHeight:function(){return{height:this.fixed?"".concat(this.height,"px"):null}},stickyStyle:function(){var t={};return this.fixed&&this.offsetTop&&(t.top="".concat(this.offsetTop,"px")),t}},methods:{onScroll:function(){var t=this;this.height=this.$el.offsetHeight;var e=this.scroller.scrollTop,i=this.getElementTop(),s=function(){t.$emit("scroll",{scrollTop:e,isFixed:t.fixed})};i<=this.offsetTop?this.fixed=!0:this.fixed=!1,s()},getElementTop:function(){return this.$el.getBoundingClientRect().top}},watch:{fixed:function(t){this.$emit("change",t)}}},A=V,N=(i("b19d"),Object(I["a"])(A,B,R,!1,null,null,null)),q=N.exports,z="PAGE_TOP",U={components:{vsticky:q},props:{data:{type:Array,required:!0}},inject:["isCardScrollRelocation"],data:function(){return{curItem:{},curItemIndex:0,pageScroller:null,offsetTop:0}},mounted:function(){this.pageScroller=this.getPageScroller(),this.offsetTop=document.getElementById("f-main-bpage").offsetTop,this.data.length&&this.selectBarItem(0,!0)},methods:{clickIndexBar:function(t,e){this.clicking=!0,this.selectBarItem(e),this.scrollTo(t.id);var i=this;setTimeout((function(){i.clicking=!1}),1e3)},selectBarItem:function(t,e){var i=this;this.curItem=this.data[t],this.curItemIndex=t,this.$nextTick((function(){i.moveBottomLine(i.curItem.id,e)}))},moveBottomLine:function(t,e){var i=this.$refs[t][0].offsetLeft-10+this.$refs[t][0].offsetWidth/2-14;this.$refs.bottomline.style.transform="translateX(".concat(i,"px)"),this.$refs.bottomline.style.transitionDuration=e?"0s":"0.3s"},scrollTo:function(t){if(t!=z){var e=document.getElementById(t);if(e){var i=this.pageScroller.scrollTop+e.getBoundingClientRect().top-this.$refs.sticky.$el.offsetHeight-this.offsetTop;this.pageScroller.scrollTo({top:i,behavior:"smooth"})}}else this.pageScroller.scrollTo({top:0,behavior:"smooth"})},fixedChange:function(t){console.log("isFixed:".concat(t))},scrollChange:function(t){var e=this.isCardScrollRelocation&&this.isCardScrollRelocation();if(!e&&!this.clicking&&t.isFixed&&this.data[0].id==z){var i=this.data[1].id,s=document.getElementById(i).getBoundingClientRect().top;s>70+this.offsetTop?this.selectBarItem(0):s<50+this.offsetTop&&this.selectBarItem(1)}},getPageScroller:function(){return Object(M["a"])(this.$el)},reset:function(){this.$refs.sticky.fixed=!1,this.data.length&&this.selectBarItem(0,!0)}}},j=U,H=(i("85d9"),Object(I["a"])(j,P,O,!1,null,null,null)),W=H.exports,G=i("a026"),J={props:["options","row"],render:function(t){if(this.options&&this.options.tmpl&&this.options.tmpl.template){var e={};if(this.options.tmpl.mixin){var i=new Function("return "+this.options.tmpl.mixin+";");e=i()}var s=G["a"].extend({mixins:[e],props:["row"],template:this.options.tmpl.template,data:function(){return{}},methods:{}});return t(s,{attrs:{row:this.row}})}return t("span","no custom template content")}},K=J,X=Object(I["a"])(K,s,n,!1,null,null,null),Q=X.exports,Y={components:{hremarkSubject:T,hboard:$,hindexbar:W,HremarkVue:Q},props:{page:{type:Object,required:!0},ctx:{type:Object,required:!0},row:{type:Object,required:!0}},data:function(){return{onlyRenderComps:!1,hcomponents:[],useIndexBar:!1,COMP_TYPE:{remark_subject:"remark_template_subject",vue:"vue",board:"board"},dynamicVueComps:{},vueView:null,hBottomCls:""}},mounted:function(){this.hcomponents=this.page.headerConfig.components,this.onlyRenderComps=this.page.headerConfig.onlyComponents,this.useIndexBar=this.page.useIndexBar,this.barItems=[{id:"PAGE_TOP",name:this.getLang("basic_info")},{id:"wfBlock",name:this.getLang("wflog_wl")}],this.updateCls()},methods:{getCtrl:function(t){return this.$refs[t]?this.$refs[t][0]:{}},getLang:function(t,e){return window.idp.lang.get(t,e)},updateCls:function(){this.useIndexBar||(this.hBottomCls="f-header-bottom")},initBoardData:function(){var t=this;this.hcomponents.forEach((function(e){e.type==t.COMP_TYPE.board&&t.getCtrl(e.id)&&t.getCtrl(e.id).initData&&t.getCtrl(e.id).initData()}))},resetIndexBar:function(){this.$refs.indexBar&&this.$refs.indexBar.reset()}}},Z=Y,tt=(i("fcd6"),Object(I["a"])(Z,v,b,!1,null,null,null)),et=tt.exports,it=i("c968"),st=i("d8ad"),nt=i("c956"),ot=i("1b34"),at=i("154e"),rt=i("45fd"),lt=i("810a"),ct=i("c0bb"),ht=i("7a3b"),dt=i("3daa"),ut=i("1968"),ft=i("7342"),pt=i("0141"),mt=i("3f09"),gt=i("74b9"),vt=i("0771"),bt=i("2870"),wt=i("8c1b"),yt=i("4901"),Ct=(i("72fb"),i("2f04")),kt=window.idp.Notify,xt={},It={},_t=null,Tt={},Ft={components:{dyheader:et,dypanel:g["a"],vtoolbar:l["a"],FarrisBpage:it["a"],CircleMenu:bt["a"],ActionMenu:wt["a"],vdialog:Ct["a"]},props:["tab","viewTag"],provide:function(){return{viewTag:this.getViewTag,isCardScrollRelocation:this.getScrollRelocation}},watch:{stepIndex:function(){this.setStepVisible()},status:function(t,e){"edit"==t&&"add"==e&&(this.addSave=!0),this.initStep()},floatBtn:{deep:!0,handler:function(t){this.updateCurFloatBtn()}},$route:{immediate:!0,handler:function(t,e){t&&t.path&&t.path.indexOf("recorddetail")>-1?this.beforeRouteEnter4WF(t,e):e&&e.path&&e.path.indexOf("recorddetail")>-1&&this.beforeRouteLeave4WF(t,e)}},listItem:{deep:!0,handler:function(t,e){for(var i=0,s=Object.keys(t);i<s.length;i++){var n=s[i];if(!e[n])return;t[n].length<e[n].length&&this.express.triggerALL("detailBack")}}},pageTitle:{immediate:!0,handler:function(t){window.document.title=t}}},computed:{listItem:function(){return JSON.parse(JSON.stringify(this.listItems))},pageCls:function(){var t=[];return this.page.flex&&t.push("f-flex-wrap"),this.overlay&&t.push("f-flex-overlay"),(this.page.block||"en"==window.idp.lang.getLang().id)&&t.push("f-flex-block"),this.page.useHeader&&this.isView&&t.push("f-header-wrap"),t},showStepBar:function(){return this.stepIndex!==this.stepData.length-1},isPageHeader:function(){return this.isView&&this.useHeader}},mixins:[vt["a"]],data:function(){return{stepIndex:0,enableStep:!1,setpBtns:[],stepData:[],pageLoading:!1,isView:!1,useHeader:!1,isCard:!1,version:"",isCustom:!0,lazyRender:!1,ctx:{},pageTitle:"",styleId:"",uistate:"",actionState:"",show:!0,name:"card",dataId:"",status:"view",formItem:{},listItems:{},schema:[],page:{},code:"",controls:{},container:{},hashDs:{},uiConfig:{},currentFloatBtn:{type:"",btnLength:8,item1:{name:"",icon:"",click:"",id:"",hide:!1},item2:{name:"",icon:"",click:"",id:"",hide:!1},item3:{name:"",icon:"",click:"",id:"",hide:!1},item4:{name:"",icon:"",click:"",id:"",hide:!1},item5:{name:"",icon:"",click:"",id:"",hide:!1},item6:{name:"",icon:"",click:"",id:"",hide:!1},item7:{name:"",icon:"",click:"",id:"",hide:!1},item8:{name:"",icon:"",click:"",id:"",hide:!1},actions:[]},floatBtn:{type:"",btnLength:8,item1:{name:"",icon:"",click:"",id:"",hide:!1},item2:{name:"",icon:"",click:"",id:"",hide:!1},item3:{name:"",icon:"",click:"",id:"",hide:!1},item4:{name:"",icon:"",click:"",id:"",hide:!1},item5:{name:"",icon:"",click:"",id:"",hide:!1},item6:{name:"",icon:"",click:"",id:"",hide:!1},item7:{name:"",icon:"",click:"",id:"",hide:!1},item8:{name:"",icon:"",click:"",id:"",hide:!1},actions:[]},wfChoose:!1,wfProcess:[],wfUserChoose:!1,wfUser:[],resultUser:[],activeStepCtrls:[],keyboardShow:!1,overlay:!1,splitTitleList:[],showFloatIcon:!1,hasloadWfScript:!1,addSave:!1,langResources:{},locale:{},bizOps:[],lockTimes:0,lockIndex:void 0,locked:!1,scrollRelocation:!1,navbarRCmp:null}},activated:function(){var t=this.dataId;if(this.from=Tt,this.activeExpress(),this.$route.query.detailReturn){var e=this.$route.query.detailReturn,i=this.listItems[e.table];if(i)if(-1==e.index)i.push(e.row);else for(var s in e.row)i[e.index][s]=e.row[s];this.express.triggerALL("detailBack"),this.express.triggerLogicAll(),this.event.emitGlobal("afterDetailBack",[e])}if(this.$route.query.custom&&(this.isCustom=!0),!this.$route.query.back){if("editwithlock"==this.status&&this.unLock(),this.dataId=this.$route.query.dataId,this.status=this.$route.query.status,this.dataId&&"add"!=this.status){if("loaded"==this.uistate){if(this.pageLoading=!0,console.log("初始空数据结构"),this.isPageHeader&&this.$refs.pageHeader){var n=this.$refs.pageHeader;Array.isArray(n)&&(n=n[0]),n.initBoardData()}var o=lt["a"].getSchemaStruct(this.schema);this.formItem=o.formItem,this.listItems=o.listItems,this.loadData()}}else this.addData();this.clearErrorMessage(),this.actionState=""}this.activeScrollRelocation(t),this.pageTitle&&(document.title=this.pageTitle),this.$route.query.wfAfterSubmit&&(this.event.emitGlobal("submitSuccess",[this]),this.loadData(),kt({type:"success",message:this.getLang("card_submitSuccess")})),this.$route.query.back||this.activeStatus()},beforeRouteEnter:function(t,e,i){console.log("路由进来!"),It[t.name]||xt[t.name]||(xt[t.name]=!0,It[t.name]=e.name),Tt.query=JSON.parse(JSON.stringify(e.query)),Tt.fullpath=e.fullPath,Tt.name=e.name,i()},beforeRouteLeave:function(t,e,i){var s=arguments,n=this;return Object(r["a"])(regeneratorRuntime.mark((function o(){var a,r,l;return regeneratorRuntime.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(a=!0,r=!1,!n.event){o.next=10;break}if(a=n.event.emitGlobal("beforeRouteLeave",[t,e,i]),t.name&&t.name===It[e.name]&&(r=!0,a=n.event.emitGlobal("pageBack",[n.name,n.styleId,!0])),!a||!a.then){o.next=10;break}return o.next=8,a;case 8:l=o.sent,a=l;case 10:if(0!=a){o.next=13;break}return i(!1),o.abrupt("return");case 13:"editwithlock"==n.status&&r&&n.unLock(),n.clearWatermark(!0),n.$route.query&&n.$route.query.wfviewpagedeep&&window.parent["cancelTaskCenterPopstate"]&&window.parent["cancelTaskCenterPopstate"](!0),console.log(s),n.scrollRef&&(n.scroll=n.scrollRef.scrollTop),i();case 19:case"end":return o.stop()}}),o)})))()},mounted:function(){var t=this;console.log(this.$route),this.styleId=this.$route.meta.styleId,_t=new nt["b"](this.styleId),this.dataId=this.$route.query.dataId||"",this.status=this.$route.query.status||"add",this.service=new nt["b"](this.styleId),this.$route.query.workItemId,this.dealSetContextYear(),this.styleId&&this.dataId&&"add"!=this.status&&this.loadFirstData(),"en"==window.idp.lang.getLang().id?c["a"].use("en-US",f.a):"zh-CHT"==window.idp.lang.getLang().id&&c["a"].use("zh-TW",m.a),st["a"].$on("component-change",(function(e,i,s,n,o,a){return o&&t.styleId!=o?null:t.viewTag!==a?null:t.event.emit(e,i,s,n)})),this.loadUIComponent(),this.isWeiXin()&&yt["a"].initConfig()},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},initLangResources:function(){this.setpBtns=[{id:"back",title:"",name:this.getLang("card_stepBack"),style:"6",icon:"arrow-left",hide:!1},{id:"next",title:"",name:this.getLang("card_stepNext"),style:"7"}],this.langResources=window.idp.lang.initLangResources(this.styleId),this.pageTitle=this.langResources[this.name+"_title"]?this.langResources[this.name+"_title"]:this.pageTitle},setTabHide:function(t,e,i){var s=this.container[t].childs.find((function(t){return t.id==e}));s&&(s.hide=i,this.control.getLayout(t).updateCurLayouts())},scrollTab:function(t,e){this.control.getLayout(t).scrollTab(e)},dealSetContextYear:function(){var t=window.idp.utils.getQuery("year")||this.$route.query.year||"";t&&"null"!=t&&4==t.length&&(console.log(t),window.idp.context.setYear(t))},getViewTag:function(){return this.viewTag},getScrollRelocation:function(){return this.scrollRelocation},activeScrollRelocation:function(t){var e=this;this.scrollRef&&this.dataId==t?(this.scrollRelocation=!0,this.scrollRef.scrollTop=this.scroll,setTimeout((function(){e.scrollRelocation=!1}),100),0==this.scrollRef.scrollTop&&this.resetHeaderIndexBar()):this.resetHeaderIndexBar()},resetHeaderIndexBar:function(){this.$refs.pageHeader&&this.$refs.pageHeader[0]&&this.$refs.pageHeader[0].resetIndexBar()},beforeRouteEnter4WF:function(t,e){e&&(Tt.fullpath=e.fullPath,Tt.name=e.name,It[t.name]||xt[t.name]||(xt[t.name]=!0,It[t.name]=e.name)),Tt.query=JSON.parse(JSON.stringify(t.query))},toggleMenu:function(t){this.$refs.menu.toggle(t)},beforeRouteLeave4WF:function(t,e){var i=arguments,s=this;return Object(r["a"])(regeneratorRuntime.mark((function n(){var o,a,r;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(o=!0,a=!1,!s.event){n.next=10;break}if(o=s.event.emitGlobal("beforeRouteLeave",[t,e]),t.name&&t.name===It[e.name]&&(a=!0,o=s.event.emitGlobal("pageBack",[s.name,s.styleId,!0])),!o||!o.then){n.next=10;break}return n.next=8,o;case 8:r=n.sent,o=r;case 10:if(0!=o){n.next=12;break}return n.abrupt("return");case 12:"editwithlock"==s.status&&a&&s.unLock(),console.log(i),s.scrollRef&&(s.scroll=s.scrollRef.scrollTop);case 15:case"end":return n.stop()}}),n)})))()},clearErrorMessage:function(){for(var t in this.controls){var e=this.control.get(t);e&&e.setErrorMessage&&e.setErrorMessage("")}},clickStep:function(t){var e=this,i=this.event.emit("beforeStepChange","all","all",[this,this.stepIndex,t]);0!=i&&(this.stepIndex<t&&this.page.isStepCheck?this.stepCheck().then((function(i){i||(e.stepIndex=t)})):this.stepIndex=t,this.event.emit("stepChange","all","all",[this,t]))},initStep:function(){if(this.page.tabs&&this.page.tabs.length&&!this.page.isStep){var t=[];this.page.tabs.forEach((function(e){var i=[];e.fields&&(i=e.fields.split(",")),t.push({title:e.title,childs:i})})),this.stepData=t,this.enableStep=!0,"wfview"!=this.status&&"wf"!=this.status||(this.enableStep=!1),this.$route.query.back||this.addSave||(this.stepIndex=0),this.setStepVisible(),this.event.emit("afterStepChange","all","all",[this])}},initExtendStepBtn:function(){var t=this;if(this.page&&this.page.stepbarExtend&&this.actionCtrls&&this.actionCtrls.toolbars){var e,i=!0,s=!1,n=void 0;try{for(var o,a=this.actionCtrls.toolbars[Symbol.iterator]();!(i=(o=a.next()).done);i=!0){var r=o.value;if(e=this.controls[r]&&this.controls[r].btns&&this.controls[r].btns.find((function(e){return e.id==t.page.stepbarExtend})),e){e.srcCtrl=r;break}}}catch(c){s=!0,n=c}finally{try{i||null==a.return||a.return()}finally{if(s)throw n}}if(e){console.log(e);var l={hide:!1,icon:e.icon,id:e.id,name:e.name,style:"6",title:"",script:e.script,srcCtrl:e.srcCtrl};this.setExtendStepBtn(l)}}},clearExtendStepBtn:function(){3==this.setpBtns.length&&this.setpBtns.pop()},setExtendStepBtn:function(t){t&&(this.clearExtendStepBtn(),this.setpBtns.push(t))},stepButtonClick:function(t,e){var i=this,s="back"==t.id?this.stepIndex-1:"next"==t.id?this.stepIndex+1:this.stepIndex,n=this.event.emit("beforeStepChange","all","all",[this,this.stepIndex,s,t]);0!=n&&("back"==t.id?this.step(!1):"next"==t.id?this.page.isStepCheck?this.stepCheck().then((function(t){t||i.step(!0)})):i.step(!0):(t.script||t.srcCtrl)&&st["a"].$emit("component-change","click","toolbar",t.srcCtrl,[t,void 0,t.srcCtrl],this.styleId,void 0),console.log(arguments))},stepCheck:function(t){var e=this,i=this.stepIndex,s=this.stepData.find((function(t,e){return e==i}));this.activeStepCtrls=[];var n=this.modelController.getSaveData(this.formItem,this.listItems),o=new Promise((function(o,a){e.validate.validateDataTable(n,e.mainFields,(function(n,a){e.unFoldSplitTitle();var r=!1;if(!n){var l=!1;s.childs.includes("main")&&(l=!0);var c=!1;i!=e.stepData.length-1&&e.enableStep||(c=!0),c&&0!=s.childs.length&&e.enableStep&&(c=!1);var d="",u=0;for(var f in a)if(a[f].errorKeys.length)if(f==e.modelController.maintable||""==f)for(var p in a[f].errorKeys){var m=e.control.get(e.getMapCtrl(a[f].errorKeys[p],e.modelController.maintable));m&&m.setErrorMessage&&(s.childs.includes(a[f].errorKeys[p])||l||c)&&(m.setErrorMessage(a[f].message[p]),u+=1,d+=a[f].message[p]+"\n",r=!0)}else{var g="grid_"+f,v=e.control.get(g);if(s.childs.includes(g)){v.clearValidateMessage();for(var b in a[f].errorKeys){var w=a[f].breakIndex[b];e.listItems[f][w].__validate=!0,0==b&&(e.listItems[f][w].__validateMessage=""),e.listItems[f][w].__validateMessage+=a[f].message[b]+"\n",v.vprops.title?(u+=1,d+=v.vprops.title+a[f].message[b]+"\n"):(u+=1,d+=a[f].message[b]+"\n"),r=!0}}}""!=d&&!1!==t&&h["a"].alert({message:e.getLang("card_validateFail",{0:u})})}o(r)}),!0)}));return o},step:function(t){if(t){if(this.stepIndex==this.stepData.length-1)return;this.stepIndex++}else{if(0==this.stepIndex)return;this.stepIndex--}this.event.emit("stepChange","all","all",[this,this.stepIndex])},setStepVisible:function(){var t=this.stepIndex,e=this.stepData.find((function(e,i){return i==t}));this.activeStepCtrls=[],this.setpBtns[0].hide=0==t;var i=!1;e.childs.includes("main")&&(i=!0);var s=!1;for(var n in t!=this.stepData.length-1&&this.enableStep||(s=!0),s&&0!=e.childs.length&&this.enableStep&&(s=!1),this.actionCtrls.controls){var o=this.actionCtrls.controls[n],a=this.control.get(o);if(a&&a.setVisible){if("vue"==a.$parent.type&&e.childs.includes(o)||s)a.setShow(!0);else if("vue"==a.$parent.type){a.setShow(!1);continue}if("upload"==a.$parent.type&&e.childs.includes(o)||s)a.setShow(!0);else if("upload"==a.$parent.type){a.setShow(!1);continue}e.childs.includes(o)||i||s?a.setShow(!0):a.setShow(!1)}}for(var r in this.actionCtrls.grids){var l=this.actionCtrls.grids[r],c=this.control.get(l);c&&c.setVisible&&(e.childs.includes(l)||s?c.setShow(!0):c.setShow(!1))}for(var h in this.actionCtrls.toolbars){var d=this.actionCtrls.toolbars[h],u=this.control.get(d);"toolbar"==d&&(!s&&t!=this.stepData.length-1||"wfview"===this.status?u&&u.setVisible(!1):u&&u.setVisible(!0))}this.initExtendStepBtn()},isWeiXin:function(){var t=navigator.userAgent.toLowerCase();return-1!=t.indexOf("micromessenger")},setUIReadonly:function(t,e){for(var i in console.log(this.actionCtrls),this.actionCtrls.inputs){var s=this.actionCtrls.inputs[i],n=this.control.get(s);n&&n.setDisabled&&n.setDisabled(t)}for(var o in this.actionCtrls.toolbars){var a=this.actionCtrls.toolbars[o],r=this.control.get(a);null!=r&&(r.setDisabled&&r.setDisabled(t),e?r.setVisible(!1):0==e&&r.setVisible(!0))}for(var l in this.actionCtrls.grids){var c=this.actionCtrls.grids[l],h=this.control.get(c);null!==h&&(h.setDisabled&&h.setDisabled(t),h.setToolBarDisabled&&h.setToolBarDisabled(t),e?h.setToolBarVisible&&h.setToolBarVisible(!t):0==e&&h.setToolBarVisible&&h.setToolBarVisible(!0))}this.actionState=t?"view":""},afterResources:function(){var t=this;this.initView((function(){var e=t.event.emit("viewReady","all","all",[t]);e||(t.showFloatIcon=!0),t.event.emit("pageActive","all","all",[t]),t.activeStatus()}))},activeResurces:function(){var t=this;this.moduleManager=new gt["a"](this.styleId,this.version),this.page.noScript?this.afterResources():this.moduleManager.execModule([this.code+"/card"],(function(e,i){e&&e.init(t),i&&i.init(t),t.afterResources()}),this.isCustom,this.customCode)},activeStatus:function(){var t=this,e=this;this.addSave=!1,this.initStep(),"wfview"==this.status?(window.IDP_EXTEND_WATERMARK&&e.context&&e.context.get&&e.setWatermark(e.context.get("UserName")),this.isView=!0,this.isCard=!0):(this.isView=!1,this.isCard="card"==this.page.cardType);var i=function(t){t.initStep(),t.express&&(t.express.triggerReadALL(),t.express.triggerRequireALL(),t.express.triggerHideALL(),t.express.triggerLogicAll()),t.event&&t.event.emitGlobal("afterActiveStatus",[e])};this.$nextTick((function(){"wf"==e.status||"wfview"==e.status?(e.setUIReadonly(!0,!0),i(e)):"view"==t.status?(e.setUIReadonly(!0,!1),i(e)):"editwithlock"!=t.status?(e.setUIReadonly(!1,!1),i(e)):e.lockData(e,i)}))},lockData:function(t,e){t=t||this,t.locked||(t.locked=!0,t.service.editCardData(t.styleId,t.dataId).then((function(i){"ok"==i.Code?(console.log("数据锁占用成功。"),t.lockTimes++,t.setUIReadonly(!1,!1),t.lockIndex=setInterval((function(){t.service.editCardData(t.styleId,t.dataId).then((function(e){if("ok"==e.Code){if(console.log("数据锁第"+t.lockTimes+"次续期成功，当前占用继续保持。"),t.lockTimes++,t.setUIReadonly(!1,!1),t.lockTimes>=7){t.locked=!1,t.lockTimes=0,clearInterval(t.lockIndex);var i=function(){t.alert(t.getLang("alert_lockdata")),t.setUIReadonly(!0,!1)};setTimeout(i,59e4)}}else h["a"].alert({message:t.getLang("err_lockdata",{0:e.Message})}),console.log("数据锁第"+t.lockTimes+"次续期失败,当前占用已解除。"),t.locked=!1,t.lockTimes=0,clearInterval(t.lockIndex),t.setUIReadonly(!0,!1)}))}),59e4),e&&e(t)):(h["a"].alert({message:t.getLang("err_lockdata",{0:i.Message})}),console.log("当前编辑数据已被上锁："+i.Message),clearInterval(t.lockIndex),t.locked=!1,t.setUIReadonly(!0,!1),e&&e(t))}),(function(i){console.log("失败"+i),e&&e(t)})))},changeViewStatus:function(t){t?(this.status="wfview",window.IDP_EXTEND_WATERMARK&&this.setWatermark(this.context.get("UserName")),this.isView=!0,this.isCard=!0,this.activeStatus()):(this.status="edit",this.isView=!1,this.isCard=!1,this.activeStatus())},initColspan:function(){for(var t=0,e=Object.keys(this.controls);t<e.length;t++){var i=e[t];if("grid"==this.controls[i].type&&this.controls[i].cols&&this.controls[i].cols.length){var s=!0,n=!1,o=void 0;try{for(var a,r=this.controls[i].cols[Symbol.iterator]();!(s=(a=r.next()).done);s=!0){var l=a.value;l.isfield||"input"!=this.controls[this.controls[i].id+"."+l.id].type||(this.controls[this.controls[i].id+"."+l.id].type="label")}}catch(c){n=!0,o=c}finally{try{s||null==r.return||r.return()}finally{if(n)throw o}}}}},activeLogicExpress:function(){if(this.express)for(var t in this.basic.logic)"1"==this.basic.logic[t].type&&2==this.basic.logic[t].triggerType||(this.basic.logic[t].express.dep=this.params.getMatchDep(this.basic.logic[t].express.express),this.express.addLogicExpress(this.basic.logic[t].express,t))},activeExpress:function(){var t=this;this.express&&this.express.changeExpressContext({setValue:function(e,i,s,n,o){var a=e.srctable;if(s)if(void 0==o){var r=t.getGridData(a);for(var l in r)t.dataManager.setValue(a,e.srcfield,i,l)}else t.dataManager.setValue(a,e.srcfield,i,o);else if(e.srctable&&e.srctable.toLowerCase().startsWith("hboard_")){if(t.$refs.pageHeader&&t.$refs.pageHeader[0]&&t.$refs.pageHeader[0].getCtrl(e.srctable.toLowerCase())){var c=t.$refs.pageHeader[0].getCtrl(e.srctable.toLowerCase());e.srcfield.startsWith("val_")?c.setVal(e.srcfield,i):c.setCurrency(e.srcfield,i)}}else e.srctable?t.dataManager.setValue(e.srctable,e.srcfield,i,0):t.dataManager.setMainValue(e.srcfield,i)},setReadOnly:function(e,i,s,n,o){if(e.ctrlid){var a=t.control.get(e.ctrlid);if(e.ctrlid==n&&s)return;a&&a.setDisabled&&a.setDisabled(i)}},setRequired:function(e,i,s,n,o){console.log(t.control),console.log(arguments);var a=t.modelController.maintable;if(e.srctable==a||e.ctrlid&&t.control.get(e.ctrlid)&&t.control.get(e.ctrlid).setRequired){var r="";if(e.ctrlid){r=t.controls[e.ctrlid].label;var l=t.control.get(e.ctrlid);l&&l.setRequired&&l.setRequired(i)}var c=r+t.getLang("card_required");e.srctable&&e.srctable!=a&&e.srcfield.indexOf(".")<0&&(e.srcfield=e.srctable+"."+e.srcfield),t.validate.setRequired(e.srcfield,c,i)}},setHide:function(e,i,s,n,o){if(console.log(arguments),e.ctrlid){var a=t.control.get(e.ctrlid);if(e.ctrlid==n&&s)return;a&&a.setVisible&&!a.isToolbar&&a.setVisible(i)}},getValue:function(){},setLogicResult:function(e,i){var s,n=t.basic.logic[i];if(n)if(e)for(var o in n.matchOptions){var a=!0,r=!1,l=void 0;try{for(var c,h=n.matchOptions[o].targets.split(",")[Symbol.iterator]();!(a=(c=h.next()).done);a=!0){var d=c.value;s=n.matchOptions[o].type.startsWith("ctrl")?t.control.get(d):d,t.toggleLogic(s,n.matchOptions[o].type)}}catch(w){r=!0,l=w}finally{try{a||null==h.return||h.return()}finally{if(r)throw l}}}else for(var u in n.mismatchOptions){var f=!0,p=!1,m=void 0;try{for(var g,v=n.mismatchOptions[u].targets.split(",")[Symbol.iterator]();!(f=(g=v.next()).done);f=!0){var b=g.value;s=n.mismatchOptions[u].type.startsWith("ctrl")?t.control.get(b):b,t.toggleLogic(s,n.mismatchOptions[u].type)}}catch(w){p=!0,m=w}finally{try{f||null==v.return||v.return()}finally{if(p)throw m}}}}})},loadUIComponent:function(){var t=this,e=this.styleId,i={styleId:this.styleId,formType:"2",controlId:"",fields:[]},s=[],n=!1,o=ct["a"].hasServerSelectStore(e);o&&s.push(_t.getColSetData(i)),ct["a"].getSchema(e)&&(n=!0,this.version=ct["a"].getVersion(e),this.moduleManager=new gt["a"](e,this.version),s.push(this.moduleManager.loadFormLinks())),n||s.push(_t.getStyleInfo(e)),Promise.all(s).then((function(i){if(o){var s=i[0].Data;ct["a"].setSelectOpitons(e,s)}var a=n?ct["a"].getSchema(e):i[o?1:0].Data,r=lt["a"].getMobiConfig("card",ct["a"].getFormInfo(e));t.version=ct["a"].getVersion(e),t.uiConfig=r,t.schema=a,t.code=r.code||e,t.customCode=r.customCode,t.page=r.config,t.page.NavbarTmpl&&(t.navbarRCmp=JSON.stringify(t.page.NavbarTmpl)),t.useHeader=t.page.useHeader,t.isCard="card"==t.page.cardType,t.basic=r.basic;var l=r.basic.dsList;if(l)for(var c in l)t.hashDs[l[c].info.SqlId]=l[c];t.pageTitle=t.page.title,t.setup(),t.loaded()}))},initView:function(t){var e=this;this.container=this.uiConfig.container,this.initLang(),this.initColspan(),this.popupConfig=this.uiConfig.popups,this.$nextTick((function(){if(e.defineScrollRef(),e.dataId&&"add"!=e.status){if(e.pageLoading=!0,e.firstLoading&&(e.loadInitData(),window.parent["taskCenterLoadingClose"]))try{window.parent["taskCenterLoadingClose"](!0)}catch(i){console.error(i)}e.viewReady=!0}else e.addData();t&&t()}))},defineScrollRef:function(){if(this.uiConfig.config.flex){var t=0,e=!1;for(var i in this.container){if(!this.container[i].fixed){this.scrollRef=this.$refs.refCols[t],e=!0;break}t++}e||(this.scrollRef=this.$refs.refLayout.parentNode)}else this.scrollRef=this.$refs.refLayout.parentNode},setup:function(){var t=this;this.view=this,this.ctrlFieldMap={},this.actionCtrls={controls:[],inputs:[],toolbars:[],grids:[]},this.ctrlValidateMap={},this.ctrlLabelMap={},this.mainFields={},this.lang={},this.dataManager=new ht["a"],this.store=new ft["a"],this.context=ot["a"],this.params=new ut["a"](this.dataManager,this.store,this.lang),this.modelController=new ht["b"](this.params),this.sortManager=new ht["c"](this.uiConfig.controls),this.event=new dt["b"](this.uiConfig.controls),this.custom=new dt["a"],this.gridController=new dt["c"](this),this.validate=new pt["a"](this.dataManager),this.express=new mt["a"](this.dataManager,this.params,this.modelController),this.workflow=new rt["a"](this.styleId,this.event),this.validate.setValidateRowDelegate((function(e,i,s,n){return t.express.validateGridRow(e,i,s,n)})),this.initLangResources(),this.initPopups(),this.activeExpress(),this.activeLogicExpress(),this.control=new at["a"](this.$refs,"card"),this.ctx={event:this.event,params:this.params,express:this.express,control:this.control,data:this.dataManager,modelController:this.modelController,store:this.store,langResources:this.langResources},this.activeResurces(),this.initData(),this.bind(),this.uistate="loaded"},initData:function(t){var e=lt["a"].getSchemaStruct(this.schema);this.formItem=e.formItem,this.listItems=e.listItems,this.modelController.setSchema(this.schema),this.modelController.setDefault(this.basic.defval)},initVirtualField:function(){for(var t in this.schema)if(!0===this.schema[t].isMain)for(var e in this.schema[t].Cols)"0"===this.schema[t].Cols[e].IsReal&&void 0===this.formItem[this.schema[t].Cols[e].Field]&&this.$set(this.formItem,this.schema[t].Cols[e].Field,"")},bind:function(){var t=this,e=this.uiConfig.container,i=this;for(var s in e)for(var n in e[s].childs){e[s].childs[n];for(var o in e[s].childs[n].childs){var a=e[s].childs[n].childs[o],r=this.uiConfig.controls[a];switch(r.type){case"grid":console.log(this.$refs),this.gridController.add(a,r,this),this.regToolbar(a+"_toolbar",!0,r),this.regGrid(a,r);break;case"toolbar":this.regToolbar(a,!1,r);break;case"gridmenu":this.regGridMenu(a);break;case"tab":this.regTab(a);break;case"title":this.recordTitle(a),this.actionCtrls.controls.push(r.id);break;case"wf":this.loadWfScript(),this.actionCtrls.controls.push(r.id);break;default:if(r.field){var l=this.modelController.maintable;r.table&&r.table!=l?this.validate.setRules(r.table+"."+r.field,r.rules):this.validate.setRules(r.field,r.rules),r.table&&(l=r.table),this.express.addExpress(l,r.field,r.express,r.id,!1,"",r.force),this.addCtrlFields(l,r.field,r.id,!0)}this.actionCtrls.controls.push(r.id);break}}}i.regFloatBtn(),i.updateCurFloatBtn(),this.initHiddenCtrls(),this.service.getCustomConfig(this.styleId).then((function(s){for(var n in"ok"==s.Code&&s.Data.bizOps&&(i.bizOps=s.Data.bizOps.map((function(t){return t.id}))),e)for(var o in e[n].childs){e[n].childs[o];for(var a in e[n].childs[o].childs){var r=e[n].childs[o].childs[a],l=t.uiConfig.controls[r];switch(l.type){case"grid":t.initBtnHidden(r+"_toolbar",!0,l,!0);break;case"toolbar":t.initBtnHidden(r,!1,l,!0);break;default:break}}}i.updateCurFloatBtn()}))},initHiddenCtrls:function(){for(var t in this.page.HideCtrls){var e=this.page.HideCtrls[t],i=this.uiConfig.controls[e],s=this.modelController.maintable;i.table&&(s=i.table),i.field&&this.express.addExpress(s,i.field,i.express,i.id,!1,"",i.force)}},addCtrlFields:function(t,e,i,s){if(console.log("table:"+t+"field:"+e+"ctrlId:"+i),t&&e&&(s&&this.actionCtrls.inputs.push(i),t=t.toUpperCase(),this.mainFields[t]=this.mainFields[t]||[],this.mainFields[t].push(e),console.log(this.mainFields),i)){this.ctrlFieldMap[t]=this.ctrlFieldMap[t]||{},this.ctrlFieldMap[t][e]=this.ctrlFieldMap[t][e]||[],this.ctrlFieldMap[t][e].push(i);var n=this.uiConfig.controls[i];this.ctrlLabelMap[t+":"+e]=n.label}},loadWfScript:function(){},regGrid:function(t,e){var i=this;this.actionCtrls.grids.push(t),this.event.on("rowclick","grid",t,(function(t,e,s){if(t.onRowClick){var n="return ("+t.onRowClick+")(view,row,index)",o=new Function("view","row","index",n);o(i,e,s)}})),this.event.on("swipeclick","grid",t,(function(t,e,s){if(t.onSwipeClick){var n="return ("+t.onSwipeClick+")(view,row,index)",o=new Function("view","row","index",n);o(i,e,s)}})),this.event.on("swipeButtonClick","grid",t,(function(t,e,s,n,o){if(console.log("ccc"),n.buttonList&&n.buttonList[e]&&n.buttonList[e].script){var a="return ("+n.buttonList[e].script+")( view, row, index, props)",r=new Function("view","row","index","props",a);r(i,s,o,n,{})}})),this.initGridCols(e,e.cols),this.initGridCols(e,e.hideCols,!0)},initGridCols:function(t,e,i){for(var s in e){var n=t.id+"."+e[s].id,o=this.uiConfig.controls[n];if(o){if(!i){var a=t.table+"."+o.field;this.validate.setRules(a,o.rules)}this.addCtrlFields(t.table,o.field,n),this.express.addExpress(t.table,o.field,o.express,t.id,!0,o.label,o.force)}}},getGridFilter:function(t){return this.getGridFilterExpress(t)},getGridFilterExpress:function(t){var e=this.uiConfig.controls[t].dscode,i=[],s=[],n=this.gridController.getGridFilter(this.uiConfig.controls[t].filter);s=s.concat(this.gridController.getFilterDataSource(e)),s.length>0&&(s[s.length-1].Logic="",n.length>0&&(n[n.length-1].Logic="and")),i=i.concat(n).concat(s);var o=this.event.emit("beforeGridFilter","grid",t,[i]);return o||i},loadInitData:function(){var t=this;if(this.fristLoadData){var e=this.sortManager.initSortField(this.fristLoadData),i=lt["a"].getInstanceData(e);this.formItem=i.formItem,this.initVirtualField(),this.listItems=i.listItems,this.modelController.setModel(e),this.dataManager.setData(this.formItem,this.listItems),this.$nextTick((function(){t.event.emitGlobal("loadData",[t]),t.showFloatIcon=!0,t.express.triggerALLCalc("view"),t.express.triggerHideALL(),t.express.triggerReadALL(),t.express.triggerRequireALL(),t.pageLoading=!1,t.express.triggerLogicAll()}))}},loadFirstData:function(){var t=this,e={id:this.dataId,styleId:this.styleId};_t.getCardData(e).then((function(e){if(t.fristLoadData=e.Data,t.firstLoading=!0,t.viewReady&&(t.loadInitData(),t.firstLoading=!1,window.parent["taskCenterLoadingClose"]))try{window.parent["taskCenterLoadingClose"](!0)}catch(i){console.error(i)}}))},loadData:function(t){var e=this;""!=this.$route.query.workItemId&&"recorddetail"!=this.viewTag||this.loading(this.getLang("card_loading"));var i={id:this.dataId,styleId:this.styleId};null!==t&&""!==t&&void 0!==t&&(i.id=t);var s=new Promise((function(t,s){e.service.getCardData(i).then((function(i){console.log(i);var s=e.sortManager.initSortField(i.Data),n=lt["a"].getInstanceData(s);e.formItem=n.formItem,e.initVirtualField(),e.listItems=n.listItems,e.modelController.setModel(s),e.dataManager.setData(e.formItem,e.listItems),d["a"].clear(),e.$nextTick((function(){if(e.event.emitGlobal("loadData",[e]),window.parent["taskCenterLoadingClose"])try{window.parent["taskCenterLoadingClose"](!0)}catch(i){console.error(i)}e.showFloatIcon=!0,e.express.triggerALL("view"),e.pageLoading=!1,t(!0)}))}))}));return s},unLock:function(t){this.locked&&(this.locked=!1,this.lockTimes=0,t=t||this.dataId,clearInterval(this.lockIndex),this.service.cancelCardData(this.styleId,t))},updateForm:function(t,e,i,s,n){if(i&&i.options&&i.options.table&&i.options.table!=this.modelController.maintable){if(n)return;this.listItems[i.options.table][0][t]=e,this.express.trigger(i.options.table,t,null,this.gridController.hashDsCode[i.options.dscode],0,this.listItems[i.options.table][0]),this.express.triggerLogicExpress(i.options.table,t)}else{if(this.formItem[t]=e,n)return;this.express.trigger(this.modelController.maintable,t),this.express.triggerLogicExpress(this.modelController.maintable,t),this.$forceUpdate();var o=[t,e,this.modelController.maintable,i];this.event.emitGlobal("dataChange",o),st["a"].$emit("component-change","valueChange","all","all",o,this.styleId,this.viewTag)}},setFieldValue:function(t){var e=Object.assign({},t),i=e.table,s=e.field,n=e.value,o=(e.ctrlId,e.index),a=e.isChange;if(i!=this.modelController.maintable&&i)try{this.listItems[i][o][s]=n,this.express.trigger(i,s),this.express.triggerLogicExpress(i,s),this.express.triggerTableOne(i,this.listItems[i][o],o,"edit")}catch(r){console.error("setFieldValue对子表字段赋值时发生异常："+r+"请检查参数是否正确")}else this.formItem[s]=n,a&&(this.express.trigger(i,s),this.express.triggerLogicExpress(i,s))},setTableDataWithTrigger:function(t,e){t=t.toUpperCase(),this.listItems[t]=e;var i=this.modelController.hashScheama[t].Cols;this.express.triggerTable(t,i)},toggleLogicAction:function(){var t=this;for(var e in t.basic.logic){var i=t.express.getExpressValue(t.basic.logic[e].express.script);if(i){if("3"==t.basic.logic[e].triggerType)for(var s in t.basic.logic[e].matchOptions){var n=t.control.get(t.basic.logic[e].matchOptions[s].targets);this.toggleLogic(n,t.basic.logic[e].matchOptions[s].type)}}else if("3"==t.basic.logic[e].triggerType)for(var o in t.basic.logic[e].mismatchOptions){var a=t.control.get(t.basic.logic[e].mismatchOptions[o].targets);this.toggleLogic(a,t.basic.logic[e].mismatchOptions[o].type)}}},saveDataWithLock:function(t){return this.saveData(t,!0)},saveData:function(t,e){var i=this,s="add"===this.status,n=this.modelController.getSaveData(this.formItem,this.listItems,s,this),o={data:n,styleId:this.styleId,type:"add"==this.status?"0":"1",formType:"3",extParam:{},checkRequired:!0};t&&t.auditType&&(o.auditType=t.auditType),t&&!t.checkRequired&&(o.checkRequired=t.checkRequired),i.event.emitGlobal("beforeSave",[o]);var a=new Promise((function(t,s){i.validate.validateDataTable(n,i.mainFields,(function(s,n,a,r,l,c){if(s||!o.checkRequired){i.loading(i.getLang("card_saving")),window.IDP_GLOBAL_BEFORESAVE&&window.IDP_GLOBAL_BEFORESAVE();var u=i.event.emitGlobal("afterValidate",[o]);if(0==u)return void d["a"].clear();i.service.saveCardData(o).then((function(s){if(d["a"].clear(),"ok"==s.Code){console.log(s),i.status="edit",i.dataId=i.formItem[i.modelController.pkcol],i.isWf?i.page.wfsubmit&&i.workflow.wfConfig.WfPCId?(i.isWf=!1,i.wfSubmit()):(i.getWorkFlow(),i.isWf=!1,i.loadData()):(i.loadData(),kt({type:"success",message:i.getLang("card_saveSuccess")})),t(!0);var n=i.event.emitGlobal("afterSaveSuccess",[i]);!1!==n&&i.locked&&(i.locked=!1,i.lockTimes=0,clearInterval(i.lockIndex),e?i.lockData():(i.status="view",i.setUIReadonly(!0,!1)))}else i.dealSaveErrorMsg(s,t,i);i.isWf=!1,i.event.emitGlobal("afterSave",[i]),window.IDP_GLOBAL_AFTERSAVE&&window.IDP_GLOBAL_AFTERSAVE()}),(function(e){i.modelController.recoverCode(i.formItem),i.event.emitGlobal("afterSaveError",[i]),t(!1)}))}else i.isWf=!1,i.unFoldSplitTitle(),i.setValidateMessage(n,a,r,l,c),1==c.length?h["a"].alert({message:a}).then((function(){})):h["a"].alert({message:i.getLang("card_validateFail",{0:c.length})}).then((function(){}))}))}));return a},dealSaveErrorMsg:function(t,e,i){if(i.event.emitGlobal("afterSaveError",[i]),this.modelController.recoverCode(this.formItem),"check"==t.Code&&t.Message)try{var s=JSON.parse(t.Message);i.alert(s.msg)}catch(n){i.alert(t.Message)}else"over length"==t.Code&&t.Message?(i.alert(i.getLang("card_overLengthErr",{0:i.ctrlLabelMap[t.Message]})),e(!1)):"unKnow"==t.Code?e(!1):"lockdata"==t.Code?i.alert(i.getLang("err_lockdata",{0:t.Message})):"wfCheck"==t.Code?i.alert(i.getLang("err_wfcheck")):(i.alert(t.Message),e(!1))},setValidateMessage:function(t,e,i,s,n){var o=this;if(this.modelController.recoverCode(this.formItem),s=s||o.modelController.maintable,s==o.modelController.maintable||""==s||o.control.get(o.getMapCtrl(i[0],s))){var a=o.control.get(o.getMapCtrl(i[0],s)),r=a.$el.offsetTop;if(r>window.screen.availHeight/2){var l=r-window.screen.availHeight/2;o.scrollRef.scrollTop=l,a.onFocus&&a.onFocus()}else o.scrollRef.scrollTop=0;for(var c in i){var h=o.control.get(o.getMapCtrl(i[c],s));h.setErrorMessage&&h.setErrorMessage(e[c])}}else{var d=o.control.get("grid_"+s).$el.offsetTop,u=o.control.get("grid_"+s).$el.offsetHeight,f=u/o.control.get("grid_"+s).list.length,p=f*(n[0]+1)+d;for(var m in p>window.screen.availHeight/2?o.scrollRef.scrollTop=p-window.screen.availHeight/2:o.scrollRef.scrollTop=0,i){var g=n[m];o.listItems[s][g].__validate=!0,o.listItems[s][g].__validateMessage=e[m]}}},setValidateMessageAll:function(t){var e=[];for(var i in t)if(e=e.concat(t[i].message),t[i].errorKeys.length)if(i==self.modelController.maintable||""==i)for(var s in t[i].errorKeys)self.control.get(t[i].errorKeys[s]).setErrorMessage(t[i].message[s]);else for(var n in t[i].errorKeys){var o=t[i].breakIndex[n];self.listItems[i][o].__validate=!0,self.listItems[i][o].__validateMessage=t[i].message[n]}},getMapCtrl:function(t,e){return t.indexOf(".")>-1&&(t=t.split(".")[1]),this.ctrlFieldMap[e][t][0]},addData:function(){if(this.modelController){this.status="add";var t=this.modelController.getDefaultData();for(var e in this.event.emitGlobal("beforeAddData",[t]),this.formItem=t,this.listItems){this.listItems[e].splice(0,this.listItems[e].length);var i=this.modelController.getDefaultDetailData(e,this.formItem);if(i&&this.listItems[e].push(i),this.modelController.hashScheama[e]&&"2"==this.modelController.hashScheama[e].EditType){var s=this.modelController.getDefaultData(e,this.formItem);this.listItems[e].push(s)}}this.dealCodeSetting(this.formItem),this.event.emitGlobal("afterAddData",[t]),this.dataManager.setData(this.formItem,this.listItems),this.express.triggerALL(),this.express.triggerLogicAll()}},dealCodeSetting:function(t){var e=this;if(!this.codeControls)for(var i in this.codeControls=[],this.controls){var s=this.controls[i];if(!s)return;"code"==s.type&&s.editor_code.isCreate&&s.editor_code.isShowCode&&(this.modelController.setCodeTempField(s.table,s.field),this.codeControls.push(s))}var n=function(){var i=e.codeControls[o],s=i.id,n=i.editor_code.ruleid,a=i.editor_code.isShowCode;_t.getCode(n,t,a).then((function(t){"ok"==t.Code&&(e.control.get(s).currentValue=t.Data,e.formItem[s]=t.Data)}))};for(var o in this.codeControls)n()},editRow:function(t,e,i){this.event.emitGlobal("beforeEditGridRow",[t,e]),this.openDetail(t,e,i,"edit")},addRow:function(t,e){var i=this.modelController.getDefaultData(t,this.formItem);i.__validate=!1,i.__validateMessage="",this.event.emitGlobal("beforeAddGridRow",[t,i,this]),e?this.openDetail(t,i,-1,"add"):this.listItems[t].push(i),this.event.emitGlobal("afterAddGridRow",[t,i,this])},openDetail:function(t,e,i,s){var n=this.gridController.getGridIdByTable(t);this.$router.push({name:"detail",path:"/detail",params:{gridId:n,styleId:this.styleId,dataId:this.dataId,rowIndex:i,status:s,rowData:e,controls:this.controls,vprops:this.controls[n],express:this.express,validate:this.validate,mainFields:this.mainFields,parent:this,actionState:s,listData:this.listItems[t],formItem:this.formItem,viewTag:this.viewTag}}).catch((function(t){return t}))},getWorkFlow:function(){var t=this,e="";this.formItem[this.workflow.wfConfig.DwField]&&(e=this.formItem[this.workflow.wfConfig.DwField]||"");var i=this.formItem[this.workflow.wfConfig.Id];_t.getWorkItem(t.workflow.wfConfig.styleId,e,i,t.workflow.extData).then((function(e){if("ok"==e.Code&&e.Data){if(1==e.Data||"string"==typeof e.Data||e.Data instanceof Array&&1==e.Data.length)return kt({type:"success",message:t.getLang("card_submitSuccess")}),t.event.emitGlobal("submitSuccess",[t]),void t.loadData();if(e.Data.assigneeInfoList)return void t.showSelectUserNew(e.Data.procInstanceId,e.Data.assigneeInfoList);e.Data.length>1?(t.wfProcess=t.workflow.getName(e.Data),t.wfChoose=!0):1==e.Data.length?t.submit(e.Data[0].Id):kt({type:"danger",message:t.getLang("card_submitEmpty")})}else kt({type:"danger",message:e.Message})}))},start:function(){this.isWf=!0,this.event.emitGlobal("beforeSubmit",[this]),this.saveData()},selectWf:function(t){this.wfChoose=!1;var e=this.workflow.getValue(this.workflow.dataItems,t);this.submit(e)},wfSubmit:function(t){var e=this.workflow.wfConfig.WfPCId;t&&(e=t);var i=this;this.$router.push({name:"iframe",path:"/iframe",query:{hidebar:"true"},params:{src:window.idp.basePath.getBasePath()+"/platform/runtime/wf/webapp/mobile-submit/index.html#/?bizDefKey="+e+"&dataId="+i.formItem[i.modelController.pkcol]}})},submit:function(t){var e=this,i=e.formItem[this.workflow.wfConfig.Id],s=e.formItem[this.workflow.wfConfig.DfIdField],n=[i,s],o=this.event.emitGlobal("beforeWfSubmit",n);o&&(i=o),this.loading(e.getLang("card_submiting")),_t.startWorkFlow(this.workflow.wfConfig.styleId,i,t,s,this.workflow.extData).then((function(t){d["a"].clear(),"ok"==t.Code?(t.Data.Users?(e.wfUser=t.Data.Users,e.wfUserChoose=!0):t.Data.assigneeInfoList?e.showSelectUserNew(t.Data.procInstanceId,t.Data.assigneeInfoList):(kt({type:"success",message:e.getLang("card_submitSuccess")}),e.loadData()),e.event.emitGlobal("submitSuccess",[e])):kt({type:"danger",message:t.Message})}))},cancelSubmit:function(){this.workflow.cancelSubmit(this.formItem,this.workflow)},formatUserList:function(t){var e=this;e.wfUser=[],t.forEach((function(t,i){t.assigneeUsers&&t.assigneeUsers.forEach((function(i,s){i.activityInstId=t.activityInstId,e.wfUser.push(i)}))}))},confirmSubmitUserForCloud:function(){var t=this;if(this.resultUser.length){var e={};this.resultUser.forEach((function(i){var s=t.wfUser.find((function(t){return t.Id==i}));s&&(e[s.activityInstId]=e[s.activityInstId]||[],e[s.activityInstId].push(i))}));var i=[];for(var s in e)i.push({activityInstId:s,assigneeIds:e[s]});var n=this.workflow.wfConfig,o=this.formItem[n.Id],a=this.formItem[n.DfIdField],r=this.currentProcessInstanceId;this.loading(t.getLang("card_submiting")),_t.startWorkFlowUserCloud(n.styleId,o,r,a,i,r,this.workflow.extData).then((function(e){d["a"].clear(),"ok"==e.Code?(t.wfUserChoose=!1,kt({type:"success",message:t.getLang("card_submitSuccess")}),t.loadData()):kt({type:"danger",message:e.Message})}))}else alert(t.getLang("card_resultUserEmpty"))},showSelectUserNew:function(t,e){this.currentProcessInstanceId=t,this.formatUserList(e),this.wfUserChoose=!0},showError:function(t,e,i,s){},recordTitle:function(t){this.splitTitleList.push(t)},unFoldSplitTitle:function(){for(var t in this.splitTitleList){var e=this.control.get(this.splitTitleList[t]);e.isFold=!1}},changeTab:function(t,e){this.event.emitGlobal("tabChange",[t,e])}}},Lt=Ft,St=(i("5c9c"),Object(I["a"])(Lt,o,a,!1,null,null,null));e["default"]=St.exports},e0d9:function(t,e,i){"use strict";var s=i("0934"),n=i.n(s);n.a},e333:function(t,e,i){},e554:function(t,e,i){"use strict";var s=i("26c6"),n=i.n(s);n.a},e7e7:function(t,e,i){},e9a1:function(t,e,i){"use strict";var s=i("29c5"),n=i.n(s);n.a},e9fc:function(t,e,i){},eb4b:function(t,e,i){"use strict";var s=i("f79d"),n=i.n(s);n.a},ed53:function(t,e,i){"use strict";var s=i("83e9"),n=i.n(s);n.a},ed64:function(t,e,i){"use strict";var s=i("9976"),n=i.n(s);n.a},eec9:function(t,e,i){"use strict";var s=i("7f4f"),n=i.n(s);n.a},ef77:function(t,e,i){"use strict";var s=i("64ac"),n=i.n(s);n.a},efee:function(t,e,i){"use strict";i.r(e);i("d81d");var s,n,o,a,r,l,c=i("a026"),h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("van-field",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],ref:t.refElement,class:t.cls,attrs:{placeholder:t.placeholder,clearable:"",required:t.options.required,readonly:t.disabled,type:t.inputType,autosize:"",label:t.label,rows:"1",value:t.currentValue,"error-message-align":"right","error-message":t.errorMessage},on:{keypress:t.onKeyPress,focus:t.onFocus,blur:t.onBlur,clear:t.onClear,click:t.onClick,input:t.onInputEvent},scopedSlots:t._u([{key:"right-icon",fn:function(){return[t.percent?i("div",{staticStyle:{"margin-left":"-7px","font-size":"16px"}},[t._v("%")]):t._e()]},proxy:!0},t.isStepper?{key:"input",fn:function(){return[i("div",{staticStyle:{width:"100%"}},[i("van-stepper",{staticStyle:{float:"right"},attrs:{integer:"",min:t.min,"input-width":t.stepperWidth,max:t.max,"default-value":"0",disabled:t.disabled,"disable-input":""},on:{focus:t.onFocus,blur:t.onBlur},model:{value:t.currentValue,callback:function(e){t.currentValue=e},expression:"currentValue"}})],1)]},proxy:!0}:null,{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0},t.rightView?{key:"extra",fn:function(){return[i(t.rightView,{tag:"div",attrs:{value:t.currentValue,ctx:t.ctx,gridEditParm:t.gridEditParm}})]},proxy:!0}:null],null,!0),model:{value:t.currentText,callback:function(e){t.currentText=e},expression:"currentText"}})],1)},d=[],u=(i("99af"),i("fb6a"),i("a9e3"),i("b680"),i("d3b7"),i("acd8"),i("4d63"),i("ac1f"),i("25f0"),i("8a79"),i("5319"),i("1276"),i("2ca0"),i("39ea")),f=i("74a5"),p=i("d8ad"),m=i("901e"),g=i.n(m),v={name:"vinput",props:["name","type","label","value","options","readonly","styleId","ctx","gridEditParm"],components:{popLabel:f["a"]},activated:function(){this.inputEvent=!1},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick},cls:function(){var t="";return this.disabled&&(t+=" f-input-readonly "),this.textMode&&(t+=" f-input-secondary "),t}},data:function(){return{percent:!1,rightView:null,refElement:"input",disabled:!1,inputType:"textarea",currentValue:"",currentText:"",visible:!0,show:!0,required:!1,placeholder:"",inputEvent:!1,textMode:!1,errorMessage:"",keyboardShow:!1,showPopover:!1,min:-1e15,max:1e15,asterProtection:!1,asterInput:!1,asterType:"",isStepper:!1,stepperWidth:""}},mounted:function(){if(this.currentValue=this.value,this.disabled=this.readonly,this.placeholder=this.options.placeholder,this.textMode=this.options.textMode,this.note=this.options.note||"",this.asterProtection=this.options.asterProtection||!1,this.asterInput=this.options.asterInput||!1,this.asterType=this.options.asterType||"Default",this.rightView=this.buildSingleComponent("rightTpl","rightView",["value","ctx","gridEditParm"]),"number"==this.options.type){if(this.options.editor_number.isStepper){this.isStepper=!0,this.precision=0,this.currentValue=this.currentValue||"0";var t=this.options.editor_number.stepperWidth;this.stepperWidth=t?"px"==t.slice(-2)?t:t+"px":"32px"}else"0"==this.options.editor_number.decimal?this.inputType="digit":"ios"==u["a"].getDeviceType()?this.inputType="textarea":this.inputType="number",""===this.options.editor_number.decimal||void 0===this.options.editor_number.decimal||null===this.options.editor_number.decimal?this.precision=2:this.precision=this.options.editor_number.decimal;this.thousand=this.options.editor_number.thousand,this.prefix=this.options.editor_number.prefix,this.suffix=this.options.editor_number.suffix,this.thousand&&(this.inputType="textarea"),this.options.editor_number.max&&(this.max=this.options.editor_number.max),this.options.editor_number.min&&(this.min=this.options.editor_number.min)}if(null==this.value&&(this.currentValue=""),"number"==this.options.type){var e=this.formatValue(this.value);if(this.currentValue=e,this.thousand){var i=this.currentValue.split(".");i[0]&&i[0].length&&(void 0===i[1]||null===i[1]?this.currentValue=this.toThousands(i[0]):this.currentValue=this.toThousands(i[0])+"."+i[1])}this.fixText(!0),this.options.fparams&&this.options.fparams.percent||this.options.editor_number&&this.options.editor_number.percent?(this.percent=!0,this.currentValue=100*this.value,this.max=100,this.min=0):this.percent=!1}},methods:{handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},emitListeners:function(t,e){var i=this.options.id,s=this.options.type;return this.gridEditParm&&(i=this.gridEditParm.id,s="grid",e.push(this.gridEditParm.field),e.push(this.gridEditParm.rowindex),e.push(this.gridEditParm.record)),p["a"].$emit("component-change",t,s,i,e,this.styleId,this.viewTag?this.viewTag():void 0)},returnRef:function(){return this.$refs["input"]},onFocus:function(){if(this.emitListeners("getFocus",[this,this.name,this.currentValue]),!this.disabled){if(this.inputEvent=!0,this.currentText=this.currentValue,"number"==this.options.type){if(this.fixText(),this.thousand){var t=new RegExp(",","g");this.currentText=this.currentText.replace(t,"")}0===parseFloat(this.currentText)&&(this.currentText=""),this.keyboardShow=!0;var e=this.formatValue(this.currentText,!0);this.currentText=e}this.keyboardShow=!0,console.log(arguments)}},onBlur:function(){if(console.log(this.options),this.emitListeners("lostFocus",[this,this.name,this.currentText]),this.inputEvent=!1,"number"==this.options.type){var t=this.validateValue(this.currentText);if(""!=t&&null!=t&&void 0!=t||(t=0),t=this.formatValue(t),this.currentValue=t,this.onInputEvent(this.currentValue),this.fixText(),this.thousand){var e=this.currentText.split(".");void 0===e[1]||null===e[1]?this.currentText=this.toThousands(e[0]):this.currentText=this.toThousands(e[0])+"."+e[1]}this.fixText(!0),this.keyboardShow=!1}this.currentValue=this.currentText,this.currentText=this.asterProtection?window.idp.utils.asterFormat(this.currentValue,this.asterType):this.currentValue,console.log(arguments)},onClear:function(){console.log(arguments)},onClick:function(){console.log(arguments)},validate:function(t){if(!t||"-"==t)return!0;var e=("digit"==this.inputType?/^-?\d+$/:/^(-?\d+)(\.)?(\d+)?$/).test(t);return console.log(e),e},getValue:function(){return this.currentValue},setDisabled:function(t){this.readonly||(this.disabled=t)},setRequired:function(t){this.options.required=t,this.$forceUpdate()},setVisible:function(t){var e=this;this.visible=!t,this.$nextTick((function(){e.$refs.input.adjustSize&&e.$refs.input.adjustSize()}))},setShow:function(t){var e=this;this.show=t,this.$nextTick((function(){e.$refs.input.adjustSize&&e.$refs.input.adjustSize()}))},parseNumber:function(t){var e="digit"==this.inputType;if(null==t||""==t)return null;"string"!=typeof t&&(t=(t||"").toString());var i=/^\-/.test(t);if(e){if("0"==t)return 0;t=t.replace(/\D+|^[0]+/g,"")}else t=t.replace(/[^0-9.]/g,""),/^[0]+[1-9]+/.test(t)&&(t=t.replace(/^[0]+/,""));return!e&&this.precision&&+this.precision<=7&&(t=parseFloat(t).toFixed(this.precision)),"NaN"==t?0:(i&&(t="-"+t),t)},validateValue:function(t){var e=t;return Number(t)>Number(this.max)&&(e=this.max),Number(t)<Number(this.min)&&(e=this.min),e},formatValue:function(t){var e="";return 0===t||""!=t&&null!=t&&void 0!=t?(e=new g.a(t),(this.precision||0===this.precision&&!this.options.editor_number.zerodel)&&(e=e.toFixed(Number(this.precision))),this.options.editor_number.zerodel&&(e=parseFloat(t).toString()),e):""},onKeyPress:function(){this.inputEvent=!0},fixText:function(t){this.currentText&&this.currentText.length&&(t?(this.prefix&&!this.currentText.startsWith(this.prefix)&&(this.currentText=this.prefix.concat(this.currentText)),this.suffix&&!this.currentText.endsWith(this.suffix)&&(this.currentText=this.currentText.concat(this.suffix))):(this.prefix&&this.currentText.startsWith(this.prefix)&&(this.currentText=this.currentText.slice(this.prefix.length)),this.suffix&&this.currentText.endsWith(this.suffix)&&(this.currentText=this.currentText.slice(0,this.currentText.length-this.suffix.length))))},onInputEvent:function(t,e){"null"!=t&&("number"==this.options.type&&(this.validate(t)?this.inputEvent||(t=this.formatValue(t)):t=this.parseNumber(t)),this.currentText=t,this.inputEvent?this.currentText=t:"number"==this.options.type?(this.thousand&&(this.currentText=this.currentValue),this.fixText(!0)):this.asterProtection&&(this.currentText=window.idp.utils.asterFormat(t,this.asterType)),this.percent&&(t/=100),this.$emit("input",this.name,t,this,void 0,e))},setErrorMessage:function(t){this.errorMessage=t},closeKeyboard:function(){this.keyboardShow=!1},showNote:function(){console.log(this.note),this.showPopover=!0},toThousands:function(t){t=(t||0).toString();var e="",i=!1;(t<0||"-"==t[0])&&(i=!0),t=t.replace("-","");while(t.length>3)e=","+t.slice(-3)+e,t=t.slice(0,t.length-3);return t&&(e=t+e),i&&(e="-"+e),e},clearValue:function(){this.currentValue="",this.$emit("input",this.name,this.currentValue,this)},buildSingleComponent:function(t,e,i){var s=this.options["editor_number"];if("number"==this.options.type&&s[t]&&s[t].template){var n={};if(s[t].mixin){var o=new Function("return "+s[t].mixin+";");n=o()}return this[e]=c["a"].extend({mixins:[n],props:i,template:s[t].template,data:function(){return{}},methods:{}}),this[e]}}},watch:{options:function(){if(this.visible=!0,this.disabled=1==this.readonly,this.placeholder=this.options.placeholder,this.textMode=this.options.textMode,this.rightView=this.buildSingleComponent("rightTpl","rightView",["value","text"]),"number"==this.options.type){"0"==this.options.editor_number.decimal?this.inputType="digit":"ios"==u["a"].getDeviceType()?this.inputType="textarea":this.inputType="number",this.precision=this.options.editor_number.decimal,this.thousand=this.options.editor_number.thousand,this.prefix=this.options.editor_number.prefix,this.suffix=this.options.editor_number.suffix;var t=this.formatValue(this.currentValue);this.currentValue=t,this.options.fparams&&this.options.fparams.percent||this.options.editor_number&&this.options.editor_number.percent?(this.percent=!0,this.max=100,this.min=0):this.percent=!1}else this.inputType="textarea",this.thousand=!1},value:function(t){var e=this;if(null==t)this.isStepper?this.currentValue="0":this.currentValue="";else if(this.setErrorMessage(""),"number"==this.options.type&&this.percent){if(this.inputEvent)return;this.currentValue=this.validateValue(100*t)}else if(this.inputEvent||"number"!=this.options.type)this.thousand||(this.currentValue=t);else{var i=this.formatValue(this.validateValue(t));if(this.currentValue=i,this.thousand){var s=this.formatValue(this.validateValue(t)),n=s.split(".");void 0===n[1]||null===n[1]?this.currentValue=this.toThousands(n[0]):this.currentValue=this.toThousands(n[0])+"."+n[1]}this.fixText(!0)}this.isStepper&&this.$nextTick((function(){e.onInputEvent(e.currentValue)}))},currentValue:function(t){this.inputEvent||this.onInputEvent(t,!0)}}},b=v,w=(i("e554"),i("2877")),y=Object(w["a"])(b,h,d,!1,null,null,null),C=y.exports,k=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("van-field",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],class:{"f-input-block":t.isBlock},attrs:{name:"checkbox",label:t.label,readonly:t.disabled,required:t.options.required,"error-message":t.errorMessage,"error-message-align":"right"},scopedSlots:t._u([{key:"input",fn:function(){return[i("div",{staticClass:"f-number-range"},[i("div",{staticClass:"f-number-range-input"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.value1,expression:"value1"}],staticClass:"f-number-box",attrs:{type:"number"},domProps:{value:t.value1},on:{input:[function(e){e.target.composing||(t.value1=e.target.value)},t.handelChange1]}})]),i("div",{staticClass:"f-number-range-split"},[t._v("~")]),i("div",{staticClass:"f-number-range-input"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.value2,expression:"value2"}],staticClass:"f-number-box",attrs:{type:"number"},domProps:{value:t.value2},on:{input:[function(e){e.target.composing||(t.value2=e.target.value)},t.handelChange2]}})])])]},proxy:!0},{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0}])})},x=[],I={name:"vnumberrange",props:["name","type","label","value","options","readonly","styleId","ctx","gridEditParm"],components:{popLabel:f["a"]},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick}},created:function(){},mounted:function(){},activated:function(){},data:function(){return{value1:"",value2:"",isBlock:!0,disabled:!1,visible:!0,show:!0,errorMessage:"",showPopover:!1,note:""}},methods:{handelChange1:function(t){this.setChange()},handelChange2:function(t){this.setChange()},setChange:function(){this.$emit("input",this.name,this.getValue())},getValue:function(){return this.value1+" - "+this.value2},handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},setDisabled:function(t){this.readonly||(this.disabled=t)},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},setRequired:function(t){this.options.required=t,this.$forceUpdate()},initOptions:function(){}},watch:{options:function(){this.initOptions(),this.disabled=this.readonly,this.visible=!0},value:function(t){if(t){var e=t.split(" - ");e.length&&(this.value1=e[0],this.value2=e[1])}else this.value1="",this.value2=""}}},_=I,T=(i("4bef"),i("4e92"),Object(w["a"])(_,k,x,!1,null,null,null)),F=T.exports,L=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("van-field",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],ref:"input",staticClass:"f-textarea",class:{"f-input-readonly":t.disabled},attrs:{placeholder:t.placeholder,readonly:t.disabled,required:t.options.required,rows:"2",autosize:"",type:"textarea",label:t.label,maxlength:t.wordLimit,"show-word-limit":t.showWordLimit,"error-message-align":"right","error-message":t.errorMessage},on:{input:t.onInputEvent},scopedSlots:t._u([t.isSingleLine&&t.disabled?{key:"input",fn:function(){return[i("div",{staticStyle:{width:"100%"}},[t.newStyle?i("div",{staticClass:"f-text-expander"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.achorExpand,expression:"achorExpand"}],staticClass:"f-text-toggle",attrs:{id:t.options.id+"-text-toggle",type:"checkbox"},domProps:{checked:Array.isArray(t.achorExpand)?t._i(t.achorExpand,null)>-1:t.achorExpand},on:{change:function(e){var i=t.achorExpand,s=e.target,n=!!s.checked;if(Array.isArray(i)){var o=null,a=t._i(i,o);s.checked?a<0&&(t.achorExpand=i.concat([o])):a>-1&&(t.achorExpand=i.slice(0,a).concat(i.slice(a+1)))}else t.achorExpand=n}}}),i("div",{ref:"textWrap",staticClass:"f-text-box",style:"-webkit-line-clamp:"+t.rows},[i("div",{ref:"f-text-container"},[i("div",{staticClass:"f-text-empty",style:"height:"+t.emptyHeight+"px"}),t.isTextOverFlow?i("label",{staticClass:"f-toggle-btn",on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.toggleExpand(e)}}},[t._v(t._s(t.achorText))]):t._e(),t._v(" "+t._s(t.currentValue)+" ")])])]):i("div",{ref:"textWrap",class:t.achorCls},[i("span",{ref:"textInfo"},[t._v(t._s(t.wrap?t.currentText:t.currentValue))]),i("a",{directives:[{name:"show",rawName:"v-show",value:t.isTextOverFlow,expression:"isTextOverFlow"}],staticClass:"achor",on:{click:function(e){return t.toggleExpand()}}},[t._v(t._s(t.achorText))])])])]},proxy:!0}:null,{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0}],null,!0),model:{value:t.currentValue,callback:function(e){t.currentValue=e},expression:"currentValue"}})},S=[],D=(i("c975"),{name:"vinput",props:["name","type","label","value","options","readonly","styleId","ctx","gridEditParm"],components:{popLabel:f["a"]},data:function(){return{achorExpand:!1,isSingleLine:!0,isTextOverFlow:!1,disabled:!1,currentValue:this.value,visible:!0,show:!0,showWordLimit:!1,wordLimit:null,placeholder:"",errorMessage:"",showPopover:!1,note:"",autoExpand:!1,rows:2,emptyHeight:24,wrap:!0,newStyle:!1}},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick},achorText:function(){return this.achorExpand?this.getLang("textA_packUp"):this.getLang("textA_expand")},achorCls:function(){return this.achorExpand?["f-textarea-line","open"]:"en"==window.idp.lang.getLang().id?["f-textarea-line","en"]:["f-textarea-line"]},currentText:function(){return this.achorExpand?this.value:this.value?this.value.split("\n")[0]:""}},mounted:function(){this.disabled=this.readonly,this.note=this.options.note,null==this.value&&(this.currentValue=""),this.placeholder=this.options.placeholder,this.newStyle=window.IDP_CUSTOM_TEXTAREA_NEWSTYLE||!1,this.options.editor_textarea&&(this.options.editor_textarea.wordlimit&&(this.wordLimit=this.options.editor_textarea.wordlimit,this.showWordLimit=!0),this.autoExpand=this.options.editor_textarea.autoExpand||!1,this.rows=2),this.calcEmptyHeight(),window.addEventListener("resize",this.calcTextOverFlow)},activated:function(){var t=this;this.achorExpand=!1,this.$nextTick((function(){t.calcTextOverFlow()}))},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},calcTextOverFlow:function(){var t=this,e=!1;if(this.achorExpand&&(e=!0,this.emptyHeight=0,this.achorExpand=!1),!this.newStyle){if(this.$refs.textWrap){if(this.$refs.textWrap.clientWidth<this.$refs.textWrap.scrollWidth)return this.isTextOverFlow=!0,this.achorExpand=this.autoExpand,this.wrap=!1,this.isTextOverFlow;if(this.value&&this.value.indexOf("\n")>=0)return this.isTextOverFlow=!0,this.achorExpand=this.autoExpand,this.wrap=!0,this.isTextOverFlow}return this.isTextOverFlow=!1,this.isTextOverFlow}this.isTextOverFlow=!1,this.emptyHeight=0,this.rows=2,this.$nextTick((function(){return t.$refs.textWrap&&t.$refs.textWrap.clientHeight<t.$refs.textWrap.scrollHeight?(t.isTextOverFlow=!0,t.achorExpand=t.autoExpand||e,t.calcEmptyHeight(),t.isTextOverFlow):(t.isTextOverFlow=!1,t.rows=3,t.achorExpand=t.autoExpand||e,t.calcEmptyHeight(),t.isTextOverFlow)}))},toggleExpand:function(){this.achorExpand=!this.achorExpand},onInputEvent:function(t){"null"!=t&&this.$emit("input",this.name,t,this)},setDisabled:function(t){this.readonly||(this.disabled=t)},setRequired:function(t){this.options.required=t,this.$forceUpdate()},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},setPlaceholder:function(t){this.placeholder=t},setErrorMessage:function(t){this.errorMessage=t},clearValue:function(){this.currentValue="",this.$emit("input",this.name,this.currentValue,this)},calcEmptyHeight:function(){var t=this;this.newStyle&&(this.emptyHeight=0,this.$nextTick((function(){var e=t.$refs["f-text-container"];t.emptyHeight=e?e.clientHeight-t.getComputedLineHeight(e):24})))},getComputedLineHeight:function(t){var e=window.getComputedStyle(t),i=parseFloat(e.lineHeight),s=e.lineHeight.slice(-2),n="px"===s?1:"em"===s?parseFloat(e.fontSize):1;return i*n}},watch:{options:function(){var t=this;this.achorExpand=!1,this.visible=!0,this.disabled=1==this.readonly,this.placeholder=this.options.placeholder,this.options.editor_textarea&&(this.options.editor_textarea.wordlimit&&(this.wordLimit=this.options.editor_textarea.wordlimit,this.showWordLimit=!0),this.autoExpand=this.options.editor_textarea.autoExpand||!1),this.$nextTick((function(){t.calcTextOverFlow()}))},value:function(t){var e=this;this.currentValue=null==t?"":t,this.errorMessage="",this.achorExpand=!1,this.$nextTick((function(){e.calcTextOverFlow()}))},currentValue:function(){var t=this;this.achorExpand=!1,this.$nextTick((function(){t.calcTextOverFlow()}))},disabled:function(){var t=this;this.achorExpand=!1,this.$nextTick((function(){t.calcTextOverFlow()}))},show:function(t){var e=this;t&&(this.achorExpand=!1,this.$nextTick((function(){e.$refs.input.adjustSize&&e.$refs.input.adjustSize(),e.calcTextOverFlow()})))},visible:function(t){var e=this;t&&(this.achorExpand=!1,this.$nextTick((function(){e.$refs.input.adjustSize&&e.$refs.input.adjustSize(),e.calcTextOverFlow()})))},achorExpand:function(t){this.calcEmptyHeight()}}}),E=D,P=(i("aed2"),Object(w["a"])(E,L,S,!1,null,"5a68e994",null)),O=P.exports,B=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("van-collapse",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],attrs:{accordion:""},model:{value:t.expand,callback:function(e){t.expand=e},expression:"expand"}},[i("van-collapse-item",{attrs:{title:t.label,name:"1"}},[t.value?i("div",{staticStyle:{width:"100%",overflow:"auto"}},[i("div",{domProps:{innerHTML:t._s(t.value)}})]):i("div",{staticClass:"f-empty-list"},[i("div",{class:{"f-empty-imgShow":!0,"f-empty-imgShow-chs":"zh-CHS"==t.langCode,"f-empty-imgShow-cht":"zh-CHT"==t.langCode,"f-empty-imgShow-en":"en"==t.langCode}},[i("img",{staticClass:"f-empty-image"})])])])],1)],1)},R=[],M={name:"vrichtext",props:["name","type","label","value","options","readonly","styleId","ctx","gridEditParm"],data:function(){return{expand:"",isFold:!1,autoFold:!1,visible:!0,show:!0,langCode:"zh-CHS"}},mounted:function(){this.autoExpand=this.options.editor_richeditor.autoExpand||!1,this.autoExpand&&this.toggleExpand(),this.langCode=window.idp.lang.getLang().id},methods:{setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},toggleExpand:function(){"1"==this.expand?this.expand="":this.expand="1"}}},V=M,A=(i("1c58"),Object(w["a"])(V,B,R,!1,null,"0702ac84",null)),N=A.exports,q=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.isSwitch?t._e():i("van-field",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],attrs:{name:"checkbox",label:t.label,required:t.options.required,"error-message":t.errorMessage,"error-message-align":"right"},scopedSlots:t._u([{key:"input",fn:function(){return[t.isBtn?t._e():i("van-checkbox-group",{attrs:{disabled:t.disabled,direction:"horizontal"},on:{change:t.onInputEvent},model:{value:t.currentValue,callback:function(e){t.currentValue=e},expression:"currentValue"}},t._l(t.data,(function(e,s){return i("van-checkbox",{key:s,class:{"f-input-checked":t.currentValue.includes(e.name)},staticStyle:{margin:"5px"},attrs:{name:e.name,shape:"square"},on:{click:t.onClick}},[t._v(t._s(e.text))])})),1),t.isBtn?i("check-button",{attrs:{data:t.data,disabled:t.disabled},on:{input:t.onInputEvent},model:{value:t.currentValue,callback:function(e){t.currentValue=e},expression:"currentValue"}}):t._e()]},proxy:!0},{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0}],null,!1,2586284631)}),t.isSwitch?i("van-field",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],staticClass:"f-switch",attrs:{center:"",required:t.options.required,label:t.label,"error-message":t.errorMessage,"error-message-align":"right"},scopedSlots:t._u([{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0},{key:"input",fn:function(){return[i("div")]},proxy:!0},{key:"right-icon",fn:function(){return[i("van-switch",{attrs:{disabled:t.disabled,size:"22px"},on:{click:t.onClick,input:t.onInputEvent},model:{value:t.switchOn,callback:function(e){t.switchOn=e},expression:"switchOn"}})]},proxy:!0}],null,!1,1054591923)}):t._e()],1)},z=[],U=(i("4de4"),i("a15b"),i("27ae")),j=i("c0bb"),H=i("b914"),W={name:"vcheckbox",props:["name","type","label","value","options","readonly","styleId"],inject:["viewTag"],components:{checkButton:H["a"],popLabel:f["a"]},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick}},data:function(){return{isBtn:!1,currentValue:[],data:[],editor:{},disabled:!0,visible:!0,show:!0,errorMessage:"",showPopover:!1,note:"",isSwitch:!1,switchOn:!1}},activated:function(){this.value&&(this.currentValue=this.value.split(",")),this.editor.ismul||1!=this.currentValue.length||"0"!=this.currentValue[0]||(this.currentValue=[]),this.isSwitch&&"1"==this.value&&(this.switchOn=!0)},mounted:function(){console.log("checkbox 初始"),console.log(this.value),console.log(this.options),this.disabled=this.readonly,this.note=this.options.note,this.initCheckBoxOptions(),this.value&&(this.currentValue=this.value.split(",")),this.editor.ismul||1!=this.currentValue.length||"0"!=this.currentValue[0]||(this.currentValue=[]),this.isSwitch&&"1"==this.value&&(this.switchOn=!0)},methods:{handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},emitListeners:function(t,e){var i=this.options.id,s="checklist";return this.gridEditParm&&(i=this.gridEditParm.id,s="grid",e.push(this.gridEditParm.field),e.push(this.gridEditParm.rowindex),e.push(this.gridEditParm.record)),p["a"].$emit("component-change",t,s,i,e,this.styleId,this.viewTag?this.viewTag():void 0)},setDisabled:function(t){this.readonly||(this.disabled=t)},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},setRequired:function(t){this.options.required=t,this.$forceUpdate()},initCheckBoxOptions:function(){var t=this.options.editor_checklist;if(this.data=[],this.editor=t,this.ismul=this.editor.ismul||!1,this.ismul&&(t.isBtn||window.IDP_CUSTOM_ALLCHECKBUTTON)&&(this.isBtn=!0),this.isSwitch=!this.ismul&&this.editor.switch,0==this.ismul){var e=[];e.push({name:"1",text:t.label}),this.data=e}else if("str"==t.datatype){var i=U["Base64"].decode(t.json),s=i.split(";");for(var n in s){var o=s[n].split(",");this.data.push({name:o[0],text:o[1],checked:!1})}}else{var a=j["a"].getSelectOpitons(this.styleId,this.options.id);for(var r in a)this.data.push({name:a[r][t.key],text:a[r][t.value],checked:!1})}},onClick:function(){var t;0==this.editor.ismul||this.isSwitch?(t=this.isSwitch?this.switchOn:1==this.currentValue.length&&"1"==this.currentValue[0],t?this.emitListeners("click",[this,"1"]):(this.$emit("input",this.name,"0",this),this.emitListeners("click",[this,"0"]))):(this.$emit("input",this.name,this.currentValue.filter((function(t){return t})).join(","),this),this.emitListeners("click",[this,this.currentValue.filter((function(t){return t})).join(",")]))},onInputEvent:function(t){var e;(this.setErrorMessage(""),console.log(this.currentValue),0==this.editor.ismul||this.isSwitch)?(e=this.isSwitch?this.switchOn:1==this.currentValue.length&&"1"==this.currentValue[0],e?(this.$emit("input",this.name,"1",this),this.emitListeners("selected",[this,"1"])):(this.$emit("input",this.name,"0",this),this.emitListeners("selected",[this,"0"]))):(this.$emit("input",this.name,this.currentValue.filter((function(t){return t})).join(","),this),this.emitListeners("selected",[this,this.currentValue.filter((function(t){return t})).join(",")]))},setErrorMessage:function(t){this.errorMessage=t},clearValue:function(){this.currentValue=[],this.$emit("input",this.name,this.currentValue,this)}},watch:{value:function(t){this.setErrorMessage(""),console.log(t),this.editor.ismul?this.currentValue=null!=t?t.split(","):[]:(this.currentValue="1"==t?["1"]:[],this.isSwitch&&"1"==t?this.switchOn=!0:this.switchOn=!1)},options:function(){this.visible=!0,this.disabled=this.readonly,this.initCheckBoxOptions()}}},G=W,J=(i("d5c3"),Object(w["a"])(G,q,z,!1,null,null,null)),K=J.exports,X=i("3a4e"),Q=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}]},[i("van-field",{class:{"f-input-readonly":t.disabled},attrs:{"is-link":!t.disabled,readonly:"",required:t.options.required,clickable:"",name:"calendar",value:t.currentText,label:t.label,placeholder:t.placeholder,"error-message-align":"right","error-message":t.errorMessage},on:{click:t.openCalendar},scopedSlots:t._u([{key:"right-icon",fn:function(){return[t.disabled||!t.currentText||t.notClear?t._e():i("van-icon",{attrs:{name:"clear"},on:{touchend:function(e){return e.stopPropagation(),e.preventDefault(),t.clearValue(e)},click:function(e){return e.stopPropagation(),t.clearValue(e)}}})]},proxy:!0},{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0}])}),t.isCalender?i("van-calendar",{ref:"calendar",attrs:{"get-container":"#app",type:t.getType(),title:t.getLang("date_title"),"confirm-text":t.getLang("date_confirm"),range:t.range,color:"#388fff",round:!1,"min-date":t.minDate,"max-date":t.maxDate,"allow-same-day":t.sameDay,"show-subtitle":!1},on:{confirm:t.onConfirm,opened:t.openedCalendar},scopedSlots:t._u([{key:"title",fn:function(){return[i("div",{staticClass:"timeSelect"},[i("van-icon",{attrs:{name:"arrow-left",color:"#388fff",size:"23"},on:{click:function(e){return t.scrollToDate("down","year")}}}),i("van-icon",{attrs:{name:"arrow-left",color:"#388fff",size:"15"},on:{click:function(e){return t.scrollToDate("down","month")}}}),i("p",[t._v(t._s(t.calendarSub))]),i("van-icon",{attrs:{name:"arrow",color:"#388fff",size:"15"},on:{click:function(e){return t.scrollToDate("up","month")}}}),i("van-icon",{attrs:{name:"arrow",color:"#388fff",size:"23"},on:{click:function(e){return t.scrollToDate("up","year")}}})],1)]},proxy:!0}],null,!1,3260762219),model:{value:t.showCalendar,callback:function(e){t.showCalendar=e},expression:"showCalendar"}}):t._e(),i("van-popup",{attrs:{position:"bottom","get-container":"#app"},model:{value:t.showPicker,callback:function(e){t.showPicker=e},expression:"showPicker"}},[i("van-datetime-picker",{attrs:{type:"datetime",title:t.getLang("date_choosedatetime"),"confirm-button-text":t.getLang("date_confirm"),"cancel-button-text":t.getLang("date_cancel"),"min-date":t.minDateForPicker,"max-date":t.maxDateForPicker},on:{confirm:t.onConfirm,cancel:function(e){t.showPicker=!1}},scopedSlots:t._u([{key:"columns-top",fn:function(){return[i("div",[i("div",{staticClass:"f-date-column-top"},[t._v(t._s(t.getLang("date_year")))]),i("div",{staticClass:"f-date-column-top"},[t._v(t._s(t.getLang("date_month")))]),i("div",{staticClass:"f-date-column-top"},[t._v(t._s(t.getLang("date_day")))]),i("div",{staticClass:"f-date-column-top"},[t._v(t._s(t.getLang("date_hour")))]),i("div",{staticClass:"f-date-column-top"},[t._v(t._s(t.getLang("date_minute")))])])]},proxy:!0}]),model:{value:t.currentDate,callback:function(e){t.currentDate=e},expression:"currentDate"}})],1),i("van-popup",{attrs:{round:"",position:"bottom"},model:{value:t.yearPicker,callback:function(e){t.yearPicker=e},expression:"yearPicker"}},[i("van-picker",{attrs:{"show-toolbar":"",columns:t.columns,defaultIndex:"20",title:t.yearTitle},on:{cancel:function(e){t.yearPicker=!1},confirm:t.onConfirm},scopedSlots:t._u([{key:"columns-top",fn:function(){return[i("div",t._l(t.columnTitle,(function(e,s){return i("div",{key:s,staticClass:"f-date-column-top",style:t.columnStyle},[t._v(t._s(e))])})),0)]},proxy:!0}])})],1)],1)},Y=[],Z=(i("e25e"),i("c1df")),tt=i.n(Z),et=i("c466"),it={name:"vinput",props:["name","type","label","value","options","readonly","styleId","ctx","gridEditParm"],components:{popLabel:f["a"]},data:function(){return{showPicker:!1,showCalendar:!1,yearPicker:!1,currentValue:this.value,currentDate:new Date,currentText:"",range:!1,format:"yyyy-MM-dd",disabled:!1,minDate:new Date(2010,0,1),maxDate:new Date(2030,0,1),visible:!0,show:!0,placeholder:"",columns:[],yearTitle:"",columnTitle:[],errorMessage:"",note:"",showPopover:!1,isCalender:!1,minDateForPicker:new Date(1900,0,1),maxDateForPicker:new Date(2099,0,1),sameDay:!1,notClear:!1,calendarSub:""}},mounted:function(){this.disabled=this.readonly,this.placeholder=this.options.placeholder,this.note=this.options.note,this.format=this.options.editor_date.format||"yyyy-MM-dd",this.range=!!this.options.editor_date.range,this.notClear=this.options.editor_date.notcancelable||!1,""!=this.options.editor_date.showType&&null!=this.options.editor_date.showType&&void 0!=this.options.editor_date.showType||(this.isCalender=!0,this.sameDay=this.options.editor_date.sameDay);var t=new Date;if(""!==this.options.editor_date.min&&null!==this.options.editor_date.min&&void 0!==this.options.editor_date.min){var e=t.getTime();e+=864e5*this.options.editor_date.min,this.minDate=new Date(e)}if(""!==this.options.editor_date.max&&null!==this.options.editor_date.max&&void 0!==this.options.editor_date.max){var i=t.getTime();i+=864e5*this.options.editor_date.max,this.maxDate=new Date(i)}this.makeColumns(),this.value?this.currentText=et["a"].dateFormat(this.value,this.format):this.currentText=""},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},clearValue:function(){this.currentValue="",this.currentText="",this.$emit("input",this.name,this.currentText,this)},onInputEvent:function(t){this.$emit("input",this.name,t,this)},openCalendar:function(){this.readonly||this.disabled||("datetime"==this.options.editor_date.showType?this.showPicker=!0:"year"==this.options.editor_date.showType||"time"==this.options.editor_date.showType||"month"==this.options.editor_date.showType?("year"==this.options.editor_date.showType?this.yearTitle=this.getLang("date_chooseY"):"month"==this.options.editor_date.showType?this.yearTitle=this.getLang("date_chooseM"):this.yearTitle=this.getLang("date_chooseT"),this.yearPicker=!0):this.showCalendar=!0)},onConfirm:function(t){var e=new Date;t.length>1?2==t.length?"month"==this.options.editor_date.showType?(e.setFullYear(t[0]),e.setMonth(t[1]-1,1),this.currentValue=et["a"].getFormatData(e,this.format)):(e.setHours(t[0]),e.setMinutes(t[1]),this.range?this.currentValue=et["a"].getFormatData(t[0],this.format)+" - "+et["a"].getFormatData(t[1],this.format):this.currentValue=et["a"].getFormatData(e,this.format)):3==t.length&&(e.setHours(t[0]),e.setMinutes(t[1]),e.setSeconds(t[2]),this.currentValue=et["a"].getFormatData(e,this.format)):"year"==this.options.editor_date.showType?this.currentValue=String(t):"month"==this.options.editor_date.showType?this.currentValue=t[0]:this.currentValue=et["a"].getFormatData(t,this.format),this.currentText=this.currentValue,this.showCalendar=!1,this.showPicker=!1,this.yearPicker=!1,this.$emit("input",this.name,this.currentValue,this)},setDisabled:function(t){this.readonly||(this.disabled=t)},getValue:function(){return this.currentValue},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},setRequired:function(t){this.options.required=t,this.$forceUpdate()},getType:function(){return this.range?"range":"single"},makeColumns:function(){this.columnTitle=[];var t=[];if("year"==this.options.editor_date.showType)for(var e=new Date,i=e.getFullYear()-20,s=e.getFullYear()+20,n=i;n<=s;n++)t.push(n);else if("month"==this.options.editor_date.showType){for(var o={values:[],defaultIndex:0},a=1;a<13;a++){var r=a;o.values.push(r)}for(var l={values:[],defaultIndex:20},c=new Date,h=c.getFullYear()-20,d=c.getFullYear()+20,u=h;u<=d;u++)l.values.push(u);t.push(l),t.push(o),this.columnTitle.push(this.getLang("date_year")),this.columnTitle.push(this.getLang("date_month"))}else{for(var f={values:[],defaultIndex:1},p={values:[],defaultIndex:1},m=0;m<24;m++)f.values.push(m);for(var g=0;g<60;g++)g<10&&(g="0"+g.toString()),p.values.push(g);t.push(f),this.columnTitle.push(this.getLang("date_hour")),t.push(p),this.columnTitle.push(this.getLang("date_minute")),null!==this.options.editor_date.format&&void 0!==this.options.editor_date.format&&this.options.editor_date.format.indexOf("s")>-1&&(t.push(p),this.columnTitle.push(this.getLang("date_second")))}this.columns=t},scrollToDate:function(t,e){var i=et["a"].dateFormat(this.calendarSub,"yyyy-MM").split("-"),s=new Date(i[0],i[1]-1),n="";"up"==t&&(n=tt()(s).add(1,e)),"down"==t&&(n=tt()(s).add(-1,e));var o=tt()(n).diff(tt()(this.minDate),"months"),a=tt()(this.maxDate).diff(tt()(n),"months");if(o<=-13||a<-11)return window.idp.warn(this.getLang("date_overrange")),null;var r=n.format("yyyy-MM").split("-");this.$refs.calendar.scrollToDate(new Date(r[0],r[1]))},openedCalendar:function(){var t=this;this.$watch((function(){return t.$refs.calendar.subtitle}),(function(e){var i=et["a"].dateFormat(e,"yyyy-MM").split("-"),s=new Date(i[0],i[1]),n=tt()(s).add(1,"months");if(n<=t.maxDate&&n>=t.minDate){tt()(n).diff(tt()(t.minDate),"months")<1&&(n=tt()(s));var o=n.format("yyyy"),a=parseInt(n.format("MM"));t.calendarSub=t.getLang("date_subtitle",{0:o,1:a})}}),{immediate:!0})},setErrorMessage:function(t){this.errorMessage=t}},watch:{value:function(t){this.currentValue=t,this.errorMessage="","time"==this.options.editor_date.showType?this.currentText=t:t?this.range?this.currentText=t:"month"==this.options.editor_date.showType&&this.format.indexOf("y")<0&&this.format.indexOf("Y")<0?this.currentText=t:this.currentText=et["a"].dateFormat(t,this.format):this.currentText=""},options:function(){this.disabled=this.readonly,this.placeholder=this.options.placeholder,this.visible=!0,this.format=this.options.editor_date.format||"yyyy-MM-dd",this.range=!!this.options.editor_date.range,this.value?this.currentText=et["a"].dateFormat(this.value,this.format):this.currentText=""}},computed:{getContainer:function(){return this.options&&this.options.editor_date&&this.options.editor_date.transfer?"body":null},showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick},columnStyle:function(){var t=this.columnTitle.length>0?100/this.columnTitle.length:0,e="width:"+t+"%";return e}}},st=it,nt=(i("ed64"),Object(w["a"])(st,Q,Y,!1,null,null,null)),ot=nt.exports,at=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}]},[i("van-field",{staticClass:"f-select-moreLine",class:{"f-input-readonly":t.disabled},attrs:{type:t.inputType,"is-link":!t.disabled,readonly:"",clickable:"",name:"picker",value:t.currentText,label:t.label,placeholder:t.placeholder,required:t.options.required,"error-message":t.errorMessage,"error-message-align":"right",rows:"1",autosize:""},on:{click:t.openPicker},scopedSlots:t._u([{key:"right-icon",fn:function(){return[!t.disabled&&t.currentText&&t.showClear?i("van-icon",{attrs:{name:"clear"},on:{touchend:function(e){return e.stopPropagation(),e.preventDefault(),t.clearValue(e)},click:function(e){return e.stopPropagation(),t.clearValue(e)}}}):t._e()]},proxy:!0},{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0}])}),i("van-popup",{class:{"f-dropdown-isMul":t.isMul,"f-dropdown-isMul-overflow":t.isMul&&t.data.length>=6},attrs:{position:"bottom",round:t.isMul,"get-container":"body"},on:{open:t.openPopup,close:t.closePopup},model:{value:t.showPicker,callback:function(e){t.showPicker=e},expression:"showPicker"}},[t.isMul?t._e():i("van-picker",{attrs:{title:t.getLang("dropdown_pleaseSelect"),"show-toolbar":"",columns:t.data},on:{confirm:t.onConfirm,cancel:function(e){t.showPicker=!1}}}),t.isMul?i("div",[i("div",{staticClass:"dropdowngroup"},[i("van-checkbox-group",{ref:t.dropdownGroup,model:{value:t.checked,callback:function(e){t.checked=e},expression:"checked"}},[i("van-cell-group",{attrs:{inset:""}},t._l(Object.keys(t.hashIndex),(function(e){return i("van-cell",{key:e,attrs:{clickable:"",title:t.data[e]},on:{click:function(i){return t.toggle(e)}},scopedSlots:t._u([{key:"right-icon",fn:function(){return[i("van-checkbox",{ref:e,refInFor:!0,attrs:{name:e},on:{click:function(t){t.stopPropagation()}}})]},proxy:!0}],null,!0)})})),1)],1)],1),i("div",{staticClass:"f-toolbar"},[i("a",{staticClass:"f-toolbar-item  vertical",on:{click:t.cancelSelect}},[t._v(" "+t._s(t.getLang("dropdown_cancel")))]),i("a",{staticClass:"f-toolbar-item  primary highlight ",staticStyle:{margin:"10px"},on:{click:t.confirmSelect}},[t._v(" "+t._s(t.getLang("dropdown_confirm")))])])]):t._e()],1),i("van-action-sheet",{attrs:{actions:t.actions,"cancel-text":t.getLang("dropdown_cancel"),"close-on-click-action":""},on:{cancel:function(e){t.showAction=!1},select:t.onSelect},model:{value:t.showAction,callback:function(e){t.showAction=e},expression:"showAction"}})],1)},rt=[],lt=(i("4160"),i("159b"),{name:"vselect",props:["name","type","label","value","options","readonly","styleId","ctx","gridEditParm"],components:{popLabel:f["a"]},inject:["viewTag"],computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick}},data:function(){return{showAction:!1,showPicker:!1,currentValue:"",showClear:!0,isMul:!1,currentText:"",data:[],hashIndex:{},orgData:[],actions:[],isAction:!1,hashKeys:{},visible:!0,show:!0,disabled:!1,placeholder:"",errorMessage:"",showPopover:!1,note:"",inputType:"textarea",dropdownGroup:"dropdownGroup",checked:[]}},mounted:function(){console.log("dropdown 初始"),console.log(this.value),console.log(this.options),this.initSelectOptions(),this.disabled=this.readonly,this.placeholder=this.options.placeholder,this.showClear=!this.options.editor_dropdown.notcancelable,this.note=this.options.note},activated:function(){},methods:{handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},getLang:function(t,e){return window.idp.lang.get(t,e)},emitListeners:function(t,e){var i=this.options.id,s=this.options.type;return this.gridEditParm&&(i=this.gridEditParm.id,s="grid",e.push(this.gridEditParm.field),e.push(this.gridEditParm.rowindex),e.push(this.gridEditParm.record)),p["a"].$emit("component-change",t,s,i,e,this.styleId,this.viewTag?this.viewTag():void 0)},updateMapInfo:function(t){var e=this.options["editor_dropdown"];if("str"!=e.datatype&&e.returncols&&e.setcols){var i=e.returncols.split(";"),s=e.setcols.split(";");for(var n in i)this.$emit("input",s[n],this.orgData[t][i[n]],this)}},initSelectOptions:function(){this.data=[],this.actions=[],this.hashIndex={};var t=this,e=this.options.editor_dropdown;if(this.isMul=e.ismul||!1,this.isAction=e.isAction,"str"==e.datatype){var i=U["Base64"].decode(e.json),s=i.split(";");for(var n in s){var o=s[n].split(",");this.data.push(o[1]),this.hashKeys[o[0]]=n,this.hashIndex[n]=o[0]}}else{var a=j["a"].getSelectOpitons(this.styleId,this.options.id);for(var r in a=this.isMul?a.slice(0,1e3):a,a)this.data.push(a[r][e.value]),this.hashKeys[a[r][e.key]]=r,this.hashIndex[r]=a[r][e.key];this.orgData=a,console.log(a)}this.data.forEach((function(e){t.actions.push({name:e})})),this.currentText=this.getText(this.value)},getText:function(t){var e=this;if("string"==typeof t&&-1!=t.indexOf(";")){var i=t.split(";"),s=[];return i.forEach((function(t){s.push(e.data[e.hashKeys[t]])})),s.join(";")}var n=this.hashKeys[t];return this.data[n]},onInputEvent:function(t){this.$emit("input",this.name,t,this)},setDisabled:function(t){this.readonly||(this.disabled=t)},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},setRequired:function(t){this.options.required=t,this.$forceUpdate()},openPicker:function(){this.readonly||this.disabled||(this.isAction?this.showAction=!0:this.showPicker=!0)},onConfirm:function(t,e){this.currentText=t,this.currentValue=this.hashIndex[e],console.log(arguments),this.showPicker=!1,this.onAfterSelect(e)},onSelect:function(t,e){this.currentText=t.name,this.currentValue=this.hashIndex[e],console.log(arguments),this.showAction=!1,this.onAfterSelect(e)},onAfterSelect:function(t){this.$emit("input",this.name,this.currentValue,this),this.emitListeners("selected",[this,this.currentValue,this.data]),this.updateMapInfo(t)},clearValue:function(){this.currentText="",this.currentValue="",this.emitListeners("clearValue",[this]),this.$emit("input",this.name,this.currentText,this)},setErrorMessage:function(t){this.errorMessage=t},onAfterCheck:function(){this.$emit("input",this.name,this.currentValue,this),this.emitListeners("selected",[this,this.currentValue,this.data]);var t=[];t=-1==this.currentValue.indexOf(";")?t.push(this.currentValue):this.currentValue.split(";"),this.updateMapInfo(t)},stopClick:function(){},toggle:function(t){this.$refs[t][0].toggle()},openPopup:function(){var t=this;this.isMul&&this.currentValue&&(-1==this.currentValue.indexOf(";")?this.$refs[this.hashKeys[this.currentValue]][0].toggle(!0):this.currentValue.split(";").forEach((function(e){t.$refs[t.hashKeys[e]][0].toggle(!0)})))},closePopup:function(){this.isMul&&this.$refs.dropdownGroup.toggleAll(!1)},cancelSelect:function(){this.showPicker=!1},confirmSelect:function(){var t=this,e=this.checked.filter((function(t){return""!=t})),i=[],s=[];e.forEach((function(e){s.push(t.hashIndex[e]),i.push(t.data[e])})),this.currentValue=s.join(";"),this.currentText=i.join(";"),this.showPicker=!1,this.onAfterCheck()}},watch:{value:function(t){this.currentText=this.getText(t),this.errorMessage=""},options:function(){this.initSelectOptions(),this.disabled=this.readonly,this.visible=!0,this.placeholder=this.options.placeholder,this.showClear=!this.options.editor_dropdown.notcancelable}}}),ct=lt,ht=(i("444e"),Object(w["a"])(ct,at,rt,!1,null,"d0638c64",null)),dt=ht.exports,ut=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}]},[t.textmode?t._e():i("van-field",{ref:t.refElement,staticClass:"f-lookup-moreLine",class:{"f-input-readonly":t.disabled},attrs:{placeholder:t.placeholder,"is-link":!t.disabled&&!t.rightView&&!t.textmode,readonly:"",clickable:"",name:"lookup",type:t.inputType,value:t.showText,label:t.label,required:t.options.required,rows:"1",autosize:"","error-message":t.errorMessage,"error-message-align":"right"},on:{click:t.openLookUp},scopedSlots:t._u([{key:"right-icon",fn:function(){return[t.disabled||!t.currentText||t.notClear?t._e():i("van-icon",{attrs:{name:"clear"},on:{touchend:function(e){return e.stopPropagation(),e.preventDefault(),t.clearValue(e)},click:function(e){return e.stopPropagation(),t.clearValue(e)}}})]},proxy:!0},{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0},t.rightView?{key:"extra",fn:function(){return[i(t.rightView,{tag:"div",attrs:{text:t.currentText,value:t.currentValue}})]},proxy:!0}:null],null,!0)}),t.textmode?i("van-field",{ref:t.refElement,class:{"f-input-readonly":t.disabled},attrs:{disabled:t.disabled,placeholder:t.placeholder,name:"lookup",value:t.showText,label:t.label,required:t.options.required,"error-message":t.errorMessage,"error-message-align":"right"},on:{blur:t.onBlur,focus:t.onFocus,input:t.onInputEvent,keypress:t.onKeyPress},scopedSlots:t._u([{key:"right-icon",fn:function(){return[t.disabled||!t.currentText||t.notClear?t._e():i("van-icon",{attrs:{name:"clear"},on:{touchend:function(e){return e.stopPropagation(),e.preventDefault(),t.clearValue(e)},click:function(e){return e.stopPropagation(),t.clearValue(e)}}})]},proxy:!0},{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0},{key:"extra",fn:function(){return[i("div",{staticClass:"van-field__right-icon",staticStyle:{padding:"0 6px"}},[t.disabled||t.rightView?t._e():i("van-icon",{attrs:{name:"arrow"},on:{touchend:function(e){return e.stopPropagation(),e.preventDefault(),t.openLookupWithoutVal(e)},click:function(e){return e.stopPropagation(),t.openLookupWithoutVal(e)}}})],1),t.rightView?i(t.rightView,{tag:"div",attrs:{text:t.currentText,value:t.currentValue}}):t._e()]},proxy:!0}],null,!1,1608040568)}):t._e()],1)},ft=[],pt=(i("96cf"),i("1da1")),mt={name:"vinput",props:["id","name","type","label","value","options","readonly","styleId","gridEditParm","ctx"],inject:["viewTag"],components:{popLabel:f["a"]},data:function(){return{rightView:null,currentValue:this.value,currentText:this.value,visible:!0,show:!0,disabled:!1,placeholder:"",notClear:!1,errorMessage:"",textmode:!1,refElement:"input",showPopover:!1,putChangeFilter:!1,note:"",inputType:"textarea",valiInfo:{},asterProtection:!1,asterInput:!1,asterType:""}},mounted:function(){console.log(this.options),console.log(this.gridEditParm),this.disabled=this.readonly,this.textmode=this.options["editor_lookup"].textmode,this.asterProtection=this.options.asterProtection||!1,this.asterInput=this.options.asterInput||!1,this.asterType=this.options.asterType||"Default",this.valiField=this.options.idfield||"",this.textmode&&(this.notAutoSel=this.options["editor_lookup"].notAutoSel),this.placeholder=this.options.placeholder,this.notClear=this.options["editor_lookup"].notcancelable,this.rightView=this.buildSingleComponent("rightTpl","rightView",["value","text"]),this.codeField=this.options["editor_lookup"].codefield,this.note=this.options.note},activated:function(){if(this.$route.query.returnValue){var t=this.$route.query.returnValue.controlId;if(t==this._uid)if(Array.isArray(this.$route.query.returnValue.data)){var e=this.$route.query.returnValue.data;this.updateMapInfoList(e),this.emitListeners("selected",[this,e])}else{var i=this.$route.query.returnValue.data;console.log("帮助回弹.."),this.updateMapInfo(i),this.emitListeners("selected",[this,[i]])}}},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick},showText:function(){var t=this.options["editor_lookup"];if(t.getText){var e=new Function("data","return ("+t.getText+")(data)");return e(this.getRow())}return this.asterProtection?window.idp.utils.asterFormat(this.currentText,this.asterType):this.currentText}},methods:{handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},clearValue:function(){this.currentValue="",this.currentText="",this.putChangeFilter=!1;var t=this.options["editor_lookup"];if(this.emitListeners("clearValue",[this]),this.$emit("input",this.name,this.currentText),t.returncols&&t.setcols){var e=t.returncols.split(";"),i=t.setcols.split(";");for(var s in e)this.$emit("input",i[s],"")}},buildSingleComponent:function(t,e,i){var s=this.options["editor_lookup"];if(s[t]&&s[t].template){var n={};if(s[t].mixin){var o=new Function("return "+s[t].mixin+";");n=o()}return this[e]=c["a"].extend({mixins:[n],props:i,template:s[t].template,data:function(){return{}},methods:{}}),this[e]}},getValue:function(){return this.currentValue},getText:function(){return this.currentText},getRow:function(){return this.gridEditParm?this.gridEditParm.record:this.ctx.data.formItems},emitListeners:function(t,e){var i=this.options.id,s="lookup";return this.gridEditParm&&(i=this.gridEditParm.id,s="grid",e.push(this.gridEditParm.field),e.push(this.gridEditParm.rowindex),e.push(this.gridEditParm.record)),p["a"].$emit("component-change",t,s,i,e,this.styleId,this.viewTag?this.viewTag():void 0)},setDisabled:function(t){this.readonly||(this.disabled=t)},setRequired:function(t){this.options.required=t,this.$forceUpdate()},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},updateMapInfo:function(t){var e=this.options["editor_lookup"];if(this.putChangeFilter=!1,this.currentValue=this.currentText=t[e.namefield],e.namefield!=e.idfield&&(this.currentValue=t[e.idfield]),this.codeField=t[e.codefield],this.$emit("input",this.name,this.currentText,this),e.returncols&&e.setcols){var i=e.returncols.split(";"),s=e.setcols.split(";");for(var n in i)this.$emit("input",s[n],t[i[n]])}},updateMapInfoList:function(t){this.currentText="",this.currentValue="",this.codeField="",this.putChangeFilter=!1;var e=this.options["editor_lookup"];if(e.returncols&&e.setcols)var i=e.returncols.split(";"),s=e.setcols.split(";");var n=[],o=[],a=[];for(var r in t)n.push(t[r][e.namefield]),o.push(t[r][e.idfield]),a.push(t[r][e.codefield]);if(this.currentText=n.join(";"),this.currentValue=o.join(";"),this.codeField=a.join(";"),this.$emit("input",this.name,this.currentText,this),e.returncols&&e.setcols)for(var l in i){var c=this.getMultiValue(t,i[l]).join(";");this.$emit("input",s[l],c)}},getMultiValue:function(t,e){var i=[];return t.forEach((function(t){i.push(t[e])})),i},getFilter:function(){var t=this;return Object(pt["a"])(regeneratorRuntime.mark((function e(){var i,s,n,o,a,r,l,c,h,d;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=[],s=[],n=t.options.editor_lookup,n.sysFilter)for(o in n.sysFilter)a=JSON.parse(JSON.stringify(n.sysFilter[o])),a.Value=t.parseValue(n.sysFilter[o].Value),(a.IsExpress||"in"==a.Operate||"not in"==a.Operate)&&(a.Value=u["a"].jse(a.Value)),"1"==a.FType&&(a.Value=Number(a.Value)),"2"==a.FType&&(a.IsDate=!0),o!=n.sysFilter.length-1||a.Logic||(a.Logic="AND"),s.push(a);if(n.filter)for(r in n.filter)l=JSON.parse(JSON.stringify(n.filter[r])),l.Value=t.parseValue(n.filter[r].Value),(l.IsExpress||"in"==l.Operate||"not in"==l.Operate)&&(l.Value=u["a"].jse(l.Value)),"1"==l.FType&&(l.Value=Number(l.Value)),"2"==l.FType&&(l.IsDate=!0),s.push(l);if(c=t.emitListeners("beforeHelpFilter",[t])||[],!c.then){e.next=11;break}return e.next=9,c;case 9:h=e.sent,c=h||[];case 11:return t.textmode&&!t.notAutoSel&&t.putChangeFilter&&(d=[{Field:n.idfield,IgnoreCase:n.ignoreCase||!1,IsExpress:!1,Left:"(",Logic:" or ",Operate:"like",Right:"",Value:t.currentText},{Field:n.namefield,IgnoreCase:n.ignoreCase||!1,IsExpress:!1,Left:"",Logic:"",Operate:"like",Right:")",Value:t.currentText}],s=s.concat(d)),c&&c.length>0&&s.length>0&&(s[s.length-1].Logic="and"),i=s.concat(c),i=t.emitListeners("overwriteHelpFilter",[t,i])||i,e.abrupt("return",i);case 16:case"end":return e.stop()}}),e)})))()},setEditor:function(t){var e=this.options["editor_lookup"];for(var i in t)e[i]=t[i]},parseValue:function(t){if(this.ctx&&this.ctx.params){var e=!1,i=0,s=null,n="";return this.gridEditParm&&(e=!0,i=this.gridEditParm.rowindex,s=this.gridEditParm.record,n=this.gridEditParm.table),this.ctx.params.parser(t,e,i,s,n)}return t},onBeforeConfirm:function(t){if(this.options.onBeforeConfirm&&0==this.options.onBeforeConfirm(t))return!1},openLookUp:function(){var t=this;return Object(pt["a"])(regeneratorRuntime.mark((function e(){var i,s,n,o,a,r,l,c;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.readonly&&!t.disabled){e.next=2;break}return e.abrupt("return");case 2:if(i=t.emitListeners("beforeOpen",[t,t.options]),s=t,0!=i){e.next=6;break}return e.abrupt("return");case 6:return n=t.options["editor_lookup"],e.next=9,t.getFilter();case 9:o=e.sent,a="/lookup",r="lookup",t.valiField?(t.valiInfo={},l=[],t.valiField==t.options.field?l=(t.currentValue||"").split(";"):!t.gridEditParm&&window.idp.uiview.formItem[t.valiField]?l=window.idp.uiview.formItem[t.valiField].split(";"):t.gridEditParm&&t.gridEditParm.record&&t.gridEditParm.record[t.valiField]&&(l=t.gridEditParm.record[t.valiField].split(";")),l.forEach((function(e){return t.valiInfo[e]=!0}))):t.valiInfo={},c={lookup:!0,controlId:t._uid,styleId:n.sqlid,realStyleId:n.sqlid,filter:o,title:n.title,async:n.async,favor:n.favor,formId:t.styleId,childonly:n.childonly,calcChildOnly:n.calcChildOnly,selectAll:n.selectall,onBeforeConfirm:function(t){return s.emitListeners("beforeConfirm",[s,t])},ctx:t.ctx,autoFold:n.autoFold,userJS:n.userJS,ttComp:n.ttComp,isMul:n.ismul,noCode:n.noCode,helpData:[],idField:n.idfield,valiInfo:t.valiInfo,helpInput:[],isTotal:n.isTotal,autoChild:n.autoChild,notOpenFavor:n.notOpenFavor,isForiegn:n.isForiegn,checkremember:n.checkremember,rootPager:n.rootPager,runtime:n.runtime,group:n.group,store:t.ctx?t.ctx.store:{},pager:n.pager,pageSize:n.pageSize,completed:n.completed,isIgnoreCase:n.isIgnoreCase,localSearch:n.localSearch,frequent:n.frequent,bizid:n.bizid,bizopid:n.bizopid},t.putChangeFilter&&(c.putChangeFilter=t.putChangeFilter),n.isCity&&(a="/city",r="city",c.cityConfig=n.cityConfig),n.completed&&(a="/"+n.sqlid+"/list",c.runtime&&(a="/"+n.sqlid+"~~~/list",c.runtimeCompleteMobileList=!0),r=n.sqlid+"list",c.empty=n.empty),t.$router.push({name:r,path:a,query:{id:"help"},params:c});case 18:case"end":return e.stop()}}),e)})))()},onInputEvent:function(t){this.$emit("input",this.name,t)},setErrorMessage:function(t){this.errorMessage=t},getCodeField:function(){return this.codeField},onFocus:function(){this.inputEvent=!0,console.log(arguments)},onBlur:function(){this.inputEvent=!1,this.notAutoSel?this.value=this.currentValue=this.currentText:this.textmode,console.log(arguments)},onKeyPress:function(){this.inputEvent=!0},matchInput:function(){this.openLookUp()},openLookupWithoutVal:function(){this.putChangeFilter=!1,this.openLookUp()},stopClick:function(){},setTextMode:function(t){this.textmode=t}},watch:{readonly:function(){this.disabled=this.readonly},options:function(){this.visible=!0,this.disabled=this.readonly,this.placeholder=this.options.placeholder,this.rightView=this.buildSingleComponent("rightTpl","rightView",["value","text"])},value:function(t){this.currentText=t,this.errorMessage=""}}},gt=mt,vt=(i("ac5c"),Object(w["a"])(gt,ut,ft,!1,null,null,null)),bt=vt.exports,wt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("van-field",{attrs:{name:"text",label:t.label},scopedSlots:t._u([{key:"input",fn:function(){return[i("div",{staticStyle:{flex:"1",width:"100px"}},[t._v(t._s(t.currentValue))])]},proxy:!0}])})},yt=[],Ct={name:"vtext",props:["name","type","label","value","options"],components:{},data:function(){return{currentValue:this.value}},methods:{onInputEvent:function(t){}},watch:{value:function(t){}}},kt=Ct,xt=Object(w["a"])(kt,wt,yt,!1,null,null,null),It=xt.exports,_t=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",t._l(t.data,(function(e,s){return i("div",{key:s,staticClass:"f-detail-card"},[i("div",{staticClass:"f-detail-card-title"},[t._v("明细"+t._s(s+1))]),t._l(t.ctrls,(function(s,n){return i("dylistcard",{key:n,attrs:{readonly:t.readonly,type:t.controls[s].type,vprops:t.controls[s],value:e[t.controls[s].field]}})}))],2)})),0)},Tt=[],Ft={name:"dylistcard",props:{type:{type:String,required:!0},vprops:{type:Object,required:!1},value:{type:Object,required:!1},readonly:{type:Boolean,required:!1}},data:function(){return{}},components:{dycontrol:Je},methods:{},render:function(t){return t(Je,{attrs:{type:this.type,vprops:this.vprops,value:this.value,readonly:this.readonly}})}},Lt=Ft,St=Object(w["a"])(Lt,s,n,!1,null,null,null),Dt=St.exports,Et={name:"dygrid",props:{styleId:{type:String,required:!1},gridId:{type:String,required:!1},type:{type:String,required:!1},title:{type:String,required:!1},controls:{type:Object,required:!1},vprops:{type:Object,required:!1},listItems:{type:Object,required:!1},readonly:{type:Boolean,required:!1}},components:{dylistcard:Dt},mounted:function(){this.initGridView(),console.log(this.controls)},data:function(){return{data:[],ctrls:[],activeNames:[0]}},methods:{initGridView:function(){this.ctrls=[];var t=this.vprops.id,e=this.vprops.table;this.data=this.listItems[e]||[];var i=this.vprops.cols;for(var s in i)this.ctrls.push(t+"."+i[s]["id"])}},watch:{listItems:function(){this.initGridView()}}},$t=Et,Pt=(i("2501"),Object(w["a"])($t,_t,Tt,!1,null,null,null)),Ot=Pt.exports,Bt=i("693a"),Rt=i("4232"),Mt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("van-tabs",{attrs:{color:t.tabColor},on:{click:t.onTabClick},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},t._l(t.curTabs,(function(t,e){return i("van-tab",{key:e,attrs:{title:t.name}})})),1)},Vt=[],At={name:"vinput",props:["name","type","label","value","options","readonly","vprops","styleId"],inject:["viewTag"],components:{},mounted:function(){this.vprops.data.forEach((function(t,e){t.index=e,t.hide=!1})),this.curTabs=this.vprops.data},activated:function(){},data:function(){return{active:0,tabColor:"#388fff",curTabs:[]}},methods:{onTabClick:function(t){console.log(arguments);this.curTabs;p["a"].$emit("component-change","click","tab",this.vprops.id,[this.vprops,this.vprops.data[t],t],this.styleId,this.viewTag?this.viewTag():void 0)},setTabHide:function(t,e){this.vprops.data[t].hide=e,this.updateCurTabs()},updateCurTabs:function(){var t=this;this.curTabs=[],this.vprops.data.forEach((function(e){e.hide||t.curTabs.push(e)}))},setValue:function(t){this.vprops.data=t,this.updateCurTabs()},getValue:function(){return this.active}},watch:{}},Nt=At,qt=Object(w["a"])(Nt,Mt,Vt,!1,null,null,null),zt=qt.exports,Ut=i("2e27"),jt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("van-notice-bar",{directives:[{name:"show",rawName:"v-show",value:t.visible,expression:"visible"}],attrs:{"left-icon":t.icon,mode:t.closeable,scrollable:t.scrollable,wrapable:t.wrapable,text:t.text,color:t.color,background:t.background},on:{click:t.onClick}})},Ht=[],Wt={name:"vinput",props:["name","type","label","value","options","readonly","vprops","text"],components:{},activated:function(){console.log(this)},data:function(){return{scrollable:!0,wrapable:!0,color:"",background:"",closeable:!1,visible:!0,icon:""}},mounted:function(){this.scrollable=this.vprops.scrollable,this.wrapable=this.vprops.wrapable,this.color=this.vprops.color,this.background=this.vprops.background,this.vprops.closeable&&(this.closeable="closeable"),this.icon=this.vprops.icon},methods:{onClick:function(t,e){console.log(arguments)}},watch:{setText:function(t){this.text=t},setVisible:function(t){this.visible=!t},value:function(t){this.currentValue=t}}},Gt=Wt,Jt=Object(w["a"])(Gt,jt,Ht,!1,null,null,null),Kt=Jt.exports,Xt=i("6596"),Qt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],class:{"lee-upLoader-block":!0,"lee-upLoader-list":t.isList,"van-cell--required":t.options.required}},[i("div",{staticClass:"van-cell__title van-field__label"},[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})],1),i("van-uploader",{ref:"upload",attrs:{label:t.label,previewSize:t.previewSize,previewImage:t.previewImage,previewFullImage:t.previewFullImage,previewOptions:t.previewOptions,disabled:t.disabledState,deletable:t.deletable,"show-upload":t.showUpload,uploadIcon:t.uploadIcon,lazyLoad:t.lazyLoad,uploadText:t.uploadText,imageFit:t.imageFit,"after-read":t.afterRead,"before-delete":t.beforeDelete,maxCount:t.editor.maxCount||1/0,multiple:t.editor.isMul,accept:t.getAccept(),capture:t.getCapture(),"max-size":t.maxSize?1024*t.maxSize*1024:1/0},on:{oversize:t.onOversize,"click-preview":t.clickPreview,"close-preview":t.closePreview,delete:t.afterDelete},scopedSlots:t._u([t.isList?{key:"preview-cover",fn:function(e){return[i("previewCover",{ref:"preview"+e.index,staticClass:"f-preview-cover",attrs:{tpl:t.tpl,item:e,preview:t.textPreview}})]}}:null],null,!0),model:{value:t.fileList,callback:function(e){t.fileList=e},expression:"fileList"}}),t.showExtendAction()?t._e():i("div",[t.isOverLength()?i("div",{staticClass:"f-upload-img"},[i("div",{staticClass:"f-upload-content",on:{click:t.beforeUpload}},[i("i",{staticClass:"van-icon van-icon-plus van-uploader__upload-icon"})])]):t._e()]),t.showExtendAction()?i("van-popover",{attrs:{trigger:"click",placement:"right",actions:t.extendImageWay,"get-container":t.getContainer,"close-on-click-action":!1},on:{select:t.triggerExtendImageSelect},scopedSlots:t._u([{key:"reference",fn:function(){return[t.isOverLength()?i("div",{staticClass:"f-upload-img"},[i("div",{staticClass:"f-upload-content",on:{click:t.beforeUpload}},[i("i",{staticClass:"van-icon van-icon-plus van-uploader__upload-icon"})])]):t._e()]},proxy:!0}],null,!1,3831950237),model:{value:t.selectExtendImageWay,callback:function(e){t.selectExtendImageWay=e},expression:"selectExtendImageWay"}}):t._e(),i("van-action-sheet",{attrs:{actions:t.imageWay},on:{select:t.triggerImageSelect},model:{value:t.selectImageWay,callback:function(e){t.selectImageWay=e},expression:"selectImageWay"}}),i("van-action-sheet",{attrs:{actions:t.extendUploadWay,"cancel-text":t.getLang("actions_cancel"),"close-on-click-action":""},on:{select:t.triggerExtendUploadSelect},model:{value:t.selectExtendUploadWay,callback:function(e){t.selectExtendUploadWay=e},expression:"selectExtendUploadWay"}}),i("van-image-preview",{key:"refresh"+t.imgPreviewRefresh,ref:"imgPreview",attrs:{getContainer:"#app",images:t.prevImgs,closeable:!0,loop:!1,"start-position":t.prevStartPos},on:{close:t.closePreview},model:{value:t.showImagePreview,callback:function(e){t.showImagePreview=e},expression:"showImagePreview"}}),t.errorMessage?i("div",{staticClass:"van-field__error-message van-field__error-message--right"},[t._v(t._s(t.errorMessage))]):t._e()],1)},Yt=[],Zt=(i("a4d3"),i("e01a"),i("d28b"),i("cb29"),i("7db0"),i("c740"),i("caad"),i("a434"),i("b64b"),i("2532"),i("3ca3"),i("498a"),i("ddb0"),i("2b3d"),i("0486")),te=i("d399"),ee=i("2241"),ie=(i("3532"),i("1b34")),se={name:"uploadCover",props:["item","tpl","preview"],data:function(){return{}},mounted:function(){},render:function(t){var e=this,i={mixins:{},template:'<div class="preview-cover van-ellipsis" @click.stop.prevent="preview(item)">\n    <span>{{ item.file.name }}</span>  \n<span style="display: block; font-size: 13px; color: #888888; bottom: 0; position: absolute">{{item.file.status}}</span>\n    <span style="display: block; font-size: 13px; color: #888888; bottom: 0; right: 0; position: absolute">{{item.file.createTime}}</span>\n</div>',data:function(){return{preview:e.preview}},props:["item"]};if(this.tpl){if(this.tpl.mixin){var s=new Function("return "+this.tpl.mixin+";");i.mixins=[s()]}i.template=this.tpl.template}var n=c["a"].extend(i);return t(n,{attrs:{item:this.item}})}},ne=se,oe=Object(w["a"])(ne,o,a,!1,null,null,null),ae=oe.exports,re=window.idp.Notify,le={components:{ButtonBar:Rt["a"],previewCover:ae,popLabel:f["a"]},props:["fieldName","label","options","placeholder","editor","row","styleId","value","ctx","readOnly","gridEditParm"],inject:["viewTag"],data:function(){return{fileList:[],currentText:"",currentDataId:"",visible:!0,show:!0,name:0,previewSize:"50px",previewImage:!0,previewFullImage:!1,previewOptions:{closeOnPopstate:!0,closeable:!0},multiple:!0,disabled:!1,deletable:!0,showUpload:!0,lazyLoad:!1,resultType:"dataUrl",uploadText:"",imageFit:"string",uploadIcon:"plus",accept:"",errorMessage:"",realAccept:"",isList:!0,basicUrl:"/api/BP/EIS/v1.0/",url:"upload/chunkuploadforstring",downloadUrl:"imagedownload/getimagefile?JSEID=",imgUrl:"imagedownload/getimagefile?GUID=",removeUrl:"task/deleteFile",getAudioUrl:"task/getAudioUrl",getFileList:"task/getFiles",getImgForMobile:"/ImageMobile/UploadMobileFiles",statusMap:{},dataId:"",enableExt:["jpg","jpeg","bmp","png","gif","doc","docx","pdf","ppt","pptx","xls","xlsx","txt","rar","zip","7z","mp3","mp4","mov","ofd"],imgExt:["jpg","jpeg","bmp","png","gif"],disabledState:!1,selectImageWay:!1,imageWay:[{name:"拍照",type:"1"},{name:"选择图片",type:"2"},{name:"录视频",type:"3"}],isYunpuls:!1,tempFilePath:"",extendImageWay:[],selectExtendImageWay:!1,extendImageWayCall:"",maxSize:0,rotate:[],prevImages:[],prevStartPos:0,showImagePreview:!1,imgPreviewRefresh:0,showRotBtn:!1,audioBuffer:void 0,audioContext:void 0,note:"",selectExtendUploadWay:!1,extendUploadWay:[]}},created:function(){},mounted:function(){var t=this;this.service=Zt["a"],this.disabledState=this.readOnly,this.editor.ext&&this.editor.ext.split(",").forEach((function(e,i){t.realAccept+="."+e+","})),this.realAccept.length>0&&(this.realAccept=this.realAccept.substring(0,this.realAccept.length-1)),("3"==this.editor.display||this.editor.isCard)&&(this.isList=!1),this.ctx&&(this.dataId=this.getDataId()),this.initLangResources(),this.editor.canVideo&&this.imageWay.push({name:this.getLang("uploader_selectVideo"),type:"5"}),this.editor.canAudio&&this.imageWay.push({name:this.getLang("uploader_recordAudio"),type:"4"}),this.showRotBtn=this.editor.rotImage,this.basicUrl=window.idp.basePath.getBasePath()+this.basicUrl,this.editor.isCustom&&this.editor.tpl?this.tpl=this.editor.tpl:this.tpl=void 0,this.editor.onlyCamera&&this.imageWay.splice(1,1),this.editor.fileSize&&(this.maxSize=Number(this.editor.fileSize)),this.note=this.options.note,this.extendUploadWay=window.IDP_EXTEND_UPLOADWAY||[]},activated:function(){if(this.$route.query.returnValue){var t=this.$route.query.returnValue.controlId;t==this._uid&&this.emitListeners("onClosePreview",[this])}},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},initLangResources:function(){var t=this;this.statusMap={uploading:{status:"uploading",message:t.getLang("uploader_uploading")},succeed:{status:"succeed",message:t.getLang("uploader_uploadSucced")},failed:{status:"failed",message:t.getLang("uploader_uploadFailed")}},this.imageWay=[{name:t.getLang("uploader_takePicture"),type:"1"},{name:t.getLang("uploader_choosePicture"),type:"2"},{name:t.getLang("uploader_recordVideo"),type:"3"}]},load:function(){var t=this;this.setErrorMessage(""),console.log(this.label);var e=this;if(0!==this.value){var i={billID:this.dataId,tag:void 0==this.editor.fileTag?"":this.editor.fileTag};0!=this.emitListeners("beforeLoad",[e.service,this.basicUrl,e,i])&&e.service.post(e.getFileList,i).then((function(i){if(e.fileList=[],i.data.length>0)for(var s in i.data){var n="";n=".mp4"==i.data[s].EXT||".mov"==i.data[s].EXT?"https://baike.baidu.com/pic/%E9%9F%B3%E9%A2%91%E6%96%87%E4%BB%B6/18644462/1/b3b7d0a20cf431adcbefe3608f7abbaf2edda3cc321f?fr=lemma&ct=single#aid=1&pic=b3b7d0a20cf431adcbefe3608f7abbaf2edda3cc321f":e.basicUrl+e.imgUrl+i.data[s].ID;var o=i.data[s].EXT.toLowerCase().substring(1);e.fileList.push({url:n,name:"pic",message:"",status:"",isImage:e.isImgCheck(i.data[s].EXT.replace(".","")),file:{name:i.data[s].FileName,webkitRelativePath:"",type:-1!=e.imgExt.indexOf(o)?"image/"+o:"application/"+o,size:"",lastModified:"",status:e.getLang("uploader_uploadSucced"),id:i.data[s].ID,jseid:i.data[s].JSEID,createTime:window.DateFormat(i.data[s].CreateTime,"yyyy-MM-dd hh:mm")},deletable:!t.disabledState}),t.updateFileIcon()}}))}else e.fileList=[]},emitListeners:function(t,e){var i=this.options.id,s="upload";return this.gridEditParm&&(i=this.gridEditParm.id,s="grid",e.push(this.gridEditParm.field),e.push(this.gridEditParm.rowindex),e.push(this.gridEditParm.record)),p["a"].$emit("component-change",t,s,i,e,this.styleId,this.viewTag?this.viewTag():void 0)},getDataId:function(){try{return this.editor.refField&&""!=this.editor.refField?this.row?this.row[this.editor.refField]:"":this.gridEditParm?this.gridEditParm.record[this.ctx.modelController.scheaminfo.find(this.matchId).KeyCol]:this.ctx.data.formItems[this.ctx.modelController.pkcol]}catch(e){var t;return console.error("附件控件".concat(null===(t=this.options)||void 0===t?void 0:t.id,"未能获取到附件关联字段值！")),""}},matchId:function(t){return t.Code==this.gridEditParm.table},getAccept:function(){return window.IDP_CLIENT_NOTYUNPLUS&&this.editor.onlyCamera?"image/*":""},getCapture:function(){return window.IDP_CLIENT_NOTYUNPLUS&&this.editor.onlyCamera?"camera":null},afterDelete:function(t,e){var i=this,s={fileID:t.file.id,billID:this.dataId};0!=this.emitListeners("beforeDelete",[i,i.fieldName,t])&&(s.sourceParam=this.emitListeners("beforeDeleteFile",[i,i.fieldName,t])||s.sourceParam,i.service.post(i.removeUrl,s).then((function(t){i.emitListeners("afterDelete",[i,i.fieldName])}))),0==i.fileList.length&&i.$emit("input",i.fieldName,0)},afterRead:function(t,e){var i=this,s=this.emitListeners("afterReadFile",[i,i.fieldName,t]);s&&s instanceof Promise?s.then((function(t){i.afterRead2Upload(t)})):s?this.afterRead2Upload(s):this.afterRead2Upload(t)},afterRead2Upload:function(t){if(!Array.isArray(t)&&this.uploadFile(t),Array.isArray(t)){for(var e in t)this.setStatus(t[e],"uploading");this.uploadFiles(0,t)}},handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},setRequired:function(t){this.options.required=t,this.$forceUpdate()},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},setErrorMessage:function(t){this.errorMessage=t},uploadFile:function(t,e){var i=this,s=t.file.name.split("."),n=function(t){if((!i.realAccept||!i.realAccept.length)&&i.extCheck(s[s.length-1])||i.realAccept.length>1&&-1==i.realAccept.indexOf(s[s.length-1]))return i.fileList=i.fileList.filter((function(e){return e.file.name!=t.file.name})),void re({type:"danger",message:i.getLang("uploader_unSupportedType",{fileName:t.file.name,fileType:t.file.name.split(".").pop()})});if(0!=i.emitListeners("beforeUpload",[i,i.fieldName,t])){i.setErrorMessage("");var n=i.buildFormData(t);i.service.post(i.getImgForMobile,n).then((function(s){if(Object.keys(s.data).length>0&&s.data.fileid){var n=i.fileList.findIndex((function(t){return t.file.name==s.data.filename}));n=n>-1?n:i.fileList.length-1,i.setStatus(t,"succeed"),t.file.id=s.data.fileid,t.file.jseid=s.data.jseid,t.ext=s.data.fileext,i.isYunpuls&&e&&(i.fileList[n].file.id=s.data.fileid,i.fileList[n].url=i.basicUrl+i.imgUrl+s.data.fileid),i.$emit("input",i.fieldName,1),re({type:"success",message:i.getLang("uploader_uploadSuccess")+t.file.name}),i.emitListeners("afterUpLoadSuccess",[i,i.fieldName,t]),i.updateFileIcon(n,t)}else i.setStatus(t,"failed")})).catch((function(e){i.setStatus(t,"failed")}))}};i.setStatus(t,"uploading");var o=i.emitListeners("afterReadFile",[i,i.fieldName,t]);o&&o instanceof Promise?o.then((function(t){n(t)})):n(o||t)},uploadFiles:function(t,e){var i=this;if(t!=e.length){var s=e[t].file.name.split(".");if(this.extCheck(s[s.length-1])||this.realAccept.length>1&&-1==this.realAccept.indexOf(s[s.length-1]))i.fileList=i.fileList.filter((function(i){return i.file.name!=e[t].file.name})),re({type:"danger",message:i.getLang("uploader_unSupportedType",{fileName:e[t].file.name,fileType:e[t].file.name.split(".").pop()})}),i.uploadFiles(t+1,e);else if(0!=this.emitListeners("beforeUploadList",[i,i.fieldName,e,t])){var n=i.buildFormData(e[t]);i.service.post("/ImageMobile/UploadMobileFiles",n).then((function(s){if(Object.keys(s.data).length>0&&s.data.fileid){var n=i.fileList.findIndex((function(t){return t.file.name==s.data.filename}));n=n>-1?n:i.fileList.length-1,i.setStatus(e[t],"succeed"),e[t].file.id=s.data.fileid,e[t].file.jseid=s.data.jseid,e[t].ext=s.data.fileext,re({type:"success",message:i.getLang("uploader_uploadSuccess")+e[t].file.name}),i.emitListeners("afterUpLoadSuccess",[i,i.fieldName,e,t]),i.updateFileIcon(n),i.uploadFiles(t+1,e),i.$emit("input",i.fieldName,1)}else i.setStatus(e[t],"failed"),i.uploadFiles(t+1,e)})).catch((function(s){console.log("err: ",s),i.setStatus(e[t],"failed"),i.uploadFiles(t+1,e)}))}}},setStatus:function(t,e){var i=this;t.status=i.statusMap[e].status,t.message=i.statusMap[e].message,t.file.status=i.statusMap[e].message},buildFormData:function(t){var e=this,i={fileName:t.file.name,fileData:t.content.split(",")[1],dataid:e.dataId,datacode:e.editor.refBill||"",mkid:e.editor.sysMark||"SYS",tag:void 0==e.editor.fileTag?"":e.editor.fileTag,filetype:t.file.type.split("/")[1]};return e.editor.sourceSys&&(i.sourceParam={},i.sourceParam.sourceSys=e.editor.sourceSys),i.sourceParam=this.emitListeners("beforeUploadMobileFiles",[e,e.fieldName,i.sourceParam])||i.sourceParam,i},closePreview:function(){var t=this;setTimeout((function(){t.rotate=[],t.imgPreviewRefresh++,t.deleteBtn(),t.emitListeners("onClosePreview",[t])}),300)},onOversize:function(t,e){console.log(t),te["a"].fail(this.getLang("uploader_oversize")+String(this.maxSize)+" MB")},clickPreview:function(t,e){this.editor&&this.editor.isCustom?this.$refs["preview"+e.index]&&this.$refs["preview"+e.index].$el.click():this.filePreview(t,e)},filePreview:function(t,e){var i=this;if(!t||"uploading"!=t.status){if(t&&i.isImgCheck(t.file.name.split(".").pop()))this.emitListeners("onOpenPreview",[this,t,e]),this.previewPic(e.index);else if(t&&t.file&&t.file.type&&t.file.type.toLowerCase().includes("pdf"))console.log("open EIS pdf page: file id is "+t.file.id),this.emitListeners("onOpenPreview",[this,t,e]),this.$router.push({name:"iframe",path:"/iframe",query:{hidebar:this.$route.query.hidebar},params:{src:window.idp.basePath.getBasePath()+"/apps/eis/eiscore/views/libs/pdfjs/web/viewer.html?j=true&GUID="+t.file.id,title:t.file.name,controlId:this._uid}});else if(t&&t.file&&t.file.type&&t.file.type.toLowerCase().includes("mp4")||t.file.type.toLowerCase().includes("quicktime"))console.log("open EIS videoplayer page: jseid id is "+t.file.jseid),this.emitListeners("onOpenPreview",[this,t,e]),this.$router.push({name:"iframe",path:"/iframe",query:{hidebar:this.$route.query.hidebar},params:{src:window.idp.basePath.getBasePath()+"/apps/eis/eiscore/views/videoplayer.html?JSEID="+t.file.jseid,title:t.file.name,controlId:this._uid}});else if(t&&t.file&&t.file.type&&t.file.type.toLowerCase().includes("mp3")||t.ext&&t.ext.toLowerCase().includes("mp3"))console.log("open EIS audioplayer page: jseid id is "+t.file.jseid),this.emitListeners("onOpenPreview",[this,t,e]),this.$router.push({name:"iframe",path:"/iframe",query:{hidebar:this.$route.query.hidebar},params:{src:window.idp.basePath.getBasePath()+"/apps/eis/eiscore/views/audioplayer.html?JSEID="+t.file.jseid,title:t.file.name,controlId:this._uid}});else if(t&&t.file&&t.file.name&&"ofd"==this.getFileExt(t.file.name).toLowerCase()){console.log("open EIS ofd page: file id is "+t.file.id);var s=encodeURIComponent(window.idp.basePath.getBasePath()+"/api/BP/EIS/v1.0/imagedownload/getimagefile?GUID="+t.file.id);this.emitListeners("onOpenPreview",[this,t,e]),this.$router.push({name:"iframe",path:"/iframe",query:{hidebar:this.$route.query.hidebar},params:{src:window.idp.basePath.getBasePath()+"/apps/eis/eiscore/views/libs/ofdview/index.html?url="+s,title:t.file.name,controlId:this._uid}})}else if(t&&t.file&&this.isDocFile(t.file.name)){var n=window.navigator.userAgent.toLowerCase(),o=n.indexOf("dingtalk")>=0;console.log("open EIS viewer page: jseid id is "+t.file.jseid);var a=t.file.name.split(".").pop(),r=window.idp.basePath.getBasePath()+"/apps/eis/eiscore/views/libs/pdfjspc/web/viewer.html?j=true&JSEID="+t.file.jseid+"&extType="+a+"&isShowSignature=false";("pdfjs"==this.editor.docjs||o)&&(r=window.idp.basePath.getBasePath()+"/apps/eis/eiscore/views/libs/pdfjs/web/viewer.html?j=true&JSEID="+t.file.jseid+"&extType="+a+"&isShowSignature=false"),this.emitListeners("onOpenPreview",[this,t,e]),this.$router.push({name:"iframe",path:"/iframe",query:{hidebar:this.$route.query.hidebar},params:{src:r,title:t.file.name,controlId:this._uid}})}else if(t&&t.file&&!this.checkPreviewExt(t.file.type)&&this.$refs.upload)if(window.UPLOADER_EXTEND_DOWNFILE){var l=this.editor.sysMark||"SYS",c=this.basicUrl+this.downloadUrl+t.file.jseid+"&mkid="+l+"&isDownload=true";window.idp.confirm(i.getLang("uploader_cantPreviewType_down",{0:t.file.type}),(function(){window.UPLOADER_EXTEND_DOWNFILE(c,t.file.id,t.file.name,t.file.type)}))}else re({type:"warning",message:i.getLang("uploader_cantPreviewType",{0:t.file.type}),duration:1e3});return!1}re({type:"warning",message:i.getLang("uploader_cantPreviewLoading"),duration:1e3})},imgInfo:function(t){this.vertical=JSON.stringify(t.vertical),this.rootHeight=t.rootHeight,this.rootWidth=t.rootWidth,this.imageRatio=t.imageRatio,this.setCtrl=function(t){t.rootHeight=this.rootHeight,t.rootWidth=this.rootWidth,t.imageRatio=this.imageRatio,t.vertical=JSON.parse(this.vertical)}},initRotateBtn:function(){var t=this;if(this.showRotBtn){var e=document.querySelector(".van-image-preview__index"),i=document.querySelector(".van-swipe__track"),s=document.createElement("BUTTON");this.rotate=new Array(Number(e.innerHTML.split("/")[1])).fill(0),s.innerHTML="<i class='van-icon van-icon-replay'>\x3c!----\x3e</i>",s.classList.add("ratClass"),s.addEventListener("click",(function(){var s=e.innerHTML.slice(0,e.innerHTML.indexOf("/")-1)-1,n=t.$refs.imgPreview.$children[0].$children[s];n.resetScale(),i.childNodes[s].childNodes[0].childNodes[0].style["transition-duration"]="0.3s",i.childNodes[s].childNodes[0].childNodes[0].style.transform=t.rotate[s]%180!=0?"rotate(".concat(t.rotate[s]+=90,"deg)"):"scale(".concat(Math.min(1/n.imageRatio,n.rootHeight/n.rootWidth),") rotate(").concat(t.rotate[s]+=90,"deg)"),n.imageRatio=1/n.imageRatio})),$.extend(s.style,{transform:"rotateZ(90deg)",border:0,position:"absolute",bottom:"25px",left:"50%",margin:"0 15px",height:"50px",width:"50px","font-size":"25px",color:"#3a90ff",background:"none"}),$(".van-image-preview")[0].appendChild(s);var n=document.createElement("BUTTON");n.innerHTML="<i class='van-icon van-icon-replay'>\x3c!----\x3e</i>",n.classList.add("ratClass"),n.addEventListener("click",(function(){var s=e.innerHTML.slice(0,e.innerHTML.indexOf("/")-1)-1,n=t.$refs.imgPreview.$children[0].$children[s];n.resetScale(),i.childNodes[s].childNodes[0].childNodes[0].style["transition-duration"]="0.3s",i.childNodes[s].childNodes[0].childNodes[0].style.transform=t.rotate[s]%180!=0?"rotate(".concat(t.rotate[s]-=90,"deg)"):"scale(".concat(Math.min(1/n.imageRatio,n.rootHeight/n.rootWidth),") rotate(").concat(t.rotate[s]-=90,"deg)"),n.imageRatio=1/n.imageRatio})),$.extend(n.style,{transform:"rotate(90deg) scaleY(-1)",border:0,position:"absolute",bottom:"25px",right:"50%",margin:"0 15px",height:"50px",width:"50px","font-size":"25px",color:"#3a90ff",background:"none"}),$(".van-image-preview")[0].appendChild(n)}},deleteBtn:function(){this.rotate=[],this.imageRatio=[],$(".ratClass").remove()},getFileExt:function(t){if(t){var e=t.split(".");if(e.length>1)return e[e.length-1]}return""},isDocFile:function(t){var e=this.getFileExt(t).toLowerCase(),i=["doc","ppt","xls","docx","xlsx","pptx","csv","wps","wpt","dot","txt","ofd"];return!!i.includes(e)},beforeDelete:function(t,e){var i=this;console.log("args: ",e),console.log("file: ",t);var s=this,n={fileID:t.file.id,billID:this.dataId};return ee["a"].confirm({title:s.getLang("uploader_confirm"),message:s.getLang("uploader_confirmDelete")}).then((function(){n.sourceParam=i.emitListeners("beforeDeleteFile",[s,s.fieldName,t])||n.sourceParam,s.service.post(s.removeUrl,n).then((function(t){s.fileList.splice(e.index,1),s.emitListeners("afterDelete",[s,s.fieldName]),0==s.fileList.length&&s.$emit("input",s.fieldName,0)})),0==s.fileList.length&&s.$emit("input",s.fieldName,0)})).catch((function(){0==s.fileList.length&&s.$emit("input",s.fieldName,0)})),!1},afterReadFailed:function(t){},beforeRead:function(t){return!0},updateFileIcon:function(t,e){void 0==t&&(t=this.fileList.length-1);var i=window.idp.basePath.getBasePath()+"/apps/fastdweb/views/runtime/libs/leeui/css/images/mdpi/",s=e||this.fileList[t];s&&s.file&&!s.file.createTime&&(s.file.createTime=window.DateFormat(new Date,"yyyy-MM-dd hh:mm")),s.isImage=!0;var n=s.file.name.split(".").pop().toLowerCase();"doc"==n||"docx"==n?s.content=i+"word.svg":"pdf"==n?s.content=i+"pdf.svg":"ppt"==n||"pptx"==n?s.content=i+"ppt.svg":"xls"==n||"xlsx"==n?s.content=i+"excel.svg":"txt"==n?s.content=i+"txt.svg":"rar"==n||"zip"==n||"7z"==n?s.content=i+"zip.svg":"mp3"==n?s.content=i+"sound.svg":"mp4"==n||"mov"==n?s.content=i+"video.svg":["jpg","jpeg","bmp","png","gif"].indexOf(n)<0&&(s.content=i+"other.svg"),this.fileList[t]=s},previewPic:function(t){for(var e=this,i=[],s=0,n=0;n<this.fileList.length;n++){var o=this.fileList[n].file.name.split(".").pop();e.isImgCheck(o)&&(i.push(this.fileList[n].url||URL.createObjectURL(this.fileList[n].file)),n<t&&s++)}this.imagePreview(i,s)},imagePreview:function(t,e){var i=this;this.prevImgs=t,this.prevStartPos=e,this.showImagePreview=!0,this.$nextTick((function(){i.initRotateBtn()}))},imageWatcher:function(t){this.pos=t,this.moveXwatcher,this.moveYwatcher,this.zoomingwatcher,this.movingwatcher},getCurrentLang:function(){return"zh-CN"},getTips:function(t){var e=this;return e.i18n[this.getCurrentLang][t]},extCheck:function(t){var e=this;return-1==e.enableExt.indexOf(t.toLowerCase())},checkPreviewExt:function(t){return!!(t.includes("pdf")||t.includes("image")||t.includes("mp4")||t.includes("mov"))},isImgCheck:function(t){return this.imgExt.includes(t.toLowerCase())},setDisabled:function(t){this.showUpload=!t,this.readOnly||(this.disabledState=t)},textPreview:function(t){var e=t.file.name.split(".").pop();this.$refs.upload&&this.isImgCheck(e)&&-1!=t.index?(this.emitListeners("onOpenPreview",[t.file]),this.previewPic(t.index)):this.filePreview(t)},notPreview:function(t){},showExtendAction:function(){return window.IDP_CLIENT_NOTYUNPLUS&&this.extendImageWay&&this.extendImageWay.length>0},getContainer:function(){return document.querySelector(".f-upload-img")},beforeUpload:function(){var t=this;return window.IDP_CLIENT_NOTYUNPLUS?this.extendImageWay&&this.extendImageWay.length>0?t.callH5=!1:this.extendUploadWay&&this.extendUploadWay.length>0?t.selectExtendUploadWay=!0:t.defaultChooseFile():(t.isYunpuls=!0,t.selectImageWay=!0,t.callH5=!1),!1},defaultChooseFile:function(){var t=this;t.callH5=!0,t.accept="image/*",t.capture="camera",console.log("has been in "),t.$refs.upload&&(console.log("call upload"),t.$refs.upload.chooseFile())},triggerExtendImageSelect:function(t){var e={defaultChooseFile:this.defaultChooseFile,uploadFile:this.uploadFile,fileList:this.fileList};this.extendImageWayCall&&this.extendImageWayCall(t,e)},triggerExtendUploadSelect:function(t){var e={defaultChooseFile:this.defaultChooseFile,uploadFile:this.uploadFile,fileList:this.fileList};window.IDP_EXTEND_UPLOADWAYCALL&&window.IDP_EXTEND_UPLOADWAYCALL(t,e)},triggerImageSelect:function(t){var e=this;switch(t.type){case"1":var i={quality:100,encodingType:imp.iCamera.EncodingType.JPEG,targetWidth:0,targetHeight:0};i=this.emitListeners("beforeCallup",[e,e.fieldName,i])||i,window.top.imp.iCamera.open(i,this.successCallbackCamera,this.errorCallback);break;case"2":var s={quality:100,encodingType:imp.iCamera.EncodingType.JPEG,targetWidth:0,targetHeight:0};s=this.emitListeners("beforeCallup",[e,e.fieldName,s])||s,window.top.imp.iCamera.select(s,this.successCallbackCamera,this.errorCallback);break;case"3":var n={id:"video_"+ie["a"].getSession().UserCode+"_"+(new Date).valueOf(),fps:30,quality:1,duration:10};n=this.emitListeners("beforeCallup",[e,e.fieldName,n])||n,window.top.imp.iVideo.record(n,this.successCallback,this.errorCallback);break;case"4":var o={id:"audio_"+ie["a"].getSession().UserCode+"_"+(new Date).valueOf(),local:!0};o=this.emitListeners("beforeCallup",[e,e.fieldName,o])||o,window.top.imp.iAudio.recordingAudio(o,this.successCallbackAudio,this.errorCallback);break;case"5":var a={maxNum:"6",mType:1};a=this.emitListeners("beforeCallup",[e,e.fieldName,a])||a,window.imp.iPhoto.selectPicsFromAlbum(a,this.successCallbackVideo,this.errorCallback);break;default:break}},successCallbackCamera:function(t){this.selectImageWay=!1,console.log("select image"),console.log(t);var e="";if(t.length)for(var i in t)this.tempFilePath=t[i].originalUrl,e=t[i].originalData,this.getImgFile(e);else this.tempFilePath=t.originalUrl,e=t.originalData,this.getImgFile(e)},successCallback:function(t){t&&(this.selectImageWay=!1,this.tempFilePath=t.path,console.log(t),window.top.imp.iFile.getBase64(this.tempFilePath,this.getVideoFile,this.readFailedCallback))},successCallbackVideo:function(t){var e=this;if(this.selectImageWay=!1,0!=t.state)if(2!=t.state){if(t.result&&t.result.data){var i=!0,s=!1,n=void 0;try{for(var o,a=t.result.data[Symbol.iterator]();!(i=(o=a.next()).done);i=!0){var r=o.value;this.uploadVideo(r)}}catch(l){s=!0,n=l}finally{try{i||null==a.return||a.return()}finally{if(s)throw n}}}}else re({type:"warning",message:e.getLang("uploader_audioCancel")});else re({type:"danger",message:e.getLang("uploader_videoFailed")})},uploadVideo:function(t){if(t){this.selectImageWay=!1;var e={};if(t&&"string"==typeof t){var i=t.replace("{","").replace("}","");i.split(",").forEach((function(t){var i=t.trim().split("=");e[i[0]]=i[1]}))}this.tempFilePath="sdcard:"+e.mediaPath,window.top.imp.iFile.getBase64(this.tempFilePath,this.getVideoFile,this.readFailedCallback)}},successCallbackAudio:function(t){var e=this;this.selectImageWay=!1,0!=t.state?2!=t.state?(this.tempFilePath="sdcard:"+t.result.path,console.log(t),window.top.imp.iFile.getBase64(this.tempFilePath,this.getAudioile,this.readFailedCallback)):re({type:"warning",message:e.getLang("uploader_audioCancel")}):re({type:"danger",message:e.getLang("uploader_audioFailed")})},errorCallback:function(t){re({type:"danger",message:t.errorMessage})},getAudioile:function(t){console.log(t.name);var e=this,i=this.getFileInfoByPath(e.tempFilePath),s=i.split(".");e.tempFile={url:"",name:"pic",message:"",status:"uploading",isImage:!1,file:{name:"audio_"+ie["a"].getSession().UserCode+"_"+(new Date).valueOf()+"."+s[1],webkitRelativePath:"",type:s[1],size:"",lastModified:"",status:e.getLang("uploader_uploading"),id:""},deletable:!e.disabledState},e.fileList.push(e.tempFile),e.tempFile.content="imp,"+t,this.uploadFile(e.tempFile)},getVideoFile:function(t){try{console.log(t.name);var e=this,i=this.getFileInfoByPath(e.tempFilePath),s=i.split(".");e.tempFile={url:"",name:"pic",message:"",status:"uploading",isImage:!1,file:{name:i,webkitRelativePath:"",type:s[1],size:"",lastModified:"",status:e.getLang("uploader_uploading"),id:""},deletable:!e.disabledState},e.fileList.push(e.tempFile),e.tempFile.content="imp,"+t,this.uploadFile(e.tempFile)}catch(n){console.err("视频文件读取失败："+n)}},getImgFile:function(t){console.log(t.name);var e=this,i=this.getFileInfoByPath(e.tempFilePath),s=i.split(".");e.tempFile={url:e.tempFilePath,name:"pic",message:"",status:"uploading",isImage:!1,file:{name:i,webkitRelativePath:"",type:"image/"+s[1],lastModified:"",status:e.getLang("uploader_uploading"),id:""},deletable:!e.disabledState},e.fileList.push(e.tempFile),e.tempFile.content="data:image/jpeg;base64,"+t,this.uploadFile(e.tempFile,!0)},readFailedCallback:function(t){re({type:"danger",message:t.errorMessage})},getFileInfoByPath:function(t){var e=t.split("/"),i=e[e.length-1];return i},isOverLength:function(){return!this.disabledState&&(this.editor.maxCount?this.fileList.length<this.editor.maxCount:this.fileList.length<9)},playAudio:function(t){var e=this;return Object(pt["a"])(regeneratorRuntime.mark((function i(){var s,n,o;return regeneratorRuntime.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.stopPlayAudio(),s=e,e.fileList[t]){i.next=4;break}return i.abrupt("return",s.onStopPlay(t));case 4:if(n=e.fileList[t].file?e.fileList[t].file.jseid:"",n){i.next=7;break}return i.abrupt("return",s.onStopPlay(t));case 7:o={jseid:n},e.service.post(e.getAudioUrl,o).then(function(){var e=Object(pt["a"])(regeneratorRuntime.mark((function e(i){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!i||"ok"!=i.code||!i.data){e.next=5;break}return e.next=3,s.play(i.data,t);case 3:e.next=6;break;case 5:return e.abrupt("return",s.onStopPlay(t));case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 9:case"end":return i.stop()}}),i)})))()},stopPlayAudio:function(){this.audioBuffer&&this.audioBuffer.stop&&this.audioBuffer.stop()},play:function(t,e){var i=this;return Object(pt["a"])(regeneratorRuntime.mark((function s(){return regeneratorRuntime.wrap((function(s){while(1)switch(s.prev=s.next){case 0:i.decodeAudioData(t).then((function(t){i.audioConfig=t,i.startPlay(t,e)}));case 1:case"end":return s.stop()}}),s)})))()},startPlay:function(t,e){var i=arguments,s=this,n=this.audioContext,o=t.audioArrayBuffer;this.audioBuffer=n.createBufferSource(),this.audioBuffer.buffer=o,this.audioBuffer.connect(n.destination),this.emitListeners("startPlayAudio",[this,this.fieldName,e,this.fileList[e]]),this.audioBuffer.start(0,0),this.audioBuffer.onended=function(){console.log("音频播放结束"),console.log(i),s.audioBuffer=void 0,s.onStopPlay(e)}},onStopPlay:function(t){this.emitListeners("stopPlayAudio",[this,this.fieldName,t,this.fileList[t]])},onFailPlay:function(t,e){this.emitListeners("stopPlayAudio",[this,this.fieldName,t,this.fileList[t],e])},decodeAudioData:function(t){var e=this;if(console.log("-------解码音频文件------"),window.AudioContext||window.webkitAudioContext)return new Promise((function(i,s){e.audioContext=e.audioContext||new(window.AudioContext||window.webkitAudioContext),t=window.idp.basePath.getBasePath()+t,fetch(t).then((function(t){return t.blob()})).then((function(t){return t.arrayBuffer()})).then((function(t){e.audioContext.decodeAudioData(t,(function(t){var s=e.audioContext.createBufferSource();s.buffer=t,i({audioArrayBuffer:t,source:s})})).catch((function(t){console.error("Error decoding audio data:",t),s(t)}))})).catch((function(t){console.error(t),s(t)}))}));console.error("未找到音频解析器！")}},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick}},watch:{options:function(){},row:{deep:!0,handler:function(){this.dataId=this.getDataId()}},ctx:function(t,e){t&&(this.dataId=this.getDataId())},dataId:function(t,e){(null!==t||void 0!==t||""!==t)&&t!=this.currentDataId&&this.load(),this.currentDataId=t},value:function(t,e){this.currentText=t,this.dataId=this.getDataId()},selectExtendImageWay:function(t,e){console.log(t)}}},ce=le,he=(i("24cb"),Object(w["a"])(ce,Qt,Yt,!1,null,null,null)),de=he.exports,ue=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.show&&t.visible,expression:"show&&visible"}],staticClass:"f-label",style:t.style},[i("span",[t._v(t._s(t.vprops.label))])])},fe=[],pe={name:"label",props:["vprops"],data:function(){return{show:!0,style:"",visible:!0}},methods:{setShow:function(t){this.show=t},getStyle:function(){this.vprops.fontsize&&(this.style+="font-size:"+this.vprops.fontsize+"px;"),this.vprops.color&&(this.style+="color:"+this.vprops.color+";"),this.vprops.align&&(this.style+="text-align:"+this.vprops.align+";"),this.vprops.margin&&(this.style+="margin:"+this.vprops.margin+"px;")},setVisible:function(t){this.visible=!t}},mounted:function(){this.getStyle()}},me=pe,ge=(i("bdeb"),Object(w["a"])(me,ue,fe,!1,null,"3bc320d6",null)),ve=ge.exports,be=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}]},[i("van-field",{class:{"f-input-readonly":t.disabled},attrs:{placeholder:t.placeholder,"is-link":!t.disabled&&!t.rightView&&!t.textmode,readonly:"",clickable:"",name:"langbox",value:t.currentText,label:t.label,required:t.options.required,"error-message":t.errorMessage,"error-message-align":"right"},on:{click:t.openLangBox,input:t.onInputEvent},scopedSlots:t._u([{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0},t.rightView?{key:"extra",fn:function(){return[i(t.rightView,{tag:"div",attrs:{text:t.currentText,value:t.currentValue}})]},proxy:!0}:null],null,!0)}),i("van-popup",{style:t.style,attrs:{"get-container":"#app",position:"center",round:""},model:{value:t.showLangBox,callback:function(e){t.showLangBox=e},expression:"showLangBox"}},[i("div",{staticStyle:{"text-align":"center",padding:"10px 0"}},[t._v("请录入多语言")]),i("div",[t._l(t.langSuffix,(function(e,s){return i("van-field",{key:s,staticStyle:{"border-top":"1px solid #ebedf0"},attrs:{label:t.getLabel(e),name:e,"input-align":"right",readonly:t.disabled,clearable:"",autofocus:0===s,t:""},on:{input:function(i){return t.onInputLang(e)}},model:{value:t.valueBox["value"+e],callback:function(i){t.$set(t.valueBox,"value"+e,i)},expression:"valueBox['value' + item]"}})})),i("div",{staticClass:"f-lang-btn-wrap"},[i("div",{staticClass:"f-lang-btn f-lang-btn-left",on:{touch:function(e){return e.preventDefault(),t.closeLangBox(e)},click:function(e){return e.preventDefault(),t.closeLangBox(e)}}},[t._v("关闭")]),i("div",{staticClass:"f-lang-btn f-lang-btn-right",on:{touch:function(e){return e.preventDefault(),t.confirmLangBox(e)},click:function(e){return e.preventDefault(),t.confirmLangBox(e)}}},[t._v("确定")])])],2)])],1)},we=[],ye={name:"langbox",props:["id","name","type","label","value","options","readonly","styleId","gridEditParm","ctx","row"],components:{popLabel:f["a"]},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick}},data:function(){return{show:!0,visible:!0,disabled:!1,currentText:"",currentValue:"",notClear:!1,curLang:"",langSuffix:[],errorMessage:"",showPopover:!1,note:"",valueBox:{},style:{height:"",width:"80%"},showLangBox:!1}},mounted:function(){for(var t in this.disabled=this.readOnly,this.curLang=ie["a"].getLanguage(),this.langSuffix=ie["a"].getLanguageSuffix(),this.name?this.namePrefix=this.name.replace("$LANGUAGE$",""):this.namePrefix=this.id,this.langSuffix)console.log(this.namePrefix+this.langSuffix[t]),this.$set(this.valueBox,"value"+this.langSuffix[t],"");this.style.height=this.getHeight(),this.note=this.options.note,this.setValue(),this.setLangValue()},activated:function(){console.log("lang active")},methods:{handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},openLangBox:function(){this.readOnly||this.disabled||(this.setLangValue(),this.showLangBox=!0)},setErrorMessage:function(t){this.errorMessage=t},setDisabled:function(t){this.readonly||(this.disabled=t)},setRequired:function(t){this.options.required=t,this.$forceUpdate()},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},getValue:function(){return this.value},clearValue:function(){this.currentText=""},closeLangBox:function(){this.showLangBox=!1},confirmLangBox:function(){for(var t in this.langSuffix)this.$emit("input",this.namePrefix+this.langSuffix[t],this.valueBox["value"+this.langSuffix[t]],this);this.showLangBox=!1,this.setValue(),this.$emit("input",this.name,this.currentText,this)},setValue:function(){var t=ie["a"].getCurrentSuffix();this.bindName=this.namePrefix+t,this.value=this.row[this.bindName],this.currentText=this.row[this.bindName]},setLangValue:function(){for(var t in this.langSuffix)this.valueBox["value"+this.langSuffix[t]]=this.row[this.namePrefix+this.langSuffix[t]]},getLabel:function(t){switch(t){case"_CHS":return"简体中文";case"_CHT":return"繁体中文";case"_EN":return"英文";case"_ES":return"西班牙语";case"_PT":return"葡萄牙语"}},onInputEvent:function(t){this.$emit("input",this.bindName,this.currentText,this),this.$emit("input",this.name,this.currentText,this)},onInputCHN:function(t){this.$emit("input",this.namePrefix+"_CHS",this.valueBox.value_CHS,this)},onInputCHT:function(t){this.$emit("input",this.namePrefix+"_CHT",this.valueBox.value_CHT,this)},onInputEN:function(t){this.$emit("input",this.namePrefix+"_EN",this.valueBox.value_EN,this)},onInputLang:function(t,e){console.log(t)},getHeight:function(){var t=(85+45*this.langSuffix.length).toString()+"px";return t},stopClick:function(){}},watch:{value:function(t){this.currentText=t,this.setLangValue(),this.setValue(),this.setErrorMessage("")}}},Ce=ye,ke=(i("bc6d"),Object(w["a"])(Ce,be,we,!1,null,"7d10e701",null)),xe=ke.exports,Ie=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{directives:[{name:"show",rawName:"v-show",value:t.show&&t.visible,expression:"show&&visible"}],staticClass:"f-panel-detail",attrs:{id:"wfBlock"}},[i("div",{staticClass:"f-panel-title f-panel-grid",class:{"f-panel-hidden":t.isFold},on:{click:t.toggleHeader}},[t.customIconSrc?i("img",{attrs:{src:t.customIconSrc}}):t._e(),i("span",{staticClass:"f-panel-header"},[t._v(" "+t._s(t.getLang("wflog_wl"))+" ")]),i("span",{staticClass:"f-panel-arrow"},[i("van-icon",{attrs:{name:"arrow"}})],1)]),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.isFold,expression:"!isFold"}],attrs:{id:t.idSelector}})])},_e=[],Te=i("72fb"),Fe={name:"wflog",props:["row","styleId","ctx","options","isView"],data:function(){return{idSelector:"",show:!0,isFold:!1,visible:!0,typeId:"",customIconSrc:""}},mounted:function(){this.customIconSrc=window.IDP_CUSTOM_HEADER_ICON,this.pkCol=this.ctx.modelController.pkcol,this.idSelector=this.options.id+"_block",this.isFold=this.options.isFold,this.show=this.options.isHidden?this.isView:this.show,this.typeId=this.options.typeId||"wf",this.formType=this.options.formType;var t=this;this.ctx&&this.ctx.event&&this.ctx.event.map({loadData:function(){t.loadData()}})},activated:function(){},methods:{getLang:function(t,e){return window.idp.lang.get(t,e)},loadData:function(){var t=this;if(window["gspTaskCenterBizLog"])this.refreshHtml();else{var e="/platform/runtime/wf/webapp/mobiletaskcenter/taskComment.js?v="+this.getVersion();Te["a"].loadScript(e).then((function(e){console.log(e),t.refreshHtml()}))}},getVersion:function(){return et["a"].getFormatData(new Date,"yyyyMMdd")},refreshHtml:function(){var t={formType:this.formType,billId:this.row[this.pkCol]},e={bizInstId:this.row[this.pkCol],isIncludeHistory:this.options.isIncludeHistory||!1,typeId:this.typeId,pfCurrentTaskCommentParam:t,pfPredictionTaskCommentParam:t};e.isIncludeHistory=!!this.options.isHistory,window["gspTaskCenterBizLog"]["ApprovalLogUtil"].getBizlogHtml(e,window.document.querySelector("#"+this.idSelector)).then((function(t){}))},getWfInfo:function(){},foldWfInfo:function(){},setShow:function(t){this.show=t},setVisible:function(t){this.visible=!t},toggleHeader:function(){this.isFold=!this.isFold}},watch:{isView:function(t){this.show=t},row:{deep:!0,handler:function(){this.loadData()}}}},Le=Fe,Se=(i("afd6"),Object(w["a"])(Le,Ie,_e,!1,null,null,null)),De=Se.exports,Ee=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("van-field",{directives:[{name:"show",rawName:"v-show",value:t.visible&&t.show,expression:"visible&&show"}],class:{"f-input-block":t.isBlock},attrs:{name:"checkbox",label:t.label,readonly:t.disabled,required:t.options.required,"error-message":t.errorMessage,"error-message-align":"right"},scopedSlots:t._u([{key:"input",fn:function(){return[i("div",{staticStyle:{margin:"0 auto"}},[i("div",{},[t.currentValue?i("img",{staticStyle:{height:"120px"},attrs:{src:t.currentValue},on:{click:t.preview}}):t._e()]),i("div",{staticClass:"f-list-filter-button",staticStyle:{"text-align":"center"}},[i("van-button",{attrs:{type:"info"},on:{click:t.setSign}},[t._v("设置")])],1)]),i("van-popup",{style:{width:"100%",height:"60%"},attrs:{position:"bottom","get-container":"body"},model:{value:t.showPop,callback:function(e){t.showPop=e},expression:"showPop"}},[i("div",{staticStyle:{display:"flex",height:"100%","flex-direction":"column"}},[i("div",[i("sign",{ref:"sign",attrs:{width:t.signWidth,height:t.signHeight,isCrop:!0,lineWidth:"6"}})],1),i("div",{staticClass:"f-list-filter-button"},[i("van-button",{attrs:{type:"info"},on:{click:t.handleConfirm}},[t._v(" 确定 ")]),i("van-button",{attrs:{type:"default"},on:{click:t.handleReset}},[t._v(" 清空 ")])],1)])])]},proxy:!0},{key:"label",fn:function(){return[i("pop-label",{attrs:{showLink:t.showLink,label:t.label,note:t.note},on:{linkclick:t.handleLinkClick}})]},proxy:!0}])})},$e=[],Pe=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("canvas",{ref:"canvas",on:{mousedown:t.mouseDown,mousemove:t.mouseMove,mouseup:t.mouseUp,touchstart:t.touchStart,touchmove:t.touchMove,touchend:t.touchEnd}})},Oe=[],Be=i("2909"),Re={props:{width:{type:Number,default:800},height:{type:Number,default:300},lineWidth:{type:Number,default:4},lineColor:{type:String,default:"#000000"},bgColor:{type:String,default:""},isCrop:{type:Boolean,default:!1},isClearBgColor:{type:Boolean,default:!0},format:{type:String,default:"image/png"},quality:{type:Number,default:1}},data:function(){return{hasDrew:!1,resultImg:"",points:[],canvasTxt:null,startX:0,startY:0,isDrawing:!1,sratio:1}},computed:{ratio:function(){return this.height/this.width},stageInfo:function(){return this.$refs.canvas.getBoundingClientRect()},myBg:function(){return this.bgColor?this.bgColor:"rgba(255, 255, 255, 0)"}},watch:{myBg:function(t){this.$refs.canvas.style.background=t}},beforeMount:function(){window.addEventListener("resize",this.$_resizeHandler)},beforeDestroy:function(){window.removeEventListener("resize",this.$_resizeHandler)},mounted:function(){var t=this,e=this.$refs.canvas;e.height=this.height,e.width=this.width,e.style.background=this.myBg,this.$_resizeHandler(),document.onmouseup=function(){t.isDrawing=!1}},methods:{$_resizeHandler:function(){var t=this.$refs.canvas;t.style.width=this.width+"px";var e=parseFloat(window.getComputedStyle(t).width);t.style.height=this.ratio*e+"px",this.canvasTxt=t.getContext("2d"),this.canvasTxt.scale(1*this.sratio,1*this.sratio),this.sratio=e/this.width,this.canvasTxt.scale(1/this.sratio,1/this.sratio)},mouseDown:function(t){t=t||event,t.preventDefault(),this.isDrawing=!0,this.hasDrew=!0;var e={x:t.offsetX,y:t.offsetY};this.drawStart(e)},mouseMove:function(t){if(t=t||event,t.preventDefault(),this.isDrawing){var e={x:t.offsetX,y:t.offsetY};this.drawMove(e)}},mouseUp:function(t){t=t||event,t.preventDefault();var e={x:t.offsetX,y:t.offsetY};this.drawEnd(e),this.isDrawing=!1},touchStart:function(t){if(t=t||event,t.preventDefault(),this.hasDrew=!0,1===t.touches.length){var e={x:t.targetTouches[0].clientX-this.$refs.canvas.getBoundingClientRect().left,y:t.targetTouches[0].clientY-this.$refs.canvas.getBoundingClientRect().top};this.drawStart(e)}},touchMove:function(t){if(t=t||event,t.preventDefault(),1===t.touches.length){var e={x:t.targetTouches[0].clientX-this.$refs.canvas.getBoundingClientRect().left,y:t.targetTouches[0].clientY-this.$refs.canvas.getBoundingClientRect().top};this.drawMove(e)}},touchEnd:function(t){if(t=t||event,t.preventDefault(),1===t.touches.length){var e={x:t.targetTouches[0].clientX-this.$refs.canvas.getBoundingClientRect().left,y:t.targetTouches[0].clientY-this.$refs.canvas.getBoundingClientRect().top};this.drawEnd(e)}},drawStart:function(t){this.startX=t.x,this.startY=t.y,this.canvasTxt.beginPath(),this.canvasTxt.moveTo(this.startX,this.startY),this.canvasTxt.lineTo(t.x,t.y),this.canvasTxt.lineCap="round",this.canvasTxt.lineJoin="round",this.canvasTxt.lineWidth=this.lineWidth*this.sratio,this.canvasTxt.stroke(),this.canvasTxt.closePath(),this.points.push(t)},drawMove:function(t){this.canvasTxt.beginPath(),this.canvasTxt.moveTo(this.startX,this.startY),this.canvasTxt.lineTo(t.x,t.y),this.canvasTxt.strokeStyle=this.lineColor,this.canvasTxt.lineWidth=this.lineWidth*this.sratio,this.canvasTxt.lineCap="round",this.canvasTxt.lineJoin="round",this.canvasTxt.stroke(),this.canvasTxt.closePath(),this.startY=t.y,this.startX=t.x,this.points.push(t)},drawEnd:function(t){this.canvasTxt.beginPath(),this.canvasTxt.moveTo(this.startX,this.startY),this.canvasTxt.lineTo(t.x,t.y),this.canvasTxt.lineCap="round",this.canvasTxt.lineJoin="round",this.canvasTxt.stroke(),this.canvasTxt.closePath(),this.points.push(t),this.points.push({x:-1,y:-1})},generate:function(t){var e=this,i=t&&t.format?t.format:this.format,s=t&&t.quality?t.quality:this.quality,n=new Promise((function(t,n){if(e.hasDrew){var o=e.canvasTxt.getImageData(0,0,e.$refs.canvas.width,e.$refs.canvas.height);e.canvasTxt.globalCompositeOperation="destination-over",e.canvasTxt.fillStyle=e.myBg,e.canvasTxt.fillRect(0,0,e.$refs.canvas.width,e.$refs.canvas.height),e.resultImg=e.$refs.canvas.toDataURL(i,s);var a=e.resultImg;if(e.canvasTxt.clearRect(0,0,e.$refs.canvas.width,e.$refs.canvas.height),e.canvasTxt.putImageData(o,0,0),e.canvasTxt.globalCompositeOperation="source-over",e.isCrop){var r,l=e.getCropArea(o.data),c=document.createElement("canvas"),h=c.getContext("2d");c.width=l[2]-l[0],c.height=l[3]-l[1];var d=(r=e.canvasTxt).getImageData.apply(r,Object(Be["a"])(l));h.globalCompositeOperation="destination-over",h.putImageData(d,0,0),h.fillStyle=e.myBg,h.fillRect(0,0,c.width,c.height),a=c.toDataURL(i,s),c=null}t(a)}else n("Warning: Not Signned!")}));return n},reset:function(){this.canvasTxt.clearRect(0,0,this.$refs.canvas.width,this.$refs.canvas.height),this.isClearBgColor&&(this.$emit("update:bgColor",""),this.$refs.canvas.style.background="rgba(255, 255, 255, 0)"),this.points=[],this.hasDrew=!1,this.resultImg=""},getCropArea:function(t){for(var e=this.$refs.canvas.width,i=0,s=this.$refs.canvas.height,n=0,o=0;o<this.$refs.canvas.width;o++)for(var a=0;a<this.$refs.canvas.height;a++){var r=4*(o+this.$refs.canvas.width*a);(t[r]>0||t[r+1]>0||t[r+2]||t[r+3]>0)&&(n=Math.max(a,n),i=Math.max(o,i),s=Math.min(a,s),e=Math.min(o,e))}e++,i++,s++,n++;var l=[e,s,i,n];return l}}},Me=Re,Ve=(i("2771"),Object(w["a"])(Me,Pe,Oe,!1,null,"e30d8f74",null)),Ae=Ve.exports,Ne=i("28a2"),qe={name:"signbox",props:["name","type","label","value","options","readonly","styleId","ctx","gridEditParm"],components:{popLabel:f["a"],sign:Ae},computed:{showLink:function(){return this.options.islink&&this.options.link&&this.options.link.mobileClick}},created:function(){},mounted:function(){},activated:function(){},data:function(){return{signWidth:"",signHeight:"",showPop:!1,currentValue:"",isBlock:!0,disabled:!1,visible:!0,show:!0,errorMessage:"",showPopover:!1,note:""}},methods:{setSign:function(){this.signHeight=.6*document.documentElement.clientHeight-80,this.signWidth=document.documentElement.clientWidth,this.showPop=!0},preview:function(){Object(Ne["a"])([this.currentValue])},handleLinkClick:function(){var t=new Function("ctx","value","gridEditParm","("+this.options.link.mobileClick+")(ctx,value,gridEditParm)");t(this.ctx,this.value,this.gridEditParm)},setDisabled:function(t){this.readonly||(this.disabled=t)},setVisible:function(t){this.visible=!t},setShow:function(t){this.show=t},setRequired:function(t){this.options.required=t,this.$forceUpdate()},initOptions:function(){},handleConfirm:function(){var t=this;this.$refs.sign.generate().then((function(e){t.resultImg=e,t.currentValue=e,t.$emit("input",t.name,t.currentValue),t.showPop=!1})).catch((function(t){alert(t)}))},handleReset:function(){this.$refs.sign.reset()}},watch:{options:function(){this.initOptions(),this.disabled=this.readonly,this.visible=!0},value:function(t){this.currentValue=t}}},ze=qe,Ue=(i("be65"),Object(w["a"])(ze,Ee,$e,!1,null,null,null)),je=Ue.exports,He={name:"dycontrol",props:["styleId","type","value","vprops","gridEditParm","ctx","parent","row","listItems","callbackGetFilter","isView","bizOps"],inject:["viewTag"],data:function(){return{keyword:"asdfas",tabColor:"#388fff",readonly:null,hasloadWfScript:!1}},components:{vnumberrange:F,vinput:C,vtextarea:O,vrichtext:N,vcheckbox:K,vradio:X["a"],vdate:ot,vselect:dt,vlookup:bt,vtext:It,vgrid:Ot,vsearch:Bt["a"],vtoolbar:Rt["a"],vtabbar:zt,vgridmenu:Ut["a"],vtitle:Xt["a"],vuploader:de,vlabel:ve,vlangbox:xe,vwflog:De,vsign:je},methods:{onInput:function(t,e,i,s,n){this.$emit("input",t,e,i,s,n)},getVueComponent:function(t,e){var i=this.styleId+t.id;if(this.ctx&&!e&&(this.ctx.vueInstances=this.ctx.vueInstances||{},this.ctx.vueInstances[i]))return this.ctx.vueInstances[i];var s=null;if(t["tpl"]&&t["tpl"].template){var n={};if(t["tpl"].mixin){var o=new Function("return "+t["tpl"].mixin+";");n=o()}s=c["a"].extend({mixins:[n],props:["styleId","ctx","row","onInput"],template:t["tpl"].template,data:function(){return{}},mounted:function(){var t=this;this.ctx&&this.ctx.event&&this.ctx.event.map({loadData:function(){t.$loadData&&t.$loadData()}})},methods:{}})}return this.ctx&&this.ctx.vueInstances&&(this.ctx.vueInstances[i]=s),s}},render:function(t){var e=this.vprops,i=e.label,s=e.field,n=e.text,o=this.value;this.readonly=!!this.vprops.readonly;var a=null;this.gridEditParm&&(a={table:this.gridEditParm.table,id:this.gridEditParm.id,rowindex:this.gridEditParm.rowindex,record:this.gridEditParm.record,field:this.vprops.field});var r=this.ctx,l=this.parent;if(1==this.vprops.hide)return null;switch(this.type){case"gridmenu":return t(Ut["a"],{attrs:{styleId:this.styleId,vprops:this.vprops,callbackGetFilter:this.callbackGetFilter}});case"searchbar":return t(Bt["a"],{attrs:{styleId:this.styleId},on:{input:this.onInput}});case"tab":return t(zt,{attrs:{styleId:this.styleId,vprops:this.vprops}});case"notice":return t(Kt,{attrs:{styleId:this.styleId,vprops:this.vprops,text:n}});case"vue":var c=this.getVueComponent(this.vprops,!!this.gridEditParm);return this.vprops.hide?null:t(c,{attrs:{styleId:this.styleId,ctx:r,row:this.row},on:{input:this.onInput}});case"grid":return null;case"label":return t(ve,{attrs:{styleId:this.styleId,vprops:this.vprops}});case"title":case"grouptitle":return t(Xt["a"],{attrs:{id:this.vprops.id,parent:l,ctx:r,title:i,options:this.vprops}});case"toolbar":for(var h in this.vprops.btns)void 0==this.vprops.btns[h].hide&&(this.vprops.btns[h].hide=!1,console.log("btn init"));return t(Rt["a"],{attrs:{data:this.vprops.btns,id:this.vprops.id,styleId:this.styleId,bizOps:this.bizOps}});case"upload":return t(de,{attrs:{label:i,value:o,ctx:this.ctx,gridEditParm:this.gridEditParm,row:this.row,fieldName:s,styleId:this.styleId,options:this.vprops,readOnly:this.readonly,editor:this.vprops.editor_upload},on:{input:this.onInput}});case"textarea":return t(O,{attrs:{ctx:r,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"date":return t(ot,{attrs:{ctx:r,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"lookup":return t(bt,{attrs:{ctx:r,gridEditParm:a,row:this.row,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"dropdown":return t(dt,{attrs:{ctx:r,gridEditParm:a,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"checklist":return t(K,{attrs:{gridEditParm:a,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"radio":return t(X["a"],{attrs:{ctx:r,gridEditParm:a,row:this.row,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"lang":return t(xe,{attrs:{ctx:r,gridEditParm:a,row:this.row,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"wf":return t(De,{attrs:{ctx:r,styleId:this.styleId,row:this.row,options:this.vprops,isView:this.isView}});case"numberrange":return t(F,{attrs:{ctx:r,gridEditParm:a,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"sign":return t(je,{attrs:{ctx:r,gridEditParm:a,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});case"richeditor":return t(N,{attrs:{ctx:r,gridEditParm:a,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}});default:return t(C,{attrs:{ctx:r,gridEditParm:a,name:s,label:i,value:o,options:this.vprops,readonly:this.readonly,styleId:this.styleId},on:{input:this.onInput}})}}},We=He,Ge=Object(w["a"])(We,r,l,!1,null,null,null),Je=e["default"]=Ge.exports},f1d7:function(t,e,i){"use strict";var s=i("abcc"),n=i.n(s);n.a},f1e3:function(t,e,i){},f243:function(t,e,i){"use strict";var s=i("18a8"),n=i.n(s);n.a},f320:function(t,e,i){},f79d:function(t,e,i){},fca3:function(t,e,i){"use strict";var s=i("4af5"),n=i.n(s);n.a},fcd6:function(t,e,i){"use strict";var s=i("4045"),n=i.n(s);n.a},fff5:function(t,e,i){"use strict";var s=i("1c05"),n=i.n(s);n.a}}]);