webpackJsonp([49],{320:function(t,e,i){i(423);var s=i(12)(i(404),i(432),"data-v-77dca4f6",null);s.options.__file="E:\\source\\cloud\\normal\\before\\BP_Front\\src\\components\\style\\components\\audit.vue",s.esModule&&Object.keys(s.esModule).some(function(t){return"default"!==t&&"__esModule"!==t})&&console.error("named exports are not supported in *.vue files."),s.options.functional&&console.error("[vue-loader] audit.vue: functional components are not supported with templates, they should use render functions."),t.exports=s.exports},404:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=i(284);e.default={props:{value:"",styleId:"",params:null},computed:{feildData:function(){return this.data.length?this.data[this.currentIndex]:[]},getStyle:function(){if(this.showHorzontal)return{"padding-right":"17px"}}},data:function(){return{checkAll:!1,showHorzontal:!1,data:[{tbCode:"",tbName:"",cols:[{fieldCode:"11",fieldName:"22",enable:!1}]}],currentIndex:0,model:{type:1,styleId:"",enable:!1,sync:!1,auditFunction:"",auditCategory:"",auditEvent:"",tableAudits:[]},configJson:{},styleInfo:{},showClose:!1}},watch:{checkAll:function(){},value:function(){this.load()},currentIndex:function(){this.calcScrollStatus(),this.refreshCheck()}},mounted:function(){this.params&&this.params.showClose&&(this.showClose=!0),this.load()},methods:{handelCheck:function(t){this.data[this.currentIndex].cols.forEach(function(e){e.enable=t})},refreshBusiness:function(){},businessKeyHandle:function(t){t.businessKey&&(t.enable=!0,this.data[this.currentIndex].cols.forEach(function(e){e.fieldCode!=t.fieldCode&&(e.businessKey=!1)}),this.$forceUpdate())},refreshCheck:function(){this.data[this.currentIndex].cols.filter(function(t){return 1==t.enable}).length==this.data[this.currentIndex].cols.length?this.checkAll=!0:this.checkAll=!1},calcScrollStatus:function(){var t=this;this.$nextTick(function(){window.setTimeout(function(){var e=t.$refs.bodyWrapper;if(e){e.scrollTop>0&&(t.showHorzontal=!0),e.scrollTop=1;var i=e.scrollTop;e.scrollTop=0,t.showHorzontal=0!==i}})})},close:function(){window.idpDesignerCloseHandler&&window.idpDesignerCloseHandler()},save:function(){var t=this;this.configJson.styleAudit=this.getSaveData(),this.styleInfo.ConfigJson=JSON.stringify(this.configJson),s.a.SaveBasic(this.styleInfo).then(function(e){console.log(e.Data),"ok"==e.Code?t.$Message.success("保存成功！"):t.$Notice.error({title:"保存失败",desc:e.Message})})},getSaveData:function(){var t=this;return this.data.forEach(function(e){var i=t.model.tableAudits.find(function(t){return t.tbCode==e.tbCode});i&&(i.keyCol="",i.colAudits=[],e.cols.forEach(function(t){t.enable&&i.colAudits.push({fieldCode:t.fieldCode,fieldName:t.fieldName,lang:t.lang}),t.businessKey&&(i.keyCol=t.fieldCode)}))}),this.model},getCol:function(t,e){var i=!1,s={fieldCode:e.FieldCode,fieldName:e.FieldName,enable:!1,real:"1"==e.IsReal,lang:"1"==e.IsLang},n=this.model.tableAudits.find(function(e){return e.tbCode==t});if(n){n.colAudits.find(function(t){return t.fieldCode==e.FieldCode})&&(i=!0),n.keyCol==s.fieldCode&&(s.businessKey=!0)}return s.enable=i,s},refreshTabInfo:function(){var t=this;this.data.forEach(function(e){t.model.tableAudits.find(function(t){return t.tbCode==e.tbCode})||t.model.tableAudits.push({tbCode:e.tbCode,tbName:e.tbName,colAudits:[]})}),this.calcScrollStatus()},isLinkField:function(t,e){if(!t||0==t.length)return!1;for(var i=0;i<t.length;i++)if(t[i].ShowFields&&t[i].ShowFields){var s=t[i].ShowFields.find(function(t){return t.ShowCode==e.fieldCode});if(s)return!0}return!1},load:function(){var t=this;this.params&&this.params.custom&&(this.styleId=this.params.styleId,this.custom=this.params.custom),this.value?this.model=JSON.parse(JSON.stringify(this.value)):this.model.styleId=this.styleId,this.custom&&(sessionStorage.ResourceId=this.styleId,s.a.GetBasic(this.styleId).then(function(e){"ok"==e.Code&&(t.styleInfo=e.Data,t.styleInfo.ConfigJson&&(t.configJson=JSON.parse(t.styleInfo.ConfigJson),t.configJson.styleAudit&&(t.model=JSON.parse(JSON.stringify(t.configJson.styleAudit)))))})),this.data=[],this.currentIndex=0,s.a.GetModel(this.styleId).then(function(e){t.data=[],e.Data.forEach(function(e){var i={tbCode:e.tab.Code,tbName:e.tab.ShowName,cols:[]};e.cols.forEach(function(s){var n=t.getCol(e.tab.Code,s);(n.real||t.isLinkField(e.links,n))&&i.cols.push(n)}),t.data.push(i),t.refreshTabInfo()})})}},components:{}}},423:function(t,e){},432:function(t,e,i){t.exports={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"audit-wrap"},[t.custom?i("div",{staticClass:"audit-header"},[i("div",{staticClass:"audit-title"},[i("Icon",{staticClass:"audit-title-icon",attrs:{type:"md-paper"}}),t._v(" "),i("div",[t._v("数据变更")])],1),t._v(" "),i("div",[i("Button",{staticStyle:{margin:"5px 3px"},attrs:{slot:"extra",type:"primary",size:"small"},on:{click:t.save},slot:"extra"},[t._v("\n                保存\n            ")]),t._v(" "),t.params.showClose?i("Button",{staticStyle:{margin:"5px 3px"},attrs:{slot:"extra",type:"default",size:"small"},on:{click:t.close},slot:"extra"},[t._v("\n                关闭\n            ")]):t._e()],1)]):t._e(),t._v(" "),i("div",{staticClass:"audit-table"},[i("Form",{staticStyle:{"max-width":"600px","margin-top":"20px"},attrs:{model:t.model,"label-width":80}},[i("FormItem",[i("Checkbox",{model:{value:t.model.enable,callback:function(e){t.$set(t.model,"enable",e)},expression:"model.enable"}},[t._v("启用审计")]),t._v(" "),"0"!=t.model.type?[i("Poptip",{attrs:{placement:"bottom",width:"300"}},[i("Button",{attrs:{size:"small"}},[t._v("\n                            配置说明\n                        ")]),t._v(" "),i("div",{staticClass:"api",attrs:{slot:"content"},slot:"content"},[i("span",{staticClass:"Poptip-span"},[t._v(" 变更表的方式 需开发人员自己指定表单的变更记录表信息，表结构需要符合如下规则。")]),t._v(" "),i("table",{staticClass:"table-note"},[i("tr",[i("th",[t._v("字段名")]),t._v(" "),i("th",[t._v("说明")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        id\n                                    ")]),t._v(" "),i("td",[t._v("主键")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        styleid\n                                    ")]),t._v(" "),i("td",[t._v("表单id")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        dataId\n                                    ")]),t._v(" "),i("td",[t._v("数据id")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        userId\n                                    ")]),t._v(" "),i("td",[t._v("用户id")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        userCode\n                                    ")]),t._v(" "),i("td",[t._v("用户账号")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        createTime\n                                    ")]),t._v(" "),i("td",[t._v("创建时间 时间戳")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        content\n                                    ")]),t._v(" "),i("td",[t._v("变更内容 大文本")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        clientIP\n                                    ")]),t._v(" "),i("td",[t._v("客户端IP 字符串")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        functionId\n                                    ")]),t._v(" "),i("td",[t._v("功能菜单Id")])]),t._v(" "),i("tr",[i("td",[t._v("\n                                        jsonInfo\n                                    ")]),t._v(" "),i("td",[t._v("扩展数据 大文本")])])])])],1)]:t._e()],2),t._v(" "),i("FormItem",{attrs:{label:"记录方式"}},[i("Select",{attrs:{filterable:"",size:"small",placeholder:"请选择记录变更方式"},model:{value:t.model.type,callback:function(e){t.$set(t.model,"type",e)},expression:"model.type"}},[i("Option",{attrs:{value:"0"}},[t._v(" 审计")]),t._v(" "),i("Option",{attrs:{value:"1"}},[t._v(" 变更表")]),t._v(" "),i("Option",{attrs:{value:"2"}},[t._v(" 审计+变更表")]),t._v(" "),i("Option",{attrs:{value:"3"}},[t._v(" 业务变更日志 ")])],1)],1),t._v(" "),"0"!=t.model.type&&"3"!=t.model.type?i("FormItem",{attrs:{label:"变更记录表"}},[i("Input",{attrs:{size:"small",placeholder:""},model:{value:t.model.logTable,callback:function(e){t.$set(t.model,"logTable",e)},expression:"model.logTable"}})],1):t._e(),t._v(" "),"1"!=t.model.type&&"3"!=t.model.type?i("FormItem",{attrs:{label:"审计类别id"}},[i("Input",{attrs:{size:"small",placeholder:""},model:{value:t.model.auditCategory,callback:function(e){t.$set(t.model,"auditCategory",e)},expression:"model.auditCategory"}})],1):t._e(),t._v(" "),"1"!=t.model.type&&"3"!=t.model.type?i("FormItem",{attrs:{label:"审计事件id"}},[i("Input",{attrs:{size:"small",placeholder:""},model:{value:t.model.auditEvent,callback:function(e){t.$set(t.model,"auditEvent",e)},expression:"model.auditEvent"}})],1):t._e(),t._v(" "),"1"!=t.model.type&&"3"!=t.model.type?i("FormItem",{attrs:{label:"功能菜单"}},[i("Input",{attrs:{size:"small",placeholder:""},model:{value:t.model.auditFunction,callback:function(e){t.$set(t.model,"auditFunction",e)},expression:"model.auditFunction"}})],1):t._e()],1),t._v(" "),i("div",{staticClass:"audit-set"},[i("div",{staticClass:"title"},[t._v("审计字段设置")]),t._v(" "),i("div",{staticStyle:{display:"flex","align-items":"center"}},[i("RadioGroup",{attrs:{type:"button","button-style":"solid"},model:{value:t.currentIndex,callback:function(e){t.currentIndex=e},expression:"currentIndex"}},t._l(t.data,function(e,s){return i("Radio",{attrs:{value:s,label:s}},[t._v(t._s(e.tbName))])}),1)],1)]),t._v(" "),i("div",{staticClass:"table-wrap"},[i("div",{ref:"headerWrapper",staticClass:"table-header",style:t.getStyle},[i("table",{staticClass:"columninfo"},[t._m(0),t._v(" "),i("tr",[t._m(1),t._v(" "),t._m(2),t._v(" "),i("th",{attrs:{width:"60"}},[i("Checkbox",{on:{input:t.handelCheck},model:{value:t.checkAll,callback:function(e){t.checkAll=e},expression:"checkAll"}}),t._v("\n                            是否记录\n                        ")],1),t._v(" "),"3"==t.model.type?i("th",{attrs:{width:"40"}},[t._v("\n                            业务主键\n                        ")]):t._e()])])]),t._v(" "),i("div",{ref:"bodyWrapper",staticClass:"table-body"},[i("table",{staticClass:"columninfo"},[t._m(3),t._v(" "),t._l(t.feildData.cols,function(e,s){return[i("tr",[i("td",{attrs:{width:"100"}},[i("span",{staticStyle:{"word-break":"break-all"},attrs:{title:e.fieldCode}},[t._v(t._s(e.fieldCode))])]),t._v(" "),i("td",{attrs:{width:"100"}},[i("span",{staticStyle:{"word-break":"break-all"},attrs:{title:e.fieldName}},[t._v(t._s(e.fieldName))])]),t._v(" "),i("td",{staticStyle:{"text-align":"center"},attrs:{width:"60"}},[i("Checkbox",{on:{input:t.refreshCheck},model:{value:e.enable,callback:function(i){t.$set(e,"enable",i)},expression:"item.enable"}})],1),t._v(" "),"3"==t.model.type?i("td",{staticStyle:{"text-align":"center"},attrs:{width:"40"}},[i("Checkbox",{on:{"on-change":function(i){return t.businessKeyHandle(e)}},model:{value:e.businessKey,callback:function(i){t.$set(e,"businessKey",i)},expression:"item.businessKey"}})],1):t._e()])]})],2)])])],1)])},staticRenderFns:[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("colgroup",[i("col",{attrs:{width:"100"}}),t._v(" "),i("col",{attrs:{width:"100"}}),t._v(" "),i("col",{attrs:{width:"60"}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("th",{attrs:{width:"100"}},[i("span",[t._v("字段编号")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("th",{attrs:{width:"100"}},[i("span",[t._v("字段名称")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("colgroup",[i("col",{attrs:{width:"100"}}),t._v(" "),i("col",{attrs:{width:"100"}}),t._v(" "),i("col",{attrs:{width:"60"}})])}]},t.exports.render._withStripped=!0},490:function(t,e,i){"use strict";function s(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)}Object.defineProperty(e,"__esModule",{value:!0});var n=i(492),a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};e.default={name:"apiop",props:{value:{type:Object,default:null},styleId:{type:String,default:""},custom:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}},apiList:{type:Object,default:function(){return{}}}},data:function(){return{model:{},currentApi:"common",currentApiData:[],opSelectedColumns:[{type:"selection",width:60,align:"center"},{title:"功能操作名称",key:"name",align:"center"},{title:"编号",key:"code",align:"center"},{title:"ID",key:"id",align:"center"}],functionSelectModalVisible:!1,businessTreeData:[],filteredBusinessTree:[],businessTreeSearch:"",selectedBusinessIds:[],functionTableData:[],filteredFunctionTable:[],functionTableSearch:"",selectedFunctions:[],functionTableTotal:0,functionTableCurrent:1,functionTablePageSize:10,functionTablePagedData:[],functionTableLoading:!1,functionTableColumns:[{type:"selection",width:60,align:"center"},{title:"编号",key:"code",align:"center",width:120},{title:"名称",key:"name",align:"center",width:150},{title:"功能分组",key:"businessName",align:"center",width:120},{title:"业务对象路径",key:"fullpath",align:"left",ellipsis:!0,tooltip:!0}],customFields:{key:"title"},selectedItems:[]}},computed:{hasSelectedItems:function(){return this.selectedItems&&this.selectedItems.length>0}},watch:{value:{handler:function(t){if(t){var e={};Object.keys(this.apiList).forEach(function(t){e[t]=[]}),"object"===(void 0===t?"undefined":a(t))&&Object.keys(t).forEach(function(i){Array.isArray(t[i])&&i in e&&(e[i]=JSON.parse(JSON.stringify(t[i])))}),this.model=e,this.updateCurrentApiData()}},immediate:!0},currentApi:{handler:function(){this.updateCurrentApiData()}},selectedBusinessIds:{handler:function(){this.filterFunctionTableByBusiness()}}},created:function(){if(0===Object.keys(this.model).length&&Object.keys(this.apiList).length>0){var t={};Object.keys(this.apiList).forEach(function(e){t[e]=[]}),this.model=t}},mounted:function(){},methods:{updateCurrentApiData:function(){this.model[this.currentApi]||this.$set(this.model,this.currentApi,[]),Array.isArray(this.model[this.currentApi])?this.currentApiData=JSON.parse(JSON.stringify(this.model[this.currentApi])):this.currentApiData=[]},selectApi:function(t){this.currentApi=t},getApiTagColor:function(t){return{common:"blue",add:"green",update:"orange",delete:"red",get:"purple",qry:"geekblue"}[t]||"default"},hasApiConfig:function(t){return this.model&&this.model[t]&&Array.isArray(this.model[t])&&this.model[t].length>0},getApiConfigCount:function(t){return this.hasApiConfig(t)?this.model[t].length:0},getApiConfigData:function(t){return this.hasApiConfig(t)?this.model[t]:[]},showFunctionSelectModal:function(){this.functionSelectModalVisible=!0,this.selectedFunctions=[],this.functionTableSearch="",this.$Spin.show({render:function(t){return t("div",[t("Icon",{props:{type:"ios-loading",size:18},style:{animation:"ani-demo-spin 1s linear infinite"}}),t("div","正在加载数据...")])}}),Promise.all([n.a.getBusinessObjects(),n.a.getFuncOperations()]).then(function(t){var e=t[0],i=t[1],s={};e.forEach(function(t){s[t.id]=t});var n={};e.forEach(function(t){n[t.id]=this.getBusinessFullPath(t.id,s)}.bind(this)),i.forEach(function(t){t.businessId&&n[t.businessId]?(t.fullpath=n[t.businessId],t.businessName=s[t.businessId]?s[t.businessId].name:""):(t.fullpath="",t.businessName="")}),this.functionTableData=i,this.filteredFunctionTable=i.slice(),this.updateFunctionTablePagination(this.filteredFunctionTable),this.$Spin.hide()}.bind(this)).catch(function(t){console.error("加载数据失败:",t),this.$Message.error("加载数据失败，请重试"),this.$Spin.hide()}.bind(this))},confirmFunctionSelect:function(){if(0===this.selectedFunctions.length)return void this.$Message.warning("请选择至少一个功能操作");this.model[this.currentApi]&&Array.isArray(this.model[this.currentApi])||(this.$set(this.model,this.currentApi,[]),this.updateCurrentApiData());var t=this.model[this.currentApi].map(function(t){return t.id}),e=this;this.selectedFunctions.forEach(function(i){-1===t.indexOf(i.id)&&(e.model[e.currentApi].push({id:i.id,code:i.code,name:i.name}),t.push(i.id))}),this.updateCurrentApiData(),this.functionSelectModalVisible=!1,this.$Message.success("成功添加 "+this.selectedFunctions.length+" 项功能操作")},removeAuthItem:function(t,e){var i=this;this.$Modal.confirm({title:"确认删除",content:"确定要删除此授权配置吗？",onOk:function(){i.model[t]&&Array.isArray(i.model[t])&&(i.model[t].splice(e,1),i.updateCurrentApiData())}})},viewApiConfig:function(t){this.currentApi=t},save:function(){this.$emit("input",this.model),this.custom&&this.$emit("on-save",this.model),this.$Message.success("保存成功")},close:function(){this.$emit("on-close")},getSaveData:function(){return this.model},transformBusinessTreeData:function(t){var e={},i=[];return t.forEach(function(t){e[t.id]={title:t.name,id:t.id,code:t.code,expand:!0,children:[],origin:t}}),t.forEach(function(t){var s=e[t.id];t.parentID&&e[t.parentID]?e[t.parentID].children.push(s):i.push(s)}),i},renderBusinessTree:function(t,e){var i=(e.root,e.node,e.data);return t("span",{style:{display:"inline-block",width:"100%",cursor:"pointer"},on:{click:function(){}}},i.title)},filterBusinessTree:function(){if(!this.businessTreeSearch)return void(this.filteredBusinessTree=this.transformBusinessTreeData(this.businessTreeData));var t=this.businessTreeSearch.toLowerCase(),e=this.businessTreeData.filter(function(e){return e.name.toLowerCase().includes(t)||e.code.toLowerCase().includes(t)});this.filteredBusinessTree=this.transformBusinessTreeData(e)},handleBusinessTreeSelect:function(t){t.length>0?this.selectedBusinessIds=[t[0].id]:this.selectedBusinessIds=[]},filterFunctionTable:function(){var t=this.functionTableData.slice();if(this.functionTableSearch){var e=this.functionTableSearch.toLowerCase();t=t.filter(function(t){return t.name.toLowerCase().indexOf(e)>-1||t.code.toLowerCase().indexOf(e)>-1||t.fullpath&&t.fullpath.toLowerCase().indexOf(e)>-1})}this.filteredFunctionTable=t,this.updateFunctionTablePagination(t)},filterFunctionTableByBusiness:function(){var t=this,e=[].concat(s(this.functionTableData));if(this.selectedBusinessIds.length>0&&(e=e.filter(function(e){return t.selectedBusinessIds.includes(e.businessId)})),this.functionTableSearch){var i=this.functionTableSearch.toLowerCase();e=e.filter(function(t){return t.name.toLowerCase().includes(i)||t.code.toLowerCase().includes(i)})}this.filteredFunctionTable=e,this.updateFunctionTablePagination(e)},handleFunctionTableSelect:function(t){this.selectedFunctions=t},getBusinessFullPath:function(t,e){for(var i=[],s=t;s&&e[s];){var n=e[s];i.unshift(n.name),s=n.parentID}return i.join(" / ")},configureApi:function(t){this.currentApi=t,this.showFunctionSelectModal()},selectFunction:function(t){for(var e=!1,i=0;i<this.selectedFunctions.length;i++)if(this.selectedFunctions[i].id===t.id){e=!0;break}if(e){for(var s=[],i=0;i<this.selectedFunctions.length;i++)this.selectedFunctions[i].id!==t.id&&s.push(this.selectedFunctions[i]);this.selectedFunctions=s,this.$Message.info("已取消选择")}else this.selectedFunctions.push(t),this.$Message.success("已选择");if(this.$refs.functionTable&&"function"==typeof this.$refs.functionTable.toggleSelect){for(var n=-1,i=0;i<this.filteredFunctionTable.length;i++)if(this.filteredFunctionTable[i].id===t.id){n=i;break}n>=0&&this.$refs.functionTable.toggleSelect(n)}},removeSelectedItems:function(){var t=this;if(0===this.selectedItems.length)return void this.$Message.warning("请先选择要删除的项");this.$Modal.confirm({title:"确认删除",content:"确定要删除选中的 "+this.selectedItems.length+" 项功能操作吗？",onOk:function(){if(!t.model[t.currentApi]||!Array.isArray(t.model[t.currentApi]))return t.$set(t.model,t.currentApi,[]),void t.updateCurrentApiData();var e=t.selectedItems.map(function(t){return t.id});t.model[t.currentApi]=t.model[t.currentApi].filter(function(t){return-1===e.indexOf(t.id)}),t.updateCurrentApiData(),t.selectedItems=[],t.$Message.success("删除成功")}})},handleRightGridSelectionChange:function(t){this.selectedItems=t},handleFunctionTablePageSizeChange:function(t){this.functionTablePageSize=t,this.functionTableCurrent=1,this.updateFunctionTablePagination(this.filteredFunctionTable)},handleFunctionTablePageChange:function(t){this.functionTableCurrent=t,this.updateFunctionTablePagination(this.filteredFunctionTable)},updateFunctionTablePagination:function(t){this.functionTableTotal=t.length;var e=Math.ceil(this.functionTableTotal/this.functionTablePageSize)||1;this.functionTableCurrent>e&&(this.functionTableCurrent=1);var i=(this.functionTableCurrent-1)*this.functionTablePageSize,s=Math.min(i+this.functionTablePageSize,t.length);this.functionTablePagedData=t.slice(i,s)}}}},492:function(t,e,i){"use strict";i.d(e,"a",function(){return n});var s=i(493),n={getBusinessObjects:function(){return i.i(s.a)({url:"/api/runtime/sys/v1.0/business-objects",method:"get"})},getFuncOperations:function(){return i.i(s.a)({url:"/api/runtime/sys/v1.0/funcOperations/all",method:"get"})}}},493:function(t,e,i){"use strict";var s=i(56),n=i.n(s),a=i(57),o=n.a.create({timeout:6e5});o.interceptors.request.use(a.a,a.b),o.interceptors.response.use(a.c,a.d),e.a=o},494:function(t,e){},495:function(t,e,i){i(494);var s=i(12)(i(490),i(496),"data-v-61a0a9b6",null);s.options.__file="E:\\source\\cloud\\normal\\before\\BP_Front\\src\\components\\style\\components\\apiop.vue",s.esModule&&Object.keys(s.esModule).some(function(t){return"default"!==t&&"__esModule"!==t})&&console.error("named exports are not supported in *.vue files."),s.options.functional&&console.error("[vue-loader] apiop.vue: functional components are not supported with templates, they should use render functions."),t.exports=s.exports},496:function(t,e,i){t.exports={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"apiop-wrap"},[t.custom?i("div",{staticClass:"apiop-header"},[i("div",{staticClass:"apiop-title"},[i("Icon",{staticClass:"apiop-title-icon",attrs:{type:"md-key"}}),t._v(" "),i("div",[t._v("数据权限")])],1),t._v(" "),i("div",[i("Button",{staticStyle:{margin:"5px 3px"},attrs:{slot:"extra",type:"primary",size:"small"},on:{click:t.save},slot:"extra"},[t._v("\n                保存\n            ")]),t._v(" "),t.params&&t.params.showClose?i("Button",{staticStyle:{margin:"5px 3px"},attrs:{slot:"extra",type:"default",size:"small"},on:{click:t.close},slot:"extra"},[t._v("\n                关闭\n            ")]):t._e()],1)]):t._e(),t._v(" "),i("div",{staticClass:"apiop-content"},[i("div",{staticClass:"api-config-wrap"},[i("Alert",{staticStyle:{"margin-bottom":"16px"},attrs:{type:"info","show-icon":""}},[i("template",{slot:"desc"},[i("p",[t._v("通用(common)配置将作为默认配置应用于所有未单独配置的API操作。")]),t._v(" "),i("p",[t._v("单独配置的API操作优先级高于通用配置。")])])],2),t._v(" "),i("Row",{staticClass:"api-layout",attrs:{gutter:16}},[i("Col",{staticClass:"api-list-col",attrs:{span:6}},[i("Card",{staticClass:"api-list-card",attrs:{title:"API列表"}},[i("div",{staticClass:"api-list"},t._l(t.apiList,function(e,s){return i("div",{key:s,staticClass:"api-list-item",class:{"api-list-item-active":t.currentApi===s},on:{click:function(e){return t.selectApi(s)}}},[i("span",{staticClass:"api-name",class:"api-name-"+s},[t._v(t._s(e))]),t._v(" "),i("div",{staticClass:"api-status-indicator"},[i("Badge",{attrs:{status:t.hasApiConfig(s)?"success":"default"}})],1)])}),0)])],1),t._v(" "),i("Col",{staticClass:"api-config-col",attrs:{span:18}},[i("Card",{staticClass:"api-config-card"},[i("div",{attrs:{slot:"title"},slot:"title"},[i("span",[t._v(t._s(t.apiList[t.currentApi]||"")+" API功能操作列表")])]),t._v(" "),i("div",{staticStyle:{display:"flex",gap:"8px"},attrs:{slot:"extra"},slot:"extra"},[i("Button",{staticStyle:{"margin-right":"6px"},attrs:{type:"primary",size:"small"},on:{click:t.showFunctionSelectModal}},[t._v("\n                                添加功能操作\n                            ")]),t._v(" "),i("Button",{attrs:{type:"error",size:"small",disabled:!t.hasSelectedItems},on:{click:t.removeSelectedItems}},[t._v("\n                                删除选中\n                            ")])],1),t._v(" "),i("div",{staticClass:"api-config-header"},[i("div",{staticClass:"api-config-status"},[t.hasApiConfig(t.currentApi)?i("span",{staticClass:"api-configured"},[i("Icon",{attrs:{type:"md-checkmark-circle",color:"#19be6b"}}),t._v("\n                                    已配置 "+t._s(t.getApiConfigCount(t.currentApi))+" 项功能操作\n                                ")],1):i("span",{staticClass:"api-not-configured"},[i("Icon",{attrs:{type:"md-information-circle",color:"#ff9900"}}),t._v("\n                                    "+t._s("common"===t.currentApi?"未配置（所有API将无功能操作限制）":"未配置（将使用通用配置）")+"\n                                ")],1)])]),t._v(" "),i("div",{staticClass:"api-config-content"},[i("Table",{ref:"rightGrid",attrs:{border:"",height:"300",size:"small",columns:t.opSelectedColumns,data:t.currentApiData,"no-data-text":"common"===t.currentApi?"暂无通用功能操作配置":"暂无功能操作配置，将使用通用配置"},on:{"on-selection-change":t.handleRightGridSelectionChange}})],1)])],1)],1)],1)]),t._v(" "),i("Modal",{staticClass:"function-select-modal",attrs:{title:"功能操作",width:"1000","mask-closable":!1,styles:{top:"20px"}},model:{value:t.functionSelectModalVisible,callback:function(e){t.functionSelectModalVisible=e},expression:"functionSelectModalVisible"}},[i("div",{staticClass:"function-select-content",staticStyle:{height:"400px"}},[i("Row",{staticClass:"function-select-layout"},[i("Col",{staticClass:"function-table-col",attrs:{span:24}},[i("div",{staticClass:"search-box"},[i("Input",{attrs:{search:"",placeholder:"搜索功能操作"},on:{"on-search":t.filterFunctionTable},model:{value:t.functionTableSearch,callback:function(e){t.functionTableSearch=e},expression:"functionTableSearch"}})],1),t._v(" "),i("div",{staticClass:"function-table-container"},[i("Table",{ref:"functionTable",attrs:{border:"",size:"small",columns:t.functionTableColumns,data:t.functionTablePagedData,loading:t.functionTableLoading,height:"319"},on:{"on-selection-change":t.handleFunctionTableSelect}}),t._v(" "),i("div",{staticStyle:{margin:"10px",overflow:"hidden"}},[i("div",{staticStyle:{float:"right"}},[i("Page",{attrs:{total:t.functionTableTotal,"page-size":t.functionTablePageSize,current:t.functionTableCurrent,size:"small","show-elevator":"","show-sizer":""},on:{"on-page-size-change":t.handleFunctionTablePageSizeChange,"on-change":t.handleFunctionTablePageChange}})],1)])],1)])],1)],1),t._v(" "),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("Button",{attrs:{type:"default"},on:{click:function(e){t.functionSelectModalVisible=!1}}},[t._v("取消")]),t._v(" "),i("Button",{attrs:{type:"primary",disabled:0===t.selectedFunctions.length},on:{click:t.confirmFunctionSelect}},[t._v("确定")])],1)])],1)},staticRenderFns:[]},t.exports.render._withStripped=!0},546:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=i(320),n=i.n(s),a=i(495),o=i.n(a),l=i(637),r=i.n(l),c=i(284);i(83);e.default={props:{params:{type:Object,default:"",required:!0}},data:function(){return{model:{},config:{styleAudit:null,apiOp:null,dataAuth:null},modId:"",apiList:{common:"通用",add:"新增",update:"更新",delete:"删除",get:"获取",exportCard:"导出",importCard:"导入"}}},mounted:function(){this.load()},methods:{pushVO:function(){var t=this;c.a.PushBE(this.params.styleid,"true").then(function(e){"ok"==e.Code?t.$Message.success("推送成功"):t.$Notice.error({title:"推送失败",desc:e.Message})})},pushBE:function(){var t=this;c.a.PushBE(this.params.styleid,"false").then(function(e){"ok"==e.Code?t.$Message.success("推送成功"):t.$Notice.error({title:"推送失败",desc:e.Message})})},updateModelByVo:function(){var t=this;c.a.updateBeTable(this.model.ModId,this.model.Id).then(function(e){"ok"==e.Code?t.$Message.success("更新成功"):t.$Notice.error({title:"更新失败",desc:e.Message})})},load:function(){var t=this;c.a.GetBasic(this.params.styleid).then(function(e){console.log(e.Data),t.model=e.Data,t.model.ConfigJson&&(t.config=JSON.parse(t.model.ConfigJson)),t.modId=e.Data.ModId})},save:function(){var t=this;console.log(this.$refs.audit.getSaveData()),this.config.styleAudit=this.$refs.audit.getSaveData(),this.model.ConfigJson=JSON.stringify(this.config),c.a.SaveBasic(this.model).then(function(e){console.log(e.Data),"ok"==e.Code?t.$Message.success("保存成功！"):t.$Notice.error({title:"保存失败",desc:e.Message})})},onMenuDropClick:function(t){switch(t){case"pushVo":this.pushVO();break;case"updateModelByVo":this.updateModelByVo()}}},components:{audit:n.a,apiop:o.a,dataauth:r.a}}},547:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=i(331);e.default={name:"dataauth",props:{value:{type:Object,default:null},styleId:{type:String,default:""},custom:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}},modId:{type:String,default:""}},data:function(){return{model:[],indeterminate:!1,checkAll:!1,batchFields:[],batchAuthFieldId:"",batchAuthorizationId:"",batchAuthOp:"",modelCols:[],showBatchPanel:!1}},computed:{hasSelected:function(){return this.model.some(function(t){return t.checked})}},watch:{value:{handler:function(t){var e=this;t&&(this.model=JSON.parse(JSON.stringify(t)),this.model||(this.model=[]),this.model.forEach(function(t){e.$set(t,"checked",!1)}))},immediate:!0},modId:{handler:function(t){t&&this.load()}}},methods:{load:function(){var t=this;s.a.GetModel(this.modId).then(function(e){t.modelCols=e.Data[0].cols})},addItem:function(){this.model.push({field:"",authFieldId:"",authorizationId:"",authOp:"",remark:"",checked:!1})},removeItem:function(t){var e=this;this.$Modal.confirm({title:"确认删除",content:"确定要删除这条记录吗？",onOk:function(){e.model.splice(t,1)}})},removeSelected:function(){var t=this;this.hasSelected&&this.$Modal.confirm({title:"确认删除",content:"确定要删除选中的记录吗？",onOk:function(){t.model=t.model.filter(function(t){return!t.checked}),t.checkAll=!1,t.indeterminate=!1}})},handleCheckAll:function(){var t=this;this.indeterminate?(this.checkAll=!1,this.indeterminate=!1,this.model.forEach(function(t){t.checked=!1})):(this.checkAll=!this.checkAll,this.model.forEach(function(e){e.checked=t.checkAll}))},batchAdd:function(){var t=this;if(!this.batchFields||!this.batchFields.length)return void this.$Message.warning("请选择字段");this.batchFields.forEach(function(e){t.model.push({field:e,authFieldId:t.batchAuthFieldId,authorizationId:t.batchAuthorizationId,authOp:t.batchAuthOp,remark:"",checked:!1})}),this.$Message.success("成功添加 "+this.batchFields.length+" 个字段"),this.batchFields=[],this.showBatchPanel=!1},save:function(){var t=JSON.parse(JSON.stringify(this.model));t.forEach(function(t){delete t.checked}),this.$emit("input",t),this.custom&&this.$emit("on-save",t),this.$Message.success("保存成功")},close:function(){this.$emit("on-close")},getSaveData:function(){var t=JSON.parse(JSON.stringify(this.model));return t.forEach(function(t){delete t.checked}),t}}}},620:function(t,e){},636:function(t,e,i){var s=i(12)(i(546),i(677),null,null);s.options.__file="E:\\source\\cloud\\normal\\before\\BP_Front\\src\\components\\style\\components\\basic.vue",s.esModule&&Object.keys(s.esModule).some(function(t){return"default"!==t&&"__esModule"!==t})&&console.error("named exports are not supported in *.vue files."),s.options.functional&&console.error("[vue-loader] basic.vue: functional components are not supported with templates, they should use render functions."),t.exports=s.exports},637:function(t,e,i){i(620);var s=i(12)(i(547),i(682),"data-v-627387d0",null);s.options.__file="E:\\source\\cloud\\normal\\before\\BP_Front\\src\\components\\style\\components\\dataauth.vue",s.esModule&&Object.keys(s.esModule).some(function(t){return"default"!==t&&"__esModule"!==t})&&console.error("named exports are not supported in *.vue files."),s.options.functional&&console.error("[vue-loader] dataauth.vue: functional components are not supported with templates, they should use render functions."),t.exports=s.exports},677:function(t,e,i){t.exports={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("Tabs",{staticStyle:{position:"absolute",top:"0",bottom:"0",left:"0",right:"0",overflow:"auto"},attrs:{animated:!1}},[i("TabPane",{staticStyle:{padding:"5px"},attrs:{label:"基本配置",name:"basic"}},[i("div",{staticStyle:{padding:"10px"}},[i("Form",{staticStyle:{"max-width":"600px","margin-top":"20px"},attrs:{model:t.model,"label-width":200}},[i("FormItem",{attrs:{label:"数据模型ID"}},[i("Input",{attrs:{readonly:"",placeholder:""},model:{value:t.model.ModId,callback:function(e){t.$set(t.model,"ModId",e)},expression:"model.ModId"}})],1),t._v(" "),i("FormItem",{attrs:{label:"表单ID"}},[i("Input",{attrs:{readonly:"",placeholder:""},model:{value:t.model.Id,callback:function(e){t.$set(t.model,"Id",e)},expression:"model.Id"}})],1),t._v(" "),i("FormItem",{attrs:{label:"表单名称"}},[i("Input",{attrs:{placeholder:""},model:{value:t.model.Name,callback:function(e){t.$set(t.model,"Name",e)},expression:"model.Name"}})],1),t._v(" "),"1"==t.model.Security?i("FormItem",{attrs:{label:"密级体系"}},[i("Input",{attrs:{placeholder:""},model:{value:t.model.SecuritySystem,callback:function(e){t.$set(t.model,"SecuritySystem",e)},expression:"model.SecuritySystem"}})],1):t._e(),t._v(" "),"1"==t.model.Security?i("FormItem",{attrs:{label:"级别ID字段"}},[i("Input",{attrs:{placeholder:""},model:{value:t.model.LevelIdField,callback:function(e){t.$set(t.model,"LevelIdField",e)},expression:"model.LevelIdField"}})],1):t._e(),t._v(" "),"1"==t.model.Security?i("FormItem",{attrs:{label:"级别字段"}},[i("Input",{attrs:{placeholder:""},model:{value:t.model.LevelField,callback:function(e){t.$set(t.model,"LevelField",e)},expression:"model.LevelField"}})],1):t._e(),t._v(" "),i("FormItem",[i("Checkbox",{attrs:{"true-value":"1","false-value":"0"},model:{value:t.model.LockData,callback:function(e){t.$set(t.model,"LockData",e)},expression:"model.LockData"}},[t._v("启用数据锁")]),t._v(" "),i("Checkbox",{attrs:{"true-value":"1","false-value":"0"},model:{value:t.model.Security,callback:function(e){t.$set(t.model,"Security",e)},expression:"model.Security"}},[t._v("启用密级保护")])],1)],1)],1)]),t._v(" "),i("TabPane",{staticStyle:{padding:"5px"},attrs:{label:"数据变更",name:"audit"}},[i("audit",{ref:"audit",attrs:{styleId:this.params.styleid,value:t.config.styleAudit}})],1),t._v(" "),i("Button",{staticStyle:{margin:"5px 3px"},attrs:{slot:"extra",type:"primary",size:"small"},on:{click:t.save},slot:"extra"},[t._v("\n        保存\n    ")]),t._v(" "),t.params.isVo?t._e():i("Button",{staticStyle:{margin:"5px 3px"},attrs:{slot:"extra",type:"primary",size:"small"},on:{click:t.pushBE},slot:"extra"},[t._v("\n        生成BE\n    ")]),t._v(" "),i("Dropdown",{staticStyle:{"margin-right":"10px"},attrs:{slot:"extra",placement:"bottom-end"},on:{"on-click":t.onMenuDropClick},slot:"extra"},[i("Button",{attrs:{size:"small"}},[t._v("\n            更多\n            "),i("Icon",{attrs:{type:"ios-arrow-down"}})],1),t._v(" "),i("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[t.params.isVo?t._e():i("DropdownItem",{attrs:{name:"pushVo"}},[t._v("生成VO")]),t._v(" "),i("DropdownItem",{attrs:{name:"updateModelByVo"}},[t._v("从VO模型同步")])],1)],1)],1)},staticRenderFns:[]},t.exports.render._withStripped=!0},682:function(t,e,i){t.exports={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dataauth-wrap"},[t.custom?i("div",{staticClass:"dataauth-header"},[i("div",{staticClass:"dataauth-title"},[i("Icon",{staticClass:"dataauth-title-icon",attrs:{type:"md-lock"}}),t._v(" "),i("div",[t._v("功能操作")])],1),t._v(" "),i("div",[i("Button",{staticStyle:{margin:"5px 3px"},attrs:{slot:"extra",type:"primary",size:"small"},on:{click:t.save},slot:"extra"},[t._v("\n                保存\n            ")]),t._v(" "),t.params&&t.params.showClose?i("Button",{staticStyle:{margin:"5px 3px"},attrs:{slot:"extra",type:"default",size:"small"},on:{click:t.close},slot:"extra"},[t._v("\n                关闭\n            ")]):t._e()],1)]):t._e(),t._v(" "),i("div",{staticClass:"dataauth-content"},[i("div",{staticClass:"operation-area"},[i("Button",{staticStyle:{"margin-right":"6px"},attrs:{type:"primary",size:"small",icon:"md-add"},on:{click:t.addItem}},[t._v("添加字段")]),t._v(" "),i("Button",{staticStyle:{"margin-right":"6px"},attrs:{type:"error",size:"small",icon:"md-trash",disabled:!t.hasSelected},on:{click:t.removeSelected}},[t._v("删除选中")]),t._v(" "),i("Poptip",{attrs:{placement:"bottom-start",width:"850"},model:{value:t.showBatchPanel,callback:function(e){t.showBatchPanel=e},expression:"showBatchPanel"}},[i("Button",{staticStyle:{"margin-right":"6px"},attrs:{type:"primary",size:"small",icon:"md-list"}},[t._v("批量添加")]),t._v(" "),i("div",{attrs:{slot:"content"},slot:"content"},[i("Card",{attrs:{bordered:!1,"dis-hover":""}},[i("p",{attrs:{slot:"title"},slot:"title"},[i("Icon",{attrs:{type:"md-list"}}),t._v("\n                            批量添加\n                        ")],1),t._v(" "),i("div",[i("Form",{attrs:{"label-width":100}},[i("FormItem",{attrs:{label:"批量设置字段"}},[i("Select",{attrs:{filterable:"",multiple:"",clearable:"",size:"small",placeholder:"字段名"},model:{value:t.batchFields,callback:function(e){t.batchFields=e},expression:"batchFields"}},t._l(t.modelCols,function(e){return i("Option",{key:e.Code,attrs:{value:e.Code}},[t._v("("+t._s(e.Code)+")"+t._s(e.Name)+"\n                                        ")])}),1)],1),t._v(" "),i("FormItem",{attrs:{label:"权限字段编号"}},[i("Input",{attrs:{placeholder:"权限字段编号"},model:{value:t.batchAuthFieldId,callback:function(e){t.batchAuthFieldId=e},expression:"batchAuthFieldId"}})],1),t._v(" "),i("FormItem",{attrs:{label:"权限对象标识"}},[i("Input",{attrs:{placeholder:"权限对象标识"},model:{value:t.batchAuthorizationId,callback:function(e){t.batchAuthorizationId=e},expression:"batchAuthorizationId"}})],1),t._v(" "),i("FormItem",{attrs:{label:"数据操作标识"}},[i("Input",{attrs:{placeholder:"数据操作标识"},model:{value:t.batchAuthOp,callback:function(e){t.batchAuthOp=e},expression:"batchAuthOp"}})],1),t._v(" "),i("FormItem",[i("Button",{attrs:{type:"primary"},on:{click:t.batchAdd}},[t._v("批量添加")]),t._v(" "),i("Button",{staticStyle:{"margin-left":"8px"},attrs:{type:"default"},on:{click:function(e){t.showBatchPanel=!1}}},[t._v("关闭")])],1)],1)],1)])],1)],1)],1),t._v(" "),i("div",{staticClass:"table-wrap"},[i("div",{staticClass:"table-header"},[i("table",{staticClass:"columninfo"},[i("thead",[i("tr",[i("th",{staticStyle:{width:"40px"}},[t._v("操作")]),t._v(" "),i("th",{staticStyle:{width:"40px"}},[i("Checkbox",{attrs:{indeterminate:t.indeterminate,value:t.checkAll},on:{"on-change":t.handleCheckAll}})],1),t._v(" "),i("th",{staticStyle:{width:"250px"}},[t._v("字段名")]),t._v(" "),i("th",{staticStyle:{width:"150px"}},[t._v("权限字段编号")]),t._v(" "),i("th",{staticStyle:{width:"150px"}},[t._v("权限对象标识")]),t._v(" "),i("th",{staticStyle:{width:"150px"}},[t._v("数据操作标识")]),t._v(" "),i("th",[t._v("备注")])])])])]),t._v(" "),i("div",{staticClass:"table-body"},[i("table",{staticClass:"columninfo"},[i("tbody",t._l(t.model,function(e,s){return i("tr",{key:s},[i("td",{staticStyle:{width:"40px"}},[i("Button",{attrs:{type:"text",size:"small",icon:"md-close"},on:{click:function(e){return e.stopPropagation(),t.removeItem(s)}}})],1),t._v(" "),i("td",{staticStyle:{width:"40px"}},[i("Checkbox",{model:{value:e.checked,callback:function(i){t.$set(e,"checked",i)},expression:"item.checked"}})],1),t._v(" "),i("td",{staticStyle:{width:"250px"}},[i("Select",{attrs:{filterable:"",clearable:"",size:"small",placeholder:"字段名"},model:{value:e.field,callback:function(i){t.$set(e,"field",i)},expression:"item.field"}},t._l(t.modelCols,function(e){return i("Option",{key:e.Code,attrs:{value:e.Code}},[t._v("("+t._s(e.Code)+")"+t._s(e.Name)+"\n                                    ")])}),1)],1),t._v(" "),i("td",{staticStyle:{width:"150px"}},[i("Input",{attrs:{size:"small",placeholder:"权限字段编号"},model:{value:e.authFieldId,callback:function(i){t.$set(e,"authFieldId",i)},expression:"item.authFieldId"}})],1),t._v(" "),i("td",{staticStyle:{width:"150px"}},[i("Input",{attrs:{size:"small",placeholder:"权限对象标识"},model:{value:e.authorizationId,callback:function(i){t.$set(e,"authorizationId",i)},expression:"item.authorizationId"}})],1),t._v(" "),i("td",{staticStyle:{width:"150px"}},[i("Input",{attrs:{size:"small",placeholder:"数据操作标识"},model:{value:e.authOp,callback:function(i){t.$set(e,"authOp",i)},expression:"item.authOp"}})],1),t._v(" "),i("td",[i("Input",{attrs:{size:"small",placeholder:"备注说明"},model:{value:e.remark,callback:function(i){t.$set(e,"remark",i)},expression:"item.remark"}})],1)])}),0)])])])])])},staticRenderFns:[]},t.exports.render._withStripped=!0}});