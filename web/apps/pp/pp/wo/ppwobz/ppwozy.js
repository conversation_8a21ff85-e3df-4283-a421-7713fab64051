/**
 * 作业清单
 */
(function (idp) {
    window.wozymenu = {
        save: function () {
            idp.uiview.endEdit();
        },
        edit: function () {
            var gb = idp.uiview.modelController.deafaultData[0].data[0].WO_GB;
            if (gb === "1") {
                pppubfunctions.warn(idp.lang.get("WO_50"));//任务已关闭不允许修改
                return false;
            }
            return idp.uiview.edit();
        },
        //取消
        cancel: function () {
            return idp.uiview.cancel();
        },
        close: function () {
            return idp.uiview.close();
        },
    }
    function getContext() {
        if (window.parent && window.parent.$ && window.parent.$.leeDialog) {
            return window.parent;
        }
        return window;
    }


    //精度配置处理
    window.accConfig = {
        grid_WOZY: {
            valueField: 'WOZY_WLID',
            zslFields: ["WOZY_BZYL", "WOZY_JHYL", "WOZY_SJYL"],
            fslFields: [],
            accFields: {
                mainField: 'WOZY_JLDWJD', //主计量精度字段
                auxField: '', //辅计量精度字段
            }
        }
    }

    idp.event.bind("domReady", function (e, context) {
        idp.event.register('grid_main', "beforeGridInit", function (e, p) {

        });
        idp.event.register("grid_main", "beforeEdit", function (e, index, rowdata, col) {

        });
        idp.event.register('grid_main', "beforeApplyEditor", function (e, index, rowdata, column) {

        });
    });
    idp.event.bind("viewReady", function (e, context) {

    });
    idp.event.bind("mountReady", function (event, carduicontroller) {

    });
    idp.event.bind("loadData", function (event, loaddata) {
        //加载数据后进行数据精度得处理
        var worow = idp.uiview.modelController.deafaultData[0].data[0];
        if (worow.WO_CPID && worow.WO_CPID.trim()) {
            pppubfunctions.cardPrecisionConvWithPreCol("WO_JHTCSL", "WO_CPID_PPWL_JLDWJD");
        } else {
            pppubfunctions.cardPrecisionConvWithPreCol("WO_JHTCSL", "WO_JLDWID_ACCURACY");
        }
    });
})(window.idp);
