/**
 * 作业清单
 */
(function (idp) {
    window.wocbmenu = {
        openWozx: function () {
            var row = idp.control.get('grid_main').getCheckedRows();
            if (row.length <= 0 || row.length > 1) {
                pppubfunctions.warn(idp.lang.get("WO_13"));
                return false;
            } else {
                var LXID = row[0].WO_LXID;//任务类型ID
                var ZZID = row[0].WO_ZZID;//组织ID
                var isworkItem = pppubfunctions.isworkItem() ? "1" : "0";
                let params ='runtime=true&j=true&fdim=' + encodeURIComponent(LXID) + '&sdim=' + encodeURIComponent(ZZID) + '&workItem=' + encodeURIComponent(isworkItem);
                //"备料清单"
                idp.utils.openurl("f15c1138-7128-ac23-781c-6c29bc746225", idp.lang.get("WO_85"), idp.utils.buildURL("/apps/fastdweb/views/runtime/page/card/cardpreview.html?j=true&styleid=f15c1138-7128-ac23-781c-6c29bc746225&dataid=" + row[0].ID + "&status=view&" + params + '#/&funcid='));
                return true;
            }
        },
        openWozy: function () {
            var row = idp.control.get('grid_main').getCheckedRows();
            if (row.length <= 0 || row.length > 1) {
                pppubfunctions.warn(idp.lang.get("WO_13"));
                return false;
            } else {
                var LXID = row[0].WO_LXID;//任务类型ID
                var ZZID = row[0].WO_ZZID;//组织ID
                let params = 'runtime=true&j=true&fdim=' + encodeURIComponent(LXID) + '&sdim=' + encodeURIComponent(ZZID);
                idp.utils.openurl("026f4e56-6aaa-8508-b410-b9d30c8f2244", "作业清单", "/apps/fastdweb/views/runtime/page/card/cardpreview.html?j=true&styleid=026f4e56-6aaa-8508-b410-b9d30c8f2244&dataid=" + row[0].ID + "&status=view&" + params + '#/&funcid=');
                return true;
            }
        },
        evaluateCostVariances: function () {
            var row = idp.control.get('grid_main').getCheckedRows();
            if (row.length <= 0) {
                pppubfunctions.warn(idp.lang.get("PPPUBLIC_ADD_CHECK"));
                return false;
            }
            let notClosedWo = row.filter(r => r.WO_GB !== "1");
            if (notClosedWo.length <= 0) {
                pppubfunctions.warn(idp.lang.get('WO_314'));//"已关闭数据不能进行核算");
                return false;
            }

            idp.loading(idp.lang.get("LOADING"))
            let Ids = row.map(r => r.ID);
            idp.service.fetch("/api/pp/pp/v1.0/ppwo/wocb/evaluatecost", {woIds: Ids}).done(function (data) {
                if (data.success) {
                    pppubfunctions.success(idp.lang.get("WO_312"));//"生产成本差异分析核算完成");
                    idp.func.refresh();
                } else {
                    pppubfunctions.warn(data.msg);
                    idp.loaded();
                }
            });

        }
    }
    window.statusColorConfig = {
        gridid: "grid_main",
        dataColumn: "WO_ZID_WOZ_ZT",
        showColumn: "WO_ZID_WOZ_ZT",
    }
    function getContext() {
        if (window.parent && window.parent.$ && window.parent.$.leeDialog) {
            return window.parent;
        }
        return window;
    }
    function setNextQjmxList(controlId, lxid, dqrq) {
        let qjmx = {};
        dqrq = new Date(dqrq).format("yyyy-MM-dd");
        //调用服务端方法,根据期间类型获取当前时间的下一期间
        idp.service.fetch("/api/pd/pd/v1.0/ppmbd/ppqj/GetNextQjmx", {
            ppqjmxQjid: lxid,
            ppqjmxKsrq: dqrq
        }, false).done(function (data) {
            if (data.success) {
                if (data.data)
                    qjmx = data.data[0];
            } else {
                pppubfunctions.warn(data.msg);
            }
        });
        if (qjmx.id) {
            idp.control.get(controlId).setValue(qjmx.id, qjmx["ppqjmxMc"]);
        }
    }
    function setDefPeriod() {
        let defPeriod = pppubfunctions.localStoragegetItem("WocbDefPeriod");
        if (defPeriod) {
            idp.control.get("dropdown_540950").setValue(defPeriod.ID, defPeriod.NAME$LANGUAGE$);
            return defPeriod.ID;
        }
        return 'WEEK';
    }
    //精度配置处理
    window.accConfig = {
        grid_main: {
            valueField: 'WO_CPID',
            zslFields: ["WO_JHTCSL", "WO_YJCCSL", "WO_SJCCSL", "WO_RWRKSL", "WO_SJRKSL", "WO_LYSL", "WO_FPSL"],
            fslFields: ["WO_FJHTCSL", "WO_FYJCCSL"],
            accFields: {
                mainField: 'WO_JLDWJD',
                auxField: 'WO_FJLDWJD'
            }
        }
    }

    idp.event.bind("domReady", function (e, context) {
        idp.event.register('grid_main', "beforeGridInit", function (e, p) {

        });
        idp.event.register("grid_main", "beforeEdit", function (e, index, rowdata, col) {

        });
        idp.event.register('grid_main', "beforeApplyEditor", function (e, index, rowdata, column) {

        });
        idp.event.register('grid_main', "beforeGridFilter", function (e, filter) {
            if (filter.length > 0) {
                filter[filter.length - 1].Logic = " and ";
            }
            //根据组织过滤
            if (!idp.control.get('lookup_337787').getValue()) {
                filter.push({
                    "Left": "",
                    "Field": "WOZ.WOZ_ZZID",
                    "Operate": "=",
                    "IsExpress": false,
                    "Value": "A",
                    "Right": "",
                    "Logic": " "
                });
            }
            if (filter.length > 0) {
                filter[filter.length - 1].Logic = " and ";
            }
            filter.push({
                "Left": "",
                "Field": "WO.WO_BG",
                "Operate": "=",
                "IsExpress": false,
                "Value": "0",
                "Right": "",
                "Logic": "  "
            });
            filter = pppubfunctions.getMaxSecLevel("WOZ.WOZ_MJDJ", filter);
            filter = pppubfunctions.getWorkCenterGlobalFilter(filter, "WO.WO_GZZXID", true);
            filter = pppubfunctions.departmentFilter(filter, "WOZ.WOZ_BMID");
            return filter;
        });
        idp.event.register("col_filter", "solutionSet", function () {
            pppubfunctions.setDefaultValueForPpzzsc('lookup_337787');
            var zzid = idp.control.get('lookup_337787').getValue();
            if (zzid) {
                idp.store.commit("OrgID", zzid);
            } else {
                idp.store.commit("OrgID", "");
            }
            let qjlx = setDefPeriod();
            setNextQjmxList("lookup_748683", qjlx, new Date());
        });
        idp.event.register("grid_main", "afterShowData", function (e, data) {
            idp.loaded();
        });
    });
    idp.event.bind("viewReady", function (e, context) {
        //工作中心帮助前
        idp.event.register("lookup_884107", "beforeHelpFilter", function () {
            var zzid = idp.control.get('lookup_337787').getValue().trim();
            //根据组织进行过滤
            var filter = [];
            if (zzid) {
                filter.push({
                    "Left": "",
                    "Field": "PPSCZX_ZZID",
                    "Operate": "=",
                    "IsExpress": false,
                    "Value": zzid,
                    "Right": "",
                    "Logic": ""
                });
            }
            return filter;
        });
        //期间类型帮助后
        idp.event.register("dropdown_540950", "selected", function (e, value, name, obj) {
            pppubfunctions.localStoragesetItem("WocbDefPeriod", obj);
            //根据期间类型,获取期间明细并赋值
            setNextQjmxList("lookup_748683", value, new Date());
        });
        //阻止计划期间打开
        idp.event.register("lookup_748683", "beforeOpen", function () {
            //期间类型为空时阻止打开
            let QJID = idp.control.get("dropdown_540950").getValue();
            if (!QJID) {
                pppubfunctions.warn(idp.lang.get("WO_01"));
                return false;
            }
        });
        //计划期间帮助前
        idp.event.register("lookup_748683", "beforeHelpFilter", function () {
            //根据期间类型进行过滤
            let qjid = idp.control.get("dropdown_540950").getValue();
            var filter = [];
            filter.push({
                "Left": "",
                "Field": "ppqjmx_qjid",
                "Operate": "=",
                "IsExpress": false,
                "Value": qjid,
                "Right": "",
                "Logic": ""
            });
            return filter;
        });

    });
    idp.event.bind("mountReady", function (event, carduicontroller) {

    });
})(window.idp);
