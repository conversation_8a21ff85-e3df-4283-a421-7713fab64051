/**
 * 作业清单
 */
(function (idp) {
    window.wocbmenu = {
        openWozx: function () {
            let wodata = idp.uiview.modelController.deafaultData[0].data[0];
            var LXID = wodata.WO_LXID;//任务类型ID
            var ZZID = wodata.WO_ZZID;//组织ID
            var isworkItem = pppubfunctions.isworkItem() ? "1" : "0";
            let params ='runtime=true&j=true&fdim=' + encodeURIComponent(LXID) + '&sdim=' + encodeURIComponent(ZZID) + '&workItem=' + encodeURIComponent(isworkItem);
            //"备料清单"
            idp.utils.openurl("f15c1138-7128-ac23-781c-6c29bc746225", idp.lang.get("WO_85"), idp.utils.buildURL("/apps/fastdweb/views/runtime/page/card/cardpreview.html?j=true&styleid=f15c1138-7128-ac23-781c-6c29bc746225&dataid=" + wodata.ID + "&status=view&" + params + '#/&funcid='));
            return true;
        },
        openWozy: function () {
            let wodata = idp.uiview.modelController.deafaultData[0].data[0];
            var LXID = wodata.WO_LXID;//任务类型ID
            var ZZID = wodata.WO_ZZID;//组织ID
            let params = 'runtime=true&j=true&fdim=' + encodeURIComponent(LXID) + '&sdim=' + encodeURIComponent(ZZID);
            idp.utils.openurl("026f4e56-6aaa-8508-b410-b9d30c8f2244", "作业清单", "/apps/fastdweb/views/runtime/page/card/cardpreview.html?j=true&styleid=026f4e56-6aaa-8508-b410-b9d30c8f2244&dataid=" + wodata.ID + "&status=view&" + params + '#/&funcid=');
            return true;
        },
        evaluateCostVariances: function () {
            let wodata = idp.uiview.modelController.deafaultData[0].data[0];
            if (wodata.WO_GB === '1') {
                pppubfunctions.warn(idp.lang.get('WO_314'));//"已关闭数据不能进行核算");
                return false;
            }
            let id = wodata.ID;
            idp.loading(idp.lang.get("LOADING"))
            idp.service.fetch("/api/pp/pp/v1.0/ppwo/wocb/evaluatecost", {woIds: [id]}).done(function (data) {
                if (data.success) {
                    pppubfunctions.success(idp.lang.get("WO_312"));//"生产成本差异分析计算完成");
                    idp.uiview.loadData(id);
                } else {
                    pppubfunctions.warn(data.msg);
                    idp.loaded();
                }
            });
        },
        print: function () {
            return pppubfunctions.cardPrint("42ded585-8909-13ba-3c89-da46a5379a0d");
        },
        exportExcel: function () {
            idp.exportExcel("grid_WOCB", "", idp.lang.get("WO_313"));//"生产成本差异");
        }
    }

    function getContext() {
        if (window.parent && window.parent.$ && window.parent.$.leeDialog) {
            return window.parent;
        }
        return window;
    }


    //精度配置处理
    window.accConfig = {
        grid_WOCB: {
            valueField: 'WOCB_WLID',
            zslFields: ["WOCB_SJYL", "WOCB_BZYL", "WOCB_JHYL", 'WOCB_TDSL'],
            fslFields: [],
            accFields: {
                mainField: 'WOCB_JLDWJD', //主计量精度字段
                auxField: '', //辅计量精度字段
            }
        }
    }

    idp.event.bind("domReady", function (e, context) {
        idp.event.register('grid_WOCB', "beforeGridInit", function (e, p) {

        });
        idp.event.register("grid_WOCB", "beforeEdit", function (e, index, rowdata, col) {

        });
        idp.event.register('grid_WOCB', "beforeApplyEditor", function (e, index, rowdata, column) {

        });
    });
    idp.event.bind("viewReady", function (e, context) {
    });
    idp.event.bind("mountReady", function (event, carduicontroller) {

    });
    idp.event.bind("loadData", function (e, data) {
        //加载数据后进行数据精度得处理
        var worow = idp.uiview.modelController.deafaultData[0].data[0];
        if (worow.WO_CPID && worow.WO_CPID.trim()) {
            pppubfunctions.cardPrecisionConvWithPreCol("WO_JHTCSL", "WO_CPID_PPWL_JLDWJD");
            pppubfunctions.cardPrecisionConvWithPreCol("WO_SJRKSL", "WO_CPID_PPWL_JLDWJD");
        } else {
            pppubfunctions.cardPrecisionConvWithPreCol("WO_JHTCSL", "WO_JLDWID_ACCURACY");
            pppubfunctions.cardPrecisionConvWithPreCol("WO_SJRKSL", "WO_JLDWID_ACCURACY");
        }
        idp.loaded();
    });
    idp.event.bind("beforeLoadData", function (e, data) {
        data[1].data.forEach(row => {
            row.WOCB_CBCY = (Number(row.WOCB_SJ) - Number(row.WOCB_JH)).toFixed(2);
            row.WOCB_JHCBCY = (Number(row.WOCB_JH) - Number(row.WOCB_BZ)).toFixed(2);
            row.WOCB_SJBZCY = (Number(row.WOCB_SJ) - Number(row.WOCB_BZ)).toFixed(2);
            row.WOCB_LCCBCY = ((Number(row.WOCB_SJYL) - Number(row.WOCB_BZYL)) * Number(row.WOCB_BZDJ)).toFixed(2);
            row.WOCB_JCCBCY = (Number(row.WOCB_SJBZCY) - Number(row.WOCB_LCCBCY)).toFixed(2);
        })
    });
})(window.idp);
