(function(ke,xe){typeof exports=="object"&&typeof module<"u"?xe(exports):typeof define=="function"&&define.amd?define(["exports"],xe):(ke=typeof globalThis<"u"?globalThis:ke||self,xe(ke.RTFWidget={}))})(this,function(ke){"use strict";var Qu=Object.defineProperty;var Yu=(ke,xe,U)=>xe in ke?Qu(ke,xe,{enumerable:!0,configurable:!0,writable:!0,value:U}):ke[xe]=U;var te=(ke,xe,U)=>Yu(ke,typeof xe!="symbol"?xe+"":xe,U);/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */var fs;function xe(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const U={},Tt=[],De=()=>{},Mo=()=>!1,xn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),lr=e=>e.startsWith("onUpdate:"),we=Object.assign,cr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Do=Object.prototype.hasOwnProperty,Q=(e,t)=>Do.call(e,t),N=Array.isArray,Et=e=>wn(e)==="[object Map]",ds=e=>wn(e)==="[object Set]",j=e=>typeof e=="function",ue=e=>typeof e=="string",at=e=>typeof e=="symbol",ae=e=>e!==null&&typeof e=="object",gs=e=>(ae(e)||j(e))&&j(e.then)&&j(e.catch),ms=Object.prototype.toString,wn=e=>ms.call(e),No=e=>wn(e).slice(8,-1),bs=e=>wn(e)==="[object Object]",ar=e=>ue(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Bt=xe(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),yn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},jo=/-(\w)/g,ut=yn(e=>e.replace(jo,(t,n)=>n?n.toUpperCase():"")),Fo=/\B([A-Z])/g,mt=yn(e=>e.replace(Fo,"-$1").toLowerCase()),xs=yn(e=>e.charAt(0).toUpperCase()+e.slice(1)),ur=yn(e=>e?`on${xs(e)}`:""),bt=(e,t)=>!Object.is(e,t),fr=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ws=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},zo=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ys;const vn=()=>ys||(ys=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function hr(e){if(N(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=ue(r)?Vo(r):hr(r);if(s)for(const i in s)t[i]=s[i]}return t}else if(ue(e)||ae(e))return e}const Bo=/;(?![^(]*\))/g,Ho=/:([^]+)/,Wo=/\/\*[^]*?\*\//g;function Vo(e){const t={};return e.replace(Wo,"").split(Bo).forEach(n=>{if(n){const r=n.split(Ho);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function pr(e){let t="";if(ue(e))t=e;else if(N(e))for(let n=0;n<e.length;n++){const r=pr(e[n]);r&&(t+=r+" ")}else if(ae(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Uo=xe("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function vs(e){return!!e||e===""}const _s=e=>!!(e&&e.__v_isRef===!0),ks=e=>ue(e)?e:e==null?"":N(e)||ae(e)&&(e.toString===ms||!j(e.toString))?_s(e)?ks(e.value):JSON.stringify(e,Ss,2):String(e),Ss=(e,t)=>_s(t)?Ss(e,t.value):Et(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],i)=>(n[dr(r,i)+" =>"]=s,n),{})}:ds(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>dr(n))}:at(t)?dr(t):ae(t)&&!N(t)&&!bs(t)?String(t):t,dr=(e,t="")=>{var n;return at(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};var qo={NODE_ENV:"production"};let Te;class Go{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Te,!t&&Te&&(this.index=(Te.scopes||(Te.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Te;try{return Te=this,t()}finally{Te=n}}}on(){Te=this}off(){Te=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function Ko(){return Te}let ie;const gr=new WeakSet;class Rs{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Te&&Te.active&&Te.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,gr.has(this)&&(gr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Es(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ps(this),As(this);const t=ie,n=Ne;ie=this,Ne=!0;try{return this.fn()}finally{Cs(this),ie=t,Ne=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)wr(t);this.deps=this.depsTail=void 0,Ps(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?gr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){xr(this)&&this.run()}get dirty(){return xr(this)}}let Ts=0,Ht,Wt;function Es(e,t=!1){if(e.flags|=8,t){e.next=Wt,Wt=e;return}e.next=Ht,Ht=e}function mr(){Ts++}function br(){if(--Ts>0)return;if(Wt){let t=Wt;for(Wt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Ht;){let t=Ht;for(Ht=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function As(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Cs(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),wr(r),Zo(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function xr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Os(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Os(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Vt))return;e.globalVersion=Vt;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!xr(e)){e.flags&=-3;return}const n=ie,r=Ne;ie=e,Ne=!0;try{As(e);const s=e.fn(e._value);(t.version===0||bt(s,e._value))&&(e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ie=n,Ne=r,Cs(e),e.flags&=-3}}function wr(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)wr(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Zo(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ne=!0;const $s=[];function Ye(){$s.push(Ne),Ne=!1}function Xe(){const e=$s.pop();Ne=e===void 0?!0:e}function Ps(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ie;ie=void 0;try{t()}finally{ie=n}}}let Vt=0;class Jo{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Is{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ie||!Ne||ie===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ie)n=this.activeLink=new Jo(ie,this),ie.deps?(n.prevDep=ie.depsTail,ie.depsTail.nextDep=n,ie.depsTail=n):ie.deps=ie.depsTail=n,Ls(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ie.depsTail,n.nextDep=void 0,ie.depsTail.nextDep=n,ie.depsTail=n,ie.deps===n&&(ie.deps=r)}return n}trigger(t){this.version++,Vt++,this.notify(t)}notify(t){mr();try{qo.NODE_ENV;for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{br()}}}function Ls(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Ls(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const _n=new WeakMap,xt=Symbol(""),yr=Symbol(""),Ut=Symbol("");function me(e,t,n){if(Ne&&ie){let r=_n.get(e);r||_n.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new Is),s.map=r,s.key=n),s.track()}}function et(e,t,n,r,s,i){const o=_n.get(e);if(!o){Vt++;return}const l=c=>{c&&c.trigger()};if(mr(),t==="clear")o.forEach(l);else{const c=N(e),u=c&&ar(n);if(c&&n==="length"){const a=Number(r);o.forEach((p,d)=>{(d==="length"||d===Ut||!at(d)&&d>=a)&&l(p)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),u&&l(o.get(Ut)),t){case"add":c?u&&l(o.get("length")):(l(o.get(xt)),Et(e)&&l(o.get(yr)));break;case"delete":c||(l(o.get(xt)),Et(e)&&l(o.get(yr)));break;case"set":Et(e)&&l(o.get(xt));break}}br()}function Qo(e,t){const n=_n.get(e);return n&&n.get(t)}function At(e){const t=q(e);return t===e?t:(me(t,"iterate",Ut),Fe(e)?t:t.map(Ee))}function vr(e){return me(e=q(e),"iterate",Ut),e}const Yo={__proto__:null,[Symbol.iterator](){return _r(this,Symbol.iterator,Ee)},concat(...e){return At(this).concat(...e.map(t=>N(t)?At(t):t))},entries(){return _r(this,"entries",e=>(e[1]=Ee(e[1]),e))},every(e,t){return tt(this,"every",e,t,void 0,arguments)},filter(e,t){return tt(this,"filter",e,t,n=>n.map(Ee),arguments)},find(e,t){return tt(this,"find",e,t,Ee,arguments)},findIndex(e,t){return tt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return tt(this,"findLast",e,t,Ee,arguments)},findLastIndex(e,t){return tt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return tt(this,"forEach",e,t,void 0,arguments)},includes(...e){return kr(this,"includes",e)},indexOf(...e){return kr(this,"indexOf",e)},join(e){return At(this).join(e)},lastIndexOf(...e){return kr(this,"lastIndexOf",e)},map(e,t){return tt(this,"map",e,t,void 0,arguments)},pop(){return qt(this,"pop")},push(...e){return qt(this,"push",e)},reduce(e,...t){return Ms(this,"reduce",e,t)},reduceRight(e,...t){return Ms(this,"reduceRight",e,t)},shift(){return qt(this,"shift")},some(e,t){return tt(this,"some",e,t,void 0,arguments)},splice(...e){return qt(this,"splice",e)},toReversed(){return At(this).toReversed()},toSorted(e){return At(this).toSorted(e)},toSpliced(...e){return At(this).toSpliced(...e)},unshift(...e){return qt(this,"unshift",e)},values(){return _r(this,"values",Ee)}};function _r(e,t,n){const r=vr(e),s=r[t]();return r!==e&&!Fe(e)&&(s._next=s.next,s.next=()=>{const i=s._next();return i.value&&(i.value=n(i.value)),i}),s}const Xo=Array.prototype;function tt(e,t,n,r,s,i){const o=vr(e),l=o!==e&&!Fe(e),c=o[t];if(c!==Xo[t]){const p=c.apply(e,i);return l?Ee(p):p}let u=n;o!==e&&(l?u=function(p,d){return n.call(this,Ee(p),d,e)}:n.length>2&&(u=function(p,d){return n.call(this,p,d,e)}));const a=c.call(o,u,r);return l&&s?s(a):a}function Ms(e,t,n,r){const s=vr(e);let i=n;return s!==e&&(Fe(e)?n.length>3&&(i=function(o,l,c){return n.call(this,o,l,c,e)}):i=function(o,l,c){return n.call(this,o,Ee(l),c,e)}),s[t](i,...r)}function kr(e,t,n){const r=q(e);me(r,"iterate",Ut);const s=r[t](...n);return(s===-1||s===!1)&&Rr(n[0])?(n[0]=q(n[0]),r[t](...n)):s}function qt(e,t,n=[]){Ye(),mr();const r=q(e)[t].apply(e,n);return br(),Xe(),r}const el=xe("__proto__,__v_isRef,__isVue"),Ds=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(at));function tl(e){at(e)||(e=String(e));const t=q(this);return me(t,"has",e),t.hasOwnProperty(e)}class Ns{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return i;if(n==="__v_raw")return r===(s?i?Ws:Hs:i?Bs:zs).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const o=N(t);if(!s){let c;if(o&&(c=Yo[n]))return c;if(n==="hasOwnProperty")return tl}const l=Reflect.get(t,n,he(t)?t:r);return(at(n)?Ds.has(n):el(n))||(s||me(t,"get",n),i)?l:he(l)?o&&ar(n)?l:l.value:ae(l)?s?Vs(l):Gt(l):l}}class js extends Ns{constructor(t=!1){super(!1,t)}set(t,n,r,s){let i=t[n];if(!this._isShallow){const c=Ct(i);if(!Fe(r)&&!Ct(r)&&(i=q(i),r=q(r)),!N(t)&&he(i)&&!he(r))return c?!1:(i.value=r,!0)}const o=N(t)&&ar(n)?Number(n)<t.length:Q(t,n),l=Reflect.set(t,n,r,he(t)?t:s);return t===q(s)&&(o?bt(r,i)&&et(t,"set",n,r):et(t,"add",n,r)),l}deleteProperty(t,n){const r=Q(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&et(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!at(n)||!Ds.has(n))&&me(t,"has",n),r}ownKeys(t){return me(t,"iterate",N(t)?"length":xt),Reflect.ownKeys(t)}}class Fs extends Ns{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const nl=new js,rl=new Fs,sl=new js(!0),il=new Fs(!0),Sr=e=>e,kn=e=>Reflect.getPrototypeOf(e);function ol(e,t,n){return function(...r){const s=this.__v_raw,i=q(s),o=Et(i),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,u=s[e](...r),a=n?Sr:t?Tr:Ee;return!t&&me(i,"iterate",c?yr:xt),{next(){const{value:p,done:d}=u.next();return d?{value:p,done:d}:{value:l?[a(p[0]),a(p[1])]:a(p),done:d}},[Symbol.iterator](){return this}}}}function Sn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ll(e,t){const n={get(s){const i=this.__v_raw,o=q(i),l=q(s);e||(bt(s,l)&&me(o,"get",s),me(o,"get",l));const{has:c}=kn(o),u=t?Sr:e?Tr:Ee;if(c.call(o,s))return u(i.get(s));if(c.call(o,l))return u(i.get(l));i!==o&&i.get(s)},get size(){const s=this.__v_raw;return!e&&me(q(s),"iterate",xt),Reflect.get(s,"size",s)},has(s){const i=this.__v_raw,o=q(i),l=q(s);return e||(bt(s,l)&&me(o,"has",s),me(o,"has",l)),s===l?i.has(s):i.has(s)||i.has(l)},forEach(s,i){const o=this,l=o.__v_raw,c=q(l),u=t?Sr:e?Tr:Ee;return!e&&me(c,"iterate",xt),l.forEach((a,p)=>s.call(i,u(a),u(p),o))}};return we(n,e?{add:Sn("add"),set:Sn("set"),delete:Sn("delete"),clear:Sn("clear")}:{add(s){!t&&!Fe(s)&&!Ct(s)&&(s=q(s));const i=q(this);return kn(i).has.call(i,s)||(i.add(s),et(i,"add",s,s)),this},set(s,i){!t&&!Fe(i)&&!Ct(i)&&(i=q(i));const o=q(this),{has:l,get:c}=kn(o);let u=l.call(o,s);u||(s=q(s),u=l.call(o,s));const a=c.call(o,s);return o.set(s,i),u?bt(i,a)&&et(o,"set",s,i):et(o,"add",s,i),this},delete(s){const i=q(this),{has:o,get:l}=kn(i);let c=o.call(i,s);c||(s=q(s),c=o.call(i,s)),l&&l.call(i,s);const u=i.delete(s);return c&&et(i,"delete",s,void 0),u},clear(){const s=q(this),i=s.size!==0,o=s.clear();return i&&et(s,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=ol(s,e,t)}),n}function Rn(e,t){const n=ll(e,t);return(r,s,i)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(Q(n,s)&&s in r?n:r,s,i)}const cl={get:Rn(!1,!1)},al={get:Rn(!1,!0)},ul={get:Rn(!0,!1)},fl={get:Rn(!0,!0)},zs=new WeakMap,Bs=new WeakMap,Hs=new WeakMap,Ws=new WeakMap;function hl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function pl(e){return e.__v_skip||!Object.isExtensible(e)?0:hl(No(e))}function Gt(e){return Ct(e)?e:En(e,!1,nl,cl,zs)}function dl(e){return En(e,!1,sl,al,Bs)}function Vs(e){return En(e,!0,rl,ul,Hs)}function Tn(e){return En(e,!0,il,fl,Ws)}function En(e,t,n,r,s){if(!ae(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=s.get(e);if(i)return i;const o=pl(e);if(o===0)return e;const l=new Proxy(e,o===2?r:n);return s.set(e,l),l}function Kt(e){return Ct(e)?Kt(e.__v_raw):!!(e&&e.__v_isReactive)}function Ct(e){return!!(e&&e.__v_isReadonly)}function Fe(e){return!!(e&&e.__v_isShallow)}function Rr(e){return e?!!e.__v_raw:!1}function q(e){const t=e&&e.__v_raw;return t?q(t):e}function gl(e){return!Q(e,"__v_skip")&&Object.isExtensible(e)&&ws(e,"__v_skip",!0),e}const Ee=e=>ae(e)?Gt(e):e,Tr=e=>ae(e)?Vs(e):e;function he(e){return e?e.__v_isRef===!0:!1}function ml(e){return he(e)?e.value:e}const bl={get:(e,t,n)=>t==="__v_raw"?e:ml(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return he(s)&&!he(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function Us(e){return Kt(e)?e:new Proxy(e,bl)}function xl(e){const t=N(e)?new Array(e.length):{};for(const n in e)t[n]=yl(e,n);return t}class wl{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Qo(q(this._object),this._key)}}function yl(e,t,n){const r=e[t];return he(r)?r:new wl(e,t,n)}class vl{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Is(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Vt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ie!==this)return Es(this,!0),!0}get value(){const t=this.dep.track();return Os(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function _l(e,t,n=!1){let r,s;return j(e)?r=e:(r=e.get,s=e.set),new vl(r,s,n)}const An={},Cn=new WeakMap;let wt;function kl(e,t=!1,n=wt){if(n){let r=Cn.get(n);r||Cn.set(n,r=[]),r.push(e)}}function Sl(e,t,n=U){const{immediate:r,deep:s,once:i,scheduler:o,augmentJob:l,call:c}=n,u=C=>s?C:Fe(C)||s===!1||s===0?ft(C,1):ft(C);let a,p,d,m,_=!1,R=!1;if(he(e)?(p=()=>e.value,_=Fe(e)):Kt(e)?(p=()=>u(e),_=!0):N(e)?(R=!0,_=e.some(C=>Kt(C)||Fe(C)),p=()=>e.map(C=>{if(he(C))return C.value;if(Kt(C))return u(C);if(j(C))return c?c(C,2):C()})):j(e)?t?p=c?()=>c(e,2):e:p=()=>{if(d){Ye();try{d()}finally{Xe()}}const C=wt;wt=a;try{return c?c(e,3,[m]):e(m)}finally{wt=C}}:p=De,t&&s){const C=p,Z=s===!0?1/0:s;p=()=>ft(C(),Z)}const z=Ko(),$=()=>{a.stop(),z&&z.active&&cr(z.effects,a)};if(i&&t){const C=t;t=(...Z)=>{C(...Z),$()}}let L=R?new Array(e.length).fill(An):An;const F=C=>{if(!(!(a.flags&1)||!a.dirty&&!C))if(t){const Z=a.run();if(s||_||(R?Z.some((X,M)=>bt(X,L[M])):bt(Z,L))){d&&d();const X=wt;wt=a;try{const M=[Z,L===An?void 0:R&&L[0]===An?[]:L,m];c?c(t,3,M):t(...M),L=Z}finally{wt=X}}}else a.run()};return l&&l(F),a=new Rs(p),a.scheduler=o?()=>o(F,!1):F,m=C=>kl(C,!1,a),d=a.onStop=()=>{const C=Cn.get(a);if(C){if(c)c(C,4);else for(const Z of C)Z();Cn.delete(a)}},t?r?F(!0):L=a.run():o?o(F.bind(null,!0),!0):a.run(),$.pause=a.pause.bind(a),$.resume=a.resume.bind(a),$.stop=$,$}function ft(e,t=1/0,n){if(t<=0||!ae(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,he(e))ft(e.value,t,n);else if(N(e))for(let r=0;r<e.length;r++)ft(e[r],t,n);else if(ds(e)||Et(e))e.forEach(r=>{ft(r,t,n)});else if(bs(e)){for(const r in e)ft(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&ft(e[r],t,n)}return e}var ht={NODE_ENV:"production"};const Zt=[];let Er=!1;function Rl(e,...t){if(Er)return;Er=!0,Ye();const n=Zt.length?Zt[Zt.length-1].component:null,r=n&&n.appContext.config.warnHandler,s=Tl();if(r)Ot(r,n,11,[e+t.map(i=>{var o,l;return(l=(o=i.toString)==null?void 0:o.call(i))!=null?l:JSON.stringify(i)}).join(""),n&&n.proxy,s.map(({vnode:i})=>`at <${Ni(n,i.type)}>`).join(`
`),s]);else{const i=[`[Vue warn]: ${e}`,...t];s.length&&i.push(`
`,...El(s)),console.warn(...i)}Xe(),Er=!1}function Tl(){let e=Zt[Zt.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const r=e.component&&e.component.parent;e=r&&r.vnode}return t}function El(e){const t=[];return e.forEach((n,r)=>{t.push(...r===0?[]:[`
`],...Al(n))}),t}function Al({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",r=e.component?e.component.parent==null:!1,s=` at <${Ni(e.component,e.type,r)}`,i=">"+n;return e.props?[s,...Cl(e.props),i]:[s+i]}function Cl(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(r=>{t.push(...qs(r,e[r]))}),n.length>3&&t.push(" ..."),t}function qs(e,t,n){return ue(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:he(t)?(t=qs(e,q(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):j(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=q(t),n?t:[`${e}=`,t])}function Ot(e,t,n,r){try{return r?e(...r):e()}catch(s){On(s,t,n)}}function ze(e,t,n,r){if(j(e)){const s=Ot(e,t,n,r);return s&&gs(s)&&s.catch(i=>{On(i,t,n)}),s}if(N(e)){const s=[];for(let i=0;i<e.length;i++)s.push(ze(e[i],t,n,r));return s}}function On(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||U;if(t){let l=t.parent;const c=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let p=0;p<a.length;p++)if(a[p](e,c,u)===!1)return}l=l.parent}if(i){Ye(),Ot(i,null,10,[e,c,u]),Xe();return}}Ol(e,n,s,r,o)}function Ol(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const ye=[];let Be=-1;const $t=[];let pt=null,Pt=0;const Gs=Promise.resolve();let $n=null;function $l(e){const t=$n||Gs;return e?t.then(this?e.bind(this):e):t}function Pl(e){let t=Be+1,n=ye.length;for(;t<n;){const r=t+n>>>1,s=ye[r],i=Jt(s);i<e||i===e&&s.flags&2?t=r+1:n=r}return t}function Ar(e){if(!(e.flags&1)){const t=Jt(e),n=ye[ye.length-1];!n||!(e.flags&2)&&t>=Jt(n)?ye.push(e):ye.splice(Pl(t),0,e),e.flags|=1,Ks()}}function Ks(){$n||($n=Gs.then(Qs))}function Il(e){N(e)?$t.push(...e):pt&&e.id===-1?pt.splice(Pt+1,0,e):e.flags&1||($t.push(e),e.flags|=1),Ks()}function Zs(e,t,n=Be+1){for(;n<ye.length;n++){const r=ye[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;ye.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Js(e){if($t.length){const t=[...new Set($t)].sort((n,r)=>Jt(n)-Jt(r));if($t.length=0,pt){pt.push(...t);return}for(pt=t,Pt=0;Pt<pt.length;Pt++){const n=pt[Pt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}pt=null,Pt=0}}const Jt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Qs(e){const t=De;try{for(Be=0;Be<ye.length;Be++){const n=ye[Be];n&&!(n.flags&8)&&(ht.NODE_ENV!=="production"&&t(n),n.flags&4&&(n.flags&=-2),Ot(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;Be<ye.length;Be++){const n=ye[Be];n&&(n.flags&=-2)}Be=-1,ye.length=0,Js(),$n=null,(ye.length||$t.length)&&Qs()}}let He=null,Ys=null;function Pn(e){const t=He;return He=e,Ys=e&&e.type.__scopeId||null,t}function Ll(e,t=He,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&Ci(-1);const i=Pn(t);let o;try{o=e(...s)}finally{Pn(i),r._d&&Ci(1)}return o};return r._n=!0,r._c=!0,r._d=!0,r}function yt(e,t,n,r){const s=e.dirs,i=t&&t.dirs;for(let o=0;o<s.length;o++){const l=s[o];i&&(l.oldValue=i[o].value);let c=l.dir[r];c&&(Ye(),ze(c,n,8,[e.el,l,e,t]),Xe())}}const Ml=Symbol("_vte"),Dl=e=>e.__isTeleport;function Cr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Cr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Xs(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function In(e,t,n,r,s=!1){if(N(e)){e.forEach((_,R)=>In(_,t&&(N(t)?t[R]:t),n,r,s));return}if(Qt(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&In(e,t,n,r.component.subTree);return}const i=r.shapeFlag&4?Hr(r.component):r.el,o=s?null:i,{i:l,r:c}=e,u=t&&t.r,a=l.refs===U?l.refs={}:l.refs,p=l.setupState,d=q(p),m=p===U?()=>!1:_=>Q(d,_);if(u!=null&&u!==c&&(ue(u)?(a[u]=null,m(u)&&(p[u]=null)):he(u)&&(u.value=null)),j(c))Ot(c,l,12,[o,a]);else{const _=ue(c),R=he(c);if(_||R){const z=()=>{if(e.f){const $=_?m(c)?p[c]:a[c]:c.value;s?N($)&&cr($,i):N($)?$.includes(i)||$.push(i):_?(a[c]=[i],m(c)&&(p[c]=a[c])):(c.value=[i],e.k&&(a[e.k]=c.value))}else _?(a[c]=o,m(c)&&(p[c]=o)):R&&(c.value=o,e.k&&(a[e.k]=o))};o?(z.id=-1,Ae(z,n)):z()}}}vn().requestIdleCallback,vn().cancelIdleCallback;const Qt=e=>!!e.type.__asyncLoader,ei=e=>e.type.__isKeepAlive;function Nl(e,t){ti(e,"a",t)}function jl(e,t){ti(e,"da",t)}function ti(e,t,n=pe){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Ln(t,r,n),n){let s=n.parent;for(;s&&s.parent;)ei(s.parent.vnode)&&Fl(r,t,n,s),s=s.parent}}function Fl(e,t,n,r){const s=Ln(t,e,r,!0);ni(()=>{cr(r[t],s)},n)}function Ln(e,t,n=pe,r=!1){if(n){const s=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Ye();const l=sn(n),c=ze(t,n,e,o);return l(),Xe(),c});return r?s.unshift(i):s.push(i),i}}const nt=e=>(t,n=pe)=>{(!on||e==="sp")&&Ln(e,(...r)=>t(...r),n)},zl=nt("bm"),Bl=nt("m"),Hl=nt("bu"),Wl=nt("u"),Vl=nt("bum"),ni=nt("um"),Ul=nt("sp"),ql=nt("rtg"),Gl=nt("rtc");function Kl(e,t=pe){Ln("ec",e,t)}const Zl=Symbol.for("v-ndc"),Or=e=>e?Li(e)?Hr(e):Or(e.parent):null,Yt=we(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Or(e.parent),$root:e=>Or(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>oi(e),$forceUpdate:e=>e.f||(e.f=()=>{Ar(e.update)}),$nextTick:e=>e.n||(e.n=$l.bind(e.proxy)),$watch:e=>bc.bind(e)}),$r=(e,t)=>e!==U&&!e.__isScriptSetup&&Q(e,t),Jl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:i,accessCache:o,type:l,appContext:c}=e;let u;if(t[0]!=="$"){const m=o[t];if(m!==void 0)switch(m){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return i[t]}else{if($r(r,t))return o[t]=1,r[t];if(s!==U&&Q(s,t))return o[t]=2,s[t];if((u=e.propsOptions[0])&&Q(u,t))return o[t]=3,i[t];if(n!==U&&Q(n,t))return o[t]=4,n[t];Pr&&(o[t]=0)}}const a=Yt[t];let p,d;if(a)return t==="$attrs"&&me(e.attrs,"get",""),a(e);if((p=l.__cssModules)&&(p=p[t]))return p;if(n!==U&&Q(n,t))return o[t]=4,n[t];if(d=c.config.globalProperties,Q(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:i}=e;return $r(s,t)?(s[t]=n,!0):r!==U&&Q(r,t)?(r[t]=n,!0):Q(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:i}},o){let l;return!!n[o]||e!==U&&Q(e,o)||$r(t,o)||(l=i[0])&&Q(l,o)||Q(r,o)||Q(Yt,o)||Q(s.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Q(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function ri(e){return N(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Pr=!0;function Ql(e){const t=oi(e),n=e.proxy,r=e.ctx;Pr=!1,t.beforeCreate&&si(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:o,watch:l,provide:c,inject:u,created:a,beforeMount:p,mounted:d,beforeUpdate:m,updated:_,activated:R,deactivated:z,beforeDestroy:$,beforeUnmount:L,destroyed:F,unmounted:C,render:Z,renderTracked:X,renderTriggered:M,errorCaptured:je,serverPrefetch:Rt,expose:Le,inheritAttrs:ot,components:jt,directives:Ft,filters:mn}=t;if(u&&Yl(u,r,null),o)for(const le in o){const J=o[le];j(J)&&(r[le]=J.bind(n))}if(s){const le=s.call(n,n);ae(le)&&(e.data=Gt(le))}if(Pr=!0,i)for(const le in i){const J=i[le],lt=j(J)?J.bind(n,n):j(J.get)?J.get.bind(n,n):De,zt=!j(J)&&j(J.set)?J.set.bind(n):De,ct=Hc({get:lt,set:zt});Object.defineProperty(r,le,{enumerable:!0,configurable:!0,get:()=>ct.value,set:v=>ct.value=v})}if(l)for(const le in l)ii(l[le],r,n,le);if(c){const le=j(c)?c.call(n):c;Reflect.ownKeys(le).forEach(J=>{sc(J,le[J])})}a&&si(a,e,"c");function ge(le,J){N(J)?J.forEach(lt=>le(lt.bind(n))):J&&le(J.bind(n))}if(ge(zl,p),ge(Bl,d),ge(Hl,m),ge(Wl,_),ge(Nl,R),ge(jl,z),ge(Kl,je),ge(Gl,X),ge(ql,M),ge(Vl,L),ge(ni,C),ge(Ul,Rt),N(Le))if(Le.length){const le=e.exposed||(e.exposed={});Le.forEach(J=>{Object.defineProperty(le,J,{get:()=>n[J],set:lt=>n[J]=lt})})}else e.exposed||(e.exposed={});Z&&e.render===De&&(e.render=Z),ot!=null&&(e.inheritAttrs=ot),jt&&(e.components=jt),Ft&&(e.directives=Ft),Rt&&Xs(e)}function Yl(e,t,n=De){N(e)&&(e=Ir(e));for(const r in e){const s=e[r];let i;ae(s)?"default"in s?i=Dn(s.from||r,s.default,!0):i=Dn(s.from||r):i=Dn(s),he(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[r]=i}}function si(e,t,n){ze(N(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function ii(e,t,n,r){let s=r.includes(".")?Si(n,r):()=>n[r];if(ue(e)){const i=t[e];j(i)&&en(s,i)}else if(j(e))en(s,e.bind(n));else if(ae(e))if(N(e))e.forEach(i=>ii(i,t,n,r));else{const i=j(e.handler)?e.handler.bind(n):t[e.handler];j(i)&&en(s,i,e)}}function oi(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let c;return l?c=l:!s.length&&!n&&!r?c=t:(c={},s.length&&s.forEach(u=>Mn(c,u,o,!0)),Mn(c,t,o)),ae(t)&&i.set(t,c),c}function Mn(e,t,n,r=!1){const{mixins:s,extends:i}=t;i&&Mn(e,i,n,!0),s&&s.forEach(o=>Mn(e,o,n,!0));for(const o in t)if(!(r&&o==="expose")){const l=Xl[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const Xl={data:li,props:ci,emits:ci,methods:Xt,computed:Xt,beforeCreate:ve,created:ve,beforeMount:ve,mounted:ve,beforeUpdate:ve,updated:ve,beforeDestroy:ve,beforeUnmount:ve,destroyed:ve,unmounted:ve,activated:ve,deactivated:ve,errorCaptured:ve,serverPrefetch:ve,components:Xt,directives:Xt,watch:tc,provide:li,inject:ec};function li(e,t){return t?e?function(){return we(j(e)?e.call(this,this):e,j(t)?t.call(this,this):t)}:t:e}function ec(e,t){return Xt(Ir(e),Ir(t))}function Ir(e){if(N(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ve(e,t){return e?[...new Set([].concat(e,t))]:t}function Xt(e,t){return e?we(Object.create(null),e,t):t}function ci(e,t){return e?N(e)&&N(t)?[...new Set([...e,...t])]:we(Object.create(null),ri(e),ri(t??{})):t}function tc(e,t){if(!e)return t;if(!t)return e;const n=we(Object.create(null),e);for(const r in t)n[r]=ve(e[r],t[r]);return n}function ai(){return{app:null,config:{isNativeTag:Mo,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let nc=0;function rc(e,t){return function(r,s=null){j(r)||(r=we({},r)),s!=null&&!ae(s)&&(s=null);const i=ai(),o=new WeakSet,l=[];let c=!1;const u=i.app={_uid:nc++,_component:r,_props:s,_container:null,_context:i,_instance:null,version:Vc,get config(){return i.config},set config(a){},use(a,...p){return o.has(a)||(a&&j(a.install)?(o.add(a),a.install(u,...p)):j(a)&&(o.add(a),a(u,...p))),u},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),u},component(a,p){return p?(i.components[a]=p,u):i.components[a]},directive(a,p){return p?(i.directives[a]=p,u):i.directives[a]},mount(a,p,d){if(!c){const m=u._ceVNode||Oe(r,s);return m.appContext=i,d===!0?d="svg":d===!1&&(d=void 0),e(m,a,d),c=!0,u._container=a,a.__vue_app__=u,Hr(m.component)}},onUnmount(a){l.push(a)},unmount(){c&&(ze(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(a,p){return i.provides[a]=p,u},runWithContext(a){const p=It;It=u;try{return a()}finally{It=p}}};return u}}let It=null;function sc(e,t){if(pe){let n=pe.provides;const r=pe.parent&&pe.parent.provides;r===n&&(n=pe.provides=Object.create(r)),n[e]=t}}function Dn(e,t,n=!1){const r=pe||He;if(r||It){const s=It?It._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&j(t)?t.call(r&&r.proxy):t}}const ui={},fi=()=>Object.create(ui),hi=e=>Object.getPrototypeOf(e)===ui;function ic(e,t,n,r=!1){const s={},i=fi();e.propsDefaults=Object.create(null),pi(e,t,s,i);for(const o in e.propsOptions[0])o in s||(s[o]=void 0);n?e.props=r?s:dl(s):e.type.props?e.props=s:e.props=i,e.attrs=i}function oc(e,t,n,r){const{props:s,attrs:i,vnode:{patchFlag:o}}=e,l=q(s),[c]=e.propsOptions;let u=!1;if((r||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let p=0;p<a.length;p++){let d=a[p];if(Nn(e.emitsOptions,d))continue;const m=t[d];if(c)if(Q(i,d))m!==i[d]&&(i[d]=m,u=!0);else{const _=ut(d);s[_]=Lr(c,l,_,m,e,!1)}else m!==i[d]&&(i[d]=m,u=!0)}}}else{pi(e,t,s,i)&&(u=!0);let a;for(const p in l)(!t||!Q(t,p)&&((a=mt(p))===p||!Q(t,a)))&&(c?n&&(n[p]!==void 0||n[a]!==void 0)&&(s[p]=Lr(c,l,p,void 0,e,!0)):delete s[p]);if(i!==l)for(const p in i)(!t||!Q(t,p))&&(delete i[p],u=!0)}u&&et(e.attrs,"set","")}function pi(e,t,n,r){const[s,i]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(Bt(c))continue;const u=t[c];let a;s&&Q(s,a=ut(c))?!i||!i.includes(a)?n[a]=u:(l||(l={}))[a]=u:Nn(e.emitsOptions,c)||(!(c in r)||u!==r[c])&&(r[c]=u,o=!0)}if(i){const c=q(n),u=l||U;for(let a=0;a<i.length;a++){const p=i[a];n[p]=Lr(s,c,p,u[p],e,!Q(u,p))}}return o}function Lr(e,t,n,r,s,i){const o=e[n];if(o!=null){const l=Q(o,"default");if(l&&r===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&j(c)){const{propsDefaults:u}=s;if(n in u)r=u[n];else{const a=sn(s);r=u[n]=c.call(null,t),a()}}else r=c;s.ce&&s.ce._setProp(n,r)}o[0]&&(i&&!l?r=!1:o[1]&&(r===""||r===mt(n))&&(r=!0))}return r}const lc=new WeakMap;function di(e,t,n=!1){const r=n?lc:t.propsCache,s=r.get(e);if(s)return s;const i=e.props,o={},l=[];let c=!1;if(!j(e)){const a=p=>{c=!0;const[d,m]=di(p,t,!0);we(o,d),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!c)return ae(e)&&r.set(e,Tt),Tt;if(N(i))for(let a=0;a<i.length;a++){const p=ut(i[a]);gi(p)&&(o[p]=U)}else if(i)for(const a in i){const p=ut(a);if(gi(p)){const d=i[a],m=o[p]=N(d)||j(d)?{type:d}:we({},d),_=m.type;let R=!1,z=!0;if(N(_))for(let $=0;$<_.length;++$){const L=_[$],F=j(L)&&L.name;if(F==="Boolean"){R=!0;break}else F==="String"&&(z=!1)}else R=j(_)&&_.name==="Boolean";m[0]=R,m[1]=z,(R||Q(m,"default"))&&l.push(p)}}const u=[o,l];return ae(e)&&r.set(e,u),u}function gi(e){return e[0]!=="$"&&!Bt(e)}const mi=e=>e[0]==="_"||e==="$stable",Mr=e=>N(e)?e.map(Ve):[Ve(e)],cc=(e,t,n)=>{if(t._n)return t;const r=Ll((...s)=>(ht.NODE_ENV!=="production"&&pe&&(!n||(n.root,pe.root)),Mr(t(...s))),n);return r._c=!1,r},bi=(e,t,n)=>{const r=e._ctx;for(const s in e){if(mi(s))continue;const i=e[s];if(j(i))t[s]=cc(s,i,r);else if(i!=null){const o=Mr(i);t[s]=()=>o}}},xi=(e,t)=>{const n=Mr(t);e.slots.default=()=>n},wi=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},ac=(e,t,n)=>{const r=e.slots=fi();if(e.vnode.shapeFlag&32){const s=t._;s?(wi(r,t,n),n&&ws(r,"_",s,!0)):bi(t,r)}else t&&xi(e,t)},uc=(e,t,n)=>{const{vnode:r,slots:s}=e;let i=!0,o=U;if(r.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:wi(s,t,n):(i=!t.$stable,bi(t,s)),o=t}else t&&(xi(e,t),o={default:1});if(i)for(const l in s)!mi(l)&&o[l]==null&&delete s[l]},Ae=Sc;function fc(e){return hc(e)}function hc(e,t){const n=vn();n.__VUE__=!0;const{insert:r,remove:s,patchProp:i,createElement:o,createText:l,createComment:c,setText:u,setElementText:a,parentNode:p,nextSibling:d,setScopeId:m=De,insertStaticContent:_}=e,R=(f,h,g,w=null,b=null,x=null,T=void 0,S=null,k=!!h.dynamicChildren)=>{if(f===h)return;f&&!rn(f,h)&&(w=ee(f),v(f,b,x,!0),f=null),h.patchFlag===-2&&(k=!1,h.dynamicChildren=null);const{type:y,ref:I,shapeFlag:A}=h;switch(y){case jn:z(f,h,g,w);break;case _t:$(f,h,g,w);break;case Nr:f==null&&L(h,g,w,T);break;case We:jt(f,h,g,w,b,x,T,S,k);break;default:A&1?Z(f,h,g,w,b,x,T,S,k):A&6?Ft(f,h,g,w,b,x,T,S,k):(A&64||A&128)&&y.process(f,h,g,w,b,x,T,S,k,ce)}I!=null&&b&&In(I,f&&f.ref,x,h||f,!h)},z=(f,h,g,w)=>{if(f==null)r(h.el=l(h.children),g,w);else{const b=h.el=f.el;h.children!==f.children&&u(b,h.children)}},$=(f,h,g,w)=>{f==null?r(h.el=c(h.children||""),g,w):h.el=f.el},L=(f,h,g,w)=>{[f.el,f.anchor]=_(f.children,h,g,w,f.el,f.anchor)},F=({el:f,anchor:h},g,w)=>{let b;for(;f&&f!==h;)b=d(f),r(f,g,w),f=b;r(h,g,w)},C=({el:f,anchor:h})=>{let g;for(;f&&f!==h;)g=d(f),s(f),f=g;s(h)},Z=(f,h,g,w,b,x,T,S,k)=>{h.type==="svg"?T="svg":h.type==="math"&&(T="mathml"),f==null?X(h,g,w,b,x,T,S,k):Rt(f,h,b,x,T,S,k)},X=(f,h,g,w,b,x,T,S)=>{let k,y;const{props:I,shapeFlag:A,transition:P,dirs:D}=f;if(k=f.el=o(f.type,x,I&&I.is,I),A&8?a(k,f.children):A&16&&je(f.children,k,null,w,b,Dr(f,x),T,S),D&&yt(f,null,w,"created"),M(k,f,f.scopeId,T,w),I){for(const oe in I)oe!=="value"&&!Bt(oe)&&i(k,oe,null,I[oe],x,w);"value"in I&&i(k,"value",null,I.value,x),(y=I.onVnodeBeforeMount)&&Ue(y,w,f)}D&&yt(f,null,w,"beforeMount");const H=pc(b,P);H&&P.beforeEnter(k),r(k,h,g),((y=I&&I.onVnodeMounted)||H||D)&&Ae(()=>{y&&Ue(y,w,f),H&&P.enter(k),D&&yt(f,null,w,"mounted")},b)},M=(f,h,g,w,b)=>{if(g&&m(f,g),w)for(let x=0;x<w.length;x++)m(f,w[x]);if(b){let x=b.subTree;if(h===x||Ai(x.type)&&(x.ssContent===h||x.ssFallback===h)){const T=b.vnode;M(f,T,T.scopeId,T.slotScopeIds,b.parent)}}},je=(f,h,g,w,b,x,T,S,k=0)=>{for(let y=k;y<f.length;y++){const I=f[y]=S?dt(f[y]):Ve(f[y]);R(null,I,h,g,w,b,x,T,S)}},Rt=(f,h,g,w,b,x,T)=>{const S=h.el=f.el;let{patchFlag:k,dynamicChildren:y,dirs:I}=h;k|=f.patchFlag&16;const A=f.props||U,P=h.props||U;let D;if(g&&vt(g,!1),(D=P.onVnodeBeforeUpdate)&&Ue(D,g,h,f),I&&yt(h,f,g,"beforeUpdate"),g&&vt(g,!0),(A.innerHTML&&P.innerHTML==null||A.textContent&&P.textContent==null)&&a(S,""),y?Le(f.dynamicChildren,y,S,g,w,Dr(h,b),x):T||J(f,h,S,null,g,w,Dr(h,b),x,!1),k>0){if(k&16)ot(S,A,P,g,b);else if(k&2&&A.class!==P.class&&i(S,"class",null,P.class,b),k&4&&i(S,"style",A.style,P.style,b),k&8){const H=h.dynamicProps;for(let oe=0;oe<H.length;oe++){const re=H[oe],Pe=A[re],Re=P[re];(Re!==Pe||re==="value")&&i(S,re,Pe,Re,b,g)}}k&1&&f.children!==h.children&&a(S,h.children)}else!T&&y==null&&ot(S,A,P,g,b);((D=P.onVnodeUpdated)||I)&&Ae(()=>{D&&Ue(D,g,h,f),I&&yt(h,f,g,"updated")},w)},Le=(f,h,g,w,b,x,T)=>{for(let S=0;S<h.length;S++){const k=f[S],y=h[S],I=k.el&&(k.type===We||!rn(k,y)||k.shapeFlag&70)?p(k.el):g;R(k,y,I,null,w,b,x,T,!0)}},ot=(f,h,g,w,b)=>{if(h!==g){if(h!==U)for(const x in h)!Bt(x)&&!(x in g)&&i(f,x,h[x],null,b,w);for(const x in g){if(Bt(x))continue;const T=g[x],S=h[x];T!==S&&x!=="value"&&i(f,x,S,T,b,w)}"value"in g&&i(f,"value",h.value,g.value,b)}},jt=(f,h,g,w,b,x,T,S,k)=>{const y=h.el=f?f.el:l(""),I=h.anchor=f?f.anchor:l("");let{patchFlag:A,dynamicChildren:P,slotScopeIds:D}=h;D&&(S=S?S.concat(D):D),f==null?(r(y,g,w),r(I,g,w),je(h.children||[],g,I,b,x,T,S,k)):A>0&&A&64&&P&&f.dynamicChildren?(Le(f.dynamicChildren,P,g,b,x,T,S),(h.key!=null||b&&h===b.subTree)&&yi(f,h,!0)):J(f,h,g,I,b,x,T,S,k)},Ft=(f,h,g,w,b,x,T,S,k)=>{h.slotScopeIds=S,f==null?h.shapeFlag&512?b.ctx.activate(h,g,w,T,k):mn(h,g,w,b,x,T,k):or(f,h,k)},mn=(f,h,g,w,b,x,T)=>{const S=f.component=Ic(f,w,b);if(ei(f)&&(S.ctx.renderer=ce),Lc(S,!1,T),S.asyncDep){if(b&&b.registerDep(S,ge,T),!f.el){const k=S.subTree=Oe(_t);$(null,k,h,g)}}else ge(S,f,h,g,b,x,T)},or=(f,h,g)=>{const w=h.component=f.component;if(_c(f,h,g))if(w.asyncDep&&!w.asyncResolved){le(w,h,g);return}else w.next=h,w.update();else h.el=f.el,w.vnode=h},ge=(f,h,g,w,b,x,T)=>{const S=()=>{if(f.isMounted){let{next:A,bu:P,u:D,parent:H,vnode:oe}=f;{const Je=vi(f);if(Je){A&&(A.el=oe.el,le(f,A,T)),Je.asyncDep.then(()=>{f.isUnmounted||S()});return}}let re=A,Pe;vt(f,!1),A?(A.el=oe.el,le(f,A,T)):A=oe,P&&fr(P),(Pe=A.props&&A.props.onVnodeBeforeUpdate)&&Ue(Pe,H,A,oe),vt(f,!0);const Re=Ti(f),Ze=f.subTree;f.subTree=Re,R(Ze,Re,p(Ze.el),ee(Ze),f,b,x),A.el=Re.el,re===null&&kc(f,Re.el),D&&Ae(D,b),(Pe=A.props&&A.props.onVnodeUpdated)&&Ae(()=>Ue(Pe,H,A,oe),b)}else{let A;const{el:P,props:D}=h,{bm:H,m:oe,parent:re,root:Pe,type:Re}=f,Ze=Qt(h);vt(f,!1),H&&fr(H),!Ze&&(A=D&&D.onVnodeBeforeMount)&&Ue(A,re,h),vt(f,!0);{Pe.ce&&Pe.ce._injectChildStyle(Re);const Je=f.subTree=Ti(f);R(null,Je,g,w,f,b,x),h.el=Je.el}if(oe&&Ae(oe,b),!Ze&&(A=D&&D.onVnodeMounted)){const Je=h;Ae(()=>Ue(A,re,Je),b)}(h.shapeFlag&256||re&&Qt(re.vnode)&&re.vnode.shapeFlag&256)&&f.a&&Ae(f.a,b),f.isMounted=!0,h=g=w=null}};f.scope.on();const k=f.effect=new Rs(S);f.scope.off();const y=f.update=k.run.bind(k),I=f.job=k.runIfDirty.bind(k);I.i=f,I.id=f.uid,k.scheduler=()=>Ar(I),vt(f,!0),y()},le=(f,h,g)=>{h.component=f;const w=f.vnode.props;f.vnode=h,f.next=null,oc(f,h.props,w,g),uc(f,h.children,g),Ye(),Zs(f),Xe()},J=(f,h,g,w,b,x,T,S,k=!1)=>{const y=f&&f.children,I=f?f.shapeFlag:0,A=h.children,{patchFlag:P,shapeFlag:D}=h;if(P>0){if(P&128){zt(y,A,g,w,b,x,T,S,k);return}else if(P&256){lt(y,A,g,w,b,x,T,S,k);return}}D&8?(I&16&&B(y,b,x),A!==y&&a(g,A)):I&16?D&16?zt(y,A,g,w,b,x,T,S,k):B(y,b,x,!0):(I&8&&a(g,""),D&16&&je(A,g,w,b,x,T,S,k))},lt=(f,h,g,w,b,x,T,S,k)=>{f=f||Tt,h=h||Tt;const y=f.length,I=h.length,A=Math.min(y,I);let P;for(P=0;P<A;P++){const D=h[P]=k?dt(h[P]):Ve(h[P]);R(f[P],D,g,null,b,x,T,S,k)}y>I?B(f,b,x,!0,!1,A):je(h,g,w,b,x,T,S,k,A)},zt=(f,h,g,w,b,x,T,S,k)=>{let y=0;const I=h.length;let A=f.length-1,P=I-1;for(;y<=A&&y<=P;){const D=f[y],H=h[y]=k?dt(h[y]):Ve(h[y]);if(rn(D,H))R(D,H,g,null,b,x,T,S,k);else break;y++}for(;y<=A&&y<=P;){const D=f[A],H=h[P]=k?dt(h[P]):Ve(h[P]);if(rn(D,H))R(D,H,g,null,b,x,T,S,k);else break;A--,P--}if(y>A){if(y<=P){const D=P+1,H=D<I?h[D].el:w;for(;y<=P;)R(null,h[y]=k?dt(h[y]):Ve(h[y]),g,H,b,x,T,S,k),y++}}else if(y>P)for(;y<=A;)v(f[y],b,x,!0),y++;else{const D=y,H=y,oe=new Map;for(y=H;y<=P;y++){const Ie=h[y]=k?dt(h[y]):Ve(h[y]);Ie.key!=null&&oe.set(Ie.key,y)}let re,Pe=0;const Re=P-H+1;let Ze=!1,Je=0;const bn=new Array(Re);for(y=0;y<Re;y++)bn[y]=0;for(y=D;y<=A;y++){const Ie=f[y];if(Pe>=Re){v(Ie,b,x,!0);continue}let Qe;if(Ie.key!=null)Qe=oe.get(Ie.key);else for(re=H;re<=P;re++)if(bn[re-H]===0&&rn(Ie,h[re])){Qe=re;break}Qe===void 0?v(Ie,b,x,!0):(bn[Qe-H]=y+1,Qe>=Je?Je=Qe:Ze=!0,R(Ie,h[Qe],g,null,b,x,T,S,k),Pe++)}const Io=Ze?dc(bn):Tt;for(re=Io.length-1,y=Re-1;y>=0;y--){const Ie=H+y,Qe=h[Ie],Lo=Ie+1<I?h[Ie+1].el:w;bn[y]===0?R(null,Qe,g,Lo,b,x,T,S,k):Ze&&(re<0||y!==Io[re]?ct(Qe,g,Lo,2):re--)}}},ct=(f,h,g,w,b=null)=>{const{el:x,type:T,transition:S,children:k,shapeFlag:y}=f;if(y&6){ct(f.component.subTree,h,g,w);return}if(y&128){f.suspense.move(h,g,w);return}if(y&64){T.move(f,h,g,ce);return}if(T===We){r(x,h,g);for(let A=0;A<k.length;A++)ct(k[A],h,g,w);r(f.anchor,h,g);return}if(T===Nr){F(f,h,g);return}if(w!==2&&y&1&&S)if(w===0)S.beforeEnter(x),r(x,h,g),Ae(()=>S.enter(x),b);else{const{leave:A,delayLeave:P,afterLeave:D}=S,H=()=>r(x,h,g),oe=()=>{A(x,()=>{H(),D&&D()})};P?P(x,H,oe):oe()}else r(x,h,g)},v=(f,h,g,w=!1,b=!1)=>{const{type:x,props:T,ref:S,children:k,dynamicChildren:y,shapeFlag:I,patchFlag:A,dirs:P,cacheIndex:D}=f;if(A===-2&&(b=!1),S!=null&&In(S,null,g,f,!0),D!=null&&(h.renderCache[D]=void 0),I&256){h.ctx.deactivate(f);return}const H=I&1&&P,oe=!Qt(f);let re;if(oe&&(re=T&&T.onVnodeBeforeUnmount)&&Ue(re,h,f),I&6)W(f.component,g,w);else{if(I&128){f.suspense.unmount(g,w);return}H&&yt(f,null,h,"beforeUnmount"),I&64?f.type.remove(f,h,g,ce,w):y&&!y.hasOnce&&(x!==We||A>0&&A&64)?B(y,h,g,!1,!0):(x===We&&A&384||!b&&I&16)&&B(k,h,g),w&&E(f)}(oe&&(re=T&&T.onVnodeUnmounted)||H)&&Ae(()=>{re&&Ue(re,h,f),H&&yt(f,null,h,"unmounted")},g)},E=f=>{const{type:h,el:g,anchor:w,transition:b}=f;if(h===We){O(g,w);return}if(h===Nr){C(f);return}const x=()=>{s(g),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(f.shapeFlag&1&&b&&!b.persisted){const{leave:T,delayLeave:S}=b,k=()=>T(g,x);S?S(f.el,x,k):k()}else x()},O=(f,h)=>{let g;for(;f!==h;)g=d(f),s(f),f=g;s(h)},W=(f,h,g)=>{const{bum:w,scope:b,job:x,subTree:T,um:S,m:k,a:y}=f;_i(k),_i(y),w&&fr(w),b.stop(),x&&(x.flags|=8,v(T,f,h,g)),S&&Ae(S,h),Ae(()=>{f.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},B=(f,h,g,w=!1,b=!1,x=0)=>{for(let T=x;T<f.length;T++)v(f[T],h,g,w,b)},ee=f=>{if(f.shapeFlag&6)return ee(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const h=d(f.anchor||f.el),g=h&&h[Ml];return g?d(g):h};let se=!1;const V=(f,h,g)=>{f==null?h._vnode&&v(h._vnode,null,null,!0):R(h._vnode||null,f,h,null,null,null,g),h._vnode=f,se||(se=!0,Zs(),Js(),se=!1)},ce={p:R,um:v,m:ct,r:E,mt:mn,mc:je,pc:J,pbc:Le,n:ee,o:e};return{render:V,hydrate:void 0,createApp:rc(V)}}function Dr({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function vt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function pc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function yi(e,t,n=!1){const r=e.children,s=t.children;if(N(r)&&N(s))for(let i=0;i<r.length;i++){const o=r[i];let l=s[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[i]=dt(s[i]),l.el=o.el),!n&&l.patchFlag!==-2&&yi(o,l)),l.type===jn&&(l.el=o.el)}}function dc(e){const t=e.slice(),n=[0];let r,s,i,o,l;const c=e.length;for(r=0;r<c;r++){const u=e[r];if(u!==0){if(s=n[n.length-1],e[s]<u){t[r]=s,n.push(r);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<u?i=l+1:o=l;u<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function vi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:vi(t)}function _i(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const gc=Symbol.for("v-scx"),mc=()=>Dn(gc);function en(e,t,n){return ki(e,t,n)}function ki(e,t,n=U){const{immediate:r,deep:s,flush:i,once:o}=n,l=we({},n),c=t&&r||!t&&i!=="post";let u;if(on){if(i==="sync"){const m=mc();u=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=De,m.resume=De,m.pause=De,m}}const a=pe;l.call=(m,_,R)=>ze(m,a,_,R);let p=!1;i==="post"?l.scheduler=m=>{Ae(m,a&&a.suspense)}:i!=="sync"&&(p=!0,l.scheduler=(m,_)=>{_?m():Ar(m)}),l.augmentJob=m=>{t&&(m.flags|=4),p&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const d=Sl(e,t,l);return on&&(u?u.push(d):c&&d()),d}function bc(e,t,n){const r=this.proxy,s=ue(e)?e.includes(".")?Si(r,e):()=>r[e]:e.bind(r,r);let i;j(t)?i=t:(i=t.handler,n=t);const o=sn(this),l=ki(s,i.bind(r),n);return o(),l}function Si(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const xc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ut(t)}Modifiers`]||e[`${mt(t)}Modifiers`];function wc(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||U;let s=n;const i=t.startsWith("update:"),o=i&&xc(r,t.slice(7));o&&(o.trim&&(s=n.map(a=>ue(a)?a.trim():a)),o.number&&(s=n.map(zo)));let l,c=r[l=ur(t)]||r[l=ur(ut(t))];!c&&i&&(c=r[l=ur(mt(t))]),c&&ze(c,e,6,s);const u=r[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,ze(u,e,6,s)}}function Ri(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const i=e.emits;let o={},l=!1;if(!j(e)){const c=u=>{const a=Ri(u,t,!0);a&&(l=!0,we(o,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(ae(e)&&r.set(e,null),null):(N(i)?i.forEach(c=>o[c]=null):we(o,i),ae(e)&&r.set(e,o),o)}function Nn(e,t){return!e||!xn(t)?!1:(t=t.slice(2).replace(/Once$/,""),Q(e,t[0].toLowerCase()+t.slice(1))||Q(e,mt(t))||Q(e,t))}function ef(){}function Ti(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[i],slots:o,attrs:l,emit:c,render:u,renderCache:a,props:p,data:d,setupState:m,ctx:_,inheritAttrs:R}=e,z=Pn(e);let $,L;try{if(n.shapeFlag&4){const C=s||r,Z=ht.NODE_ENV!=="production"&&m.__isScriptSetup?new Proxy(C,{get(X,M,je){return Rl(`Property '${String(M)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(X,M,je)}}):C;$=Ve(u.call(Z,C,a,ht.NODE_ENV!=="production"?Tn(p):p,m,d,_)),L=l}else{const C=t;ht.NODE_ENV,$=Ve(C.length>1?C(ht.NODE_ENV!=="production"?Tn(p):p,ht.NODE_ENV!=="production"?{get attrs(){return Tn(l)},slots:o,emit:c}:{attrs:l,slots:o,emit:c}):C(ht.NODE_ENV!=="production"?Tn(p):p,null)),L=t.props?l:yc(l)}}catch(C){tn.length=0,On(C,e,1),$=Oe(_t)}let F=$;if(L&&R!==!1){const C=Object.keys(L),{shapeFlag:Z}=F;C.length&&Z&7&&(i&&C.some(lr)&&(L=vc(L,i)),F=Lt(F,L,!1,!0))}return n.dirs&&(F=Lt(F,null,!1,!0),F.dirs=F.dirs?F.dirs.concat(n.dirs):n.dirs),n.transition&&Cr(F,n.transition),$=F,Pn(z),$}const yc=e=>{let t;for(const n in e)(n==="class"||n==="style"||xn(n))&&((t||(t={}))[n]=e[n]);return t},vc=(e,t)=>{const n={};for(const r in e)(!lr(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function _c(e,t,n){const{props:r,children:s,component:i}=e,{props:o,children:l,patchFlag:c}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return r?Ei(r,o,u):!!o;if(c&8){const a=t.dynamicProps;for(let p=0;p<a.length;p++){const d=a[p];if(o[d]!==r[d]&&!Nn(u,d))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===o?!1:r?o?Ei(r,o,u):!0:!!o;return!1}function Ei(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const i=r[s];if(t[i]!==e[i]&&!Nn(n,i))return!0}return!1}function kc({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ai=e=>e.__isSuspense;function Sc(e,t){t&&t.pendingBranch?N(e)?t.effects.push(...e):t.effects.push(e):Il(e)}const We=Symbol.for("v-fgt"),jn=Symbol.for("v-txt"),_t=Symbol.for("v-cmt"),Nr=Symbol.for("v-stc"),tn=[];let Ce=null;function Fn(e=!1){tn.push(Ce=e?null:[])}function Rc(){tn.pop(),Ce=tn[tn.length-1]||null}let nn=1;function Ci(e,t=!1){nn+=e,e<0&&Ce&&t&&(Ce.hasOnce=!0)}function Oi(e){return e.dynamicChildren=nn>0?Ce||Tt:null,Rc(),nn>0&&Ce&&Ce.push(e),e}function jr(e,t,n,r,s,i){return Oi(Fr(e,t,n,r,s,i,!0))}function Tc(e,t,n,r,s){return Oi(Oe(e,t,n,r,s,!0))}function zn(e){return e?e.__v_isVNode===!0:!1}function rn(e,t){return e.type===t.type&&e.key===t.key}const $i=({key:e})=>e??null,Bn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ue(e)||he(e)||j(e)?{i:He,r:e,k:t,f:!!n}:e:null);function Fr(e,t=null,n=null,r=0,s=null,i=e===We?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&$i(t),ref:t&&Bn(t),scopeId:Ys,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:He};return l?(zr(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=ue(n)?8:16),nn>0&&!o&&Ce&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Ce.push(c),c}const Oe=Ec;function Ec(e,t=null,n=null,r=0,s=null,i=!1){if((!e||e===Zl)&&(e=_t),zn(e)){const l=Lt(e,t,!0);return n&&zr(l,n),nn>0&&!i&&Ce&&(l.shapeFlag&6?Ce[Ce.indexOf(e)]=l:Ce.push(l)),l.patchFlag=-2,l}if(Bc(e)&&(e=e.__vccOpts),t){t=Ac(t);let{class:l,style:c}=t;l&&!ue(l)&&(t.class=pr(l)),ae(c)&&(Rr(c)&&!N(c)&&(c=we({},c)),t.style=hr(c))}const o=ue(e)?1:Ai(e)?128:Dl(e)?64:ae(e)?4:j(e)?2:0;return Fr(e,t,n,r,s,o,i,!0)}function Ac(e){return e?Rr(e)||hi(e)?we({},e):e:null}function Lt(e,t,n=!1,r=!1){const{props:s,ref:i,patchFlag:o,children:l,transition:c}=e,u=t?Oc(s||{},t):s,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&$i(u),ref:t&&t.ref?n&&i?N(i)?i.concat(Bn(t)):[i,Bn(t)]:Bn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==We?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Lt(e.ssContent),ssFallback:e.ssFallback&&Lt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&Cr(a,c.clone(a)),a}function Cc(e=" ",t=0){return Oe(jn,null,e,t)}function Pi(e="",t=!1){return t?(Fn(),Tc(_t,null,e)):Oe(_t,null,e)}function Ve(e){return e==null||typeof e=="boolean"?Oe(_t):N(e)?Oe(We,null,e.slice()):zn(e)?dt(e):Oe(jn,null,String(e))}function dt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Lt(e)}function zr(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(N(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),zr(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!hi(t)?t._ctx=He:s===3&&He&&(He.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else j(t)?(t={default:t,_ctx:He},n=32):(t=String(t),r&64?(n=16,t=[Cc(t)]):n=8);e.children=t,e.shapeFlag|=n}function Oc(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=pr([t.class,r.class]));else if(s==="style")t.style=hr([t.style,r.style]);else if(xn(s)){const i=t[s],o=r[s];o&&i!==o&&!(N(i)&&i.includes(o))&&(t[s]=i?[].concat(i,o):o)}else s!==""&&(t[s]=r[s])}return t}function Ue(e,t,n,r=null){ze(e,t,7,[n,r])}const $c=ai();let Pc=0;function Ic(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||$c,i={uid:Pc++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Go(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:di(r,s),emitsOptions:Ri(r,s),emit:null,emitted:null,propsDefaults:U,inheritAttrs:r.inheritAttrs,ctx:U,data:U,props:U,attrs:U,slots:U,refs:U,setupState:U,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=wc.bind(null,i),e.ce&&e.ce(i),i}let pe=null,Hn,Br;{const e=vn(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),i=>{s.length>1?s.forEach(o=>o(i)):s[0](i)}};Hn=t("__VUE_INSTANCE_SETTERS__",n=>pe=n),Br=t("__VUE_SSR_SETTERS__",n=>on=n)}const sn=e=>{const t=pe;return Hn(e),e.scope.on(),()=>{e.scope.off(),Hn(t)}},Ii=()=>{pe&&pe.scope.off(),Hn(null)};function Li(e){return e.vnode.shapeFlag&4}let on=!1;function Lc(e,t=!1,n=!1){t&&Br(t);const{props:r,children:s}=e.vnode,i=Li(e);ic(e,r,i,t),ac(e,s,n);const o=i?Mc(e,t):void 0;return t&&Br(!1),o}function Mc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Jl);const{setup:r}=n;if(r){Ye();const s=e.setupContext=r.length>1?Nc(e):null,i=sn(e),o=Ot(r,e,0,[e.props,s]),l=gs(o);if(Xe(),i(),(l||e.sp)&&!Qt(e)&&Xs(e),l){if(o.then(Ii,Ii),t)return o.then(c=>{Mi(e,c)}).catch(c=>{On(c,e,0)});e.asyncDep=o}else Mi(e,o)}else Di(e)}function Mi(e,t,n){j(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ae(t)&&(e.setupState=Us(t)),Di(e)}function Di(e,t,n){const r=e.type;e.render||(e.render=r.render||De);{const s=sn(e);Ye();try{Ql(e)}finally{Xe(),s()}}}const Dc={get(e,t){return me(e,"get",""),e[t]}};function Nc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Dc),slots:e.slots,emit:e.emit,expose:t}}function Hr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Us(gl(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Yt)return Yt[n](e)},has(t,n){return n in t||n in Yt}})):e.proxy}const jc=/(?:^|[-_])(\w)/g,Fc=e=>e.replace(jc,t=>t.toUpperCase()).replace(/[-_]/g,"");function zc(e,t=!0){return j(e)?e.displayName||e.name:e.name||t&&e.__name}function Ni(e,t,n=!1){let r=zc(t);if(!r&&t.__file){const s=t.__file.match(/([^/\\]+)\.\w+$/);s&&(r=s[1])}if(!r&&e&&e.parent){const s=i=>{for(const o in i)if(i[o]===t)return o};r=s(e.components||e.parent.type.components)||s(e.appContext.components)}return r?Fc(r):n?"App":"Anonymous"}function Bc(e){return j(e)&&"__vccOpts"in e}const Hc=(e,t)=>_l(e,t,on);function Wc(e,t,n){const r=arguments.length;return r===2?ae(t)&&!N(t)?zn(t)?Oe(e,null,[t]):Oe(e,t):Oe(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&zn(n)&&(n=[n]),Oe(e,t,n))}const Vc="3.5.13";let Wr;const ji=typeof window<"u"&&window.trustedTypes;if(ji)try{Wr=ji.createPolicy("vue",{createHTML:e=>e})}catch{}const Fi=Wr?e=>Wr.createHTML(e):e=>e,Uc="http://www.w3.org/2000/svg",qc="http://www.w3.org/1998/Math/MathML",rt=typeof document<"u"?document:null,zi=rt&&rt.createElement("template"),Gc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?rt.createElementNS(Uc,e):t==="mathml"?rt.createElementNS(qc,e):n?rt.createElement(e,{is:n}):rt.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>rt.createTextNode(e),createComment:e=>rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,i){const o=n?n.previousSibling:t.lastChild;if(s&&(s===i||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===i||!(s=s.nextSibling)););else{zi.innerHTML=Fi(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=zi.content;if(r==="svg"||r==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Kc=Symbol("_vtc");function Zc(e,t,n){const r=e[Kc];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Bi=Symbol("_vod"),Jc=Symbol("_vsh"),Qc=Symbol(""),Yc=/(^|;)\s*display\s*:/;function Xc(e,t,n){const r=e.style,s=ue(n);let i=!1;if(n&&!s){if(t)if(ue(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&Wn(r,l,"")}else for(const o in t)n[o]==null&&Wn(r,o,"");for(const o in n)o==="display"&&(i=!0),Wn(r,o,n[o])}else if(s){if(t!==n){const o=r[Qc];o&&(n+=";"+o),r.cssText=n,i=Yc.test(n)}}else t&&e.removeAttribute("style");Bi in e&&(e[Bi]=i?r.display:"",e[Jc]&&(r.display="none"))}const Hi=/\s*!important$/;function Wn(e,t,n){if(N(n))n.forEach(r=>Wn(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=ea(e,t);Hi.test(n)?e.setProperty(mt(r),n.replace(Hi,""),"important"):e[r]=n}}const Wi=["Webkit","Moz","ms"],Vr={};function ea(e,t){const n=Vr[t];if(n)return n;let r=ut(t);if(r!=="filter"&&r in e)return Vr[t]=r;r=xs(r);for(let s=0;s<Wi.length;s++){const i=Wi[s]+r;if(i in e)return Vr[t]=i}return t}const Vi="http://www.w3.org/1999/xlink";function Ui(e,t,n,r,s,i=Uo(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Vi,t.slice(6,t.length)):e.setAttributeNS(Vi,t,n):n==null||i&&!vs(n)?e.removeAttribute(t):e.setAttribute(t,i?"":at(n)?String(n):n)}function qi(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Fi(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=vs(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(s||t)}function ta(e,t,n,r){e.addEventListener(t,n,r)}function na(e,t,n,r){e.removeEventListener(t,n,r)}const Gi=Symbol("_vei");function ra(e,t,n,r,s=null){const i=e[Gi]||(e[Gi]={}),o=i[t];if(r&&o)o.value=r;else{const[l,c]=sa(t);if(r){const u=i[t]=la(r,s);ta(e,l,u,c)}else o&&(na(e,l,o,c),i[t]=void 0)}}const Ki=/(?:Once|Passive|Capture)$/;function sa(e){let t;if(Ki.test(e)){t={};let r;for(;r=e.match(Ki);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):mt(e.slice(2)),t]}let Ur=0;const ia=Promise.resolve(),oa=()=>Ur||(ia.then(()=>Ur=0),Ur=Date.now());function la(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;ze(ca(r,n.value),t,5,[r])};return n.value=e,n.attached=oa(),n}function ca(e,t){if(N(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const Zi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,aa=(e,t,n,r,s,i)=>{const o=s==="svg";t==="class"?Zc(e,r,o):t==="style"?Xc(e,n,r):xn(t)?lr(t)||ra(e,t,n,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ua(e,t,r,o))?(qi(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ui(e,t,r,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ue(r))?qi(e,ut(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Ui(e,t,r,o))};function ua(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Zi(t)&&j(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Zi(t)&&ue(n)?!1:t in e}const fa=we({patchProp:aa},Gc);let Ji;function ha(){return Ji||(Ji=fc(fa))}const pa=(...e)=>{const t=ha().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=ga(r);if(!s)return;const i=t._component;!j(i)&&!i.render&&!i.template&&(i.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const o=n(s,!1,da(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),o},t};function da(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ga(e){return ue(e)?document.querySelector(e):e}var qr={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var Qi;function ma(){return Qi||(Qi=1,function(e){(function(){var t={}.hasOwnProperty;function n(){for(var i="",o=0;o<arguments.length;o++){var l=arguments[o];l&&(i=s(i,r(l)))}return i}function r(i){if(typeof i=="string"||typeof i=="number")return i;if(typeof i!="object")return"";if(Array.isArray(i))return n.apply(null,i);if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]"))return i.toString();var o="";for(var l in i)t.call(i,l)&&i[l]&&(o=s(o,l));return o}function s(i,o){return o?i?i+" "+o:i+o:i}e.exports?(n.default=n,e.exports=n):window.classNames=n})()}(qr)),qr.exports}ma();function ba(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}function xa(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),e.nonce!==void 0&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}var wa=function(){function e(n){var r=this;this._insertTag=function(s){var i;r.tags.length===0?r.insertionPoint?i=r.insertionPoint.nextSibling:r.prepend?i=r.container.firstChild:i=r.before:i=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(s,i),r.tags.push(s)},this.isSpeedy=n.speedy===void 0?!0:n.speedy,this.tags=[],this.ctr=0,this.nonce=n.nonce,this.key=n.key,this.container=n.container,this.prepend=n.prepend,this.insertionPoint=n.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(r){r.forEach(this._insertTag)},t.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(xa(this));var s=this.tags[this.tags.length-1];if(this.isSpeedy){var i=ba(s);try{i.insertRule(r,i.cssRules.length)}catch{}}else s.appendChild(document.createTextNode(r));this.ctr++},t.flush=function(){this.tags.forEach(function(r){var s;return(s=r.parentNode)==null?void 0:s.removeChild(r)}),this.tags=[],this.ctr=0},e}(),be="-ms-",Vn="-moz-",G="-webkit-",Yi="comm",Gr="rule",Kr="decl",ya="@import",Xi="@keyframes",va="@layer",_a=Math.abs,Un=String.fromCharCode,ka=Object.assign;function Sa(e,t){return de(e,0)^45?(((t<<2^de(e,0))<<2^de(e,1))<<2^de(e,2))<<2^de(e,3):0}function eo(e){return e.trim()}function Ra(e,t){return(e=t.exec(e))?e[0]:e}function K(e,t,n){return e.replace(t,n)}function Zr(e,t){return e.indexOf(t)}function de(e,t){return e.charCodeAt(t)|0}function ln(e,t,n){return e.slice(t,n)}function qe(e){return e.length}function Jr(e){return e.length}function qn(e,t){return t.push(e),e}function Ta(e,t){return e.map(t).join("")}var Gn=1,Mt=1,to=0,Se=0,fe=0,Dt="";function Kn(e,t,n,r,s,i,o){return{value:e,root:t,parent:n,type:r,props:s,children:i,line:Gn,column:Mt,length:o,return:""}}function cn(e,t){return ka(Kn("",null,null,"",null,null,0),e,{length:-e.length},t)}function Ea(){return fe}function Aa(){return fe=Se>0?de(Dt,--Se):0,Mt--,fe===10&&(Mt=1,Gn--),fe}function $e(){return fe=Se<to?de(Dt,Se++):0,Mt++,fe===10&&(Mt=1,Gn++),fe}function Ge(){return de(Dt,Se)}function Zn(){return Se}function an(e,t){return ln(Dt,e,t)}function un(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function no(e){return Gn=Mt=1,to=qe(Dt=e),Se=0,[]}function ro(e){return Dt="",e}function Jn(e){return eo(an(Se-1,Qr(e===91?e+2:e===40?e+1:e)))}function Ca(e){for(;(fe=Ge())&&fe<33;)$e();return un(e)>2||un(fe)>3?"":" "}function Oa(e,t){for(;--t&&$e()&&!(fe<48||fe>102||fe>57&&fe<65||fe>70&&fe<97););return an(e,Zn()+(t<6&&Ge()==32&&$e()==32))}function Qr(e){for(;$e();)switch(fe){case e:return Se;case 34:case 39:e!==34&&e!==39&&Qr(fe);break;case 40:e===41&&Qr(e);break;case 92:$e();break}return Se}function $a(e,t){for(;$e()&&e+fe!==57;)if(e+fe===84&&Ge()===47)break;return"/*"+an(t,Se-1)+"*"+Un(e===47?e:$e())}function Pa(e){for(;!un(Ge());)$e();return an(e,Se)}function Ia(e){return ro(Qn("",null,null,null,[""],e=no(e),0,[0],e))}function Qn(e,t,n,r,s,i,o,l,c){for(var u=0,a=0,p=o,d=0,m=0,_=0,R=1,z=1,$=1,L=0,F="",C=s,Z=i,X=r,M=F;z;)switch(_=L,L=$e()){case 40:if(_!=108&&de(M,p-1)==58){Zr(M+=K(Jn(L),"&","&\f"),"&\f")!=-1&&($=-1);break}case 34:case 39:case 91:M+=Jn(L);break;case 9:case 10:case 13:case 32:M+=Ca(_);break;case 92:M+=Oa(Zn()-1,7);continue;case 47:switch(Ge()){case 42:case 47:qn(La($a($e(),Zn()),t,n),c);break;default:M+="/"}break;case 123*R:l[u++]=qe(M)*$;case 125*R:case 59:case 0:switch(L){case 0:case 125:z=0;case 59+a:$==-1&&(M=K(M,/\f/g,"")),m>0&&qe(M)-p&&qn(m>32?io(M+";",r,n,p-1):io(K(M," ","")+";",r,n,p-2),c);break;case 59:M+=";";default:if(qn(X=so(M,t,n,u,a,s,l,F,C=[],Z=[],p),i),L===123)if(a===0)Qn(M,t,X,X,C,i,p,l,Z);else switch(d===99&&de(M,3)===110?100:d){case 100:case 108:case 109:case 115:Qn(e,X,X,r&&qn(so(e,X,X,0,0,s,l,F,s,C=[],p),Z),s,Z,p,l,r?C:Z);break;default:Qn(M,X,X,X,[""],Z,0,l,Z)}}u=a=m=0,R=$=1,F=M="",p=o;break;case 58:p=1+qe(M),m=_;default:if(R<1){if(L==123)--R;else if(L==125&&R++==0&&Aa()==125)continue}switch(M+=Un(L),L*R){case 38:$=a>0?1:(M+="\f",-1);break;case 44:l[u++]=(qe(M)-1)*$,$=1;break;case 64:Ge()===45&&(M+=Jn($e())),d=Ge(),a=p=qe(F=M+=Pa(Zn())),L++;break;case 45:_===45&&qe(M)==2&&(R=0)}}return i}function so(e,t,n,r,s,i,o,l,c,u,a){for(var p=s-1,d=s===0?i:[""],m=Jr(d),_=0,R=0,z=0;_<r;++_)for(var $=0,L=ln(e,p+1,p=_a(R=o[_])),F=e;$<m;++$)(F=eo(R>0?d[$]+" "+L:K(L,/&\f/g,d[$])))&&(c[z++]=F);return Kn(e,t,n,s===0?Gr:l,c,u,a)}function La(e,t,n){return Kn(e,t,n,Yi,Un(Ea()),ln(e,2,-2),0)}function io(e,t,n,r){return Kn(e,t,n,Kr,ln(e,0,r),ln(e,r+1,-1),r)}function Nt(e,t){for(var n="",r=Jr(e),s=0;s<r;s++)n+=t(e[s],s,e,t)||"";return n}function Ma(e,t,n,r){switch(e.type){case va:if(e.children.length)break;case ya:case Kr:return e.return=e.return||e.value;case Yi:return"";case Xi:return e.return=e.value+"{"+Nt(e.children,r)+"}";case Gr:e.value=e.props.join(",")}return qe(n=Nt(e.children,r))?e.return=e.value+"{"+n+"}":""}function Da(e){var t=Jr(e);return function(n,r,s,i){for(var o="",l=0;l<t;l++)o+=e[l](n,r,s,i)||"";return o}}function Na(e){return function(t){t.root||(t=t.return)&&e(t)}}function ja(e){var t=Object.create(null);return function(n){return t[n]===void 0&&(t[n]=e(n)),t[n]}}var Fa=function(t,n,r){for(var s=0,i=0;s=i,i=Ge(),s===38&&i===12&&(n[r]=1),!un(i);)$e();return an(t,Se)},za=function(t,n){var r=-1,s=44;do switch(un(s)){case 0:s===38&&Ge()===12&&(n[r]=1),t[r]+=Fa(Se-1,n,r);break;case 2:t[r]+=Jn(s);break;case 4:if(s===44){t[++r]=Ge()===58?"&\f":"",n[r]=t[r].length;break}default:t[r]+=Un(s)}while(s=$e());return t},Ba=function(t,n){return ro(za(no(t),n))},oo=new WeakMap,Ha=function(t){if(!(t.type!=="rule"||!t.parent||t.length<1)){for(var n=t.value,r=t.parent,s=t.column===r.column&&t.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(t.props.length===1&&n.charCodeAt(0)!==58&&!oo.get(r))&&!s){oo.set(t,!0);for(var i=[],o=Ba(n,i),l=r.props,c=0,u=0;c<o.length;c++)for(var a=0;a<l.length;a++,u++)t.props[u]=i[c]?o[c].replace(/&\f/g,l[a]):l[a]+" "+o[c]}}},Wa=function(t){if(t.type==="decl"){var n=t.value;n.charCodeAt(0)===108&&n.charCodeAt(2)===98&&(t.return="",t.value="")}};function lo(e,t){switch(Sa(e,t)){case 5103:return G+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return G+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return G+e+Vn+e+be+e+e;case 6828:case 4268:return G+e+be+e+e;case 6165:return G+e+be+"flex-"+e+e;case 5187:return G+e+K(e,/(\w+).+(:[^]+)/,G+"box-$1$2"+be+"flex-$1$2")+e;case 5443:return G+e+be+"flex-item-"+K(e,/flex-|-self/,"")+e;case 4675:return G+e+be+"flex-line-pack"+K(e,/align-content|flex-|-self/,"")+e;case 5548:return G+e+be+K(e,"shrink","negative")+e;case 5292:return G+e+be+K(e,"basis","preferred-size")+e;case 6060:return G+"box-"+K(e,"-grow","")+G+e+be+K(e,"grow","positive")+e;case 4554:return G+K(e,/([^-])(transform)/g,"$1"+G+"$2")+e;case 6187:return K(K(K(e,/(zoom-|grab)/,G+"$1"),/(image-set)/,G+"$1"),e,"")+e;case 5495:case 3959:return K(e,/(image-set\([^]*)/,G+"$1$`$1");case 4968:return K(K(e,/(.+:)(flex-)?(.*)/,G+"box-pack:$3"+be+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+G+e+e;case 4095:case 3583:case 4068:case 2532:return K(e,/(.+)-inline(.+)/,G+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(qe(e)-1-t>6)switch(de(e,t+1)){case 109:if(de(e,t+4)!==45)break;case 102:return K(e,/(.+:)(.+)-([^]+)/,"$1"+G+"$2-$3$1"+Vn+(de(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~Zr(e,"stretch")?lo(K(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(de(e,t+1)!==115)break;case 6444:switch(de(e,qe(e)-3-(~Zr(e,"!important")&&10))){case 107:return K(e,":",":"+G)+e;case 101:return K(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+G+(de(e,14)===45?"inline-":"")+"box$3$1"+G+"$2$3$1"+be+"$2box$3")+e}break;case 5936:switch(de(e,t+11)){case 114:return G+e+be+K(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return G+e+be+K(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return G+e+be+K(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return G+e+be+e+e}return e}var Va=function(t,n,r,s){if(t.length>-1&&!t.return)switch(t.type){case Kr:t.return=lo(t.value,t.length);break;case Xi:return Nt([cn(t,{value:K(t.value,"@","@"+G)})],s);case Gr:if(t.length)return Ta(t.props,function(i){switch(Ra(i,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Nt([cn(t,{props:[K(i,/:(read-\w+)/,":"+Vn+"$1")]})],s);case"::placeholder":return Nt([cn(t,{props:[K(i,/:(plac\w+)/,":"+G+"input-$1")]}),cn(t,{props:[K(i,/:(plac\w+)/,":"+Vn+"$1")]}),cn(t,{props:[K(i,/:(plac\w+)/,be+"input-$1")]})],s)}return""})}},Ua=[Va],qa=function(t){var n=t.key;if(n==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(R){var z=R.getAttribute("data-emotion");z.indexOf(" ")!==-1&&(document.head.appendChild(R),R.setAttribute("data-s",""))})}var s=t.stylisPlugins||Ua,i={},o,l=[];o=t.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+n+' "]'),function(R){for(var z=R.getAttribute("data-emotion").split(" "),$=1;$<z.length;$++)i[z[$]]=!0;l.push(R)});var c,u=[Ha,Wa];{var a,p=[Ma,Na(function(R){a.insert(R)})],d=Da(u.concat(s,p)),m=function(z){return Nt(Ia(z),d)};c=function(z,$,L,F){a=L,m(z?z+"{"+$.styles+"}":$.styles),F&&(_.inserted[$.name]=!0)}}var _={key:n,sheet:new wa({key:n,container:o,nonce:t.nonce,speedy:t.speedy,prepend:t.prepend,insertionPoint:t.insertionPoint}),nonce:t.nonce,inserted:i,registered:{},insert:c};return _.sheet.hydrate(l),_};function Ga(e){for(var t=0,n,r=0,s=e.length;s>=4;++r,s-=4)n=e.charCodeAt(r)&255|(e.charCodeAt(++r)&255)<<8|(e.charCodeAt(++r)&255)<<16|(e.charCodeAt(++r)&255)<<24,n=(n&65535)*1540483477+((n>>>16)*59797<<16),n^=n>>>24,t=(n&65535)*1540483477+((n>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(s){case 3:t^=(e.charCodeAt(r+2)&255)<<16;case 2:t^=(e.charCodeAt(r+1)&255)<<8;case 1:t^=e.charCodeAt(r)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}var Ka={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Za=/[A-Z]|^ms/g,Ja=/_EMO_([^_]+?)_([^]*?)_EMO_/g,co=function(t){return t.charCodeAt(1)===45},ao=function(t){return t!=null&&typeof t!="boolean"},Yr=ja(function(e){return co(e)?e:e.replace(Za,"-$&").toLowerCase()}),uo=function(t,n){switch(t){case"animation":case"animationName":if(typeof n=="string")return n.replace(Ja,function(r,s,i){return gt={name:s,styles:i,next:gt},s})}return Ka[t]!==1&&!co(t)&&typeof n=="number"&&n!==0?n+"px":n};function Yn(e,t,n){if(n==null)return"";var r=n;if(r.__emotion_styles!==void 0)return r;switch(typeof n){case"boolean":return"";case"object":{var s=n;if(s.anim===1)return gt={name:s.name,styles:s.styles,next:gt},s.name;var i=n;if(i.styles!==void 0){var o=i.next;if(o!==void 0)for(;o!==void 0;)gt={name:o.name,styles:o.styles,next:gt},o=o.next;var l=i.styles+";";return l}return Qa(e,t,n)}}var c=n;if(t==null)return c;var u=t[c];return u!==void 0?u:c}function Qa(e,t,n){var r="";if(Array.isArray(n))for(var s=0;s<n.length;s++)r+=Yn(e,t,n[s])+";";else for(var i in n){var o=n[i];if(typeof o!="object"){var l=o;t!=null&&t[l]!==void 0?r+=i+"{"+t[l]+"}":ao(l)&&(r+=Yr(i)+":"+uo(i,l)+";")}else if(Array.isArray(o)&&typeof o[0]=="string"&&(t==null||t[o[0]]===void 0))for(var c=0;c<o.length;c++)ao(o[c])&&(r+=Yr(i)+":"+uo(i,o[c])+";");else{var u=Yn(e,t,o);switch(i){case"animation":case"animationName":{r+=Yr(i)+":"+u+";";break}default:r+=i+"{"+u+"}"}}}return r}var fo=/label:\s*([^\s;{]+)\s*(;|$)/g,gt;function Xr(e,t,n){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var r=!0,s="";gt=void 0;var i=e[0];if(i==null||i.raw===void 0)r=!1,s+=Yn(n,t,i);else{var o=i;s+=o[0]}for(var l=1;l<e.length;l++)if(s+=Yn(n,t,e[l]),r){var c=i;s+=c[l]}fo.lastIndex=0;for(var u="",a;(a=fo.exec(s))!==null;)u+="-"+a[1];var p=Ga(s)+u;return{name:p,styles:s,next:gt}}function ho(e,t,n){var r="";return n.split(" ").forEach(function(s){e[s]!==void 0?t.push(e[s]+";"):s&&(r+=s+" ")}),r}var Ya=function(t,n,r){var s=t.key+"-"+n.name;t.registered[s]===void 0&&(t.registered[s]=n.styles)},Xa=function(t,n,r){Ya(t,n);var s=t.key+"-"+n.name;if(t.inserted[n.name]===void 0){var i=n;do t.insert(n===i?"."+s:"",i,t.sheet,!0),i=i.next;while(i!==void 0)}};function po(e,t){if(e.inserted[t.name]===void 0)return e.insert("",t,e.sheet,!0)}function go(e,t,n){var r=[],s=ho(e,r,n);return r.length<2?n:s+t(r)}var eu=function(t){var n=qa(t);n.sheet.speedy=function(l){this.isSpeedy=l},n.compat=!0;var r=function(){for(var c=arguments.length,u=new Array(c),a=0;a<c;a++)u[a]=arguments[a];var p=Xr(u,n.registered,void 0);return Xa(n,p),n.key+"-"+p.name},s=function(){for(var c=arguments.length,u=new Array(c),a=0;a<c;a++)u[a]=arguments[a];var p=Xr(u,n.registered),d="animation-"+p.name;return po(n,{name:p.name,styles:"@keyframes "+d+"{"+p.styles+"}"}),d},i=function(){for(var c=arguments.length,u=new Array(c),a=0;a<c;a++)u[a]=arguments[a];var p=Xr(u,n.registered);po(n,p)},o=function(){for(var c=arguments.length,u=new Array(c),a=0;a<c;a++)u[a]=arguments[a];return go(n.registered,r,tu(u))};return{css:r,cx:o,injectGlobal:i,keyframes:s,hydrate:function(c){c.forEach(function(u){n.inserted[u]=!0})},flush:function(){n.registered={},n.inserted={},n.sheet.flush()},sheet:n.sheet,cache:n,getRegisteredStyles:ho.bind(null,n.registered),merge:go.bind(null,n.registered,r)}},tu=function e(t){for(var n="",r=0;r<t.length;r++){var s=t[r];if(s!=null){var i=void 0;switch(typeof s){case"boolean":break;case"object":{if(Array.isArray(s))i=e(s);else{i="";for(var o in s)s[o]&&o&&(i&&(i+=" "),i+=o)}break}default:i=s}i&&(n&&(n+=" "),n+=i)}}return n};eu({key:"css"});var fn={exports:{}},nu=fn.exports,mo;function ru(){return mo||(mo=1,function(e,t){(function(n,r){r(t)})(nu,function(n){var r=Object.defineProperty,s=Object.defineProperties,i=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,u=(v,E,O)=>E in v?r(v,E,{enumerable:!0,configurable:!0,writable:!0,value:O}):v[E]=O,a=(v,E)=>{for(var O in E||(E={}))l.call(E,O)&&u(v,O,E[O]);if(o)for(var O of o(E))c.call(E,O)&&u(v,O,E[O]);return v},p=(v,E)=>s(v,i(E)),d=(v,E,O)=>new Promise((W,B)=>{var ee=ce=>{try{V(O.next(ce))}catch(Me){B(Me)}},se=ce=>{try{V(O.throw(ce))}catch(Me){B(Me)}},V=ce=>ce.done?W(ce.value):Promise.resolve(ce.value).then(ee,se);V((O=O.apply(v,E)).next())}),m=(v=>(v[v.NONE=0]="NONE",v[v.LOADING=1]="LOADING",v[v.LOADED=2]="LOADED",v[v.ERROR=3]="ERROR",v))(m||{});class _{constructor(E){this.items={},this.factory=E}getOrCreateItemByURL(E){let O=this.items[E];return O||(O=this.items[E]=this.factory(E)),O}tryGetItemByURL(E){var O;return(O=this.items[E])!=null?O:null}removeItemByURL(E){const O=this.items[E];return O&&(this.items[E]=null,O)}}const R="__RUNTIME_IMPORT__";function z(v,E){var O,W;const B=globalThis,ee=(O=B[R])!=null?O:B[R]={};return(W=ee[v])!=null?W:ee[v]=E()}const $=z("cssCache",()=>new _(v=>({url:v,status:m.NONE,el:null,error:null,reject:null})));function L(v,E,O){const W={handleLoad(){v.removeEventListener("load",W.handleLoad),v.removeEventListener("error",W.handleError),E()},handleError(B){v.removeEventListener("load",W.handleLoad),v.removeEventListener("error",W.handleError),O(B)}};v.addEventListener("load",W.handleLoad),v.addEventListener("error",W.handleError)}function F(v){const E=$.getOrCreateItemByURL(v),{status:O,error:W}=E;return O===m.LOADED?Promise.resolve():O===m.ERROR?Promise.reject(W):O===m.LOADING?new Promise((B,ee)=>{const{el:se}=E;L(se,()=>B(),V=>ee(V.error))}):(E.status=m.LOADING,new Promise((B,ee)=>{const se=document.createElement("link");se.rel="stylesheet",se.href=v,L(se,()=>{E.status=m.LOADED,B()},V=>{const ce=V.error||new Error(`Load css failed. href=${v}`);E.status=m.ERROR,E.error=ce,ee(ce)}),E.el=se,se.setAttribute("data-runtime-import-type","css"),document.head.appendChild(se)}))}function C(v){return Promise.all(v.map(E=>F(E))).then(()=>Promise.resolve()).catch(E=>Promise.reject(E))}const Z=z("jsCache",()=>new _(v=>({url:v,status:m.NONE,el:null,error:null,reject:null,exportThing:void 0}))),X=globalThis,{define:M}=X,{keys:je}=Object;let Rt=!1;typeof M<"u"&&!M.runtime_import&&(console.warn("runtime-import should NOT coexist with requiesjs or seajs or any other AMD/CMD loader."),Rt=!0);const Le=z("pendingItemMap",()=>({})),ot=function(...v){const E=v.pop(),{currentScript:O}=document;if(!O)throw new Error("currentScript is null.");const{src:W}=O,B=Le[W];if(!B)throw new Error(`Can NOT find item, src=${W}`);Le[W]=null;try{let ee=v[0]||[],se=null;typeof ee=="string"&&(se=ee,ee=v[1]||[]);const V=B.exportThing=(()=>{let ce=!1;const Me={};let f=E(...ee.map(h=>h==="exports"?(ce=!0,Me):X[h]));return!f&&ce&&(f=Me),f})();se&&(X[se]=V),V&&je(V).length===1&&V.default&&(B.exportThing=V.default,B.exportThing.default=V.default)}catch(ee){B.status=m.ERROR,ee instanceof Error&&(B.error=ee),B.reject(ee)}},jt=()=>{const{currentScript:v}=document;if(v){const{src:E}=v;if(Le[E])return!0}return!1};["amd","cmd"].forEach(v=>{Object.defineProperty(ot,v,{get:jt})}),ot.runtime_import=!0;function Ft(v,E){if(Rt)throw new Error("runtime-import UMD mode uses window.define, you should NOT have your own window.define.");X.define||(X.define=ot),Le[v]=E}function mn(v){const E=/legao-comp\/(.*)\/[\d.]+\/web.js$/.exec(v),O=window;if(E&&E.length>0){const W=E[1];O.g_config=O.g_config||{},O.g_config.appKey=W}}function or(v,E,O){v.status=m.LOADING,mn(E);const{umd:W,crossOrigin:B}=O;return new Promise((ee,se)=>{const V=document.createElement("script");if(V.src=E,V.async=!1,V.crossOrigin=B,W){V.setAttribute("data-runtime-import-type","javascript-umd"),v.reject=se;const ce=V.src;Ft(ce,v)}else V.setAttribute("data-runtime-import-type","javascript");L(V,()=>{v.status=m.LOADED,v.el=null,ee(v.exportThing)},ce=>{const Me=ce.error||new Error(`Load javascript failed. src=${E}`);v.status=m.ERROR,v.error=Me,v.el=null,se(Me)}),v.el=V,document.body.appendChild(V)})}function ge(v,E){const O=Z.getOrCreateItemByURL(v),{status:W,exportThing:B,error:ee}=O;if(W===m.LOADED)return Promise.resolve(B);if(W===m.ERROR)return Promise.reject(ee);if(W===m.LOADING){const{el:se}=O;return new Promise((V,ce)=>{L(se,()=>V(O.exportThing),Me=>ce(Me.error))})}return or(O,v,E)}function le(v,E){let O=Promise.resolve();const W=v.length-1,{umd:B}=E;return v.forEach((ee,se)=>{const V=B&&se===W;O=O.then(()=>ge(ee,p(a({},E),{umd:V})))}),O}function J(v){return d(this,null,function*(){const{scripts:E,styles:O}=v;if(O){const{urls:V}=O;yield C(V)}const{dependencies:W=[],entry:B,umd:ee=!0,crossOrigin:se="anonymous"}=E;if((B?W.concat([B]):W).length)return yield le(W.concat([B]),{umd:ee,crossOrigin:se})})}function lt(v,E){return d(this,null,function*(){const O=E??{};return yield J({scripts:{dependencies:[],entry:v,umd:O.umd,crossOrigin:O.crossOrigin}})})}function zt(v){return d(this,null,function*(){return yield J({scripts:{dependencies:[],entry:""},styles:{urls:[v]}})})}const ct=J;n.importComponent=J,n.importModule=ct,n.importScript=lt,n.importStyle=zt,Object.defineProperty(n,Symbol.toStringTag,{value:"Module"})})}(fn,fn.exports)),fn.exports}ru();function es(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var kt=es();function bo(e){kt=e}var hn={exec:()=>null};function Y(e,t=""){let n=typeof e=="string"?e:e.source;const r={replace:(s,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(_e.caret,"$1"),n=n.replace(s,o),r},getRegex:()=>new RegExp(n,t)};return r}var _e={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},su=/^(?:[ \t]*(?:\n|$))+/,iu=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,ou=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,pn=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,lu=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,ts=/(?:[*+-]|\d{1,9}[.)])/,xo=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,wo=Y(xo).replace(/bull/g,ts).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),cu=Y(xo).replace(/bull/g,ts).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),ns=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,au=/^[^\n]+/,rs=/(?!\s*\])(?:\\.|[^\[\]\\])+/,uu=Y(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",rs).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),fu=Y(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,ts).getRegex(),Xn="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",ss=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,hu=Y("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",ss).replace("tag",Xn).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),yo=Y(ns).replace("hr",pn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xn).getRegex(),pu=Y(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",yo).getRegex(),is={blockquote:pu,code:iu,def:uu,fences:ou,heading:lu,hr:pn,html:hu,lheading:wo,list:fu,newline:su,paragraph:yo,table:hn,text:au},vo=Y("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",pn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xn).getRegex(),du={...is,lheading:cu,table:vo,paragraph:Y(ns).replace("hr",pn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",vo).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Xn).getRegex()},gu={...is,html:Y(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",ss).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:hn,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Y(ns).replace("hr",pn).replace("heading",` *#{1,6} *[^
]`).replace("lheading",wo).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},mu=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,bu=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,_o=/^( {2,}|\\)\n(?!\s*$)/,xu=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,er=/[\p{P}\p{S}]/u,os=/[\s\p{P}\p{S}]/u,ko=/[^\s\p{P}\p{S}]/u,wu=Y(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,os).getRegex(),So=/(?!~)[\p{P}\p{S}]/u,yu=/(?!~)[\s\p{P}\p{S}]/u,vu=/(?:[^\s\p{P}\p{S}]|~)/u,_u=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Ro=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,ku=Y(Ro,"u").replace(/punct/g,er).getRegex(),Su=Y(Ro,"u").replace(/punct/g,So).getRegex(),To="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",Ru=Y(To,"gu").replace(/notPunctSpace/g,ko).replace(/punctSpace/g,os).replace(/punct/g,er).getRegex(),Tu=Y(To,"gu").replace(/notPunctSpace/g,vu).replace(/punctSpace/g,yu).replace(/punct/g,So).getRegex(),Eu=Y("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,ko).replace(/punctSpace/g,os).replace(/punct/g,er).getRegex(),Au=Y(/\\(punct)/,"gu").replace(/punct/g,er).getRegex(),Cu=Y(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Ou=Y(ss).replace("(?:-->|$)","-->").getRegex(),$u=Y("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Ou).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),tr=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Pu=Y(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",tr).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Eo=Y(/^!?\[(label)\]\[(ref)\]/).replace("label",tr).replace("ref",rs).getRegex(),Ao=Y(/^!?\[(ref)\](?:\[\])?/).replace("ref",rs).getRegex(),Iu=Y("reflink|nolink(?!\\()","g").replace("reflink",Eo).replace("nolink",Ao).getRegex(),ls={_backpedal:hn,anyPunctuation:Au,autolink:Cu,blockSkip:_u,br:_o,code:bu,del:hn,emStrongLDelim:ku,emStrongRDelimAst:Ru,emStrongRDelimUnd:Eu,escape:mu,link:Pu,nolink:Ao,punctuation:wu,reflink:Eo,reflinkSearch:Iu,tag:$u,text:xu,url:hn},Lu={...ls,link:Y(/^!?\[(label)\]\((.*?)\)/).replace("label",tr).getRegex(),reflink:Y(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",tr).getRegex()},cs={...ls,emStrongRDelimAst:Tu,emStrongLDelim:Su,url:Y(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Mu={...cs,br:Y(_o).replace("{2,}","*").getRegex(),text:Y(cs.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},nr={normal:is,gfm:du,pedantic:gu},dn={normal:ls,gfm:cs,breaks:Mu,pedantic:Lu},Du={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Co=e=>Du[e];function Ke(e,t){if(t){if(_e.escapeTest.test(e))return e.replace(_e.escapeReplace,Co)}else if(_e.escapeTestNoEncode.test(e))return e.replace(_e.escapeReplaceNoEncode,Co);return e}function Oo(e){try{e=encodeURI(e).replace(_e.percentDecode,"%")}catch{return null}return e}function $o(e,t){var i;const n=e.replace(_e.findPipe,(o,l,c)=>{let u=!1,a=l;for(;--a>=0&&c[a]==="\\";)u=!u;return u?"|":" |"}),r=n.split(_e.splitPipe);let s=0;if(r[0].trim()||r.shift(),r.length>0&&!((i=r.at(-1))!=null&&i.trim())&&r.pop(),t)if(r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;s<r.length;s++)r[s]=r[s].trim().replace(_e.slashPipe,"|");return r}function gn(e,t,n){const r=e.length;if(r===0)return"";let s=0;for(;s<r&&e.charAt(r-s-1)===t;)s++;return e.slice(0,r-s)}function Nu(e,t){if(e.indexOf(t[1])===-1)return-1;let n=0;for(let r=0;r<e.length;r++)if(e[r]==="\\")r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return n>0?-2:-1}function Po(e,t,n,r,s){const i=t.href,o=t.title||null,l=e[1].replace(s.other.outputLinkReplace,"$1");r.state.inLink=!0;const c={type:e[0].charAt(0)==="!"?"image":"link",raw:n,href:i,title:o,text:l,tokens:r.inlineTokens(l)};return r.state.inLink=!1,c}function ju(e,t,n){const r=e.match(n.other.indentCodeCompensation);if(r===null)return t;const s=r[1];return t.split(`
`).map(i=>{const o=i.match(n.other.beginningSpace);if(o===null)return i;const[l]=o;return l.length>=s.length?i.slice(s.length):i}).join(`
`)}var rr=class{constructor(e){te(this,"options");te(this,"rules");te(this,"lexer");this.options=e||kt}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:gn(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],r=ju(n,t[3]||"",this.rules);return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(this.rules.other.endingHash.test(n)){const r=gn(n,"#");(this.options.pedantic||!r||this.rules.other.endingSpaceChar.test(r))&&(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:gn(t[0],`
`)}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let n=gn(t[0],`
`).split(`
`),r="",s="";const i=[];for(;n.length>0;){let o=!1;const l=[];let c;for(c=0;c<n.length;c++)if(this.rules.other.blockquoteStart.test(n[c]))l.push(n[c]),o=!0;else if(!o)l.push(n[c]);else break;n=n.slice(c);const u=l.join(`
`),a=u.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");r=r?`${r}
${u}`:u,s=s?`${s}
${a}`:a;const p=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(a,i,!0),this.lexer.state.top=p,n.length===0)break;const d=i.at(-1);if((d==null?void 0:d.type)==="code")break;if((d==null?void 0:d.type)==="blockquote"){const m=d,_=m.raw+`
`+n.join(`
`),R=this.blockquote(_);i[i.length-1]=R,r=r.substring(0,r.length-m.raw.length)+R.raw,s=s.substring(0,s.length-m.text.length)+R.text;break}else if((d==null?void 0:d.type)==="list"){const m=d,_=m.raw+`
`+n.join(`
`),R=this.list(_);i[i.length-1]=R,r=r.substring(0,r.length-d.raw.length)+R.raw,s=s.substring(0,s.length-m.raw.length)+R.raw,n=_.substring(i.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:r,tokens:i,text:s}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,s={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const i=this.rules.other.listItemRegex(n);let o=!1;for(;e;){let c=!1,u="",a="";if(!(t=i.exec(e))||this.rules.block.hr.test(e))break;u=t[0],e=e.substring(u.length);let p=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,$=>" ".repeat(3*$.length)),d=e.split(`
`,1)[0],m=!p.trim(),_=0;if(this.options.pedantic?(_=2,a=p.trimStart()):m?_=t[1].length+1:(_=t[2].search(this.rules.other.nonSpaceChar),_=_>4?1:_,a=p.slice(_),_+=t[1].length),m&&this.rules.other.blankLine.test(d)&&(u+=d+`
`,e=e.substring(d.length+1),c=!0),!c){const $=this.rules.other.nextBulletRegex(_),L=this.rules.other.hrRegex(_),F=this.rules.other.fencesBeginRegex(_),C=this.rules.other.headingBeginRegex(_),Z=this.rules.other.htmlBeginRegex(_);for(;e;){const X=e.split(`
`,1)[0];let M;if(d=X,this.options.pedantic?(d=d.replace(this.rules.other.listReplaceNesting,"  "),M=d):M=d.replace(this.rules.other.tabCharGlobal,"    "),F.test(d)||C.test(d)||Z.test(d)||$.test(d)||L.test(d))break;if(M.search(this.rules.other.nonSpaceChar)>=_||!d.trim())a+=`
`+M.slice(_);else{if(m||p.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||F.test(p)||C.test(p)||L.test(p))break;a+=`
`+d}!m&&!d.trim()&&(m=!0),u+=X+`
`,e=e.substring(X.length+1),p=M.slice(_)}}s.loose||(o?s.loose=!0:this.rules.other.doubleBlankLine.test(u)&&(o=!0));let R=null,z;this.options.gfm&&(R=this.rules.other.listIsTask.exec(a),R&&(z=R[0]!=="[ ] ",a=a.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:u,task:!!R,checked:z,loose:!1,text:a,tokens:[]}),s.raw+=u}const l=s.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let c=0;c<s.items.length;c++)if(this.lexer.state.top=!1,s.items[c].tokens=this.lexer.blockTokens(s.items[c].text,[]),!s.loose){const u=s.items[c].tokens.filter(p=>p.type==="space"),a=u.length>0&&u.some(p=>this.rules.other.anyLine.test(p.raw));s.loose=a}if(s.loose)for(let c=0;c<s.items.length;c++)s.items[c].loose=!0;return s}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),r=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:s}}}table(e){var o;const t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;const n=$o(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(o=t[3])!=null&&o.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],i={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const l of r)this.rules.other.tableAlignRight.test(l)?i.align.push("right"):this.rules.other.tableAlignCenter.test(l)?i.align.push("center"):this.rules.other.tableAlignLeft.test(l)?i.align.push("left"):i.align.push(null);for(let l=0;l<n.length;l++)i.header.push({text:n[l],tokens:this.lexer.inline(n[l]),header:!0,align:i.align[l]});for(const l of s)i.rows.push($o(l,i.header.length).map((c,u)=>({text:c,tokens:this.lexer.inline(c),header:!1,align:i.align[u]})));return i}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(n)){if(!this.rules.other.endAngleBracket.test(n))return;const i=gn(n.slice(0,-1),"\\");if((n.length-i.length)%2===0)return}else{const i=Nu(t[2],"()");if(i===-2)return;if(i>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+i;t[2]=t[2].substring(0,i),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let r=t[2],s="";if(this.options.pedantic){const i=this.rules.other.pedanticHrefTitle.exec(r);i&&(r=i[1],s=i[3])}else s=t[3]?t[3].slice(1,-1):"";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(n)?r=r.slice(1):r=r.slice(1,-1)),Po(t,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const r=(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=t[r.toLowerCase()];if(!s){const i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return Po(n,s,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))return;if(!(r[1]||r[2]||"")||!n||this.rules.inline.punctuation.exec(n)){const i=[...r[0]].length-1;let o,l,c=i,u=0;const a=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(a.lastIndex=0,t=t.slice(-1*e.length+i);(r=a.exec(t))!=null;){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(l=[...o].length,r[3]||r[4]){c+=l;continue}else if((r[5]||r[6])&&i%3&&!((i+l)%3)){u+=l;continue}if(c-=l,c>0)continue;l=Math.min(l,l+c+u);const p=[...r[0]][0].length,d=e.slice(0,i+r.index+p+l);if(Math.min(i,l)%2){const _=d.slice(1,-1);return{type:"em",raw:d,text:_,tokens:this.lexer.inlineTokens(_)}}const m=d.slice(2,-2);return{type:"strong",raw:d,text:m,tokens:this.lexer.inlineTokens(m)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(this.rules.other.newLineCharGlobal," ");const r=this.rules.other.nonSpaceChar.test(n),s=this.rules.other.startingSpaceChar.test(n)&&this.rules.other.endingSpaceChar.test(n);return r&&s&&(n=n.substring(1,n.length-1)),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,r;return t[2]==="@"?(n=t[1],r="mailto:"+n):(n=t[1],r=n),{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let r,s;if(t[2]==="@")r=t[0],s="mailto:"+r;else{let i;do i=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(i!==t[0]);r=t[0],t[1]==="www."?s="http://"+t[0]:s=t[0]}return{type:"link",raw:t[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){const n=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:n}}}},st=class hs{constructor(t){te(this,"tokens");te(this,"options");te(this,"state");te(this,"tokenizer");te(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||kt,this.options.tokenizer=this.options.tokenizer||new rr,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={other:_e,block:nr.normal,inline:dn.normal};this.options.pedantic?(n.block=nr.pedantic,n.inline=dn.pedantic):this.options.gfm&&(n.block=nr.gfm,this.options.breaks?n.inline=dn.breaks:n.inline=dn.gfm),this.tokenizer.rules=n}static get rules(){return{block:nr,inline:dn}}static lex(t,n){return new hs(n).lex(t)}static lexInline(t,n){return new hs(n).inlineTokens(t)}lex(t){t=t.replace(_e.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){const r=this.inlineQueue[n];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,n=[],r=!1){var s,i,o;for(this.options.pedantic&&(t=t.replace(_e.tabCharGlobal,"    ").replace(_e.spaceLine,""));t;){let l;if((i=(s=this.options.extensions)==null?void 0:s.block)!=null&&i.some(u=>(l=u.call({lexer:this},t,n))?(t=t.substring(l.raw.length),n.push(l),!0):!1))continue;if(l=this.tokenizer.space(t)){t=t.substring(l.raw.length);const u=n.at(-1);l.raw.length===1&&u!==void 0?u.raw+=`
`:n.push(l);continue}if(l=this.tokenizer.code(t)){t=t.substring(l.raw.length);const u=n.at(-1);(u==null?void 0:u.type)==="paragraph"||(u==null?void 0:u.type)==="text"?(u.raw+=`
`+l.raw,u.text+=`
`+l.text,this.inlineQueue.at(-1).src=u.text):n.push(l);continue}if(l=this.tokenizer.fences(t)){t=t.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.heading(t)){t=t.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.hr(t)){t=t.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.blockquote(t)){t=t.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.list(t)){t=t.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.html(t)){t=t.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.def(t)){t=t.substring(l.raw.length);const u=n.at(-1);(u==null?void 0:u.type)==="paragraph"||(u==null?void 0:u.type)==="text"?(u.raw+=`
`+l.raw,u.text+=`
`+l.raw,this.inlineQueue.at(-1).src=u.text):this.tokens.links[l.tag]||(this.tokens.links[l.tag]={href:l.href,title:l.title});continue}if(l=this.tokenizer.table(t)){t=t.substring(l.raw.length),n.push(l);continue}if(l=this.tokenizer.lheading(t)){t=t.substring(l.raw.length),n.push(l);continue}let c=t;if((o=this.options.extensions)!=null&&o.startBlock){let u=1/0;const a=t.slice(1);let p;this.options.extensions.startBlock.forEach(d=>{p=d.call({lexer:this},a),typeof p=="number"&&p>=0&&(u=Math.min(u,p))}),u<1/0&&u>=0&&(c=t.substring(0,u+1))}if(this.state.top&&(l=this.tokenizer.paragraph(c))){const u=n.at(-1);r&&(u==null?void 0:u.type)==="paragraph"?(u.raw+=`
`+l.raw,u.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=u.text):n.push(l),r=c.length!==t.length,t=t.substring(l.raw.length);continue}if(l=this.tokenizer.text(t)){t=t.substring(l.raw.length);const u=n.at(-1);(u==null?void 0:u.type)==="text"?(u.raw+=`
`+l.raw,u.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=u.text):n.push(l);continue}if(t){const u="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(u);break}else throw new Error(u)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){var l,c,u;let r=t,s=null;if(this.tokens.links){const a=Object.keys(this.tokens.links);if(a.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(r))!=null;)a.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(r))!=null;)r=r.slice(0,s.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(r))!=null;)r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,o="";for(;t;){i||(o=""),i=!1;let a;if((c=(l=this.options.extensions)==null?void 0:l.inline)!=null&&c.some(d=>(a=d.call({lexer:this},t,n))?(t=t.substring(a.raw.length),n.push(a),!0):!1))continue;if(a=this.tokenizer.escape(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.tag(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.link(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(a.raw.length);const d=n.at(-1);a.type==="text"&&(d==null?void 0:d.type)==="text"?(d.raw+=a.raw,d.text+=a.text):n.push(a);continue}if(a=this.tokenizer.emStrong(t,r,o)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.codespan(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.br(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.del(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.autolink(t)){t=t.substring(a.raw.length),n.push(a);continue}if(!this.state.inLink&&(a=this.tokenizer.url(t))){t=t.substring(a.raw.length),n.push(a);continue}let p=t;if((u=this.options.extensions)!=null&&u.startInline){let d=1/0;const m=t.slice(1);let _;this.options.extensions.startInline.forEach(R=>{_=R.call({lexer:this},m),typeof _=="number"&&_>=0&&(d=Math.min(d,_))}),d<1/0&&d>=0&&(p=t.substring(0,d+1))}if(a=this.tokenizer.inlineText(p)){t=t.substring(a.raw.length),a.raw.slice(-1)!=="_"&&(o=a.raw.slice(-1)),i=!0;const d=n.at(-1);(d==null?void 0:d.type)==="text"?(d.raw+=a.raw,d.text+=a.text):n.push(a);continue}if(t){const d="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(d);break}else throw new Error(d)}}return n}},sr=class{constructor(e){te(this,"options");te(this,"parser");this.options=e||kt}space(e){return""}code({text:e,lang:t,escaped:n}){var i;const r=(i=(t||"").match(_e.notSpaceStart))==null?void 0:i[0],s=e.replace(_e.endingNewline,"")+`
`;return r?'<pre><code class="language-'+Ke(r)+'">'+(n?s:Ke(s,!0))+`</code></pre>
`:"<pre><code>"+(n?s:Ke(s,!0))+`</code></pre>
`}blockquote({tokens:e}){return`<blockquote>
${this.parser.parse(e)}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return`<hr>
`}list(e){const t=e.ordered,n=e.start;let r="";for(let o=0;o<e.items.length;o++){const l=e.items[o];r+=this.listitem(l)}const s=t?"ol":"ul",i=t&&n!==1?' start="'+n+'"':"";return"<"+s+i+`>
`+r+"</"+s+`>
`}listitem(e){var n;let t="";if(e.task){const r=this.checkbox({checked:!!e.checked});e.loose?((n=e.tokens[0])==null?void 0:n.type)==="paragraph"?(e.tokens[0].text=r+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&e.tokens[0].tokens[0].type==="text"&&(e.tokens[0].tokens[0].text=r+" "+Ke(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:r+" ",text:r+" ",escaped:!0}):t+=r+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",n="";for(let s=0;s<e.header.length;s++)n+=this.tablecell(e.header[s]);t+=this.tablerow({text:n});let r="";for(let s=0;s<e.rows.length;s++){const i=e.rows[s];n="";for(let o=0;o<i.length;o++)n+=this.tablecell(i[o]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+r+`</table>
`}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){const t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${Ke(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){const r=this.parser.parseInline(n),s=Oo(e);if(s===null)return r;e=s;let i='<a href="'+e+'"';return t&&(i+=' title="'+Ke(t)+'"'),i+=">"+r+"</a>",i}image({href:e,title:t,text:n,tokens:r}){r&&(n=this.parser.parseInline(r,this.parser.textRenderer));const s=Oo(e);if(s===null)return Ke(n);e=s;let i=`<img src="${e}" alt="${n}"`;return t&&(i+=` title="${Ke(t)}"`),i+=">",i}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:Ke(e.text)}},as=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},it=class ps{constructor(t){te(this,"options");te(this,"renderer");te(this,"textRenderer");this.options=t||kt,this.options.renderer=this.options.renderer||new sr,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new as}static parse(t,n){return new ps(n).parse(t)}static parseInline(t,n){return new ps(n).parseInline(t)}parse(t,n=!0){var s,i;let r="";for(let o=0;o<t.length;o++){const l=t[o];if((i=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&i[l.type]){const u=l,a=this.options.extensions.renderers[u.type].call({parser:this},u);if(a!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(u.type)){r+=a||"";continue}}const c=l;switch(c.type){case"space":{r+=this.renderer.space(c);continue}case"hr":{r+=this.renderer.hr(c);continue}case"heading":{r+=this.renderer.heading(c);continue}case"code":{r+=this.renderer.code(c);continue}case"table":{r+=this.renderer.table(c);continue}case"blockquote":{r+=this.renderer.blockquote(c);continue}case"list":{r+=this.renderer.list(c);continue}case"html":{r+=this.renderer.html(c);continue}case"paragraph":{r+=this.renderer.paragraph(c);continue}case"text":{let u=c,a=this.renderer.text(u);for(;o+1<t.length&&t[o+1].type==="text";)u=t[++o],a+=`
`+this.renderer.text(u);n?r+=this.renderer.paragraph({type:"paragraph",raw:a,text:a,tokens:[{type:"text",raw:a,text:a,escaped:!0}]}):r+=a;continue}default:{const u='Token with "'+c.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}}return r}parseInline(t,n=this.renderer){var s,i;let r="";for(let o=0;o<t.length;o++){const l=t[o];if((i=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&i[l.type]){const u=this.options.extensions.renderers[l.type].call({parser:this},l);if(u!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(l.type)){r+=u||"";continue}}const c=l;switch(c.type){case"escape":{r+=n.text(c);break}case"html":{r+=n.html(c);break}case"link":{r+=n.link(c);break}case"image":{r+=n.image(c);break}case"strong":{r+=n.strong(c);break}case"em":{r+=n.em(c);break}case"codespan":{r+=n.codespan(c);break}case"br":{r+=n.br(c);break}case"del":{r+=n.del(c);break}case"text":{r+=n.text(c);break}default:{const u='Token with "'+c.type+'" type was not found.';if(this.options.silent)return console.error(u),"";throw new Error(u)}}}return r}},ir=(fs=class{constructor(e){te(this,"options");te(this,"block");this.options=e||kt}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?st.lex:st.lexInline}provideParser(){return this.block?it.parse:it.parseInline}},te(fs,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),fs),Fu=class{constructor(...e){te(this,"defaults",es());te(this,"options",this.setOptions);te(this,"parse",this.parseMarkdown(!0));te(this,"parseInline",this.parseMarkdown(!1));te(this,"Parser",it);te(this,"Renderer",sr);te(this,"TextRenderer",as);te(this,"Lexer",st);te(this,"Tokenizer",rr);te(this,"Hooks",ir);this.use(...e)}walkTokens(e,t){var r,s;let n=[];for(const i of e)switch(n=n.concat(t.call(this,i)),i.type){case"table":{const o=i;for(const l of o.header)n=n.concat(this.walkTokens(l.tokens,t));for(const l of o.rows)for(const c of l)n=n.concat(this.walkTokens(c.tokens,t));break}case"list":{const o=i;n=n.concat(this.walkTokens(o.items,t));break}default:{const o=i;(s=(r=this.defaults.extensions)==null?void 0:r.childTokens)!=null&&s[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const c=o[l].flat(1/0);n=n.concat(this.walkTokens(c,t))}):o.tokens&&(n=n.concat(this.walkTokens(o.tokens,t)))}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(n=>{const r={...n};if(r.async=this.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const i=t.renderers[s.name];i?t.renderers[s.name]=function(...o){let l=s.renderer.apply(this,o);return l===!1&&(l=i.apply(this,o)),l}:t.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=t[s.level];i?i.unshift(s.tokenizer):t[s.level]=[s.tokenizer],s.start&&(s.level==="block"?t.startBlock?t.startBlock.push(s.start):t.startBlock=[s.start]:s.level==="inline"&&(t.startInline?t.startInline.push(s.start):t.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(t.childTokens[s.name]=s.childTokens)}),r.extensions=t),n.renderer){const s=this.defaults.renderer||new sr(this.defaults);for(const i in n.renderer){if(!(i in s))throw new Error(`renderer '${i}' does not exist`);if(["options","parser"].includes(i))continue;const o=i,l=n.renderer[o],c=s[o];s[o]=(...u)=>{let a=l.apply(s,u);return a===!1&&(a=c.apply(s,u)),a||""}}r.renderer=s}if(n.tokenizer){const s=this.defaults.tokenizer||new rr(this.defaults);for(const i in n.tokenizer){if(!(i in s))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,l=n.tokenizer[o],c=s[o];s[o]=(...u)=>{let a=l.apply(s,u);return a===!1&&(a=c.apply(s,u)),a}}r.tokenizer=s}if(n.hooks){const s=this.defaults.hooks||new ir;for(const i in n.hooks){if(!(i in s))throw new Error(`hook '${i}' does not exist`);if(["options","block"].includes(i))continue;const o=i,l=n.hooks[o],c=s[o];ir.passThroughHooks.has(i)?s[o]=u=>{if(this.defaults.async)return Promise.resolve(l.call(s,u)).then(p=>c.call(s,p));const a=l.call(s,u);return c.call(s,a)}:s[o]=(...u)=>{let a=l.apply(s,u);return a===!1&&(a=c.apply(s,u)),a}}r.hooks=s}if(n.walkTokens){const s=this.defaults.walkTokens,i=n.walkTokens;r.walkTokens=function(o){let l=[];return l.push(i.call(this,o)),s&&(l=l.concat(s.call(this,o))),l}}this.defaults={...this.defaults,...r}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return st.lex(e,t??this.defaults)}parser(e,t){return it.parse(e,t??this.defaults)}parseMarkdown(e){return(n,r)=>{const s={...r},i={...this.defaults,...s},o=this.onError(!!i.silent,!!i.async);if(this.defaults.async===!0&&s.async===!1)return o(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof n>"u"||n===null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));i.hooks&&(i.hooks.options=i,i.hooks.block=e);const l=i.hooks?i.hooks.provideLexer():e?st.lex:st.lexInline,c=i.hooks?i.hooks.provideParser():e?it.parse:it.parseInline;if(i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(n):n).then(u=>l(u,i)).then(u=>i.hooks?i.hooks.processAllTokens(u):u).then(u=>i.walkTokens?Promise.all(this.walkTokens(u,i.walkTokens)).then(()=>u):u).then(u=>c(u,i)).then(u=>i.hooks?i.hooks.postprocess(u):u).catch(o);try{i.hooks&&(n=i.hooks.preprocess(n));let u=l(n,i);i.hooks&&(u=i.hooks.processAllTokens(u)),i.walkTokens&&this.walkTokens(u,i.walkTokens);let a=c(u,i);return i.hooks&&(a=i.hooks.postprocess(a)),a}catch(u){return o(u)}}}onError(e,t){return n=>{if(n.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const r="<p>An error occurred:</p><pre>"+Ke(n.message+"",!0)+"</pre>";return t?Promise.resolve(r):r}if(t)return Promise.reject(n);throw n}}},St=new Fu;function ne(e,t){return St.parse(e,t)}ne.options=ne.setOptions=function(e){return St.setOptions(e),ne.defaults=St.defaults,bo(ne.defaults),ne},ne.getDefaults=es,ne.defaults=kt,ne.use=function(...e){return St.use(...e),ne.defaults=St.defaults,bo(ne.defaults),ne},ne.walkTokens=function(e,t){return St.walkTokens(e,t)},ne.parseInline=St.parseInline,ne.Parser=it,ne.parser=it.parse,ne.Renderer=sr,ne.TextRenderer=as,ne.Lexer=st,ne.lexer=st.lex,ne.Tokenizer=rr,ne.Hooks=ir,ne.parse=ne,ne.options,ne.setOptions,ne.use,ne.walkTokens,ne.parseInline,it.parse,st.lex;const zu=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},Bu={name:"rtf",props:{vueState:{type:Object,required:!0},data:{type:Object,required:!0}},data(){return{allText:""}},setup(e){const t=Gt({isapplystep:!1,dataid:"",amid:"",allText:"",amresknjh:"",amworksheetevaluationid:"",reporttitle:"",isnotcreateknowledge:!0,iscreateknowledge:!1,knowledgeid:""}),n=c=>{const u=c.replace(/(\d+\.)/g,"<keep>$1</keep>");return ne.parse(u,{breaks:!0,gfm:!0,xhtml:!0}).replace(/<keep>(\d+\.)<\/keep>/g,"$1")},r=async(c,u)=>{t.isnotcreateknowledge=!1;const a=e.vueState.data.allText;e.vueState.data.dataid;const p=atob(a),d=atob(e.vueState.data.amworksheetid),m=new Uint8Array(d.length);for(let C=0;C<d.length;C++)m[C]=d.charCodeAt(C);const _=new TextDecoder("utf-8").decode(m);t.amworksheetevaluationid=_;const R=new Uint8Array(p.length);for(let C=0;C<p.length;C++)R[C]=p.charCodeAt(C);const z=new TextDecoder("utf-8").decode(R);t.allText=z;const $=n(z);c.data.type;const F=await(await fetch("/api/eam/eampm/v1.0/rep/execute/repairWorksheet/generateknowledgebyworksheet",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:$,workInfomationId:_})})).json();F.resultType=="SUCCESS"?(t.iscreateknowledge=!0,t.reporttitle=F.appendData.title,t.knowledgeid=F.appendData.knowledgeid):t.isnotcreateknowledge=!0},s=()=>{var c="/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid="+t.knowledgeid+"&status=view&styleid=25630f43-6519-2928-44ad-a3af7d19c3de&j=true&types=readviewMod=wf&enableEdit=false&runtime=true";l(t.knowledgeid,"知识发布",c)},i=()=>{try{return window.parent&&window.parent.gspframeworkAdapterService?window.parent:window.top&&window.top.gspframeworkAdapterService?window.top:window}catch{return window}},o=c=>{const u=new RegExp("(^|&)"+c+"=([^&]*)(&|$)","i");let a;return window.location.search&&(a=window.location.search.substr(1).match(u)),a!=null||window.location.hash&&(a=window.location.hash.substr(3).match(u),a!=null)?unescape(a[2]):""},l=(c,u,a)=>{const p=i();if(p){p.allgspfuncs||p.gspframeworkAdapterService.funcSvc.getAllCachedFuncs();const d=o("pfuncid")||o("funcId")||o("funcid")||"1296a44e-b590-6341-9af2-8530ff71745d",m="funcid="+c+"&pfuncid="+d;if(a.indexOf("?")===-1?a+="?"+m:a+="&"+m,p.gspframeworkAdapterService)return p.gspframeworkAdapterService.appSvc.getFeb().post("farrisapp-click",{FuncName:u,active:!1,appType:"menu",code:c,tabId:c,funcId:d,id:c,su:"views",isjquery:!0,reload:void 0,sessionid:localStorage.session,src:a,url:a}),c}};return{...xl(t),apply:r,view:s,convertMarkdownToHtml:n}}},Hu={key:0,class:"menu-button-widget"};function Wu(e,t,n,r,s,i){return Fn(),jr(We,null,[e.isnotcreateknowledge?(Fn(),jr("div",Hu,[Fr("button",{class:"menu-button-widget-button",style:{padding:"5px 10px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},onClick:t[0]||(t[0]=o=>r.apply(n.vueState,o))}," 应用 ")])):Pi("",!0),e.iscreateknowledge?(Fn(),jr("div",{key:1,style:{cursor:"pointer",color:"blue"},onClick:t[1]||(t[1]=o=>r.view())},ks(e.reporttitle),1)):Pi("",!0)],64)}const Vu=zu(Bu,[["render",Wu],["__scopeId","data-v-3fadd662"]]);function Uu(e){return JSON.parse(JSON.stringify(e))}class qu{async initialize(){console.log("ExampleWidgetAPI initialized.")}async cleanup(){console.log("ExampleWidgetAPI cleanup.")}createWidget(){return new Gu}}class Gu{constructor(){te(this,"vueState_",Gt({options:{mode:"full",implOptions:{data:{}}},data:{}}));te(this,"vueApp_");te(this,"isMounted_",!1);const t=this.vueState_;this.vueApp_=pa({setup(){const n=t;return console.log("constuct"),en(()=>n.options,r=>{console.log("ExampleWidget options updated.",r)}),en(()=>n.data,r=>{console.log("ExampleWidget data updated.",r)}),{state:n}},render(){return console.log("render data",this.state.data),Wc(Vu,{vueState:t})}})}namespace(){return"sys"}name(){return"rtf"}options(){return this.vueState_.options}updateOptions(t){this.vueState_.options={...this.options(),...Uu(t)}}data(){return console.log(this.options().implOptions.data),this.vueState_.data}updateData(t){console.log("ExampleWidget data updating.",t),this.vueState_.data=t,console.log("ExampleWidget data updated.",this.vueState_)}async mount(t){if(this.isMounted_)throw new Error("ExampleWidget already mounted.");this.vueApp_.mount(t),this.isMounted_=!0}async unmount(){if(!this.isMounted_)throw new Error("ExampleWidget is NOT mounted.");this.vueApp_.unmount(),this.isMounted_=!1}async rerender(){}async dispose(){this.isMounted_&&this.unmount()}addEventListener(t){return()=>{}}}const us=new qu;function Ku(){return us.initialize()}function Zu(){return us.cleanup()}function Ju(){return us.createWidget()}ke.cleanup=Zu,ke.createWidget=Ju,ke.initialize=Ku,Object.defineProperty(ke,Symbol.toStringTag,{value:"Module"})});
