(function(<PERSON><PERSON>,Be){typeof exports=="object"&&typeof module<"u"?Be(exports):typeof define=="function"&&define.amd?define(["exports"],Be):(Oe=typeof globalThis<"u"?globalThis:Oe||self,Be(Oe.RTFWidget={}))})(this,function(Oe){"use strict";var pf=Object.defineProperty;var Cf=(Oe,Be,Q)=>Be in Oe?pf(Oe,Be,{enumerable:!0,configurable:!0,writable:!0,value:Q}):Oe[Be]=Q;var fr=(Oe,Be,Q)=>Cf(Oe,typeof Be!="symbol"?Be+"":Be,Q);/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Be(e){const t=Object.create(null);for(const u of e.split(","))t[u]=1;return u=>u in t}const Q={},Nt=[],We=()=>{},Ci=()=>!1,Bu=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ar=e=>e.startsWith("onUpdate:"),xe=Object.assign,Dr=(e,t)=>{const u=e.indexOf(t);u>-1&&e.splice(u,1)},Fi=Object.prototype.hasOwnProperty,re=(e,t)=>Fi.call(e,t),V=Array.isArray,Rt=e=>xu(e)==="[object Map]",an=e=>xu(e)==="[object Set]",k=e=>typeof e=="function",he=e=>typeof e=="string",Ft=e=>typeof e=="symbol",de=e=>e!==null&&typeof e=="object",Dn=e=>(de(e)||k(e))&&k(e.then)&&k(e.catch),dn=Object.prototype.toString,xu=e=>dn.call(e),Ai=e=>xu(e).slice(8,-1),hn=e=>xu(e)==="[object Object]",dr=e=>he(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Yt=Be(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),_u=e=>{const t=Object.create(null);return u=>t[u]||(t[u]=e(u))},Ei=/-(\w)/g,At=_u(e=>e.replace(Ei,(t,u)=>u?u.toUpperCase():"")),gi=/\B([A-Z])/g,xt=_u(e=>e.replace(gi,"-$1").toLowerCase()),pn=_u(e=>e.charAt(0).toUpperCase()+e.slice(1)),hr=_u(e=>e?`on${pn(e)}`:""),_t=(e,t)=>!Object.is(e,t),pr=(e,...t)=>{for(let u=0;u<e.length;u++)e[u](...t)},Cn=(e,t,u,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:u})},mi=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Fn;const vu=()=>Fn||(Fn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Cr(e){if(V(e)){const t={};for(let u=0;u<e.length;u++){const r=e[u],n=he(r)?xi(r):Cr(r);if(n)for(const i in n)t[i]=n[i]}return t}else if(he(e)||de(e))return e}const bi=/;(?![^(]*\))/g,yi=/:([^]+)/,Bi=/\/\*[^]*?\*\//g;function xi(e){const t={};return e.replace(Bi,"").split(bi).forEach(u=>{if(u){const r=u.split(yi);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Fr(e){let t="";if(he(e))t=e;else if(V(e))for(let u=0;u<e.length;u++){const r=Fr(e[u]);r&&(t+=r+" ")}else if(de(e))for(const u in e)e[u]&&(t+=u+" ");return t.trim()}const _i=Be("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function An(e){return!!e||e===""}const En=e=>!!(e&&e.__v_isRef===!0),wu=e=>he(e)?e:e==null?"":V(e)||de(e)&&(e.toString===dn||!k(e.toString))?En(e)?wu(e.value):JSON.stringify(e,gn,2):String(e),gn=(e,t)=>En(t)?gn(e,t.value):Rt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((u,[r,n],i)=>(u[Ar(r,i)+" =>"]=n,u),{})}:an(t)?{[`Set(${t.size})`]:[...t.values()].map(u=>Ar(u))}:Ft(t)?Ar(t):de(t)&&!V(t)&&!hn(t)?String(t):t,Ar=(e,t="")=>{var u;return Ft(e)?`Symbol(${(u=e.description)!=null?u:t})`:e};var vi={NODE_ENV:"production"};let Ne;class wi{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ne,!t&&Ne&&(this.index=(Ne.scopes||(Ne.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,u;if(this.scopes)for(t=0,u=this.scopes.length;t<u;t++)this.scopes[t].pause();for(t=0,u=this.effects.length;t<u;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,u;if(this.scopes)for(t=0,u=this.scopes.length;t<u;t++)this.scopes[t].resume();for(t=0,u=this.effects.length;t<u;t++)this.effects[t].resume()}}run(t){if(this._active){const u=Ne;try{return Ne=this,t()}finally{Ne=u}}}on(){Ne=this}off(){Ne=this.parent}stop(t){if(this._active){this._active=!1;let u,r;for(u=0,r=this.effects.length;u<r;u++)this.effects[u].stop();for(this.effects.length=0,u=0,r=this.cleanups.length;u<r;u++)this.cleanups[u]();if(this.cleanups.length=0,this.scopes){for(u=0,r=this.scopes.length;u<r;u++)this.scopes[u].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function Si(){return Ne}let oe;const Er=new WeakSet;class mn{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ne&&Ne.active&&Ne.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Er.has(this)&&(Er.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||yn(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,wn(this),Bn(this);const t=oe,u=ke;oe=this,ke=!0;try{return this.fn()}finally{xn(this),oe=t,ke=u,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)yr(t);this.deps=this.depsTail=void 0,wn(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Er.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){br(this)&&this.run()}get dirty(){return br(this)}}let bn=0,Zt,Qt;function yn(e,t=!1){if(e.flags|=8,t){e.next=Qt,Qt=e;return}e.next=Zt,Zt=e}function gr(){bn++}function mr(){if(--bn>0)return;if(Qt){let t=Qt;for(Qt=void 0;t;){const u=t.next;t.next=void 0,t.flags&=-9,t=u}}let e;for(;Zt;){let t=Zt;for(Zt=void 0;t;){const u=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=u}}if(e)throw e}function Bn(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function xn(e){let t,u=e.depsTail,r=u;for(;r;){const n=r.prevDep;r.version===-1?(r===u&&(u=n),yr(r),Oi(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=n}e.deps=t,e.depsTail=u}function br(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(_n(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function _n(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Xt))return;e.globalVersion=Xt;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!br(e)){e.flags&=-3;return}const u=oe,r=ke;oe=e,ke=!0;try{Bn(e);const n=e.fn(e._value);(t.version===0||_t(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{oe=u,ke=r,xn(e),e.flags&=-3}}function yr(e,t=!1){const{dep:u,prevSub:r,nextSub:n}=e;if(r&&(r.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=r,e.nextSub=void 0),u.subs===e&&(u.subs=r,!r&&u.computed)){u.computed.flags&=-5;for(let i=u.computed.deps;i;i=i.nextDep)yr(i,!0)}!t&&!--u.sc&&u.map&&u.map.delete(u.key)}function Oi(e){const{prevDep:t,nextDep:u}=e;t&&(t.nextDep=u,e.prevDep=void 0),u&&(u.prevDep=t,e.nextDep=void 0)}let ke=!0;const vn=[];function st(){vn.push(ke),ke=!1}function it(){const e=vn.pop();ke=e===void 0?!0:e}function wn(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const u=oe;oe=void 0;try{t()}finally{oe=u}}}let Xt=0;class Pi{constructor(t,u){this.sub=t,this.dep=u,this.version=u.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Sn{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!oe||!ke||oe===this.computed)return;let u=this.activeLink;if(u===void 0||u.sub!==oe)u=this.activeLink=new Pi(oe,this),oe.deps?(u.prevDep=oe.depsTail,oe.depsTail.nextDep=u,oe.depsTail=u):oe.deps=oe.depsTail=u,On(u);else if(u.version===-1&&(u.version=this.version,u.nextDep)){const r=u.nextDep;r.prevDep=u.prevDep,u.prevDep&&(u.prevDep.nextDep=r),u.prevDep=oe.depsTail,u.nextDep=void 0,oe.depsTail.nextDep=u,oe.depsTail=u,oe.deps===u&&(oe.deps=r)}return u}trigger(t){this.version++,Xt++,this.notify(t)}notify(t){gr();try{vi.NODE_ENV;for(let u=this.subs;u;u=u.prevSub)u.sub.notify()&&u.sub.dep.notify()}finally{mr()}}}function On(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)On(r)}const u=e.dep.subs;u!==e&&(e.prevSub=u,u&&(u.nextSub=e)),e.dep.subs=e}}const Su=new WeakMap,vt=Symbol(""),Br=Symbol(""),eu=Symbol("");function be(e,t,u){if(ke&&oe){let r=Su.get(e);r||Su.set(e,r=new Map);let n=r.get(u);n||(r.set(u,n=new Sn),n.map=r,n.key=u),n.track()}}function ot(e,t,u,r,n,i){const o=Su.get(e);if(!o){Xt++;return}const c=l=>{l&&l.trigger()};if(gr(),t==="clear")o.forEach(c);else{const l=V(e),d=l&&dr(u);if(l&&u==="length"){const a=Number(r);o.forEach((h,B)=>{(B==="length"||B===eu||!Ft(B)&&B>=a)&&c(h)})}else switch((u!==void 0||o.has(void 0))&&c(o.get(u)),d&&c(o.get(eu)),t){case"add":l?d&&c(o.get("length")):(c(o.get(vt)),Rt(e)&&c(o.get(Br)));break;case"delete":l||(c(o.get(vt)),Rt(e)&&c(o.get(Br)));break;case"set":Rt(e)&&c(o.get(vt));break}}mr()}function Ti(e,t){const u=Su.get(e);return u&&u.get(t)}function $t(e){const t=X(e);return t===e?t:(be(t,"iterate",eu),Ue(e)?t:t.map(Pe))}function Ou(e){return be(e=X(e),"iterate",eu),e}const Ii={__proto__:null,[Symbol.iterator](){return xr(this,Symbol.iterator,Pe)},concat(...e){return $t(this).concat(...e.map(t=>V(t)?$t(t):t))},entries(){return xr(this,"entries",e=>(e[1]=Pe(e[1]),e))},every(e,t){return ct(this,"every",e,t,void 0,arguments)},filter(e,t){return ct(this,"filter",e,t,u=>u.map(Pe),arguments)},find(e,t){return ct(this,"find",e,t,Pe,arguments)},findIndex(e,t){return ct(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ct(this,"findLast",e,t,Pe,arguments)},findLastIndex(e,t){return ct(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ct(this,"forEach",e,t,void 0,arguments)},includes(...e){return _r(this,"includes",e)},indexOf(...e){return _r(this,"indexOf",e)},join(e){return $t(this).join(e)},lastIndexOf(...e){return _r(this,"lastIndexOf",e)},map(e,t){return ct(this,"map",e,t,void 0,arguments)},pop(){return tu(this,"pop")},push(...e){return tu(this,"push",e)},reduce(e,...t){return Pn(this,"reduce",e,t)},reduceRight(e,...t){return Pn(this,"reduceRight",e,t)},shift(){return tu(this,"shift")},some(e,t){return ct(this,"some",e,t,void 0,arguments)},splice(...e){return tu(this,"splice",e)},toReversed(){return $t(this).toReversed()},toSorted(e){return $t(this).toSorted(e)},toSpliced(...e){return $t(this).toSpliced(...e)},unshift(...e){return tu(this,"unshift",e)},values(){return xr(this,"values",Pe)}};function xr(e,t,u){const r=Ou(e),n=r[t]();return r!==e&&!Ue(e)&&(n._next=n.next,n.next=()=>{const i=n._next();return i.value&&(i.value=u(i.value)),i}),n}const Ni=Array.prototype;function ct(e,t,u,r,n,i){const o=Ou(e),c=o!==e&&!Ue(e),l=o[t];if(l!==Ni[t]){const h=l.apply(e,i);return c?Pe(h):h}let d=u;o!==e&&(c?d=function(h,B){return u.call(this,Pe(h),B,e)}:u.length>2&&(d=function(h,B){return u.call(this,h,B,e)}));const a=l.call(o,d,r);return c&&n?n(a):a}function Pn(e,t,u,r){const n=Ou(e);let i=u;return n!==e&&(Ue(e)?u.length>3&&(i=function(o,c,l){return u.call(this,o,c,l,e)}):i=function(o,c,l){return u.call(this,o,Pe(c),l,e)}),n[t](i,...r)}function _r(e,t,u){const r=X(e);be(r,"iterate",eu);const n=r[t](...u);return(n===-1||n===!1)&&wr(u[0])?(u[0]=X(u[0]),r[t](...u)):n}function tu(e,t,u=[]){st(),gr();const r=X(e)[t].apply(e,u);return mr(),it(),r}const Ri=Be("__proto__,__v_isRef,__isVue"),Tn=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ft));function $i(e){Ft(e)||(e=String(e));const t=X(this);return be(t,"has",e),t.hasOwnProperty(e)}class In{constructor(t=!1,u=!1){this._isReadonly=t,this._isShallow=u}get(t,u,r){if(u==="__v_skip")return t.__v_skip;const n=this._isReadonly,i=this._isShallow;if(u==="__v_isReactive")return!n;if(u==="__v_isReadonly")return n;if(u==="__v_isShallow")return i;if(u==="__v_raw")return r===(n?i?Ln:jn:i?Mn:$n).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const o=V(t);if(!n){let l;if(o&&(l=Ii[u]))return l;if(u==="hasOwnProperty")return $i}const c=Reflect.get(t,u,Ae(t)?t:r);return(Ft(u)?Tn.has(u):Ri(u))||(n||be(t,"get",u),i)?c:Ae(c)?o&&dr(u)?c:c.value:de(c)?n?Vn(c):uu(c):c}}class Nn extends In{constructor(t=!1){super(!1,t)}set(t,u,r,n){let i=t[u];if(!this._isShallow){const l=jt(i);if(!Ue(r)&&!jt(r)&&(i=X(i),r=X(r)),!V(t)&&Ae(i)&&!Ae(r))return l?!1:(i.value=r,!0)}const o=V(t)&&dr(u)?Number(u)<t.length:re(t,u),c=Reflect.set(t,u,r,Ae(t)?t:n);return t===X(n)&&(o?_t(r,i)&&ot(t,"set",u,r):ot(t,"add",u,r)),c}deleteProperty(t,u){const r=re(t,u);t[u];const n=Reflect.deleteProperty(t,u);return n&&r&&ot(t,"delete",u,void 0),n}has(t,u){const r=Reflect.has(t,u);return(!Ft(u)||!Tn.has(u))&&be(t,"has",u),r}ownKeys(t){return be(t,"iterate",V(t)?"length":vt),Reflect.ownKeys(t)}}class Rn extends In{constructor(t=!1){super(!0,t)}set(t,u){return!0}deleteProperty(t,u){return!0}}const Mi=new Nn,ji=new Rn,Li=new Nn(!0),Vi=new Rn(!0),vr=e=>e,Pu=e=>Reflect.getPrototypeOf(e);function Hi(e,t,u){return function(...r){const n=this.__v_raw,i=X(n),o=Rt(i),c=e==="entries"||e===Symbol.iterator&&o,l=e==="keys"&&o,d=n[e](...r),a=u?vr:t?Sr:Pe;return!t&&be(i,"iterate",l?Br:vt),{next(){const{value:h,done:B}=d.next();return B?{value:h,done:B}:{value:c?[a(h[0]),a(h[1])]:a(h),done:B}},[Symbol.iterator](){return this}}}}function Tu(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Wi(e,t){const u={get(n){const i=this.__v_raw,o=X(i),c=X(n);e||(_t(n,c)&&be(o,"get",n),be(o,"get",c));const{has:l}=Pu(o),d=t?vr:e?Sr:Pe;if(l.call(o,n))return d(i.get(n));if(l.call(o,c))return d(i.get(c));i!==o&&i.get(n)},get size(){const n=this.__v_raw;return!e&&be(X(n),"iterate",vt),Reflect.get(n,"size",n)},has(n){const i=this.__v_raw,o=X(i),c=X(n);return e||(_t(n,c)&&be(o,"has",n),be(o,"has",c)),n===c?i.has(n):i.has(n)||i.has(c)},forEach(n,i){const o=this,c=o.__v_raw,l=X(c),d=t?vr:e?Sr:Pe;return!e&&be(l,"iterate",vt),c.forEach((a,h)=>n.call(i,d(a),d(h),o))}};return xe(u,e?{add:Tu("add"),set:Tu("set"),delete:Tu("delete"),clear:Tu("clear")}:{add(n){!t&&!Ue(n)&&!jt(n)&&(n=X(n));const i=X(this);return Pu(i).has.call(i,n)||(i.add(n),ot(i,"add",n,n)),this},set(n,i){!t&&!Ue(i)&&!jt(i)&&(i=X(i));const o=X(this),{has:c,get:l}=Pu(o);let d=c.call(o,n);d||(n=X(n),d=c.call(o,n));const a=l.call(o,n);return o.set(n,i),d?_t(i,a)&&ot(o,"set",n,i):ot(o,"add",n,i),this},delete(n){const i=X(this),{has:o,get:c}=Pu(i);let l=o.call(i,n);l||(n=X(n),l=o.call(i,n)),c&&c.call(i,n);const d=i.delete(n);return l&&ot(i,"delete",n,void 0),d},clear(){const n=X(this),i=n.size!==0,o=n.clear();return i&&ot(n,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(n=>{u[n]=Hi(n,e,t)}),u}function Iu(e,t){const u=Wi(e,t);return(r,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?r:Reflect.get(re(u,n)&&n in r?u:r,n,i)}const ki={get:Iu(!1,!1)},Ui={get:Iu(!1,!0)},Ki={get:Iu(!0,!1)},Gi={get:Iu(!0,!0)},$n=new WeakMap,Mn=new WeakMap,jn=new WeakMap,Ln=new WeakMap;function qi(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function zi(e){return e.__v_skip||!Object.isExtensible(e)?0:qi(Ai(e))}function uu(e){return jt(e)?e:Ru(e,!1,Mi,ki,$n)}function Ji(e){return Ru(e,!1,Li,Ui,Mn)}function Vn(e){return Ru(e,!0,ji,Ki,jn)}function Nu(e){return Ru(e,!0,Vi,Gi,Ln)}function Ru(e,t,u,r,n){if(!de(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=n.get(e);if(i)return i;const o=zi(e);if(o===0)return e;const c=new Proxy(e,o===2?r:u);return n.set(e,c),c}function Mt(e){return jt(e)?Mt(e.__v_raw):!!(e&&e.__v_isReactive)}function jt(e){return!!(e&&e.__v_isReadonly)}function Ue(e){return!!(e&&e.__v_isShallow)}function wr(e){return e?!!e.__v_raw:!1}function X(e){const t=e&&e.__v_raw;return t?X(t):e}function Yi(e){return!re(e,"__v_skip")&&Object.isExtensible(e)&&Cn(e,"__v_skip",!0),e}const Pe=e=>de(e)?uu(e):e,Sr=e=>de(e)?Vn(e):e;function Ae(e){return e?e.__v_isRef===!0:!1}function Zi(e){return Ae(e)?e.value:e}const Qi={get:(e,t,u)=>t==="__v_raw"?e:Zi(Reflect.get(e,t,u)),set:(e,t,u,r)=>{const n=e[t];return Ae(n)&&!Ae(u)?(n.value=u,!0):Reflect.set(e,t,u,r)}};function Hn(e){return Mt(e)?e:new Proxy(e,Qi)}function Xi(e){const t=V(e)?new Array(e.length):{};for(const u in e)t[u]=to(e,u);return t}class eo{constructor(t,u,r){this._object=t,this._key=u,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Ti(X(this._object),this._key)}}function to(e,t,u){const r=e[t];return Ae(r)?r:new eo(e,t,u)}class uo{constructor(t,u,r){this.fn=t,this.setter=u,this._value=void 0,this.dep=new Sn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Xt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!u,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&oe!==this)return yn(this,!0),!0}get value(){const t=this.dep.track();return _n(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function ro(e,t,u=!1){let r,n;return k(e)?r=e:(r=e.get,n=e.set),new uo(r,n,u)}const $u={},Mu=new WeakMap;let wt;function no(e,t=!1,u=wt){if(u){let r=Mu.get(u);r||Mu.set(u,r=[]),r.push(e)}}function so(e,t,u=Q){const{immediate:r,deep:n,once:i,scheduler:o,augmentJob:c,call:l}=u,d=w=>n?w:Ue(w)||n===!1||n===0?Et(w,1):Et(w);let a,h,B,E,R=!1,F=!1;if(Ae(e)?(h=()=>e.value,R=Ue(e)):Mt(e)?(h=()=>d(e),R=!0):V(e)?(F=!0,R=e.some(w=>Mt(w)||Ue(w)),h=()=>e.map(w=>{if(Ae(w))return w.value;if(Mt(w))return d(w);if(k(w))return l?l(w,2):w()})):k(e)?t?h=l?()=>l(e,2):e:h=()=>{if(B){st();try{B()}finally{it()}}const w=wt;wt=a;try{return l?l(e,3,[E]):e(E)}finally{wt=w}}:h=We,t&&n){const w=h,L=n===!0?1/0:n;h=()=>Et(w(),L)}const N=Si(),m=()=>{a.stop(),N&&N.active&&Dr(N.effects,a)};if(i&&t){const w=t;t=(...L)=>{w(...L),m()}}let T=F?new Array(e.length).fill($u):$u;const $=w=>{if(!(!(a.flags&1)||!a.dirty&&!w))if(t){const L=a.run();if(n||R||(F?L.some((J,U)=>_t(J,T[U])):_t(L,T))){B&&B();const J=wt;wt=a;try{const U=[L,T===$u?void 0:F&&T[0]===$u?[]:T,E];l?l(t,3,U):t(...U),T=L}finally{wt=J}}}else a.run()};return c&&c($),a=new mn(h),a.scheduler=o?()=>o($,!1):$,E=w=>no(w,!1,a),B=a.onStop=()=>{const w=Mu.get(a);if(w){if(l)l(w,4);else for(const L of w)L();Mu.delete(a)}},t?r?$(!0):T=a.run():o?o($.bind(null,!0),!0):a.run(),m.pause=a.pause.bind(a),m.resume=a.resume.bind(a),m.stop=m,m}function Et(e,t=1/0,u){if(t<=0||!de(e)||e.__v_skip||(u=u||new Set,u.has(e)))return e;if(u.add(e),t--,Ae(e))Et(e.value,t,u);else if(V(e))for(let r=0;r<e.length;r++)Et(e[r],t,u);else if(an(e)||Rt(e))e.forEach(r=>{Et(r,t,u)});else if(hn(e)){for(const r in e)Et(e[r],t,u);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Et(e[r],t,u)}return e}var gt={NODE_ENV:"production"};const ru=[];let Or=!1;function io(e,...t){if(Or)return;Or=!0,st();const u=ru.length?ru[ru.length-1].component:null,r=u&&u.appContext.config.warnHandler,n=oo();if(r)Lt(r,u,11,[e+t.map(i=>{var o,c;return(c=(o=i.toString)==null?void 0:o.call(i))!=null?c:JSON.stringify(i)}).join(""),u&&u.proxy,n.map(({vnode:i})=>`at <${Ts(u,i.type)}>`).join(`
`),n]);else{const i=[`[Vue warn]: ${e}`,...t];n.length&&i.push(`
`,...co(n)),console.warn(...i)}it(),Or=!1}function oo(){let e=ru[ru.length-1];if(!e)return[];const t=[];for(;e;){const u=t[0];u&&u.vnode===e?u.recurseCount++:t.push({vnode:e,recurseCount:0});const r=e.component&&e.component.parent;e=r&&r.vnode}return t}function co(e){const t=[];return e.forEach((u,r)=>{t.push(...r===0?[]:[`
`],...lo(u))}),t}function lo({vnode:e,recurseCount:t}){const u=t>0?`... (${t} recursive calls)`:"",r=e.component?e.component.parent==null:!1,n=` at <${Ts(e.component,e.type,r)}`,i=">"+u;return e.props?[n,...fo(e.props),i]:[n+i]}function fo(e){const t=[],u=Object.keys(e);return u.slice(0,3).forEach(r=>{t.push(...Wn(r,e[r]))}),u.length>3&&t.push(" ..."),t}function Wn(e,t,u){return he(t)?(t=JSON.stringify(t),u?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?u?t:[`${e}=${t}`]:Ae(t)?(t=Wn(e,X(t.value),!0),u?t:[`${e}=Ref<`,t,">"]):k(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=X(t),u?t:[`${e}=`,t])}function Lt(e,t,u,r){try{return r?e(...r):e()}catch(n){ju(n,t,u)}}function Je(e,t,u,r){if(k(e)){const n=Lt(e,t,u,r);return n&&Dn(n)&&n.catch(i=>{ju(i,t,u)}),n}if(V(e)){const n=[];for(let i=0;i<e.length;i++)n.push(Je(e[i],t,u,r));return n}}function ju(e,t,u,r=!0){const n=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||Q;if(t){let c=t.parent;const l=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${u}`;for(;c;){const a=c.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,l,d)===!1)return}c=c.parent}if(i){st(),Lt(i,null,10,[e,l,d]),it();return}}ao(e,u,n,r,o)}function ao(e,t,u,r=!0,n=!1){if(n)throw e;console.error(e)}const _e=[];let Ye=-1;const Vt=[];let mt=null,Ht=0;const kn=Promise.resolve();let Lu=null;function Do(e){const t=Lu||kn;return e?t.then(this?e.bind(this):e):t}function ho(e){let t=Ye+1,u=_e.length;for(;t<u;){const r=t+u>>>1,n=_e[r],i=nu(n);i<e||i===e&&n.flags&2?t=r+1:u=r}return t}function Pr(e){if(!(e.flags&1)){const t=nu(e),u=_e[_e.length-1];!u||!(e.flags&2)&&t>=nu(u)?_e.push(e):_e.splice(ho(t),0,e),e.flags|=1,Un()}}function Un(){Lu||(Lu=kn.then(qn))}function po(e){V(e)?Vt.push(...e):mt&&e.id===-1?mt.splice(Ht+1,0,e):e.flags&1||(Vt.push(e),e.flags|=1),Un()}function Kn(e,t,u=Ye+1){for(;u<_e.length;u++){const r=_e[u];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;_e.splice(u,1),u--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Gn(e){if(Vt.length){const t=[...new Set(Vt)].sort((u,r)=>nu(u)-nu(r));if(Vt.length=0,mt){mt.push(...t);return}for(mt=t,Ht=0;Ht<mt.length;Ht++){const u=mt[Ht];u.flags&4&&(u.flags&=-2),u.flags&8||u(),u.flags&=-2}mt=null,Ht=0}}const nu=e=>e.id==null?e.flags&2?-1:1/0:e.id;function qn(e){const t=We;try{for(Ye=0;Ye<_e.length;Ye++){const u=_e[Ye];u&&!(u.flags&8)&&(gt.NODE_ENV!=="production"&&t(u),u.flags&4&&(u.flags&=-2),Lt(u,u.i,u.i?15:14),u.flags&4||(u.flags&=-2))}}finally{for(;Ye<_e.length;Ye++){const u=_e[Ye];u&&(u.flags&=-2)}Ye=-1,_e.length=0,Gn(),Lu=null,(_e.length||Vt.length)&&qn()}}let Ze=null,zn=null;function Vu(e){const t=Ze;return Ze=e,zn=e&&e.type.__scopeId||null,t}function Co(e,t=Ze,u){if(!t||e._n)return e;const r=(...n)=>{r._d&&_s(-1);const i=Vu(t);let o;try{o=e(...n)}finally{Vu(i),r._d&&_s(1)}return o};return r._n=!0,r._c=!0,r._d=!0,r}function St(e,t,u,r){const n=e.dirs,i=t&&t.dirs;for(let o=0;o<n.length;o++){const c=n[o];i&&(c.oldValue=i[o].value);let l=c.dir[r];l&&(st(),Je(l,u,8,[e.el,c,e,t]),it())}}const Fo=Symbol("_vte"),Ao=e=>e.__isTeleport;function Tr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Tr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Jn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Hu(e,t,u,r,n=!1){if(V(e)){e.forEach((R,F)=>Hu(R,t&&(V(t)?t[F]:t),u,r,n));return}if(su(r)&&!n){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&Hu(e,t,u,r.component.subTree);return}const i=r.shapeFlag&4?Kr(r.component):r.el,o=n?null:i,{i:c,r:l}=e,d=t&&t.r,a=c.refs===Q?c.refs={}:c.refs,h=c.setupState,B=X(h),E=h===Q?()=>!1:R=>re(B,R);if(d!=null&&d!==l&&(he(d)?(a[d]=null,E(d)&&(h[d]=null)):Ae(d)&&(d.value=null)),k(l))Lt(l,c,12,[o,a]);else{const R=he(l),F=Ae(l);if(R||F){const N=()=>{if(e.f){const m=R?E(l)?h[l]:a[l]:l.value;n?V(m)&&Dr(m,i):V(m)?m.includes(i)||m.push(i):R?(a[l]=[i],E(l)&&(h[l]=a[l])):(l.value=[i],e.k&&(a[e.k]=l.value))}else R?(a[l]=o,E(l)&&(h[l]=o)):F&&(l.value=o,e.k&&(a[e.k]=o))};o?(N.id=-1,Re(N,u)):N()}}}vu().requestIdleCallback,vu().cancelIdleCallback;const su=e=>!!e.type.__asyncLoader,Yn=e=>e.type.__isKeepAlive;function Eo(e,t){Zn(e,"a",t)}function go(e,t){Zn(e,"da",t)}function Zn(e,t,u=Ee){const r=e.__wdc||(e.__wdc=()=>{let n=u;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Wu(t,r,u),u){let n=u.parent;for(;n&&n.parent;)Yn(n.parent.vnode)&&mo(r,t,u,n),n=n.parent}}function mo(e,t,u,r){const n=Wu(t,e,r,!0);Xn(()=>{Dr(r[t],n)},u)}function Wu(e,t,u=Ee,r=!1){if(u){const n=u[e]||(u[e]=[]),i=t.__weh||(t.__weh=(...o)=>{st();const c=du(u),l=Je(t,u,e,o);return c(),it(),l});return r?n.unshift(i):n.push(i),i}}const lt=e=>(t,u=Ee)=>{(!hu||e==="sp")&&Wu(e,(...r)=>t(...r),u)},bo=lt("bm"),Qn=lt("m"),yo=lt("bu"),Bo=lt("u"),xo=lt("bum"),Xn=lt("um"),_o=lt("sp"),vo=lt("rtg"),wo=lt("rtc");function So(e,t=Ee){Wu("ec",e,t)}const Oo=Symbol.for("v-ndc");function Po(e,t,u,r){let n;const i=u,o=V(e);if(o||he(e)){const c=o&&Mt(e);let l=!1;c&&(l=!Ue(e),e=Ou(e)),n=new Array(e.length);for(let d=0,a=e.length;d<a;d++)n[d]=t(l?Pe(e[d]):e[d],d,void 0,i)}else if(typeof e=="number"){n=new Array(e);for(let c=0;c<e;c++)n[c]=t(c+1,c,void 0,i)}else if(de(e))if(e[Symbol.iterator])n=Array.from(e,(c,l)=>t(c,l,void 0,i));else{const c=Object.keys(e);n=new Array(c.length);for(let l=0,d=c.length;l<d;l++){const a=c[l];n[l]=t(e[a],a,l,i)}}else n=[];return n}const Ir=e=>e?Ss(e)?Kr(e):Ir(e.parent):null,iu=xe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ir(e.parent),$root:e=>Ir(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>rs(e),$forceUpdate:e=>e.f||(e.f=()=>{Pr(e.update)}),$nextTick:e=>e.n||(e.n=Do.bind(e.proxy)),$watch:e=>Xo.bind(e)}),Nr=(e,t)=>e!==Q&&!e.__isScriptSetup&&re(e,t),To={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:u,setupState:r,data:n,props:i,accessCache:o,type:c,appContext:l}=e;let d;if(t[0]!=="$"){const E=o[t];if(E!==void 0)switch(E){case 1:return r[t];case 2:return n[t];case 4:return u[t];case 3:return i[t]}else{if(Nr(r,t))return o[t]=1,r[t];if(n!==Q&&re(n,t))return o[t]=2,n[t];if((d=e.propsOptions[0])&&re(d,t))return o[t]=3,i[t];if(u!==Q&&re(u,t))return o[t]=4,u[t];Rr&&(o[t]=0)}}const a=iu[t];let h,B;if(a)return t==="$attrs"&&be(e.attrs,"get",""),a(e);if((h=c.__cssModules)&&(h=h[t]))return h;if(u!==Q&&re(u,t))return o[t]=4,u[t];if(B=l.config.globalProperties,re(B,t))return B[t]},set({_:e},t,u){const{data:r,setupState:n,ctx:i}=e;return Nr(n,t)?(n[t]=u,!0):r!==Q&&re(r,t)?(r[t]=u,!0):re(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=u,!0)},has({_:{data:e,setupState:t,accessCache:u,ctx:r,appContext:n,propsOptions:i}},o){let c;return!!u[o]||e!==Q&&re(e,o)||Nr(t,o)||(c=i[0])&&re(c,o)||re(r,o)||re(iu,o)||re(n.config.globalProperties,o)},defineProperty(e,t,u){return u.get!=null?e._.accessCache[t]=0:re(u,"value")&&this.set(e,t,u.value,null),Reflect.defineProperty(e,t,u)}};function es(e){return V(e)?e.reduce((t,u)=>(t[u]=null,t),{}):e}let Rr=!0;function Io(e){const t=rs(e),u=e.proxy,r=e.ctx;Rr=!1,t.beforeCreate&&ts(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:o,watch:c,provide:l,inject:d,created:a,beforeMount:h,mounted:B,beforeUpdate:E,updated:R,activated:F,deactivated:N,beforeDestroy:m,beforeUnmount:T,destroyed:$,unmounted:w,render:L,renderTracked:J,renderTriggered:U,errorCaptured:ze,serverPrefetch:It,expose:Ve,inheritAttrs:ht,components:qt,directives:zt,filters:bu}=t;if(d&&No(d,r,null),o)for(const ae in o){const ue=o[ae];k(ue)&&(r[ae]=ue.bind(u))}if(n){const ae=n.call(u,u);de(ae)&&(e.data=uu(ae))}if(Rr=!0,i)for(const ae in i){const ue=i[ae],pt=k(ue)?ue.bind(u,u):k(ue.get)?ue.get.bind(u,u):We,Jt=!k(ue)&&k(ue.set)?ue.set.bind(u):We,Ct=Bc({get:pt,set:Jt});Object.defineProperty(r,ae,{enumerable:!0,configurable:!0,get:()=>Ct.value,set:x=>Ct.value=x})}if(c)for(const ae in c)us(c[ae],r,u,ae);if(l){const ae=k(l)?l.call(u):l;Reflect.ownKeys(ae).forEach(ue=>{Vo(ue,ae[ue])})}a&&ts(a,e,"c");function me(ae,ue){V(ue)?ue.forEach(pt=>ae(pt.bind(u))):ue&&ae(ue.bind(u))}if(me(bo,h),me(Qn,B),me(yo,E),me(Bo,R),me(Eo,F),me(go,N),me(So,ze),me(wo,J),me(vo,U),me(xo,T),me(Xn,w),me(_o,It),V(Ve))if(Ve.length){const ae=e.exposed||(e.exposed={});Ve.forEach(ue=>{Object.defineProperty(ae,ue,{get:()=>u[ue],set:pt=>u[ue]=pt})})}else e.exposed||(e.exposed={});L&&e.render===We&&(e.render=L),ht!=null&&(e.inheritAttrs=ht),qt&&(e.components=qt),zt&&(e.directives=zt),It&&Jn(e)}function No(e,t,u=We){V(e)&&(e=$r(e));for(const r in e){const n=e[r];let i;de(n)?"default"in n?i=Uu(n.from||r,n.default,!0):i=Uu(n.from||r):i=Uu(n),Ae(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[r]=i}}function ts(e,t,u){Je(V(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,u)}function us(e,t,u,r){let n=r.includes(".")?ms(u,r):()=>u[r];if(he(e)){const i=t[e];k(i)&&cu(n,i)}else if(k(e))cu(n,e.bind(u));else if(de(e))if(V(e))e.forEach(i=>us(i,t,u,r));else{const i=k(e.handler)?e.handler.bind(u):t[e.handler];k(i)&&cu(n,i,e)}}function rs(e){const t=e.type,{mixins:u,extends:r}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,c=i.get(t);let l;return c?l=c:!n.length&&!u&&!r?l=t:(l={},n.length&&n.forEach(d=>ku(l,d,o,!0)),ku(l,t,o)),de(t)&&i.set(t,l),l}function ku(e,t,u,r=!1){const{mixins:n,extends:i}=t;i&&ku(e,i,u,!0),n&&n.forEach(o=>ku(e,o,u,!0));for(const o in t)if(!(r&&o==="expose")){const c=Ro[o]||u&&u[o];e[o]=c?c(e[o],t[o]):t[o]}return e}const Ro={data:ns,props:ss,emits:ss,methods:ou,computed:ou,beforeCreate:ve,created:ve,beforeMount:ve,mounted:ve,beforeUpdate:ve,updated:ve,beforeDestroy:ve,beforeUnmount:ve,destroyed:ve,unmounted:ve,activated:ve,deactivated:ve,errorCaptured:ve,serverPrefetch:ve,components:ou,directives:ou,watch:Mo,provide:ns,inject:$o};function ns(e,t){return t?e?function(){return xe(k(e)?e.call(this,this):e,k(t)?t.call(this,this):t)}:t:e}function $o(e,t){return ou($r(e),$r(t))}function $r(e){if(V(e)){const t={};for(let u=0;u<e.length;u++)t[e[u]]=e[u];return t}return e}function ve(e,t){return e?[...new Set([].concat(e,t))]:t}function ou(e,t){return e?xe(Object.create(null),e,t):t}function ss(e,t){return e?V(e)&&V(t)?[...new Set([...e,...t])]:xe(Object.create(null),es(e),es(t??{})):t}function Mo(e,t){if(!e)return t;if(!t)return e;const u=xe(Object.create(null),e);for(const r in t)u[r]=ve(e[r],t[r]);return u}function is(){return{app:null,config:{isNativeTag:Ci,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let jo=0;function Lo(e,t){return function(r,n=null){k(r)||(r=xe({},r)),n!=null&&!de(n)&&(n=null);const i=is(),o=new WeakSet,c=[];let l=!1;const d=i.app={_uid:jo++,_component:r,_props:n,_container:null,_context:i,_instance:null,version:_c,get config(){return i.config},set config(a){},use(a,...h){return o.has(a)||(a&&k(a.install)?(o.add(a),a.install(d,...h)):k(a)&&(o.add(a),a(d,...h))),d},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),d},component(a,h){return h?(i.components[a]=h,d):i.components[a]},directive(a,h){return h?(i.directives[a]=h,d):i.directives[a]},mount(a,h,B){if(!l){const E=d._ceVNode||Ge(r,n);return E.appContext=i,B===!0?B="svg":B===!1&&(B=void 0),e(E,a,B),l=!0,d._container=a,a.__vue_app__=d,Kr(E.component)}},onUnmount(a){c.push(a)},unmount(){l&&(Je(c,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,h){return i.provides[a]=h,d},runWithContext(a){const h=Wt;Wt=d;try{return a()}finally{Wt=h}}};return d}}let Wt=null;function Vo(e,t){if(Ee){let u=Ee.provides;const r=Ee.parent&&Ee.parent.provides;r===u&&(u=Ee.provides=Object.create(r)),u[e]=t}}function Uu(e,t,u=!1){const r=Ee||Ze;if(r||Wt){const n=Wt?Wt._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return u&&k(t)?t.call(r&&r.proxy):t}}const os={},cs=()=>Object.create(os),ls=e=>Object.getPrototypeOf(e)===os;function Ho(e,t,u,r=!1){const n={},i=cs();e.propsDefaults=Object.create(null),fs(e,t,n,i);for(const o in e.propsOptions[0])o in n||(n[o]=void 0);u?e.props=r?n:Ji(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function Wo(e,t,u,r){const{props:n,attrs:i,vnode:{patchFlag:o}}=e,c=X(n),[l]=e.propsOptions;let d=!1;if((r||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let B=a[h];if(Ku(e.emitsOptions,B))continue;const E=t[B];if(l)if(re(i,B))E!==i[B]&&(i[B]=E,d=!0);else{const R=At(B);n[R]=Mr(l,c,R,E,e,!1)}else E!==i[B]&&(i[B]=E,d=!0)}}}else{fs(e,t,n,i)&&(d=!0);let a;for(const h in c)(!t||!re(t,h)&&((a=xt(h))===h||!re(t,a)))&&(l?u&&(u[h]!==void 0||u[a]!==void 0)&&(n[h]=Mr(l,c,h,void 0,e,!0)):delete n[h]);if(i!==c)for(const h in i)(!t||!re(t,h))&&(delete i[h],d=!0)}d&&ot(e.attrs,"set","")}function fs(e,t,u,r){const[n,i]=e.propsOptions;let o=!1,c;if(t)for(let l in t){if(Yt(l))continue;const d=t[l];let a;n&&re(n,a=At(l))?!i||!i.includes(a)?u[a]=d:(c||(c={}))[a]=d:Ku(e.emitsOptions,l)||(!(l in r)||d!==r[l])&&(r[l]=d,o=!0)}if(i){const l=X(u),d=c||Q;for(let a=0;a<i.length;a++){const h=i[a];u[h]=Mr(n,l,h,d[h],e,!re(d,h))}}return o}function Mr(e,t,u,r,n,i){const o=e[u];if(o!=null){const c=re(o,"default");if(c&&r===void 0){const l=o.default;if(o.type!==Function&&!o.skipFactory&&k(l)){const{propsDefaults:d}=n;if(u in d)r=d[u];else{const a=du(n);r=d[u]=l.call(null,t),a()}}else r=l;n.ce&&n.ce._setProp(u,r)}o[0]&&(i&&!c?r=!1:o[1]&&(r===""||r===xt(u))&&(r=!0))}return r}const ko=new WeakMap;function as(e,t,u=!1){const r=u?ko:t.propsCache,n=r.get(e);if(n)return n;const i=e.props,o={},c=[];let l=!1;if(!k(e)){const a=h=>{l=!0;const[B,E]=as(h,t,!0);xe(o,B),E&&c.push(...E)};!u&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!l)return de(e)&&r.set(e,Nt),Nt;if(V(i))for(let a=0;a<i.length;a++){const h=At(i[a]);Ds(h)&&(o[h]=Q)}else if(i)for(const a in i){const h=At(a);if(Ds(h)){const B=i[a],E=o[h]=V(B)||k(B)?{type:B}:xe({},B),R=E.type;let F=!1,N=!0;if(V(R))for(let m=0;m<R.length;++m){const T=R[m],$=k(T)&&T.name;if($==="Boolean"){F=!0;break}else $==="String"&&(N=!1)}else F=k(R)&&R.name==="Boolean";E[0]=F,E[1]=N,(F||re(E,"default"))&&c.push(h)}}const d=[o,c];return de(e)&&r.set(e,d),d}function Ds(e){return e[0]!=="$"&&!Yt(e)}const ds=e=>e[0]==="_"||e==="$stable",jr=e=>V(e)?e.map(Qe):[Qe(e)],Uo=(e,t,u)=>{if(t._n)return t;const r=Co((...n)=>(gt.NODE_ENV!=="production"&&Ee&&(!u||(u.root,Ee.root)),jr(t(...n))),u);return r._c=!1,r},hs=(e,t,u)=>{const r=e._ctx;for(const n in e){if(ds(n))continue;const i=e[n];if(k(i))t[n]=Uo(n,i,r);else if(i!=null){const o=jr(i);t[n]=()=>o}}},ps=(e,t)=>{const u=jr(t);e.slots.default=()=>u},Cs=(e,t,u)=>{for(const r in t)(u||r!=="_")&&(e[r]=t[r])},Ko=(e,t,u)=>{const r=e.slots=cs();if(e.vnode.shapeFlag&32){const n=t._;n?(Cs(r,t,u),u&&Cn(r,"_",n,!0)):hs(t,r)}else t&&ps(e,t)},Go=(e,t,u)=>{const{vnode:r,slots:n}=e;let i=!0,o=Q;if(r.shapeFlag&32){const c=t._;c?u&&c===1?i=!1:Cs(n,t,u):(i=!t.$stable,hs(t,n)),o=t}else t&&(ps(e,t),o={default:1});if(i)for(const c in n)!ds(c)&&o[c]==null&&delete n[c]},Re=ic;function qo(e){return zo(e)}function zo(e,t){const u=vu();u.__VUE__=!0;const{insert:r,remove:n,patchProp:i,createElement:o,createText:c,createComment:l,setText:d,setElementText:a,parentNode:h,nextSibling:B,setScopeId:E=We,insertStaticContent:R}=e,F=(f,D,p,b=null,A=null,g=null,S=void 0,v=null,_=!!D.dynamicChildren)=>{if(f===D)return;f&&!Du(f,D)&&(b=ne(f),x(f,A,g,!0),f=null),D.patchFlag===-2&&(_=!1,D.dynamicChildren=null);const{type:y,ref:j,shapeFlag:P}=D;switch(y){case Gu:N(f,D,p,b);break;case lu:m(f,D,p,b);break;case Vr:f==null&&T(D,p,b,S);break;case Ke:qt(f,D,p,b,A,g,S,v,_);break;default:P&1?L(f,D,p,b,A,g,S,v,_):P&6?zt(f,D,p,b,A,g,S,v,_):(P&64||P&128)&&y.process(f,D,p,b,A,g,S,v,_,De)}j!=null&&A&&Hu(j,f&&f.ref,g,D||f,!D)},N=(f,D,p,b)=>{if(f==null)r(D.el=c(D.children),p,b);else{const A=D.el=f.el;D.children!==f.children&&d(A,D.children)}},m=(f,D,p,b)=>{f==null?r(D.el=l(D.children||""),p,b):D.el=f.el},T=(f,D,p,b)=>{[f.el,f.anchor]=R(f.children,D,p,b,f.el,f.anchor)},$=({el:f,anchor:D},p,b)=>{let A;for(;f&&f!==D;)A=B(f),r(f,p,b),f=A;r(D,p,b)},w=({el:f,anchor:D})=>{let p;for(;f&&f!==D;)p=B(f),n(f),f=p;n(D)},L=(f,D,p,b,A,g,S,v,_)=>{D.type==="svg"?S="svg":D.type==="math"&&(S="mathml"),f==null?J(D,p,b,A,g,S,v,_):It(f,D,A,g,S,v,_)},J=(f,D,p,b,A,g,S,v)=>{let _,y;const{props:j,shapeFlag:P,transition:M,dirs:H}=f;if(_=f.el=o(f.type,g,j&&j.is,j),P&8?a(_,f.children):P&16&&ze(f.children,_,null,b,A,Lr(f,g),S,v),H&&St(f,null,b,"created"),U(_,f,f.scopeId,S,b),j){for(const ce in j)ce!=="value"&&!Yt(ce)&&i(_,ce,null,j[ce],g,b);"value"in j&&i(_,"value",null,j.value,g),(y=j.onVnodeBeforeMount)&&Xe(y,b,f)}H&&St(f,null,b,"beforeMount");const z=Jo(A,M);z&&M.beforeEnter(_),r(_,D,p),((y=j&&j.onVnodeMounted)||z||H)&&Re(()=>{y&&Xe(y,b,f),z&&M.enter(_),H&&St(f,null,b,"mounted")},A)},U=(f,D,p,b,A)=>{if(p&&E(f,p),b)for(let g=0;g<b.length;g++)E(f,b[g]);if(A){let g=A.subTree;if(D===g||xs(g.type)&&(g.ssContent===D||g.ssFallback===D)){const S=A.vnode;U(f,S,S.scopeId,S.slotScopeIds,A.parent)}}},ze=(f,D,p,b,A,g,S,v,_=0)=>{for(let y=_;y<f.length;y++){const j=f[y]=v?bt(f[y]):Qe(f[y]);F(null,j,D,p,b,A,g,S,v)}},It=(f,D,p,b,A,g,S)=>{const v=D.el=f.el;let{patchFlag:_,dynamicChildren:y,dirs:j}=D;_|=f.patchFlag&16;const P=f.props||Q,M=D.props||Q;let H;if(p&&Ot(p,!1),(H=M.onVnodeBeforeUpdate)&&Xe(H,p,D,f),j&&St(D,f,p,"beforeUpdate"),p&&Ot(p,!0),(P.innerHTML&&M.innerHTML==null||P.textContent&&M.textContent==null)&&a(v,""),y?Ve(f.dynamicChildren,y,v,p,b,Lr(D,A),g):S||ue(f,D,v,null,p,b,Lr(D,A),g,!1),_>0){if(_&16)ht(v,P,M,p,A);else if(_&2&&P.class!==M.class&&i(v,"class",null,M.class,A),_&4&&i(v,"style",P.style,M.style,A),_&8){const z=D.dynamicProps;for(let ce=0;ce<z.length;ce++){const se=z[ce],je=P[se],Ie=M[se];(Ie!==je||se==="value")&&i(v,se,je,Ie,A,p)}}_&1&&f.children!==D.children&&a(v,D.children)}else!S&&y==null&&ht(v,P,M,p,A);((H=M.onVnodeUpdated)||j)&&Re(()=>{H&&Xe(H,p,D,f),j&&St(D,f,p,"updated")},b)},Ve=(f,D,p,b,A,g,S)=>{for(let v=0;v<D.length;v++){const _=f[v],y=D[v],j=_.el&&(_.type===Ke||!Du(_,y)||_.shapeFlag&70)?h(_.el):p;F(_,y,j,null,b,A,g,S,!0)}},ht=(f,D,p,b,A)=>{if(D!==p){if(D!==Q)for(const g in D)!Yt(g)&&!(g in p)&&i(f,g,D[g],null,A,b);for(const g in p){if(Yt(g))continue;const S=p[g],v=D[g];S!==v&&g!=="value"&&i(f,g,v,S,A,b)}"value"in p&&i(f,"value",D.value,p.value,A)}},qt=(f,D,p,b,A,g,S,v,_)=>{const y=D.el=f?f.el:c(""),j=D.anchor=f?f.anchor:c("");let{patchFlag:P,dynamicChildren:M,slotScopeIds:H}=D;H&&(v=v?v.concat(H):H),f==null?(r(y,p,b),r(j,p,b),ze(D.children||[],p,j,A,g,S,v,_)):P>0&&P&64&&M&&f.dynamicChildren?(Ve(f.dynamicChildren,M,p,A,g,S,v),(D.key!=null||A&&D===A.subTree)&&Fs(f,D,!0)):ue(f,D,p,j,A,g,S,v,_)},zt=(f,D,p,b,A,g,S,v,_)=>{D.slotScopeIds=v,f==null?D.shapeFlag&512?A.ctx.activate(D,p,b,S,_):bu(D,p,b,A,g,S,_):lr(f,D,_)},bu=(f,D,p,b,A,g,S)=>{const v=f.component=pc(f,b,A);if(Yn(f)&&(v.ctx.renderer=De),Cc(v,!1,S),v.asyncDep){if(A&&A.registerDep(v,me,S),!f.el){const _=v.subTree=Ge(lu);m(null,_,D,p)}}else me(v,f,D,p,A,g,S)},lr=(f,D,p)=>{const b=D.component=f.component;if(nc(f,D,p))if(b.asyncDep&&!b.asyncResolved){ae(b,D,p);return}else b.next=D,b.update();else D.el=f.el,b.vnode=D},me=(f,D,p,b,A,g,S)=>{const v=()=>{if(f.isMounted){let{next:P,bu:M,u:H,parent:z,vnode:ce}=f;{const rt=As(f);if(rt){P&&(P.el=ce.el,ae(f,P,S)),rt.asyncDep.then(()=>{f.isUnmounted||v()});return}}let se=P,je;Ot(f,!1),P?(P.el=ce.el,ae(f,P,S)):P=ce,M&&pr(M),(je=P.props&&P.props.onVnodeBeforeUpdate)&&Xe(je,z,P,ce),Ot(f,!0);const Ie=ys(f),ut=f.subTree;f.subTree=Ie,F(ut,Ie,h(ut.el),ne(ut),f,A,g),P.el=Ie.el,se===null&&sc(f,Ie.el),H&&Re(H,A),(je=P.props&&P.props.onVnodeUpdated)&&Re(()=>Xe(je,z,P,ce),A)}else{let P;const{el:M,props:H}=D,{bm:z,m:ce,parent:se,root:je,type:Ie}=f,ut=su(D);Ot(f,!1),z&&pr(z),!ut&&(P=H&&H.onVnodeBeforeMount)&&Xe(P,se,D),Ot(f,!0);{je.ce&&je.ce._injectChildStyle(Ie);const rt=f.subTree=ys(f);F(null,rt,p,b,f,A,g),D.el=rt.el}if(ce&&Re(ce,A),!ut&&(P=H&&H.onVnodeMounted)){const rt=D;Re(()=>Xe(P,se,rt),A)}(D.shapeFlag&256||se&&su(se.vnode)&&se.vnode.shapeFlag&256)&&f.a&&Re(f.a,A),f.isMounted=!0,D=p=b=null}};f.scope.on();const _=f.effect=new mn(v);f.scope.off();const y=f.update=_.run.bind(_),j=f.job=_.runIfDirty.bind(_);j.i=f,j.id=f.uid,_.scheduler=()=>Pr(j),Ot(f,!0),y()},ae=(f,D,p)=>{D.component=f;const b=f.vnode.props;f.vnode=D,f.next=null,Wo(f,D.props,b,p),Go(f,D.children,p),st(),Kn(f),it()},ue=(f,D,p,b,A,g,S,v,_=!1)=>{const y=f&&f.children,j=f?f.shapeFlag:0,P=D.children,{patchFlag:M,shapeFlag:H}=D;if(M>0){if(M&128){Jt(y,P,p,b,A,g,S,v,_);return}else if(M&256){pt(y,P,p,b,A,g,S,v,_);return}}H&8?(j&16&&q(y,A,g),P!==y&&a(p,P)):j&16?H&16?Jt(y,P,p,b,A,g,S,v,_):q(y,A,g,!0):(j&8&&a(p,""),H&16&&ze(P,p,b,A,g,S,v,_))},pt=(f,D,p,b,A,g,S,v,_)=>{f=f||Nt,D=D||Nt;const y=f.length,j=D.length,P=Math.min(y,j);let M;for(M=0;M<P;M++){const H=D[M]=_?bt(D[M]):Qe(D[M]);F(f[M],H,p,null,A,g,S,v,_)}y>j?q(f,A,g,!0,!1,P):ze(D,p,b,A,g,S,v,_,P)},Jt=(f,D,p,b,A,g,S,v,_)=>{let y=0;const j=D.length;let P=f.length-1,M=j-1;for(;y<=P&&y<=M;){const H=f[y],z=D[y]=_?bt(D[y]):Qe(D[y]);if(Du(H,z))F(H,z,p,null,A,g,S,v,_);else break;y++}for(;y<=P&&y<=M;){const H=f[P],z=D[M]=_?bt(D[M]):Qe(D[M]);if(Du(H,z))F(H,z,p,null,A,g,S,v,_);else break;P--,M--}if(y>P){if(y<=M){const H=M+1,z=H<j?D[H].el:b;for(;y<=M;)F(null,D[y]=_?bt(D[y]):Qe(D[y]),p,z,A,g,S,v,_),y++}}else if(y>M)for(;y<=P;)x(f[y],A,g,!0),y++;else{const H=y,z=y,ce=new Map;for(y=z;y<=M;y++){const Le=D[y]=_?bt(D[y]):Qe(D[y]);Le.key!=null&&ce.set(Le.key,y)}let se,je=0;const Ie=M-z+1;let ut=!1,rt=0;const yu=new Array(Ie);for(y=0;y<Ie;y++)yu[y]=0;for(y=H;y<=P;y++){const Le=f[y];if(je>=Ie){x(Le,A,g,!0);continue}let nt;if(Le.key!=null)nt=ce.get(Le.key);else for(se=z;se<=M;se++)if(yu[se-z]===0&&Du(Le,D[se])){nt=se;break}nt===void 0?x(Le,A,g,!0):(yu[nt-z]=y+1,nt>=rt?rt=nt:ut=!0,F(Le,D[nt],p,null,A,g,S,v,_),je++)}const hi=ut?Yo(yu):Nt;for(se=hi.length-1,y=Ie-1;y>=0;y--){const Le=z+y,nt=D[Le],pi=Le+1<j?D[Le+1].el:b;yu[y]===0?F(null,nt,p,pi,A,g,S,v,_):ut&&(se<0||y!==hi[se]?Ct(nt,p,pi,2):se--)}}},Ct=(f,D,p,b,A=null)=>{const{el:g,type:S,transition:v,children:_,shapeFlag:y}=f;if(y&6){Ct(f.component.subTree,D,p,b);return}if(y&128){f.suspense.move(D,p,b);return}if(y&64){S.move(f,D,p,De);return}if(S===Ke){r(g,D,p);for(let P=0;P<_.length;P++)Ct(_[P],D,p,b);r(f.anchor,D,p);return}if(S===Vr){$(f,D,p);return}if(b!==2&&y&1&&v)if(b===0)v.beforeEnter(g),r(g,D,p),Re(()=>v.enter(g),A);else{const{leave:P,delayLeave:M,afterLeave:H}=v,z=()=>r(g,D,p),ce=()=>{P(g,()=>{z(),H&&H()})};M?M(g,z,ce):ce()}else r(g,D,p)},x=(f,D,p,b=!1,A=!1)=>{const{type:g,props:S,ref:v,children:_,dynamicChildren:y,shapeFlag:j,patchFlag:P,dirs:M,cacheIndex:H}=f;if(P===-2&&(A=!1),v!=null&&Hu(v,null,p,f,!0),H!=null&&(D.renderCache[H]=void 0),j&256){D.ctx.deactivate(f);return}const z=j&1&&M,ce=!su(f);let se;if(ce&&(se=S&&S.onVnodeBeforeUnmount)&&Xe(se,D,f),j&6)Y(f.component,p,b);else{if(j&128){f.suspense.unmount(p,b);return}z&&St(f,null,D,"beforeUnmount"),j&64?f.type.remove(f,D,p,De,b):y&&!y.hasOnce&&(g!==Ke||P>0&&P&64)?q(y,D,p,!1,!0):(g===Ke&&P&384||!A&&j&16)&&q(_,D,p),b&&O(f)}(ce&&(se=S&&S.onVnodeUnmounted)||z)&&Re(()=>{se&&Xe(se,D,f),z&&St(f,null,D,"unmounted")},p)},O=f=>{const{type:D,el:p,anchor:b,transition:A}=f;if(D===Ke){I(p,b);return}if(D===Vr){w(f);return}const g=()=>{n(p),A&&!A.persisted&&A.afterLeave&&A.afterLeave()};if(f.shapeFlag&1&&A&&!A.persisted){const{leave:S,delayLeave:v}=A,_=()=>S(p,g);v?v(f.el,g,_):_()}else g()},I=(f,D)=>{let p;for(;f!==D;)p=B(f),n(f),f=p;n(D)},Y=(f,D,p)=>{const{bum:b,scope:A,job:g,subTree:S,um:v,m:_,a:y}=f;Es(_),Es(y),b&&pr(b),A.stop(),g&&(g.flags|=8,x(S,f,D,p)),v&&Re(v,D),Re(()=>{f.isUnmounted=!0},D),D&&D.pendingBranch&&!D.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===D.pendingId&&(D.deps--,D.deps===0&&D.resolve())},q=(f,D,p,b=!1,A=!1,g=0)=>{for(let S=g;S<f.length;S++)x(f[S],D,p,b,A)},ne=f=>{if(f.shapeFlag&6)return ne(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const D=B(f.anchor||f.el),p=D&&D[Fo];return p?B(p):D};let ie=!1;const Z=(f,D,p)=>{f==null?D._vnode&&x(D._vnode,null,null,!0):F(D._vnode||null,f,D,null,null,null,p),D._vnode=f,ie||(ie=!0,Kn(),Gn(),ie=!1)},De={p:F,um:x,m:Ct,r:O,mt:bu,mc:ze,pc:ue,pbc:Ve,n:ne,o:e};return{render:Z,hydrate:void 0,createApp:Lo(Z)}}function Lr({type:e,props:t},u){return u==="svg"&&e==="foreignObject"||u==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:u}function Ot({effect:e,job:t},u){u?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Jo(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Fs(e,t,u=!1){const r=e.children,n=t.children;if(V(r)&&V(n))for(let i=0;i<r.length;i++){const o=r[i];let c=n[i];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=n[i]=bt(n[i]),c.el=o.el),!u&&c.patchFlag!==-2&&Fs(o,c)),c.type===Gu&&(c.el=o.el)}}function Yo(e){const t=e.slice(),u=[0];let r,n,i,o,c;const l=e.length;for(r=0;r<l;r++){const d=e[r];if(d!==0){if(n=u[u.length-1],e[n]<d){t[r]=n,u.push(r);continue}for(i=0,o=u.length-1;i<o;)c=i+o>>1,e[u[c]]<d?i=c+1:o=c;d<e[u[i]]&&(i>0&&(t[r]=u[i-1]),u[i]=r)}}for(i=u.length,o=u[i-1];i-- >0;)u[i]=o,o=t[o];return u}function As(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:As(t)}function Es(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Zo=Symbol.for("v-scx"),Qo=()=>Uu(Zo);function cu(e,t,u){return gs(e,t,u)}function gs(e,t,u=Q){const{immediate:r,deep:n,flush:i,once:o}=u,c=xe({},u),l=t&&r||!t&&i!=="post";let d;if(hu){if(i==="sync"){const E=Qo();d=E.__watcherHandles||(E.__watcherHandles=[])}else if(!l){const E=()=>{};return E.stop=We,E.resume=We,E.pause=We,E}}const a=Ee;c.call=(E,R,F)=>Je(E,a,R,F);let h=!1;i==="post"?c.scheduler=E=>{Re(E,a&&a.suspense)}:i!=="sync"&&(h=!0,c.scheduler=(E,R)=>{R?E():Pr(E)}),c.augmentJob=E=>{t&&(E.flags|=4),h&&(E.flags|=2,a&&(E.id=a.uid,E.i=a))};const B=so(e,t,c);return hu&&(d?d.push(B):l&&B()),B}function Xo(e,t,u){const r=this.proxy,n=he(e)?e.includes(".")?ms(r,e):()=>r[e]:e.bind(r,r);let i;k(t)?i=t:(i=t.handler,u=t);const o=du(this),c=gs(n,i.bind(r),u);return o(),c}function ms(e,t){const u=t.split(".");return()=>{let r=e;for(let n=0;n<u.length&&r;n++)r=r[u[n]];return r}}const ec=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${At(t)}Modifiers`]||e[`${xt(t)}Modifiers`];function tc(e,t,...u){if(e.isUnmounted)return;const r=e.vnode.props||Q;let n=u;const i=t.startsWith("update:"),o=i&&ec(r,t.slice(7));o&&(o.trim&&(n=u.map(a=>he(a)?a.trim():a)),o.number&&(n=u.map(mi)));let c,l=r[c=hr(t)]||r[c=hr(At(t))];!l&&i&&(l=r[c=hr(xt(t))]),l&&Je(l,e,6,n);const d=r[c+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Je(d,e,6,n)}}function bs(e,t,u=!1){const r=t.emitsCache,n=r.get(e);if(n!==void 0)return n;const i=e.emits;let o={},c=!1;if(!k(e)){const l=d=>{const a=bs(d,t,!0);a&&(c=!0,xe(o,a))};!u&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!i&&!c?(de(e)&&r.set(e,null),null):(V(i)?i.forEach(l=>o[l]=null):xe(o,i),de(e)&&r.set(e,o),o)}function Ku(e,t){return!e||!Bu(t)?!1:(t=t.slice(2).replace(/Once$/,""),re(e,t[0].toLowerCase()+t.slice(1))||re(e,xt(t))||re(e,t))}function Af(){}function ys(e){const{type:t,vnode:u,proxy:r,withProxy:n,propsOptions:[i],slots:o,attrs:c,emit:l,render:d,renderCache:a,props:h,data:B,setupState:E,ctx:R,inheritAttrs:F}=e,N=Vu(e);let m,T;try{if(u.shapeFlag&4){const w=n||r,L=gt.NODE_ENV!=="production"&&E.__isScriptSetup?new Proxy(w,{get(J,U,ze){return io(`Property '${String(U)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(J,U,ze)}}):w;m=Qe(d.call(L,w,a,gt.NODE_ENV!=="production"?Nu(h):h,E,B,R)),T=c}else{const w=t;gt.NODE_ENV,m=Qe(w.length>1?w(gt.NODE_ENV!=="production"?Nu(h):h,gt.NODE_ENV!=="production"?{get attrs(){return Nu(c)},slots:o,emit:l}:{attrs:c,slots:o,emit:l}):w(gt.NODE_ENV!=="production"?Nu(h):h,null)),T=t.props?c:uc(c)}}catch(w){fu.length=0,ju(w,e,1),m=Ge(lu)}let $=m;if(T&&F!==!1){const w=Object.keys(T),{shapeFlag:L}=$;w.length&&L&7&&(i&&w.some(ar)&&(T=rc(T,i)),$=kt($,T,!1,!0))}return u.dirs&&($=kt($,null,!1,!0),$.dirs=$.dirs?$.dirs.concat(u.dirs):u.dirs),u.transition&&Tr($,u.transition),m=$,Vu(N),m}const uc=e=>{let t;for(const u in e)(u==="class"||u==="style"||Bu(u))&&((t||(t={}))[u]=e[u]);return t},rc=(e,t)=>{const u={};for(const r in e)(!ar(r)||!(r.slice(9)in t))&&(u[r]=e[r]);return u};function nc(e,t,u){const{props:r,children:n,component:i}=e,{props:o,children:c,patchFlag:l}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(u&&l>=0){if(l&1024)return!0;if(l&16)return r?Bs(r,o,d):!!o;if(l&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const B=a[h];if(o[B]!==r[B]&&!Ku(d,B))return!0}}}else return(n||c)&&(!c||!c.$stable)?!0:r===o?!1:r?o?Bs(r,o,d):!0:!!o;return!1}function Bs(e,t,u){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let n=0;n<r.length;n++){const i=r[n];if(t[i]!==e[i]&&!Ku(u,i))return!0}return!1}function sc({vnode:e,parent:t},u){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=u,t=t.parent;else break}}const xs=e=>e.__isSuspense;function ic(e,t){t&&t.pendingBranch?V(e)?t.effects.push(...e):t.effects.push(e):po(e)}const Ke=Symbol.for("v-fgt"),Gu=Symbol.for("v-txt"),lu=Symbol.for("v-cmt"),Vr=Symbol.for("v-stc"),fu=[];let $e=null;function Hr(e=!1){fu.push($e=e?null:[])}function oc(){fu.pop(),$e=fu[fu.length-1]||null}let au=1;function _s(e,t=!1){au+=e,e<0&&$e&&t&&($e.hasOnce=!0)}function cc(e){return e.dynamicChildren=au>0?$e||Nt:null,oc(),au>0&&$e&&$e.push(e),e}function Wr(e,t,u,r,n,i){return cc(we(e,t,u,r,n,i,!0))}function qu(e){return e?e.__v_isVNode===!0:!1}function Du(e,t){return e.type===t.type&&e.key===t.key}const vs=({key:e})=>e??null,zu=({ref:e,ref_key:t,ref_for:u})=>(typeof e=="number"&&(e=""+e),e!=null?he(e)||Ae(e)||k(e)?{i:Ze,r:e,k:t,f:!!u}:e:null);function we(e,t=null,u=null,r=0,n=null,i=e===Ke?0:1,o=!1,c=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&vs(t),ref:t&&zu(t),scopeId:zn,slotScopeIds:null,children:u,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Ze};return c?(kr(l,u),i&128&&e.normalize(l)):u&&(l.shapeFlag|=he(u)?8:16),au>0&&!o&&$e&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&$e.push(l),l}const Ge=lc;function lc(e,t=null,u=null,r=0,n=null,i=!1){if((!e||e===Oo)&&(e=lu),qu(e)){const c=kt(e,t,!0);return u&&kr(c,u),au>0&&!i&&$e&&(c.shapeFlag&6?$e[$e.indexOf(e)]=c:$e.push(c)),c.patchFlag=-2,c}if(yc(e)&&(e=e.__vccOpts),t){t=fc(t);let{class:c,style:l}=t;c&&!he(c)&&(t.class=Fr(c)),de(l)&&(wr(l)&&!V(l)&&(l=xe({},l)),t.style=Cr(l))}const o=he(e)?1:xs(e)?128:Ao(e)?64:de(e)?4:k(e)?2:0;return we(e,t,u,r,n,o,i,!0)}function fc(e){return e?wr(e)||ls(e)?xe({},e):e:null}function kt(e,t,u=!1,r=!1){const{props:n,ref:i,patchFlag:o,children:c,transition:l}=e,d=t?Dc(n||{},t):n,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&vs(d),ref:t&&t.ref?u&&i?V(i)?i.concat(zu(t)):[i,zu(t)]:zu(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ke?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&kt(e.ssContent),ssFallback:e.ssFallback&&kt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&Tr(a,l.clone(a)),a}function ac(e=" ",t=0){return Ge(Gu,null,e,t)}function Qe(e){return e==null||typeof e=="boolean"?Ge(lu):V(e)?Ge(Ke,null,e.slice()):qu(e)?bt(e):Ge(Gu,null,String(e))}function bt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:kt(e)}function kr(e,t){let u=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(V(t))u=16;else if(typeof t=="object")if(r&65){const n=t.default;n&&(n._c&&(n._d=!1),kr(e,n()),n._c&&(n._d=!0));return}else{u=32;const n=t._;!n&&!ls(t)?t._ctx=Ze:n===3&&Ze&&(Ze.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else k(t)?(t={default:t,_ctx:Ze},u=32):(t=String(t),r&64?(u=16,t=[ac(t)]):u=8);e.children=t,e.shapeFlag|=u}function Dc(...e){const t={};for(let u=0;u<e.length;u++){const r=e[u];for(const n in r)if(n==="class")t.class!==r.class&&(t.class=Fr([t.class,r.class]));else if(n==="style")t.style=Cr([t.style,r.style]);else if(Bu(n)){const i=t[n],o=r[n];o&&i!==o&&!(V(i)&&i.includes(o))&&(t[n]=i?[].concat(i,o):o)}else n!==""&&(t[n]=r[n])}return t}function Xe(e,t,u,r=null){Je(e,t,7,[u,r])}const dc=is();let hc=0;function pc(e,t,u){const r=e.type,n=(t?t.appContext:e.appContext)||dc,i={uid:hc++,vnode:e,type:r,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new wi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:as(r,n),emitsOptions:bs(r,n),emit:null,emitted:null,propsDefaults:Q,inheritAttrs:r.inheritAttrs,ctx:Q,data:Q,props:Q,attrs:Q,slots:Q,refs:Q,setupState:Q,setupContext:null,suspense:u,suspenseId:u?u.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=tc.bind(null,i),e.ce&&e.ce(i),i}let Ee=null,Ju,Ur;{const e=vu(),t=(u,r)=>{let n;return(n=e[u])||(n=e[u]=[]),n.push(r),i=>{n.length>1?n.forEach(o=>o(i)):n[0](i)}};Ju=t("__VUE_INSTANCE_SETTERS__",u=>Ee=u),Ur=t("__VUE_SSR_SETTERS__",u=>hu=u)}const du=e=>{const t=Ee;return Ju(e),e.scope.on(),()=>{e.scope.off(),Ju(t)}},ws=()=>{Ee&&Ee.scope.off(),Ju(null)};function Ss(e){return e.vnode.shapeFlag&4}let hu=!1;function Cc(e,t=!1,u=!1){t&&Ur(t);const{props:r,children:n}=e.vnode,i=Ss(e);Ho(e,r,i,t),Ko(e,n,u);const o=i?Fc(e,t):void 0;return t&&Ur(!1),o}function Fc(e,t){const u=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,To);const{setup:r}=u;if(r){st();const n=e.setupContext=r.length>1?Ec(e):null,i=du(e),o=Lt(r,e,0,[e.props,n]),c=Dn(o);if(it(),i(),(c||e.sp)&&!su(e)&&Jn(e),c){if(o.then(ws,ws),t)return o.then(l=>{Os(e,l)}).catch(l=>{ju(l,e,0)});e.asyncDep=o}else Os(e,o)}else Ps(e)}function Os(e,t,u){k(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:de(t)&&(e.setupState=Hn(t)),Ps(e)}function Ps(e,t,u){const r=e.type;e.render||(e.render=r.render||We);{const n=du(e);st();try{Io(e)}finally{it(),n()}}}const Ac={get(e,t){return be(e,"get",""),e[t]}};function Ec(e){const t=u=>{e.exposed=u||{}};return{attrs:new Proxy(e.attrs,Ac),slots:e.slots,emit:e.emit,expose:t}}function Kr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Hn(Yi(e.exposed)),{get(t,u){if(u in t)return t[u];if(u in iu)return iu[u](e)},has(t,u){return u in t||u in iu}})):e.proxy}const gc=/(?:^|[-_])(\w)/g,mc=e=>e.replace(gc,t=>t.toUpperCase()).replace(/[-_]/g,"");function bc(e,t=!0){return k(e)?e.displayName||e.name:e.name||t&&e.__name}function Ts(e,t,u=!1){let r=bc(t);if(!r&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(r=n[1])}if(!r&&e&&e.parent){const n=i=>{for(const o in i)if(i[o]===t)return o};r=n(e.components||e.parent.type.components)||n(e.appContext.components)}return r?mc(r):u?"App":"Anonymous"}function yc(e){return k(e)&&"__vccOpts"in e}const Bc=(e,t)=>ro(e,t,hu);function xc(e,t,u){const r=arguments.length;return r===2?de(t)&&!V(t)?qu(t)?Ge(e,null,[t]):Ge(e,t):Ge(e,null,t):(r>3?u=Array.prototype.slice.call(arguments,2):r===3&&qu(u)&&(u=[u]),Ge(e,t,u))}const _c="3.5.13";let Gr;const Is=typeof window<"u"&&window.trustedTypes;if(Is)try{Gr=Is.createPolicy("vue",{createHTML:e=>e})}catch{}const Ns=Gr?e=>Gr.createHTML(e):e=>e,vc="http://www.w3.org/2000/svg",wc="http://www.w3.org/1998/Math/MathML",ft=typeof document<"u"?document:null,Rs=ft&&ft.createElement("template"),Sc={insert:(e,t,u)=>{t.insertBefore(e,u||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,u,r)=>{const n=t==="svg"?ft.createElementNS(vc,e):t==="mathml"?ft.createElementNS(wc,e):u?ft.createElement(e,{is:u}):ft.createElement(e);return e==="select"&&r&&r.multiple!=null&&n.setAttribute("multiple",r.multiple),n},createText:e=>ft.createTextNode(e),createComment:e=>ft.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ft.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,u,r,n,i){const o=u?u.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),u),!(n===i||!(n=n.nextSibling)););else{Rs.innerHTML=Ns(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const c=Rs.content;if(r==="svg"||r==="mathml"){const l=c.firstChild;for(;l.firstChild;)c.appendChild(l.firstChild);c.removeChild(l)}t.insertBefore(c,u)}return[o?o.nextSibling:t.firstChild,u?u.previousSibling:t.lastChild]}},Oc=Symbol("_vtc");function Pc(e,t,u){const r=e[Oc];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):u?e.setAttribute("class",t):e.className=t}const $s=Symbol("_vod"),Tc=Symbol("_vsh"),Ic=Symbol(""),Nc=/(^|;)\s*display\s*:/;function Rc(e,t,u){const r=e.style,n=he(u);let i=!1;if(u&&!n){if(t)if(he(t))for(const o of t.split(";")){const c=o.slice(0,o.indexOf(":")).trim();u[c]==null&&Yu(r,c,"")}else for(const o in t)u[o]==null&&Yu(r,o,"");for(const o in u)o==="display"&&(i=!0),Yu(r,o,u[o])}else if(n){if(t!==u){const o=r[Ic];o&&(u+=";"+o),r.cssText=u,i=Nc.test(u)}}else t&&e.removeAttribute("style");$s in e&&(e[$s]=i?r.display:"",e[Tc]&&(r.display="none"))}const Ms=/\s*!important$/;function Yu(e,t,u){if(V(u))u.forEach(r=>Yu(e,t,r));else if(u==null&&(u=""),t.startsWith("--"))e.setProperty(t,u);else{const r=$c(e,t);Ms.test(u)?e.setProperty(xt(r),u.replace(Ms,""),"important"):e[r]=u}}const js=["Webkit","Moz","ms"],qr={};function $c(e,t){const u=qr[t];if(u)return u;let r=At(t);if(r!=="filter"&&r in e)return qr[t]=r;r=pn(r);for(let n=0;n<js.length;n++){const i=js[n]+r;if(i in e)return qr[t]=i}return t}const Ls="http://www.w3.org/1999/xlink";function Vs(e,t,u,r,n,i=_i(t)){r&&t.startsWith("xlink:")?u==null?e.removeAttributeNS(Ls,t.slice(6,t.length)):e.setAttributeNS(Ls,t,u):u==null||i&&!An(u)?e.removeAttribute(t):e.setAttribute(t,i?"":Ft(u)?String(u):u)}function Hs(e,t,u,r,n){if(t==="innerHTML"||t==="textContent"){u!=null&&(e[t]=t==="innerHTML"?Ns(u):u);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const c=i==="OPTION"?e.getAttribute("value")||"":e.value,l=u==null?e.type==="checkbox"?"on":"":String(u);(c!==l||!("_value"in e))&&(e.value=l),u==null&&e.removeAttribute(t),e._value=u;return}let o=!1;if(u===""||u==null){const c=typeof e[t];c==="boolean"?u=An(u):u==null&&c==="string"?(u="",o=!0):c==="number"&&(u=0,o=!0)}try{e[t]=u}catch{}o&&e.removeAttribute(n||t)}function Mc(e,t,u,r){e.addEventListener(t,u,r)}function jc(e,t,u,r){e.removeEventListener(t,u,r)}const Ws=Symbol("_vei");function Lc(e,t,u,r,n=null){const i=e[Ws]||(e[Ws]={}),o=i[t];if(r&&o)o.value=r;else{const[c,l]=Vc(t);if(r){const d=i[t]=kc(r,n);Mc(e,c,d,l)}else o&&(jc(e,c,o,l),i[t]=void 0)}}const ks=/(?:Once|Passive|Capture)$/;function Vc(e){let t;if(ks.test(e)){t={};let r;for(;r=e.match(ks);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):xt(e.slice(2)),t]}let zr=0;const Hc=Promise.resolve(),Wc=()=>zr||(Hc.then(()=>zr=0),zr=Date.now());function kc(e,t){const u=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=u.attached)return;Je(Uc(r,u.value),t,5,[r])};return u.value=e,u.attached=Wc(),u}function Uc(e,t){if(V(t)){const u=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{u.call(e),e._stopped=!0},t.map(r=>n=>!n._stopped&&r&&r(n))}else return t}const Us=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Kc=(e,t,u,r,n,i)=>{const o=n==="svg";t==="class"?Pc(e,r,o):t==="style"?Rc(e,u,r):Bu(t)?ar(t)||Lc(e,t,u,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Gc(e,t,r,o))?(Hs(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Vs(e,t,r,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!he(r))?Hs(e,At(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Vs(e,t,r,o))};function Gc(e,t,u,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Us(t)&&k(u));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Us(t)&&he(u)?!1:t in e}const qc=xe({patchProp:Kc},Sc);let Ks;function zc(){return Ks||(Ks=qo(qc))}const Jc=(...e)=>{const t=zc().createApp(...e),{mount:u}=t;return t.mount=r=>{const n=Zc(r);if(!n)return;const i=t._component;!k(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const o=u(n,!1,Yc(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),o},t};function Yc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Zc(e){return he(e)?document.querySelector(e):e}var Jr={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var Gs;function Qc(){return Gs||(Gs=1,function(e){(function(){var t={}.hasOwnProperty;function u(){for(var i="",o=0;o<arguments.length;o++){var c=arguments[o];c&&(i=n(i,r(c)))}return i}function r(i){if(typeof i=="string"||typeof i=="number")return i;if(typeof i!="object")return"";if(Array.isArray(i))return u.apply(null,i);if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]"))return i.toString();var o="";for(var c in i)t.call(i,c)&&i[c]&&(o=n(o,c));return o}function n(i,o){return o?i?i+" "+o:i+o:i}e.exports?(u.default=u,e.exports=u):window.classNames=u})()}(Jr)),Jr.exports}Qc();function Xc(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}function el(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),e.nonce!==void 0&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}var tl=function(){function e(u){var r=this;this._insertTag=function(n){var i;r.tags.length===0?r.insertionPoint?i=r.insertionPoint.nextSibling:r.prepend?i=r.container.firstChild:i=r.before:i=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(n,i),r.tags.push(n)},this.isSpeedy=u.speedy===void 0?!0:u.speedy,this.tags=[],this.ctr=0,this.nonce=u.nonce,this.key=u.key,this.container=u.container,this.prepend=u.prepend,this.insertionPoint=u.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(r){r.forEach(this._insertTag)},t.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(el(this));var n=this.tags[this.tags.length-1];if(this.isSpeedy){var i=Xc(n);try{i.insertRule(r,i.cssRules.length)}catch{}}else n.appendChild(document.createTextNode(r));this.ctr++},t.flush=function(){this.tags.forEach(function(r){var n;return(n=r.parentNode)==null?void 0:n.removeChild(r)}),this.tags=[],this.ctr=0},e}(),ye="-ms-",Zu="-moz-",ee="-webkit-",qs="comm",Yr="rule",Zr="decl",ul="@import",zs="@keyframes",rl="@layer",nl=Math.abs,Qu=String.fromCharCode,sl=Object.assign;function il(e,t){return ge(e,0)^45?(((t<<2^ge(e,0))<<2^ge(e,1))<<2^ge(e,2))<<2^ge(e,3):0}function Js(e){return e.trim()}function ol(e,t){return(e=t.exec(e))?e[0]:e}function te(e,t,u){return e.replace(t,u)}function Qr(e,t){return e.indexOf(t)}function ge(e,t){return e.charCodeAt(t)|0}function pu(e,t,u){return e.slice(t,u)}function et(e){return e.length}function Xr(e){return e.length}function Xu(e,t){return t.push(e),e}function cl(e,t){return e.map(t).join("")}var er=1,Ut=1,Ys=0,Te=0,Ce=0,Kt="";function tr(e,t,u,r,n,i,o){return{value:e,root:t,parent:u,type:r,props:n,children:i,line:er,column:Ut,length:o,return:""}}function Cu(e,t){return sl(tr("",null,null,"",null,null,0),e,{length:-e.length},t)}function ll(){return Ce}function fl(){return Ce=Te>0?ge(Kt,--Te):0,Ut--,Ce===10&&(Ut=1,er--),Ce}function Me(){return Ce=Te<Ys?ge(Kt,Te++):0,Ut++,Ce===10&&(Ut=1,er++),Ce}function tt(){return ge(Kt,Te)}function ur(){return Te}function Fu(e,t){return pu(Kt,e,t)}function Au(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Zs(e){return er=Ut=1,Ys=et(Kt=e),Te=0,[]}function Qs(e){return Kt="",e}function rr(e){return Js(Fu(Te-1,en(e===91?e+2:e===40?e+1:e)))}function al(e){for(;(Ce=tt())&&Ce<33;)Me();return Au(e)>2||Au(Ce)>3?"":" "}function Dl(e,t){for(;--t&&Me()&&!(Ce<48||Ce>102||Ce>57&&Ce<65||Ce>70&&Ce<97););return Fu(e,ur()+(t<6&&tt()==32&&Me()==32))}function en(e){for(;Me();)switch(Ce){case e:return Te;case 34:case 39:e!==34&&e!==39&&en(Ce);break;case 40:e===41&&en(e);break;case 92:Me();break}return Te}function dl(e,t){for(;Me()&&e+Ce!==57;)if(e+Ce===84&&tt()===47)break;return"/*"+Fu(t,Te-1)+"*"+Qu(e===47?e:Me())}function hl(e){for(;!Au(tt());)Me();return Fu(e,Te)}function pl(e){return Qs(nr("",null,null,null,[""],e=Zs(e),0,[0],e))}function nr(e,t,u,r,n,i,o,c,l){for(var d=0,a=0,h=o,B=0,E=0,R=0,F=1,N=1,m=1,T=0,$="",w=n,L=i,J=r,U=$;N;)switch(R=T,T=Me()){case 40:if(R!=108&&ge(U,h-1)==58){Qr(U+=te(rr(T),"&","&\f"),"&\f")!=-1&&(m=-1);break}case 34:case 39:case 91:U+=rr(T);break;case 9:case 10:case 13:case 32:U+=al(R);break;case 92:U+=Dl(ur()-1,7);continue;case 47:switch(tt()){case 42:case 47:Xu(Cl(dl(Me(),ur()),t,u),l);break;default:U+="/"}break;case 123*F:c[d++]=et(U)*m;case 125*F:case 59:case 0:switch(T){case 0:case 125:N=0;case 59+a:m==-1&&(U=te(U,/\f/g,"")),E>0&&et(U)-h&&Xu(E>32?ei(U+";",r,u,h-1):ei(te(U," ","")+";",r,u,h-2),l);break;case 59:U+=";";default:if(Xu(J=Xs(U,t,u,d,a,n,c,$,w=[],L=[],h),i),T===123)if(a===0)nr(U,t,J,J,w,i,h,c,L);else switch(B===99&&ge(U,3)===110?100:B){case 100:case 108:case 109:case 115:nr(e,J,J,r&&Xu(Xs(e,J,J,0,0,n,c,$,n,w=[],h),L),n,L,h,c,r?w:L);break;default:nr(U,J,J,J,[""],L,0,c,L)}}d=a=E=0,F=m=1,$=U="",h=o;break;case 58:h=1+et(U),E=R;default:if(F<1){if(T==123)--F;else if(T==125&&F++==0&&fl()==125)continue}switch(U+=Qu(T),T*F){case 38:m=a>0?1:(U+="\f",-1);break;case 44:c[d++]=(et(U)-1)*m,m=1;break;case 64:tt()===45&&(U+=rr(Me())),B=tt(),a=h=et($=U+=hl(ur())),T++;break;case 45:R===45&&et(U)==2&&(F=0)}}return i}function Xs(e,t,u,r,n,i,o,c,l,d,a){for(var h=n-1,B=n===0?i:[""],E=Xr(B),R=0,F=0,N=0;R<r;++R)for(var m=0,T=pu(e,h+1,h=nl(F=o[R])),$=e;m<E;++m)($=Js(F>0?B[m]+" "+T:te(T,/&\f/g,B[m])))&&(l[N++]=$);return tr(e,t,u,n===0?Yr:c,l,d,a)}function Cl(e,t,u){return tr(e,t,u,qs,Qu(ll()),pu(e,2,-2),0)}function ei(e,t,u,r){return tr(e,t,u,Zr,pu(e,0,r),pu(e,r+1,-1),r)}function Gt(e,t){for(var u="",r=Xr(e),n=0;n<r;n++)u+=t(e[n],n,e,t)||"";return u}function Fl(e,t,u,r){switch(e.type){case rl:if(e.children.length)break;case ul:case Zr:return e.return=e.return||e.value;case qs:return"";case zs:return e.return=e.value+"{"+Gt(e.children,r)+"}";case Yr:e.value=e.props.join(",")}return et(u=Gt(e.children,r))?e.return=e.value+"{"+u+"}":""}function Al(e){var t=Xr(e);return function(u,r,n,i){for(var o="",c=0;c<t;c++)o+=e[c](u,r,n,i)||"";return o}}function El(e){return function(t){t.root||(t=t.return)&&e(t)}}function gl(e){var t=Object.create(null);return function(u){return t[u]===void 0&&(t[u]=e(u)),t[u]}}var ml=function(t,u,r){for(var n=0,i=0;n=i,i=tt(),n===38&&i===12&&(u[r]=1),!Au(i);)Me();return Fu(t,Te)},bl=function(t,u){var r=-1,n=44;do switch(Au(n)){case 0:n===38&&tt()===12&&(u[r]=1),t[r]+=ml(Te-1,u,r);break;case 2:t[r]+=rr(n);break;case 4:if(n===44){t[++r]=tt()===58?"&\f":"",u[r]=t[r].length;break}default:t[r]+=Qu(n)}while(n=Me());return t},yl=function(t,u){return Qs(bl(Zs(t),u))},ti=new WeakMap,Bl=function(t){if(!(t.type!=="rule"||!t.parent||t.length<1)){for(var u=t.value,r=t.parent,n=t.column===r.column&&t.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(t.props.length===1&&u.charCodeAt(0)!==58&&!ti.get(r))&&!n){ti.set(t,!0);for(var i=[],o=yl(u,i),c=r.props,l=0,d=0;l<o.length;l++)for(var a=0;a<c.length;a++,d++)t.props[d]=i[l]?o[l].replace(/&\f/g,c[a]):c[a]+" "+o[l]}}},xl=function(t){if(t.type==="decl"){var u=t.value;u.charCodeAt(0)===108&&u.charCodeAt(2)===98&&(t.return="",t.value="")}};function ui(e,t){switch(il(e,t)){case 5103:return ee+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return ee+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return ee+e+Zu+e+ye+e+e;case 6828:case 4268:return ee+e+ye+e+e;case 6165:return ee+e+ye+"flex-"+e+e;case 5187:return ee+e+te(e,/(\w+).+(:[^]+)/,ee+"box-$1$2"+ye+"flex-$1$2")+e;case 5443:return ee+e+ye+"flex-item-"+te(e,/flex-|-self/,"")+e;case 4675:return ee+e+ye+"flex-line-pack"+te(e,/align-content|flex-|-self/,"")+e;case 5548:return ee+e+ye+te(e,"shrink","negative")+e;case 5292:return ee+e+ye+te(e,"basis","preferred-size")+e;case 6060:return ee+"box-"+te(e,"-grow","")+ee+e+ye+te(e,"grow","positive")+e;case 4554:return ee+te(e,/([^-])(transform)/g,"$1"+ee+"$2")+e;case 6187:return te(te(te(e,/(zoom-|grab)/,ee+"$1"),/(image-set)/,ee+"$1"),e,"")+e;case 5495:case 3959:return te(e,/(image-set\([^]*)/,ee+"$1$`$1");case 4968:return te(te(e,/(.+:)(flex-)?(.*)/,ee+"box-pack:$3"+ye+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+ee+e+e;case 4095:case 3583:case 4068:case 2532:return te(e,/(.+)-inline(.+)/,ee+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(et(e)-1-t>6)switch(ge(e,t+1)){case 109:if(ge(e,t+4)!==45)break;case 102:return te(e,/(.+:)(.+)-([^]+)/,"$1"+ee+"$2-$3$1"+Zu+(ge(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~Qr(e,"stretch")?ui(te(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(ge(e,t+1)!==115)break;case 6444:switch(ge(e,et(e)-3-(~Qr(e,"!important")&&10))){case 107:return te(e,":",":"+ee)+e;case 101:return te(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+ee+(ge(e,14)===45?"inline-":"")+"box$3$1"+ee+"$2$3$1"+ye+"$2box$3")+e}break;case 5936:switch(ge(e,t+11)){case 114:return ee+e+ye+te(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return ee+e+ye+te(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return ee+e+ye+te(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return ee+e+ye+e+e}return e}var _l=function(t,u,r,n){if(t.length>-1&&!t.return)switch(t.type){case Zr:t.return=ui(t.value,t.length);break;case zs:return Gt([Cu(t,{value:te(t.value,"@","@"+ee)})],n);case Yr:if(t.length)return cl(t.props,function(i){switch(ol(i,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Gt([Cu(t,{props:[te(i,/:(read-\w+)/,":"+Zu+"$1")]})],n);case"::placeholder":return Gt([Cu(t,{props:[te(i,/:(plac\w+)/,":"+ee+"input-$1")]}),Cu(t,{props:[te(i,/:(plac\w+)/,":"+Zu+"$1")]}),Cu(t,{props:[te(i,/:(plac\w+)/,ye+"input-$1")]})],n)}return""})}},vl=[_l],wl=function(t){var u=t.key;if(u==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(F){var N=F.getAttribute("data-emotion");N.indexOf(" ")!==-1&&(document.head.appendChild(F),F.setAttribute("data-s",""))})}var n=t.stylisPlugins||vl,i={},o,c=[];o=t.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+u+' "]'),function(F){for(var N=F.getAttribute("data-emotion").split(" "),m=1;m<N.length;m++)i[N[m]]=!0;c.push(F)});var l,d=[Bl,xl];{var a,h=[Fl,El(function(F){a.insert(F)})],B=Al(d.concat(n,h)),E=function(N){return Gt(pl(N),B)};l=function(N,m,T,$){a=T,E(N?N+"{"+m.styles+"}":m.styles),$&&(R.inserted[m.name]=!0)}}var R={key:u,sheet:new tl({key:u,container:o,nonce:t.nonce,speedy:t.speedy,prepend:t.prepend,insertionPoint:t.insertionPoint}),nonce:t.nonce,inserted:i,registered:{},insert:l};return R.sheet.hydrate(c),R};function Sl(e){for(var t=0,u,r=0,n=e.length;n>=4;++r,n-=4)u=e.charCodeAt(r)&255|(e.charCodeAt(++r)&255)<<8|(e.charCodeAt(++r)&255)<<16|(e.charCodeAt(++r)&255)<<24,u=(u&65535)*1540483477+((u>>>16)*59797<<16),u^=u>>>24,t=(u&65535)*1540483477+((u>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(n){case 3:t^=(e.charCodeAt(r+2)&255)<<16;case 2:t^=(e.charCodeAt(r+1)&255)<<8;case 1:t^=e.charCodeAt(r)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}var Ol={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Pl=/[A-Z]|^ms/g,Tl=/_EMO_([^_]+?)_([^]*?)_EMO_/g,ri=function(t){return t.charCodeAt(1)===45},ni=function(t){return t!=null&&typeof t!="boolean"},tn=gl(function(e){return ri(e)?e:e.replace(Pl,"-$&").toLowerCase()}),si=function(t,u){switch(t){case"animation":case"animationName":if(typeof u=="string")return u.replace(Tl,function(r,n,i){return yt={name:n,styles:i,next:yt},n})}return Ol[t]!==1&&!ri(t)&&typeof u=="number"&&u!==0?u+"px":u};function sr(e,t,u){if(u==null)return"";var r=u;if(r.__emotion_styles!==void 0)return r;switch(typeof u){case"boolean":return"";case"object":{var n=u;if(n.anim===1)return yt={name:n.name,styles:n.styles,next:yt},n.name;var i=u;if(i.styles!==void 0){var o=i.next;if(o!==void 0)for(;o!==void 0;)yt={name:o.name,styles:o.styles,next:yt},o=o.next;var c=i.styles+";";return c}return Il(e,t,u)}}var l=u;if(t==null)return l;var d=t[l];return d!==void 0?d:l}function Il(e,t,u){var r="";if(Array.isArray(u))for(var n=0;n<u.length;n++)r+=sr(e,t,u[n])+";";else for(var i in u){var o=u[i];if(typeof o!="object"){var c=o;t!=null&&t[c]!==void 0?r+=i+"{"+t[c]+"}":ni(c)&&(r+=tn(i)+":"+si(i,c)+";")}else if(Array.isArray(o)&&typeof o[0]=="string"&&(t==null||t[o[0]]===void 0))for(var l=0;l<o.length;l++)ni(o[l])&&(r+=tn(i)+":"+si(i,o[l])+";");else{var d=sr(e,t,o);switch(i){case"animation":case"animationName":{r+=tn(i)+":"+d+";";break}default:r+=i+"{"+d+"}"}}}return r}var ii=/label:\s*([^\s;{]+)\s*(;|$)/g,yt;function un(e,t,u){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var r=!0,n="";yt=void 0;var i=e[0];if(i==null||i.raw===void 0)r=!1,n+=sr(u,t,i);else{var o=i;n+=o[0]}for(var c=1;c<e.length;c++)if(n+=sr(u,t,e[c]),r){var l=i;n+=l[c]}ii.lastIndex=0;for(var d="",a;(a=ii.exec(n))!==null;)d+="-"+a[1];var h=Sl(n)+d;return{name:h,styles:n,next:yt}}function oi(e,t,u){var r="";return u.split(" ").forEach(function(n){e[n]!==void 0?t.push(e[n]+";"):n&&(r+=n+" ")}),r}var Nl=function(t,u,r){var n=t.key+"-"+u.name;t.registered[n]===void 0&&(t.registered[n]=u.styles)},Rl=function(t,u,r){Nl(t,u);var n=t.key+"-"+u.name;if(t.inserted[u.name]===void 0){var i=u;do t.insert(u===i?"."+n:"",i,t.sheet,!0),i=i.next;while(i!==void 0)}};function ci(e,t){if(e.inserted[t.name]===void 0)return e.insert("",t,e.sheet,!0)}function li(e,t,u){var r=[],n=oi(e,r,u);return r.length<2?u:n+t(r)}var $l=function(t){var u=wl(t);u.sheet.speedy=function(c){this.isSpeedy=c},u.compat=!0;var r=function(){for(var l=arguments.length,d=new Array(l),a=0;a<l;a++)d[a]=arguments[a];var h=un(d,u.registered,void 0);return Rl(u,h),u.key+"-"+h.name},n=function(){for(var l=arguments.length,d=new Array(l),a=0;a<l;a++)d[a]=arguments[a];var h=un(d,u.registered),B="animation-"+h.name;return ci(u,{name:h.name,styles:"@keyframes "+B+"{"+h.styles+"}"}),B},i=function(){for(var l=arguments.length,d=new Array(l),a=0;a<l;a++)d[a]=arguments[a];var h=un(d,u.registered);ci(u,h)},o=function(){for(var l=arguments.length,d=new Array(l),a=0;a<l;a++)d[a]=arguments[a];return li(u.registered,r,Ml(d))};return{css:r,cx:o,injectGlobal:i,keyframes:n,hydrate:function(l){l.forEach(function(d){u.inserted[d]=!0})},flush:function(){u.registered={},u.inserted={},u.sheet.flush()},sheet:u.sheet,cache:u,getRegisteredStyles:oi.bind(null,u.registered),merge:li.bind(null,u.registered,r)}},Ml=function e(t){for(var u="",r=0;r<t.length;r++){var n=t[r];if(n!=null){var i=void 0;switch(typeof n){case"boolean":break;case"object":{if(Array.isArray(n))i=e(n);else{i="";for(var o in n)n[o]&&o&&(i&&(i+=" "),i+=o)}break}default:i=n}i&&(u&&(u+=" "),u+=i)}}return u};$l({key:"css"});var Eu={exports:{}},jl=Eu.exports,fi;function Ll(){return fi||(fi=1,function(e,t){(function(u,r){r(t)})(jl,function(u){var r=Object.defineProperty,n=Object.defineProperties,i=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,d=(x,O,I)=>O in x?r(x,O,{enumerable:!0,configurable:!0,writable:!0,value:I}):x[O]=I,a=(x,O)=>{for(var I in O||(O={}))c.call(O,I)&&d(x,I,O[I]);if(o)for(var I of o(O))l.call(O,I)&&d(x,I,O[I]);return x},h=(x,O)=>n(x,i(O)),B=(x,O,I)=>new Promise((Y,q)=>{var ne=De=>{try{Z(I.next(De))}catch(He){q(He)}},ie=De=>{try{Z(I.throw(De))}catch(He){q(He)}},Z=De=>De.done?Y(De.value):Promise.resolve(De.value).then(ne,ie);Z((I=I.apply(x,O)).next())}),E=(x=>(x[x.NONE=0]="NONE",x[x.LOADING=1]="LOADING",x[x.LOADED=2]="LOADED",x[x.ERROR=3]="ERROR",x))(E||{});class R{constructor(O){this.items={},this.factory=O}getOrCreateItemByURL(O){let I=this.items[O];return I||(I=this.items[O]=this.factory(O)),I}tryGetItemByURL(O){var I;return(I=this.items[O])!=null?I:null}removeItemByURL(O){const I=this.items[O];return I&&(this.items[O]=null,I)}}const F="__RUNTIME_IMPORT__";function N(x,O){var I,Y;const q=globalThis,ne=(I=q[F])!=null?I:q[F]={};return(Y=ne[x])!=null?Y:ne[x]=O()}const m=N("cssCache",()=>new R(x=>({url:x,status:E.NONE,el:null,error:null,reject:null})));function T(x,O,I){const Y={handleLoad(){x.removeEventListener("load",Y.handleLoad),x.removeEventListener("error",Y.handleError),O()},handleError(q){x.removeEventListener("load",Y.handleLoad),x.removeEventListener("error",Y.handleError),I(q)}};x.addEventListener("load",Y.handleLoad),x.addEventListener("error",Y.handleError)}function $(x){const O=m.getOrCreateItemByURL(x),{status:I,error:Y}=O;return I===E.LOADED?Promise.resolve():I===E.ERROR?Promise.reject(Y):I===E.LOADING?new Promise((q,ne)=>{const{el:ie}=O;T(ie,()=>q(),Z=>ne(Z.error))}):(O.status=E.LOADING,new Promise((q,ne)=>{const ie=document.createElement("link");ie.rel="stylesheet",ie.href=x,T(ie,()=>{O.status=E.LOADED,q()},Z=>{const De=Z.error||new Error(`Load css failed. href=${x}`);O.status=E.ERROR,O.error=De,ne(De)}),O.el=ie,ie.setAttribute("data-runtime-import-type","css"),document.head.appendChild(ie)}))}function w(x){return Promise.all(x.map(O=>$(O))).then(()=>Promise.resolve()).catch(O=>Promise.reject(O))}const L=N("jsCache",()=>new R(x=>({url:x,status:E.NONE,el:null,error:null,reject:null,exportThing:void 0}))),J=globalThis,{define:U}=J,{keys:ze}=Object;let It=!1;typeof U<"u"&&!U.runtime_import&&(console.warn("runtime-import should NOT coexist with requiesjs or seajs or any other AMD/CMD loader."),It=!0);const Ve=N("pendingItemMap",()=>({})),ht=function(...x){const O=x.pop(),{currentScript:I}=document;if(!I)throw new Error("currentScript is null.");const{src:Y}=I,q=Ve[Y];if(!q)throw new Error(`Can NOT find item, src=${Y}`);Ve[Y]=null;try{let ne=x[0]||[],ie=null;typeof ne=="string"&&(ie=ne,ne=x[1]||[]);const Z=q.exportThing=(()=>{let De=!1;const He={};let f=O(...ne.map(D=>D==="exports"?(De=!0,He):J[D]));return!f&&De&&(f=He),f})();ie&&(J[ie]=Z),Z&&ze(Z).length===1&&Z.default&&(q.exportThing=Z.default,q.exportThing.default=Z.default)}catch(ne){q.status=E.ERROR,ne instanceof Error&&(q.error=ne),q.reject(ne)}},qt=()=>{const{currentScript:x}=document;if(x){const{src:O}=x;if(Ve[O])return!0}return!1};["amd","cmd"].forEach(x=>{Object.defineProperty(ht,x,{get:qt})}),ht.runtime_import=!0;function zt(x,O){if(It)throw new Error("runtime-import UMD mode uses window.define, you should NOT have your own window.define.");J.define||(J.define=ht),Ve[x]=O}function bu(x){const O=/legao-comp\/(.*)\/[\d.]+\/web.js$/.exec(x),I=window;if(O&&O.length>0){const Y=O[1];I.g_config=I.g_config||{},I.g_config.appKey=Y}}function lr(x,O,I){x.status=E.LOADING,bu(O);const{umd:Y,crossOrigin:q}=I;return new Promise((ne,ie)=>{const Z=document.createElement("script");if(Z.src=O,Z.async=!1,Z.crossOrigin=q,Y){Z.setAttribute("data-runtime-import-type","javascript-umd"),x.reject=ie;const De=Z.src;zt(De,x)}else Z.setAttribute("data-runtime-import-type","javascript");T(Z,()=>{x.status=E.LOADED,x.el=null,ne(x.exportThing)},De=>{const He=De.error||new Error(`Load javascript failed. src=${O}`);x.status=E.ERROR,x.error=He,x.el=null,ie(He)}),x.el=Z,document.body.appendChild(Z)})}function me(x,O){const I=L.getOrCreateItemByURL(x),{status:Y,exportThing:q,error:ne}=I;if(Y===E.LOADED)return Promise.resolve(q);if(Y===E.ERROR)return Promise.reject(ne);if(Y===E.LOADING){const{el:ie}=I;return new Promise((Z,De)=>{T(ie,()=>Z(I.exportThing),He=>De(He.error))})}return lr(I,x,O)}function ae(x,O){let I=Promise.resolve();const Y=x.length-1,{umd:q}=O;return x.forEach((ne,ie)=>{const Z=q&&ie===Y;I=I.then(()=>me(ne,h(a({},O),{umd:Z})))}),I}function ue(x){return B(this,null,function*(){const{scripts:O,styles:I}=x;if(I){const{urls:Z}=I;yield w(Z)}const{dependencies:Y=[],entry:q,umd:ne=!0,crossOrigin:ie="anonymous"}=O;if((q?Y.concat([q]):Y).length)return yield ae(Y.concat([q]),{umd:ne,crossOrigin:ie})})}function pt(x,O){return B(this,null,function*(){const I=O??{};return yield ue({scripts:{dependencies:[],entry:x,umd:I.umd,crossOrigin:I.crossOrigin}})})}function Jt(x){return B(this,null,function*(){return yield ue({scripts:{dependencies:[],entry:""},styles:{urls:[x]}})})}const Ct=ue;u.importComponent=ue,u.importModule=Ct,u.importScript=pt,u.importStyle=Jt,Object.defineProperty(u,Symbol.toStringTag,{value:"Module"})})}(Eu,Eu.exports)),Eu.exports}Ll();var Vl=/[\u1680\u2000-\u200A\u202F\u205F\u3000]/,Hl=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,Wl=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/,rn={Space_Separator:Vl,ID_Start:Hl,ID_Continue:Wl},pe={isSpaceSeparator(e){return typeof e=="string"&&rn.Space_Separator.test(e)},isIdStartChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="$"||e==="_"||rn.ID_Start.test(e))},isIdContinueChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="$"||e==="_"||e==="‌"||e==="‍"||rn.ID_Continue.test(e))},isDigit(e){return typeof e=="string"&&/[0-9]/.test(e)},isHexDigit(e){return typeof e=="string"&&/[0-9A-Fa-f]/.test(e)}};let nn,Se,at,ir,Bt,qe,Fe,sn,gu;var kl=function(t,u){nn=String(t),Se="start",at=[],ir=0,Bt=1,qe=0,Fe=void 0,sn=void 0,gu=void 0;do Fe=Ul(),ql[Se]();while(Fe.type!=="eof");return typeof u=="function"?on({"":gu},"",u):gu};function on(e,t,u){const r=e[t];if(r!=null&&typeof r=="object")if(Array.isArray(r))for(let n=0;n<r.length;n++){const i=String(n),o=on(r,i,u);o===void 0?delete r[i]:Object.defineProperty(r,i,{value:o,writable:!0,enumerable:!0,configurable:!0})}else for(const n in r){const i=on(r,n,u);i===void 0?delete r[n]:Object.defineProperty(r,n,{value:i,writable:!0,enumerable:!0,configurable:!0})}return u.call(e,t,r)}let K,W,mu,Dt,G;function Ul(){for(K="default",W="",mu=!1,Dt=1;;){G=dt();const e=ai[K]();if(e)return e}}function dt(){if(nn[ir])return String.fromCodePoint(nn.codePointAt(ir))}function C(){const e=dt();return e===`
`?(Bt++,qe=0):e?qe+=e.length:qe++,e&&(ir+=e.length),e}const ai={default(){switch(G){case"	":case"\v":case"\f":case" ":case" ":case"\uFEFF":case`
`:case"\r":case"\u2028":case"\u2029":C();return;case"/":C(),K="comment";return;case void 0:return C(),le("eof")}if(pe.isSpaceSeparator(G)){C();return}return ai[Se]()},comment(){switch(G){case"*":C(),K="multiLineComment";return;case"/":C(),K="singleLineComment";return}throw fe(C())},multiLineComment(){switch(G){case"*":C(),K="multiLineCommentAsterisk";return;case void 0:throw fe(C())}C()},multiLineCommentAsterisk(){switch(G){case"*":C();return;case"/":C(),K="default";return;case void 0:throw fe(C())}C(),K="multiLineComment"},singleLineComment(){switch(G){case`
`:case"\r":case"\u2028":case"\u2029":C(),K="default";return;case void 0:return C(),le("eof")}C()},value(){switch(G){case"{":case"[":return le("punctuator",C());case"n":return C(),Pt("ull"),le("null",null);case"t":return C(),Pt("rue"),le("boolean",!0);case"f":return C(),Pt("alse"),le("boolean",!1);case"-":case"+":C()==="-"&&(Dt=-1),K="sign";return;case".":W=C(),K="decimalPointLeading";return;case"0":W=C(),K="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":W=C(),K="decimalInteger";return;case"I":return C(),Pt("nfinity"),le("numeric",1/0);case"N":return C(),Pt("aN"),le("numeric",NaN);case'"':case"'":mu=C()==='"',W="",K="string";return}throw fe(C())},identifierNameStartEscape(){if(G!=="u")throw fe(C());C();const e=cn();switch(e){case"$":case"_":break;default:if(!pe.isIdStartChar(e))throw Di();break}W+=e,K="identifierName"},identifierName(){switch(G){case"$":case"_":case"‌":case"‍":W+=C();return;case"\\":C(),K="identifierNameEscape";return}if(pe.isIdContinueChar(G)){W+=C();return}return le("identifier",W)},identifierNameEscape(){if(G!=="u")throw fe(C());C();const e=cn();switch(e){case"$":case"_":case"‌":case"‍":break;default:if(!pe.isIdContinueChar(e))throw Di();break}W+=e,K="identifierName"},sign(){switch(G){case".":W=C(),K="decimalPointLeading";return;case"0":W=C(),K="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":W=C(),K="decimalInteger";return;case"I":return C(),Pt("nfinity"),le("numeric",Dt*(1/0));case"N":return C(),Pt("aN"),le("numeric",NaN)}throw fe(C())},zero(){switch(G){case".":W+=C(),K="decimalPoint";return;case"e":case"E":W+=C(),K="decimalExponent";return;case"x":case"X":W+=C(),K="hexadecimal";return}return le("numeric",Dt*0)},decimalInteger(){switch(G){case".":W+=C(),K="decimalPoint";return;case"e":case"E":W+=C(),K="decimalExponent";return}if(pe.isDigit(G)){W+=C();return}return le("numeric",Dt*Number(W))},decimalPointLeading(){if(pe.isDigit(G)){W+=C(),K="decimalFraction";return}throw fe(C())},decimalPoint(){switch(G){case"e":case"E":W+=C(),K="decimalExponent";return}if(pe.isDigit(G)){W+=C(),K="decimalFraction";return}return le("numeric",Dt*Number(W))},decimalFraction(){switch(G){case"e":case"E":W+=C(),K="decimalExponent";return}if(pe.isDigit(G)){W+=C();return}return le("numeric",Dt*Number(W))},decimalExponent(){switch(G){case"+":case"-":W+=C(),K="decimalExponentSign";return}if(pe.isDigit(G)){W+=C(),K="decimalExponentInteger";return}throw fe(C())},decimalExponentSign(){if(pe.isDigit(G)){W+=C(),K="decimalExponentInteger";return}throw fe(C())},decimalExponentInteger(){if(pe.isDigit(G)){W+=C();return}return le("numeric",Dt*Number(W))},hexadecimal(){if(pe.isHexDigit(G)){W+=C(),K="hexadecimalInteger";return}throw fe(C())},hexadecimalInteger(){if(pe.isHexDigit(G)){W+=C();return}return le("numeric",Dt*Number(W))},string(){switch(G){case"\\":C(),W+=Kl();return;case'"':if(mu)return C(),le("string",W);W+=C();return;case"'":if(!mu)return C(),le("string",W);W+=C();return;case`
`:case"\r":throw fe(C());case"\u2028":case"\u2029":zl(G);break;case void 0:throw fe(C())}W+=C()},start(){switch(G){case"{":case"[":return le("punctuator",C())}K="value"},beforePropertyName(){switch(G){case"$":case"_":W=C(),K="identifierName";return;case"\\":C(),K="identifierNameStartEscape";return;case"}":return le("punctuator",C());case'"':case"'":mu=C()==='"',K="string";return}if(pe.isIdStartChar(G)){W+=C(),K="identifierName";return}throw fe(C())},afterPropertyName(){if(G===":")return le("punctuator",C());throw fe(C())},beforePropertyValue(){K="value"},afterPropertyValue(){switch(G){case",":case"}":return le("punctuator",C())}throw fe(C())},beforeArrayValue(){if(G==="]")return le("punctuator",C());K="value"},afterArrayValue(){switch(G){case",":case"]":return le("punctuator",C())}throw fe(C())},end(){throw fe(C())}};function le(e,t){return{type:e,value:t,line:Bt,column:qe}}function Pt(e){for(const t of e){if(dt()!==t)throw fe(C());C()}}function Kl(){switch(dt()){case"b":return C(),"\b";case"f":return C(),"\f";case"n":return C(),`
`;case"r":return C(),"\r";case"t":return C(),"	";case"v":return C(),"\v";case"0":if(C(),pe.isDigit(dt()))throw fe(C());return"\0";case"x":return C(),Gl();case"u":return C(),cn();case`
`:case"\u2028":case"\u2029":return C(),"";case"\r":return C(),dt()===`
`&&C(),"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":throw fe(C());case void 0:throw fe(C())}return C()}function Gl(){let e="",t=dt();if(!pe.isHexDigit(t)||(e+=C(),t=dt(),!pe.isHexDigit(t)))throw fe(C());return e+=C(),String.fromCodePoint(parseInt(e,16))}function cn(){let e="",t=4;for(;t-- >0;){const u=dt();if(!pe.isHexDigit(u))throw fe(C());e+=C()}return String.fromCodePoint(parseInt(e,16))}const ql={start(){if(Fe.type==="eof")throw Tt();ln()},beforePropertyName(){switch(Fe.type){case"identifier":case"string":sn=Fe.value,Se="afterPropertyName";return;case"punctuator":or();return;case"eof":throw Tt()}},afterPropertyName(){if(Fe.type==="eof")throw Tt();Se="beforePropertyValue"},beforePropertyValue(){if(Fe.type==="eof")throw Tt();ln()},beforeArrayValue(){if(Fe.type==="eof")throw Tt();if(Fe.type==="punctuator"&&Fe.value==="]"){or();return}ln()},afterPropertyValue(){if(Fe.type==="eof")throw Tt();switch(Fe.value){case",":Se="beforePropertyName";return;case"}":or()}},afterArrayValue(){if(Fe.type==="eof")throw Tt();switch(Fe.value){case",":Se="beforeArrayValue";return;case"]":or()}},end(){}};function ln(){let e;switch(Fe.type){case"punctuator":switch(Fe.value){case"{":e={};break;case"[":e=[];break}break;case"null":case"boolean":case"numeric":case"string":e=Fe.value;break}if(gu===void 0)gu=e;else{const t=at[at.length-1];Array.isArray(t)?t.push(e):Object.defineProperty(t,sn,{value:e,writable:!0,enumerable:!0,configurable:!0})}if(e!==null&&typeof e=="object")at.push(e),Array.isArray(e)?Se="beforeArrayValue":Se="beforePropertyName";else{const t=at[at.length-1];t==null?Se="end":Array.isArray(t)?Se="afterArrayValue":Se="afterPropertyValue"}}function or(){at.pop();const e=at[at.length-1];e==null?Se="end":Array.isArray(e)?Se="afterArrayValue":Se="afterPropertyValue"}function fe(e){return cr(e===void 0?`JSON5: invalid end of input at ${Bt}:${qe}`:`JSON5: invalid character '${di(e)}' at ${Bt}:${qe}`)}function Tt(){return cr(`JSON5: invalid end of input at ${Bt}:${qe}`)}function Di(){return qe-=5,cr(`JSON5: invalid identifier character at ${Bt}:${qe}`)}function zl(e){console.warn(`JSON5: '${di(e)}' in strings is not valid ECMAScript; consider escaping`)}function di(e){const t={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(t[e])return t[e];if(e<" "){const u=e.charCodeAt(0).toString(16);return"\\x"+("00"+u).substring(u.length)}return e}function cr(e){const t=new SyntaxError(e);return t.lineNumber=Bt,t.columnNumber=qe,t}var Jl=function(t,u,r){const n=[];let i="",o,c,l="",d;if(u!=null&&typeof u=="object"&&!Array.isArray(u)&&(r=u.space,d=u.quote,u=u.replacer),typeof u=="function")c=u;else if(Array.isArray(u)){o=[];for(const F of u){let N;typeof F=="string"?N=F:(typeof F=="number"||F instanceof String||F instanceof Number)&&(N=String(F)),N!==void 0&&o.indexOf(N)<0&&o.push(N)}}return r instanceof Number?r=Number(r):r instanceof String&&(r=String(r)),typeof r=="number"?r>0&&(r=Math.min(10,Math.floor(r)),l="          ".substr(0,r)):typeof r=="string"&&(l=r.substr(0,10)),a("",{"":t});function a(F,N){let m=N[F];switch(m!=null&&(typeof m.toJSON5=="function"?m=m.toJSON5(F):typeof m.toJSON=="function"&&(m=m.toJSON(F))),c&&(m=c.call(N,F,m)),m instanceof Number?m=Number(m):m instanceof String?m=String(m):m instanceof Boolean&&(m=m.valueOf()),m){case null:return"null";case!0:return"true";case!1:return"false"}if(typeof m=="string")return h(m);if(typeof m=="number")return String(m);if(typeof m=="object")return Array.isArray(m)?R(m):B(m)}function h(F){const N={"'":.1,'"':.2},m={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};let T="";for(let w=0;w<F.length;w++){const L=F[w];switch(L){case"'":case'"':N[L]++,T+=L;continue;case"\0":if(pe.isDigit(F[w+1])){T+="\\x00";continue}}if(m[L]){T+=m[L];continue}if(L<" "){let J=L.charCodeAt(0).toString(16);T+="\\x"+("00"+J).substring(J.length);continue}T+=L}const $=d||Object.keys(N).reduce((w,L)=>N[w]<N[L]?w:L);return T=T.replace(new RegExp($,"g"),m[$]),$+T+$}function B(F){if(n.indexOf(F)>=0)throw TypeError("Converting circular structure to JSON5");n.push(F);let N=i;i=i+l;let m=o||Object.keys(F),T=[];for(const w of m){const L=a(w,F);if(L!==void 0){let J=E(w)+":";l!==""&&(J+=" "),J+=L,T.push(J)}}let $;if(T.length===0)$="{}";else{let w;if(l==="")w=T.join(","),$="{"+w+"}";else{let L=`,
`+i;w=T.join(L),$=`{
`+i+w+`,
`+N+"}"}}return n.pop(),i=N,$}function E(F){if(F.length===0)return h(F);const N=String.fromCodePoint(F.codePointAt(0));if(!pe.isIdStartChar(N))return h(F);for(let m=N.length;m<F.length;m++)if(!pe.isIdContinueChar(String.fromCodePoint(F.codePointAt(m))))return h(F);return F}function R(F){if(n.indexOf(F)>=0)throw TypeError("Converting circular structure to JSON5");n.push(F);let N=i;i=i+l;let m=[];for(let $=0;$<F.length;$++){const w=a(String($),F);m.push(w!==void 0?w:"null")}let T;if(m.length===0)T="[]";else if(l==="")T="["+m.join(",")+"]";else{let $=`,
`+i,w=m.join($);T=`[
`+i+w+`,
`+N+"]"}return n.pop(),i=N,T}},Yl={parse:kl,stringify:Jl};const Zl=(e,t)=>{const u=e.__vccOpts||e;for(const[r,n]of t)u[r]=n;return u},Ql={name:"rtf",props:{vueState:{type:Object,required:!0},data:{type:Object,required:!0}},setup(e){const t=uu({optation:{},totalcankao:0,type:""}),u=()=>{var l;try{const d=e.vueState.data.allText,a=atob(d),h=window.location.href,B=new URLSearchParams(window.location.search);t.type=B.get("type");const E=new Uint8Array(a.length);for(let F=0;F<a.length;F++)E[F]=a.charCodeAt(F);const R=new TextDecoder("utf-8").decode(E);t.optation=Yl.parse(R),t.totalcankao=((l=t.optation.amcontentinfo)==null?void 0:l.length)||0,console.log("解析结果:",t.optation)}catch(d){console.error("解析失败:",d)}};Qn(()=>{u()});const r=l=>{var d=l.billId,a="/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid="+d+"&status=view&styleid=25630f43-6519-2928-44ad-a3af7d19c3de&j=true&types=readviewMod=wf&enableEdit=false&runtime=true";c(d,"知识发布",a),s},n=()=>{const l={},d={},a={},h="生成维修申请",B=window.top.AssistantIntgSDK||{},E={message:{text:h,dataid:"",files:[]},contextValues:{framework:l,user:d,capture:a},target:""};B.sendCommandViaCUI(E)},i=()=>{try{return window.parent&&window.parent.gspframeworkAdapterService?window.parent:window.top&&window.top.gspframeworkAdapterService?window.top:window}catch{return window}},o=l=>{const d=new RegExp("(^|&)"+l+"=([^&]*)(&|$)","i");let a;return window.location.search&&(a=window.location.search.substr(1).match(d)),a!=null||window.location.hash&&(a=window.location.hash.substr(3).match(d),a!=null)?unescape(a[2]):""},c=(l,d,a)=>{const h=i();if(h){const B=h.allgspfuncs||h.gspframeworkAdapterService.funcSvc.getAllCachedFuncs(),E=o("pfuncid")||o("funcId")||o("funcid"),R="funcid="+l+"&pfuncid="+E;if(a.indexOf("?")===-1?a+="?"+R:a+="&"+R,h.gspframeworkAdapterService)return h.gspframeworkAdapterService.appSvc.getFeb().post("farrisapp-click",{FuncName:d,active:!1,appType:"menu",code:l,tabId:l,funcId:E||B[0].id,id:l,su:"views",isjquery:!0,reload:void 0,sessionid:localStorage.session,src:a,url:a}),l}};return{...Xi(t),jumpknowledge:r,sendmessagetoweixiu:n}}},Xl={class:"informationsum"},ef={class:"cardlist-source",style:{"border-radius":"4px",background:"#fff"}},tf={class:"scroll-container",style:{"overflow-x":"auto","padding-bottom":"8px"}},uf={class:"cardlist-sourceid",style:{display:"inline-flex",padding:"10px",border:"0px","border-radius":"4px",gap:"8px"}},rf=["onClick"],nf={class:"sourcetitle",style:{"font-family":"PingFangSC-Regular","font-size":"14px",color:"#333333","text-align":"justify","line-height":"20px","font-weight":"400"}},sf={class:"sourcebasename",style:{"margin-top":"12px","font-family":"PingFangSC-Regular","font-size":"13px",color:"rgba(0,0,0,0.45)","font-weight":"400"}};function of(e,t,u,r,n,i){var o;return Hr(),Wr(Ke,null,[we("div",Xl,[we("span",null,"参考"+wu(e.totalcankao)+"条信息",1)]),we("div",ef,[we("div",tf,[we("div",uf,[(Hr(!0),Wr(Ke,null,Po(((o=e.optation)==null?void 0:o.amcontentinfo)||[],(c,l)=>(Hr(),Wr("div",{class:"cardlist-source-item",key:l,onClick:d=>r.jumpknowledge(c),style:{width:"248px",height:"100px","flex-shrink":"0",display:"inline-block",background:"#F8FAFF","border-radius":"8px",cursor:"pointer",padding:"16px 15px 0px 16px",flex:"0 0 248px"}},[we("div",nf,[we("span",null,wu(c.title),1)]),we("div",sf,[we("span",null,wu(c.basename),1)])],8,rf))),128))])])]),we("div",null,[t[1]||(t[1]=we("div",{style:{"padding-top":"5px"}},[we("span",null,"请查看是否需要生成维修申请")],-1)),we("button",{onClick:t[0]||(t[0]=c=>r.sendmessagetoweixiu()),style:{padding:"5px","font-size":"medium",color:"blue",background:"none",border:"none","padding-left":"10px",cursor:"pointer"}},"我需要生成维修申请")])],64)}const cf=Zl(Ql,[["render",of],["__scopeId","data-v-5df70c94"]]);function lf(e){return JSON.parse(JSON.stringify(e))}class ff{async initialize(){console.log("ExampleWidgetAPI initialized.")}async cleanup(){console.log("ExampleWidgetAPI cleanup.")}createWidget(){return new af}}class af{constructor(){fr(this,"vueState_",uu({options:{mode:"full",implOptions:{data:{}}},data:{}}));fr(this,"vueApp_");fr(this,"isMounted_",!1);const t=this.vueState_;this.vueApp_=Jc({setup(){const u=t;return console.log("constuct"),cu(()=>u.options,r=>{console.log("ExampleWidget options updated.",r)}),cu(()=>u.data,r=>{console.log("ExampleWidget data updated.",r)}),{state:u}},render(){return console.log("render data",this.state.data),xc(cf,{vueState:t})}})}namespace(){return"sys"}name(){return"rtf"}options(){return this.vueState_.options}updateOptions(t){this.vueState_.options={...this.options(),...lf(t)}}data(){return console.log(this.options().implOptions.data),this.vueState_.data}updateData(t){console.log("ExampleWidget data updating.",t),this.vueState_.data=t,console.log("ExampleWidget data updated.",this.vueState_)}async mount(t){if(this.isMounted_)throw new Error("ExampleWidget already mounted.");this.vueApp_.mount(t),this.isMounted_=!0}async unmount(){if(!this.isMounted_)throw new Error("ExampleWidget is NOT mounted.");this.vueApp_.unmount(),this.isMounted_=!1}async rerender(){}async dispose(){this.isMounted_&&this.unmount()}addEventListener(t){return()=>{}}}const fn=new ff;function Df(){return fn.initialize()}function df(){return fn.cleanup()}function hf(){return fn.createWidget()}Oe.cleanup=df,Oe.createWidget=hf,Oe.initialize=Df,Object.defineProperty(Oe,Symbol.toStringTag,{value:"Module"})});
