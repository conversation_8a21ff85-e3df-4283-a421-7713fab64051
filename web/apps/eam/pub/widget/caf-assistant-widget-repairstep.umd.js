(function(ve,me){typeof exports=="object"&&typeof module<"u"?me(exports):typeof define=="function"&&define.amd?define(["exports"],me):(ve=typeof globalThis<"u"?globalThis:ve||self,me(ve.RTFWidget={}))})(this,function(ve){"use strict";var vf=Object.defineProperty;var xf=(ve,me,G)=>me in ve?vf(ve,me,{enumerable:!0,configurable:!0,writable:!0,value:G}):ve[me]=G;var Wn=(ve,me,G)=>xf(ve,typeof me!="symbol"?me+"":me,G);/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function me(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const G={},bt=[],De=()=>{},Mi=()=>!1,on=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Un=e=>e.startsWith("onUpdate:"),be=Object.assign,Bn=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ni=Object.prototype.hasOwnProperty,Z=(e,t)=>Ni.call(e,t),F=Array.isArray,Mt=e=>ln(e)==="[object Map]",Di=e=>ln(e)==="[object Set]",L=e=>typeof e=="function",fe=e=>typeof e=="string",_t=e=>typeof e=="symbol",le=e=>e!==null&&typeof e=="object",Dr=e=>(le(e)||L(e))&&L(e.then)&&L(e.catch),ji=Object.prototype.toString,ln=e=>ji.call(e),$i=e=>ln(e).slice(8,-1),Fi=e=>ln(e)==="[object Object]",Kn=e=>fe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Nt=me(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),cn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Li=/-(\w)/g,rt=cn(e=>e.replace(Li,(t,n)=>n?n.toUpperCase():"")),Hi=/\B([A-Z])/g,ft=cn(e=>e.replace(Hi,"-$1").toLowerCase()),jr=cn(e=>e.charAt(0).toUpperCase()+e.slice(1)),kn=cn(e=>e?`on${jr(e)}`:""),ut=(e,t)=>!Object.is(e,t),Gn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},$r=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},Vi=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Fr;const fn=()=>Fr||(Fr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function qn(e){if(F(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=fe(r)?Ki(r):qn(r);if(s)for(const i in s)t[i]=s[i]}return t}else if(fe(e)||le(e))return e}const Wi=/;(?![^(]*\))/g,Ui=/:([^]+)/,Bi=/\/\*[^]*?\*\//g;function Ki(e){const t={};return e.replace(Bi,"").split(Wi).forEach(n=>{if(n){const r=n.split(Ui);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Jn(e){let t="";if(fe(e))t=e;else if(F(e))for(let n=0;n<e.length;n++){const r=Jn(e[n]);r&&(t+=r+" ")}else if(le(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ki=me("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Lr(e){return!!e||e===""}var Gi={NODE_ENV:"production"};let Se;class qi{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Se,!t&&Se&&(this.index=(Se.scopes||(Se.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Se;try{return Se=this,t()}finally{Se=n}}}on(){Se=this}off(){Se=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function Ji(){return Se}let re;const zn=new WeakSet;class Hr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Se&&Se.active&&Se.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,zn.has(this)&&(zn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Wr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Gr(this),Ur(this);const t=re,n=je;re=this,je=!0;try{return this.fn()}finally{Br(this),re=t,je=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Xn(t);this.deps=this.depsTail=void 0,Gr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?zn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Qn(this)&&this.run()}get dirty(){return Qn(this)}}let Vr=0,Dt,jt;function Wr(e,t=!1){if(e.flags|=8,t){e.next=jt,jt=e;return}e.next=Dt,Dt=e}function Yn(){Vr++}function Zn(){if(--Vr>0)return;if(jt){let t=jt;for(jt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Dt;){let t=Dt;for(Dt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Ur(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Br(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),Xn(r),zi(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function Qn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Kr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Kr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===$t))return;e.globalVersion=$t;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Qn(e)){e.flags&=-3;return}const n=re,r=je;re=e,je=!0;try{Ur(e);const s=e.fn(e._value);(t.version===0||ut(s,e._value))&&(e._value=s,t.version++)}catch(s){throw t.version++,s}finally{re=n,je=r,Br(e),e.flags&=-3}}function Xn(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)Xn(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function zi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let je=!0;const kr=[];function Je(){kr.push(je),je=!1}function ze(){const e=kr.pop();je=e===void 0?!0:e}function Gr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=re;re=void 0;try{t()}finally{re=n}}}let $t=0;class Yi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class qr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!re||!je||re===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==re)n=this.activeLink=new Yi(re,this),re.deps?(n.prevDep=re.depsTail,re.depsTail.nextDep=n,re.depsTail=n):re.deps=re.depsTail=n,Jr(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=re.depsTail,n.nextDep=void 0,re.depsTail.nextDep=n,re.depsTail=n,re.deps===n&&(re.deps=r)}return n}trigger(t){this.version++,$t++,this.notify(t)}notify(t){Yn();try{Gi.NODE_ENV;for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Zn()}}}function Jr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Jr(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const un=new WeakMap,at=Symbol(""),er=Symbol(""),Ft=Symbol("");function pe(e,t,n){if(je&&re){let r=un.get(e);r||un.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new qr),s.map=r,s.key=n),s.track()}}function Ye(e,t,n,r,s,i){const o=un.get(e);if(!o){$t++;return}const l=f=>{f&&f.trigger()};if(Yn(),t==="clear")o.forEach(l);else{const f=F(e),d=f&&Kn(n);if(f&&n==="length"){const a=Number(r);o.forEach((h,x)=>{(x==="length"||x===Ft||!_t(x)&&x>=a)&&l(h)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),d&&l(o.get(Ft)),t){case"add":f?d&&l(o.get("length")):(l(o.get(at)),Mt(e)&&l(o.get(er)));break;case"delete":f||(l(o.get(at)),Mt(e)&&l(o.get(er)));break;case"set":Mt(e)&&l(o.get(at));break}}Zn()}function Zi(e,t){const n=un.get(e);return n&&n.get(t)}function yt(e){const t=q(e);return t===e?t:(pe(t,"iterate",Ft),$e(e)?t:t.map(Ee))}function tr(e){return pe(e=q(e),"iterate",Ft),e}const Qi={__proto__:null,[Symbol.iterator](){return nr(this,Symbol.iterator,Ee)},concat(...e){return yt(this).concat(...e.map(t=>F(t)?yt(t):t))},entries(){return nr(this,"entries",e=>(e[1]=Ee(e[1]),e))},every(e,t){return Ze(this,"every",e,t,void 0,arguments)},filter(e,t){return Ze(this,"filter",e,t,n=>n.map(Ee),arguments)},find(e,t){return Ze(this,"find",e,t,Ee,arguments)},findIndex(e,t){return Ze(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ze(this,"findLast",e,t,Ee,arguments)},findLastIndex(e,t){return Ze(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ze(this,"forEach",e,t,void 0,arguments)},includes(...e){return rr(this,"includes",e)},indexOf(...e){return rr(this,"indexOf",e)},join(e){return yt(this).join(e)},lastIndexOf(...e){return rr(this,"lastIndexOf",e)},map(e,t){return Ze(this,"map",e,t,void 0,arguments)},pop(){return Lt(this,"pop")},push(...e){return Lt(this,"push",e)},reduce(e,...t){return zr(this,"reduce",e,t)},reduceRight(e,...t){return zr(this,"reduceRight",e,t)},shift(){return Lt(this,"shift")},some(e,t){return Ze(this,"some",e,t,void 0,arguments)},splice(...e){return Lt(this,"splice",e)},toReversed(){return yt(this).toReversed()},toSorted(e){return yt(this).toSorted(e)},toSpliced(...e){return yt(this).toSpliced(...e)},unshift(...e){return Lt(this,"unshift",e)},values(){return nr(this,"values",Ee)}};function nr(e,t,n){const r=tr(e),s=r[t]();return r!==e&&!$e(e)&&(s._next=s.next,s.next=()=>{const i=s._next();return i.value&&(i.value=n(i.value)),i}),s}const Xi=Array.prototype;function Ze(e,t,n,r,s,i){const o=tr(e),l=o!==e&&!$e(e),f=o[t];if(f!==Xi[t]){const h=f.apply(e,i);return l?Ee(h):h}let d=n;o!==e&&(l?d=function(h,x){return n.call(this,Ee(h),x,e)}:n.length>2&&(d=function(h,x){return n.call(this,h,x,e)}));const a=f.call(o,d,r);return l&&s?s(a):a}function zr(e,t,n,r){const s=tr(e);let i=n;return s!==e&&($e(e)?n.length>3&&(i=function(o,l,f){return n.call(this,o,l,f,e)}):i=function(o,l,f){return n.call(this,o,Ee(l),f,e)}),s[t](i,...r)}function rr(e,t,n){const r=q(e);pe(r,"iterate",Ft);const s=r[t](...n);return(s===-1||s===!1)&&ir(n[0])?(n[0]=q(n[0]),r[t](...n)):s}function Lt(e,t,n=[]){Je(),Yn();const r=q(e)[t].apply(e,n);return Zn(),ze(),r}const eo=me("__proto__,__v_isRef,__isVue"),Yr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(_t));function to(e){_t(e)||(e=String(e));const t=q(this);return pe(t,"has",e),t.hasOwnProperty(e)}class Zr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return i;if(n==="__v_raw")return r===(s?i?rs:ns:i?ts:es).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const o=F(t);if(!s){let f;if(o&&(f=Qi[n]))return f;if(n==="hasOwnProperty")return to}const l=Reflect.get(t,n,ue(t)?t:r);return(_t(n)?Yr.has(n):eo(n))||(s||pe(t,"get",n),i)?l:ue(l)?o&&Kn(n)?l:l.value:le(l)?s?ss(l):Ht(l):l}}class Qr extends Zr{constructor(t=!1){super(!1,t)}set(t,n,r,s){let i=t[n];if(!this._isShallow){const f=vt(i);if(!$e(r)&&!vt(r)&&(i=q(i),r=q(r)),!F(t)&&ue(i)&&!ue(r))return f?!1:(i.value=r,!0)}const o=F(t)&&Kn(n)?Number(n)<t.length:Z(t,n),l=Reflect.set(t,n,r,ue(t)?t:s);return t===q(s)&&(o?ut(r,i)&&Ye(t,"set",n,r):Ye(t,"add",n,r)),l}deleteProperty(t,n){const r=Z(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&Ye(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!_t(n)||!Yr.has(n))&&pe(t,"has",n),r}ownKeys(t){return pe(t,"iterate",F(t)?"length":at),Reflect.ownKeys(t)}}class Xr extends Zr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const no=new Qr,ro=new Xr,so=new Qr(!0),io=new Xr(!0),sr=e=>e,an=e=>Reflect.getPrototypeOf(e);function oo(e,t,n){return function(...r){const s=this.__v_raw,i=q(s),o=Mt(i),l=e==="entries"||e===Symbol.iterator&&o,f=e==="keys"&&o,d=s[e](...r),a=n?sr:t?or:Ee;return!t&&pe(i,"iterate",f?er:at),{next(){const{value:h,done:x}=d.next();return x?{value:h,done:x}:{value:l?[a(h[0]),a(h[1])]:a(h),done:x}},[Symbol.iterator](){return this}}}}function dn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function lo(e,t){const n={get(s){const i=this.__v_raw,o=q(i),l=q(s);e||(ut(s,l)&&pe(o,"get",s),pe(o,"get",l));const{has:f}=an(o),d=t?sr:e?or:Ee;if(f.call(o,s))return d(i.get(s));if(f.call(o,l))return d(i.get(l));i!==o&&i.get(s)},get size(){const s=this.__v_raw;return!e&&pe(q(s),"iterate",at),Reflect.get(s,"size",s)},has(s){const i=this.__v_raw,o=q(i),l=q(s);return e||(ut(s,l)&&pe(o,"has",s),pe(o,"has",l)),s===l?i.has(s):i.has(s)||i.has(l)},forEach(s,i){const o=this,l=o.__v_raw,f=q(l),d=t?sr:e?or:Ee;return!e&&pe(f,"iterate",at),l.forEach((a,h)=>s.call(i,d(a),d(h),o))}};return be(n,e?{add:dn("add"),set:dn("set"),delete:dn("delete"),clear:dn("clear")}:{add(s){!t&&!$e(s)&&!vt(s)&&(s=q(s));const i=q(this);return an(i).has.call(i,s)||(i.add(s),Ye(i,"add",s,s)),this},set(s,i){!t&&!$e(i)&&!vt(i)&&(i=q(i));const o=q(this),{has:l,get:f}=an(o);let d=l.call(o,s);d||(s=q(s),d=l.call(o,s));const a=f.call(o,s);return o.set(s,i),d?ut(i,a)&&Ye(o,"set",s,i):Ye(o,"add",s,i),this},delete(s){const i=q(this),{has:o,get:l}=an(i);let f=o.call(i,s);f||(s=q(s),f=o.call(i,s)),l&&l.call(i,s);const d=i.delete(s);return f&&Ye(i,"delete",s,void 0),d},clear(){const s=q(this),i=s.size!==0,o=s.clear();return i&&Ye(s,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=oo(s,e,t)}),n}function hn(e,t){const n=lo(e,t);return(r,s,i)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(Z(n,s)&&s in r?n:r,s,i)}const co={get:hn(!1,!1)},fo={get:hn(!1,!0)},uo={get:hn(!0,!1)},ao={get:hn(!0,!0)},es=new WeakMap,ts=new WeakMap,ns=new WeakMap,rs=new WeakMap;function ho(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function po(e){return e.__v_skip||!Object.isExtensible(e)?0:ho($i(e))}function Ht(e){return vt(e)?e:gn(e,!1,no,co,es)}function go(e){return gn(e,!1,so,fo,ts)}function ss(e){return gn(e,!0,ro,uo,ns)}function pn(e){return gn(e,!0,io,ao,rs)}function gn(e,t,n,r,s){if(!le(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=s.get(e);if(i)return i;const o=po(e);if(o===0)return e;const l=new Proxy(e,o===2?r:n);return s.set(e,l),l}function Vt(e){return vt(e)?Vt(e.__v_raw):!!(e&&e.__v_isReactive)}function vt(e){return!!(e&&e.__v_isReadonly)}function $e(e){return!!(e&&e.__v_isShallow)}function ir(e){return e?!!e.__v_raw:!1}function q(e){const t=e&&e.__v_raw;return t?q(t):e}function mo(e){return!Z(e,"__v_skip")&&Object.isExtensible(e)&&$r(e,"__v_skip",!0),e}const Ee=e=>le(e)?Ht(e):e,or=e=>le(e)?ss(e):e;function ue(e){return e?e.__v_isRef===!0:!1}function bo(e){return ue(e)?e.value:e}const _o={get:(e,t,n)=>t==="__v_raw"?e:bo(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return ue(s)&&!ue(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function is(e){return Vt(e)?e:new Proxy(e,_o)}function yo(e){const t=F(e)?new Array(e.length):{};for(const n in e)t[n]=xo(e,n);return t}class vo{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Zi(q(this._object),this._key)}}function xo(e,t,n){const r=e[t];return ue(r)?r:new vo(e,t,n)}class wo{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new qr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=$t-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&re!==this)return Wr(this,!0),!0}get value(){const t=this.dep.track();return Kr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function So(e,t,n=!1){let r,s;return L(e)?r=e:(r=e.get,s=e.set),new wo(r,s,n)}const mn={},bn=new WeakMap;let dt;function Eo(e,t=!1,n=dt){if(n){let r=bn.get(n);r||bn.set(n,r=[]),r.push(e)}}function Oo(e,t,n=G){const{immediate:r,deep:s,once:i,scheduler:o,augmentJob:l,call:f}=n,d=R=>s?R:$e(R)||s===!1||s===0?st(R,1):st(R);let a,h,x,v,I=!1,T=!1;if(ue(e)?(h=()=>e.value,I=$e(e)):Vt(e)?(h=()=>d(e),I=!0):F(e)?(T=!0,I=e.some(R=>Vt(R)||$e(R)),h=()=>e.map(R=>{if(ue(R))return R.value;if(Vt(R))return d(R);if(L(R))return f?f(R,2):R()})):L(e)?t?h=f?()=>f(e,2):e:h=()=>{if(x){Je();try{x()}finally{ze()}}const R=dt;dt=a;try{return f?f(e,3,[v]):e(v)}finally{dt=R}}:h=De,t&&s){const R=h,k=s===!0?1/0:s;h=()=>st(R(),k)}const H=Ji(),P=()=>{a.stop(),H&&H.active&&Bn(H.effects,a)};if(i&&t){const R=t;t=(...k)=>{R(...k),P()}}let D=T?new Array(e.length).fill(mn):mn;const V=R=>{if(!(!(a.flags&1)||!a.dirty&&!R))if(t){const k=a.run();if(s||I||(T?k.some((ee,$)=>ut(ee,D[$])):ut(k,D))){x&&x();const ee=dt;dt=a;try{const $=[k,D===mn?void 0:T&&D[0]===mn?[]:D,v];f?f(t,3,$):t(...$),D=k}finally{dt=ee}}}else a.run()};return l&&l(V),a=new Hr(h),a.scheduler=o?()=>o(V,!1):V,v=R=>Eo(R,!1,a),x=a.onStop=()=>{const R=bn.get(a);if(R){if(f)f(R,4);else for(const k of R)k();bn.delete(a)}},t?r?V(!0):D=a.run():o?o(V.bind(null,!0),!0):a.run(),P.pause=a.pause.bind(a),P.resume=a.resume.bind(a),P.stop=P,P}function st(e,t=1/0,n){if(t<=0||!le(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ue(e))st(e.value,t,n);else if(F(e))for(let r=0;r<e.length;r++)st(e[r],t,n);else if(Di(e)||Mt(e))e.forEach(r=>{st(r,t,n)});else if(Fi(e)){for(const r in e)st(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&st(e[r],t,n)}return e}var it={NODE_ENV:"production"};const Wt=[];let lr=!1;function Co(e,...t){if(lr)return;lr=!0,Je();const n=Wt.length?Wt[Wt.length-1].component:null,r=n&&n.appContext.config.warnHandler,s=To();if(r)xt(r,n,11,[e+t.map(i=>{var o,l;return(l=(o=i.toString)==null?void 0:o.call(i))!=null?l:JSON.stringify(i)}).join(""),n&&n.proxy,s.map(({vnode:i})=>`at <${Zs(n,i.type)}>`).join(`
`),s]);else{const i=[`[Vue warn]: ${e}`,...t];s.length&&i.push(`
`,...Ao(s)),console.warn(...i)}ze(),lr=!1}function To(){let e=Wt[Wt.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const r=e.component&&e.component.parent;e=r&&r.vnode}return t}function Ao(e){const t=[];return e.forEach((n,r)=>{t.push(...r===0?[]:[`
`],...Ro(n))}),t}function Ro({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",r=e.component?e.component.parent==null:!1,s=` at <${Zs(e.component,e.type,r)}`,i=">"+n;return e.props?[s,...Po(e.props),i]:[s+i]}function Po(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(r=>{t.push(...os(r,e[r]))}),n.length>3&&t.push(" ..."),t}function os(e,t,n){return fe(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:ue(t)?(t=os(e,q(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):L(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=q(t),n?t:[`${e}=`,t])}function xt(e,t,n,r){try{return r?e(...r):e()}catch(s){_n(s,t,n)}}function Fe(e,t,n,r){if(L(e)){const s=xt(e,t,n,r);return s&&Dr(s)&&s.catch(i=>{_n(i,t,n)}),s}if(F(e)){const s=[];for(let i=0;i<e.length;i++)s.push(Fe(e[i],t,n,r));return s}}function _n(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||G;if(t){let l=t.parent;const f=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,f,d)===!1)return}l=l.parent}if(i){Je(),xt(i,null,10,[e,f,d]),ze();return}}Io(e,n,s,r,o)}function Io(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const _e=[];let Le=-1;const wt=[];let ot=null,St=0;const ls=Promise.resolve();let yn=null;function Mo(e){const t=yn||ls;return e?t.then(this?e.bind(this):e):t}function No(e){let t=Le+1,n=_e.length;for(;t<n;){const r=t+n>>>1,s=_e[r],i=Ut(s);i<e||i===e&&s.flags&2?t=r+1:n=r}return t}function cr(e){if(!(e.flags&1)){const t=Ut(e),n=_e[_e.length-1];!n||!(e.flags&2)&&t>=Ut(n)?_e.push(e):_e.splice(No(t),0,e),e.flags|=1,cs()}}function cs(){yn||(yn=ls.then(as))}function Do(e){F(e)?wt.push(...e):ot&&e.id===-1?ot.splice(St+1,0,e):e.flags&1||(wt.push(e),e.flags|=1),cs()}function fs(e,t,n=Le+1){for(;n<_e.length;n++){const r=_e[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;_e.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function us(e){if(wt.length){const t=[...new Set(wt)].sort((n,r)=>Ut(n)-Ut(r));if(wt.length=0,ot){ot.push(...t);return}for(ot=t,St=0;St<ot.length;St++){const n=ot[St];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ot=null,St=0}}const Ut=e=>e.id==null?e.flags&2?-1:1/0:e.id;function as(e){const t=De;try{for(Le=0;Le<_e.length;Le++){const n=_e[Le];n&&!(n.flags&8)&&(it.NODE_ENV!=="production"&&t(n),n.flags&4&&(n.flags&=-2),xt(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;Le<_e.length;Le++){const n=_e[Le];n&&(n.flags&=-2)}Le=-1,_e.length=0,us(),yn=null,(_e.length||wt.length)&&as()}}let He=null,ds=null;function vn(e){const t=He;return He=e,ds=e&&e.type.__scopeId||null,t}function jo(e,t=He,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&Bs(-1);const i=vn(t);let o;try{o=e(...s)}finally{vn(i),r._d&&Bs(1)}return o};return r._n=!0,r._c=!0,r._d=!0,r}function ht(e,t,n,r){const s=e.dirs,i=t&&t.dirs;for(let o=0;o<s.length;o++){const l=s[o];i&&(l.oldValue=i[o].value);let f=l.dir[r];f&&(Je(),Fe(f,n,8,[e.el,l,e,t]),ze())}}const $o=Symbol("_vte"),Fo=e=>e.__isTeleport;function fr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,fr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function hs(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function xn(e,t,n,r,s=!1){if(F(e)){e.forEach((I,T)=>xn(I,t&&(F(t)?t[T]:t),n,r,s));return}if(Bt(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&xn(e,t,n,r.component.subTree);return}const i=r.shapeFlag&4?xr(r.component):r.el,o=s?null:i,{i:l,r:f}=e,d=t&&t.r,a=l.refs===G?l.refs={}:l.refs,h=l.setupState,x=q(h),v=h===G?()=>!1:I=>Z(x,I);if(d!=null&&d!==f&&(fe(d)?(a[d]=null,v(d)&&(h[d]=null)):ue(d)&&(d.value=null)),L(f))xt(f,l,12,[o,a]);else{const I=fe(f),T=ue(f);if(I||T){const H=()=>{if(e.f){const P=I?v(f)?h[f]:a[f]:f.value;s?F(P)&&Bn(P,i):F(P)?P.includes(i)||P.push(i):I?(a[f]=[i],v(f)&&(h[f]=a[f])):(f.value=[i],e.k&&(a[e.k]=f.value))}else I?(a[f]=o,v(f)&&(h[f]=o)):T&&(f.value=o,e.k&&(a[e.k]=o))};o?(H.id=-1,Oe(H,n)):H()}}}fn().requestIdleCallback,fn().cancelIdleCallback;const Bt=e=>!!e.type.__asyncLoader,ps=e=>e.type.__isKeepAlive;function Lo(e,t){gs(e,"a",t)}function Ho(e,t){gs(e,"da",t)}function gs(e,t,n=ae){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(wn(t,r,n),n){let s=n.parent;for(;s&&s.parent;)ps(s.parent.vnode)&&Vo(r,t,n,s),s=s.parent}}function Vo(e,t,n,r){const s=wn(t,e,r,!0);ms(()=>{Bn(r[t],s)},n)}function wn(e,t,n=ae,r=!1){if(n){const s=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Je();const l=Yt(n),f=Fe(t,n,e,o);return l(),ze(),f});return r?s.unshift(i):s.push(i),i}}const Qe=e=>(t,n=ae)=>{(!Zt||e==="sp")&&wn(e,(...r)=>t(...r),n)},Wo=Qe("bm"),Uo=Qe("m"),Bo=Qe("bu"),Ko=Qe("u"),ko=Qe("bum"),ms=Qe("um"),Go=Qe("sp"),qo=Qe("rtg"),Jo=Qe("rtc");function zo(e,t=ae){wn("ec",e,t)}const Yo=Symbol.for("v-ndc"),ur=e=>e?Js(e)?xr(e):ur(e.parent):null,Kt=be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ur(e.parent),$root:e=>ur(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>vs(e),$forceUpdate:e=>e.f||(e.f=()=>{cr(e.update)}),$nextTick:e=>e.n||(e.n=Mo.bind(e.proxy)),$watch:e=>_l.bind(e)}),ar=(e,t)=>e!==G&&!e.__isScriptSetup&&Z(e,t),Zo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:i,accessCache:o,type:l,appContext:f}=e;let d;if(t[0]!=="$"){const v=o[t];if(v!==void 0)switch(v){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return i[t]}else{if(ar(r,t))return o[t]=1,r[t];if(s!==G&&Z(s,t))return o[t]=2,s[t];if((d=e.propsOptions[0])&&Z(d,t))return o[t]=3,i[t];if(n!==G&&Z(n,t))return o[t]=4,n[t];dr&&(o[t]=0)}}const a=Kt[t];let h,x;if(a)return t==="$attrs"&&pe(e.attrs,"get",""),a(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==G&&Z(n,t))return o[t]=4,n[t];if(x=f.config.globalProperties,Z(x,t))return x[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:i}=e;return ar(s,t)?(s[t]=n,!0):r!==G&&Z(r,t)?(r[t]=n,!0):Z(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:i}},o){let l;return!!n[o]||e!==G&&Z(e,o)||ar(t,o)||(l=i[0])&&Z(l,o)||Z(r,o)||Z(Kt,o)||Z(s.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Z(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function bs(e){return F(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let dr=!0;function Qo(e){const t=vs(e),n=e.proxy,r=e.ctx;dr=!1,t.beforeCreate&&_s(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:o,watch:l,provide:f,inject:d,created:a,beforeMount:h,mounted:x,beforeUpdate:v,updated:I,activated:T,deactivated:H,beforeDestroy:P,beforeUnmount:D,destroyed:V,unmounted:R,render:k,renderTracked:ee,renderTriggered:$,errorCaptured:Re,serverPrefetch:te,expose:Me,inheritAttrs:et,components:Rt,directives:Pt,filters:rn}=t;if(d&&Xo(d,r,null),o)for(const ie in o){const Y=o[ie];L(Y)&&(r[ie]=Y.bind(n))}if(s){const ie=s.call(n,n);le(ie)&&(e.data=Ht(ie))}if(dr=!0,i)for(const ie in i){const Y=i[ie],tt=L(Y)?Y.bind(n,n):L(Y.get)?Y.get.bind(n,n):De,It=!L(Y)&&L(Y.set)?Y.set.bind(n):De,nt=Kl({get:tt,set:It});Object.defineProperty(r,ie,{enumerable:!0,configurable:!0,get:()=>nt.value,set:y=>nt.value=y})}if(l)for(const ie in l)ys(l[ie],r,n,ie);if(f){const ie=L(f)?f.call(n):f;Reflect.ownKeys(ie).forEach(Y=>{il(Y,ie[Y])})}a&&_s(a,e,"c");function he(ie,Y){F(Y)?Y.forEach(tt=>ie(tt.bind(n))):Y&&ie(Y.bind(n))}if(he(Wo,h),he(Uo,x),he(Bo,v),he(Ko,I),he(Lo,T),he(Ho,H),he(zo,Re),he(Jo,ee),he(qo,$),he(ko,D),he(ms,R),he(Go,te),F(Me))if(Me.length){const ie=e.exposed||(e.exposed={});Me.forEach(Y=>{Object.defineProperty(ie,Y,{get:()=>n[Y],set:tt=>n[Y]=tt})})}else e.exposed||(e.exposed={});k&&e.render===De&&(e.render=k),et!=null&&(e.inheritAttrs=et),Rt&&(e.components=Rt),Pt&&(e.directives=Pt),te&&hs(e)}function Xo(e,t,n=De){F(e)&&(e=hr(e));for(const r in e){const s=e[r];let i;le(s)?"default"in s?i=En(s.from||r,s.default,!0):i=En(s.from||r):i=En(s),ue(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[r]=i}}function _s(e,t,n){Fe(F(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function ys(e,t,n,r){let s=r.includes(".")?Ls(n,r):()=>n[r];if(fe(e)){const i=t[e];L(i)&&Gt(s,i)}else if(L(e))Gt(s,e.bind(n));else if(le(e))if(F(e))e.forEach(i=>ys(i,t,n,r));else{const i=L(e.handler)?e.handler.bind(n):t[e.handler];L(i)&&Gt(s,i,e)}}function vs(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let f;return l?f=l:!s.length&&!n&&!r?f=t:(f={},s.length&&s.forEach(d=>Sn(f,d,o,!0)),Sn(f,t,o)),le(t)&&i.set(t,f),f}function Sn(e,t,n,r=!1){const{mixins:s,extends:i}=t;i&&Sn(e,i,n,!0),s&&s.forEach(o=>Sn(e,o,n,!0));for(const o in t)if(!(r&&o==="expose")){const l=el[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const el={data:xs,props:ws,emits:ws,methods:kt,computed:kt,beforeCreate:ye,created:ye,beforeMount:ye,mounted:ye,beforeUpdate:ye,updated:ye,beforeDestroy:ye,beforeUnmount:ye,destroyed:ye,unmounted:ye,activated:ye,deactivated:ye,errorCaptured:ye,serverPrefetch:ye,components:kt,directives:kt,watch:nl,provide:xs,inject:tl};function xs(e,t){return t?e?function(){return be(L(e)?e.call(this,this):e,L(t)?t.call(this,this):t)}:t:e}function tl(e,t){return kt(hr(e),hr(t))}function hr(e){if(F(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ye(e,t){return e?[...new Set([].concat(e,t))]:t}function kt(e,t){return e?be(Object.create(null),e,t):t}function ws(e,t){return e?F(e)&&F(t)?[...new Set([...e,...t])]:be(Object.create(null),bs(e),bs(t??{})):t}function nl(e,t){if(!e)return t;if(!t)return e;const n=be(Object.create(null),e);for(const r in t)n[r]=ye(e[r],t[r]);return n}function Ss(){return{app:null,config:{isNativeTag:Mi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let rl=0;function sl(e,t){return function(r,s=null){L(r)||(r=be({},r)),s!=null&&!le(s)&&(s=null);const i=Ss(),o=new WeakSet,l=[];let f=!1;const d=i.app={_uid:rl++,_component:r,_props:s,_container:null,_context:i,_instance:null,version:Gl,get config(){return i.config},set config(a){},use(a,...h){return o.has(a)||(a&&L(a.install)?(o.add(a),a.install(d,...h)):L(a)&&(o.add(a),a(d,...h))),d},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),d},component(a,h){return h?(i.components[a]=h,d):i.components[a]},directive(a,h){return h?(i.directives[a]=h,d):i.directives[a]},mount(a,h,x){if(!f){const v=d._ceVNode||Te(r,s);return v.appContext=i,x===!0?x="svg":x===!1&&(x=void 0),e(v,a,x),f=!0,d._container=a,a.__vue_app__=d,xr(v.component)}},onUnmount(a){l.push(a)},unmount(){f&&(Fe(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,h){return i.provides[a]=h,d},runWithContext(a){const h=Et;Et=d;try{return a()}finally{Et=h}}};return d}}let Et=null;function il(e,t){if(ae){let n=ae.provides;const r=ae.parent&&ae.parent.provides;r===n&&(n=ae.provides=Object.create(r)),n[e]=t}}function En(e,t,n=!1){const r=ae||He;if(r||Et){const s=Et?Et._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&L(t)?t.call(r&&r.proxy):t}}const Es={},Os=()=>Object.create(Es),Cs=e=>Object.getPrototypeOf(e)===Es;function ol(e,t,n,r=!1){const s={},i=Os();e.propsDefaults=Object.create(null),Ts(e,t,s,i);for(const o in e.propsOptions[0])o in s||(s[o]=void 0);n?e.props=r?s:go(s):e.type.props?e.props=s:e.props=i,e.attrs=i}function ll(e,t,n,r){const{props:s,attrs:i,vnode:{patchFlag:o}}=e,l=q(s),[f]=e.propsOptions;let d=!1;if((r||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let x=a[h];if(On(e.emitsOptions,x))continue;const v=t[x];if(f)if(Z(i,x))v!==i[x]&&(i[x]=v,d=!0);else{const I=rt(x);s[I]=pr(f,l,I,v,e,!1)}else v!==i[x]&&(i[x]=v,d=!0)}}}else{Ts(e,t,s,i)&&(d=!0);let a;for(const h in l)(!t||!Z(t,h)&&((a=ft(h))===h||!Z(t,a)))&&(f?n&&(n[h]!==void 0||n[a]!==void 0)&&(s[h]=pr(f,l,h,void 0,e,!0)):delete s[h]);if(i!==l)for(const h in i)(!t||!Z(t,h))&&(delete i[h],d=!0)}d&&Ye(e.attrs,"set","")}function Ts(e,t,n,r){const[s,i]=e.propsOptions;let o=!1,l;if(t)for(let f in t){if(Nt(f))continue;const d=t[f];let a;s&&Z(s,a=rt(f))?!i||!i.includes(a)?n[a]=d:(l||(l={}))[a]=d:On(e.emitsOptions,f)||(!(f in r)||d!==r[f])&&(r[f]=d,o=!0)}if(i){const f=q(n),d=l||G;for(let a=0;a<i.length;a++){const h=i[a];n[h]=pr(s,f,h,d[h],e,!Z(d,h))}}return o}function pr(e,t,n,r,s,i){const o=e[n];if(o!=null){const l=Z(o,"default");if(l&&r===void 0){const f=o.default;if(o.type!==Function&&!o.skipFactory&&L(f)){const{propsDefaults:d}=s;if(n in d)r=d[n];else{const a=Yt(s);r=d[n]=f.call(null,t),a()}}else r=f;s.ce&&s.ce._setProp(n,r)}o[0]&&(i&&!l?r=!1:o[1]&&(r===""||r===ft(n))&&(r=!0))}return r}const cl=new WeakMap;function As(e,t,n=!1){const r=n?cl:t.propsCache,s=r.get(e);if(s)return s;const i=e.props,o={},l=[];let f=!1;if(!L(e)){const a=h=>{f=!0;const[x,v]=As(h,t,!0);be(o,x),v&&l.push(...v)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!f)return le(e)&&r.set(e,bt),bt;if(F(i))for(let a=0;a<i.length;a++){const h=rt(i[a]);Rs(h)&&(o[h]=G)}else if(i)for(const a in i){const h=rt(a);if(Rs(h)){const x=i[a],v=o[h]=F(x)||L(x)?{type:x}:be({},x),I=v.type;let T=!1,H=!0;if(F(I))for(let P=0;P<I.length;++P){const D=I[P],V=L(D)&&D.name;if(V==="Boolean"){T=!0;break}else V==="String"&&(H=!1)}else T=L(I)&&I.name==="Boolean";v[0]=T,v[1]=H,(T||Z(v,"default"))&&l.push(h)}}const d=[o,l];return le(e)&&r.set(e,d),d}function Rs(e){return e[0]!=="$"&&!Nt(e)}const Ps=e=>e[0]==="_"||e==="$stable",gr=e=>F(e)?e.map(We):[We(e)],fl=(e,t,n)=>{if(t._n)return t;const r=jo((...s)=>(it.NODE_ENV!=="production"&&ae&&(!n||(n.root,ae.root)),gr(t(...s))),n);return r._c=!1,r},Is=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Ps(s))continue;const i=e[s];if(L(i))t[s]=fl(s,i,r);else if(i!=null){const o=gr(i);t[s]=()=>o}}},Ms=(e,t)=>{const n=gr(t);e.slots.default=()=>n},Ns=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},ul=(e,t,n)=>{const r=e.slots=Os();if(e.vnode.shapeFlag&32){const s=t._;s?(Ns(r,t,n),n&&$r(r,"_",s,!0)):Is(t,r)}else t&&Ms(e,t)},al=(e,t,n)=>{const{vnode:r,slots:s}=e;let i=!0,o=G;if(r.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:Ns(s,t,n):(i=!t.$stable,Is(t,s)),o=t}else t&&(Ms(e,t),o={default:1});if(i)for(const l in s)!Ps(l)&&o[l]==null&&delete s[l]},Oe=Ol;function dl(e){return hl(e)}function hl(e,t){const n=fn();n.__VUE__=!0;const{insert:r,remove:s,patchProp:i,createElement:o,createText:l,createComment:f,setText:d,setElementText:a,parentNode:h,nextSibling:x,setScopeId:v=De,insertStaticContent:I}=e,T=(c,u,p,b=null,g=null,m=null,E=void 0,S=null,w=!!u.dynamicChildren)=>{if(c===u)return;c&&!zt(c,u)&&(b=Q(c),y(c,g,m,!0),c=null),u.patchFlag===-2&&(w=!1,u.dynamicChildren=null);const{type:_,ref:N,shapeFlag:C}=u;switch(_){case Cn:H(c,u,p,b);break;case gt:P(c,u,p,b);break;case br:c==null&&D(u,p,b,E);break;case Ve:Rt(c,u,p,b,g,m,E,S,w);break;default:C&1?k(c,u,p,b,g,m,E,S,w):C&6?Pt(c,u,p,b,g,m,E,S,w):(C&64||C&128)&&_.process(c,u,p,b,g,m,E,S,w,oe)}N!=null&&g&&xn(N,c&&c.ref,m,u||c,!u)},H=(c,u,p,b)=>{if(c==null)r(u.el=l(u.children),p,b);else{const g=u.el=c.el;u.children!==c.children&&d(g,u.children)}},P=(c,u,p,b)=>{c==null?r(u.el=f(u.children||""),p,b):u.el=c.el},D=(c,u,p,b)=>{[c.el,c.anchor]=I(c.children,u,p,b,c.el,c.anchor)},V=({el:c,anchor:u},p,b)=>{let g;for(;c&&c!==u;)g=x(c),r(c,p,b),c=g;r(u,p,b)},R=({el:c,anchor:u})=>{let p;for(;c&&c!==u;)p=x(c),s(c),c=p;s(u)},k=(c,u,p,b,g,m,E,S,w)=>{u.type==="svg"?E="svg":u.type==="math"&&(E="mathml"),c==null?ee(u,p,b,g,m,E,S,w):te(c,u,g,m,E,S,w)},ee=(c,u,p,b,g,m,E,S)=>{let w,_;const{props:N,shapeFlag:C,transition:M,dirs:j}=c;if(w=c.el=o(c.type,m,N&&N.is,N),C&8?a(w,c.children):C&16&&Re(c.children,w,null,b,g,mr(c,m),E,S),j&&ht(c,null,b,"created"),$(w,c,c.scopeId,E,b),N){for(const se in N)se!=="value"&&!Nt(se)&&i(w,se,null,N[se],m,b);"value"in N&&i(w,"value",null,N.value,m),(_=N.onVnodeBeforeMount)&&Ue(_,b,c)}j&&ht(c,null,b,"beforeMount");const U=pl(g,M);U&&M.beforeEnter(w),r(w,u,p),((_=N&&N.onVnodeMounted)||U||j)&&Oe(()=>{_&&Ue(_,b,c),U&&M.enter(w),j&&ht(c,null,b,"mounted")},g)},$=(c,u,p,b,g)=>{if(p&&v(c,p),b)for(let m=0;m<b.length;m++)v(c,b[m]);if(g){let m=g.subTree;if(u===m||Us(m.type)&&(m.ssContent===u||m.ssFallback===u)){const E=g.vnode;$(c,E,E.scopeId,E.slotScopeIds,g.parent)}}},Re=(c,u,p,b,g,m,E,S,w=0)=>{for(let _=w;_<c.length;_++){const N=c[_]=S?lt(c[_]):We(c[_]);T(null,N,u,p,b,g,m,E,S)}},te=(c,u,p,b,g,m,E)=>{const S=u.el=c.el;let{patchFlag:w,dynamicChildren:_,dirs:N}=u;w|=c.patchFlag&16;const C=c.props||G,M=u.props||G;let j;if(p&&pt(p,!1),(j=M.onVnodeBeforeUpdate)&&Ue(j,p,u,c),N&&ht(u,c,p,"beforeUpdate"),p&&pt(p,!0),(C.innerHTML&&M.innerHTML==null||C.textContent&&M.textContent==null)&&a(S,""),_?Me(c.dynamicChildren,_,S,p,b,mr(u,g),m):E||Y(c,u,S,null,p,b,mr(u,g),m,!1),w>0){if(w&16)et(S,C,M,p,g);else if(w&2&&C.class!==M.class&&i(S,"class",null,M.class,g),w&4&&i(S,"style",C.style,M.style,g),w&8){const U=u.dynamicProps;for(let se=0;se<U.length;se++){const X=U[se],Pe=C[X],we=M[X];(we!==Pe||X==="value")&&i(S,X,Pe,we,g,p)}}w&1&&c.children!==u.children&&a(S,u.children)}else!E&&_==null&&et(S,C,M,p,g);((j=M.onVnodeUpdated)||N)&&Oe(()=>{j&&Ue(j,p,u,c),N&&ht(u,c,p,"updated")},b)},Me=(c,u,p,b,g,m,E)=>{for(let S=0;S<u.length;S++){const w=c[S],_=u[S],N=w.el&&(w.type===Ve||!zt(w,_)||w.shapeFlag&70)?h(w.el):p;T(w,_,N,null,b,g,m,E,!0)}},et=(c,u,p,b,g)=>{if(u!==p){if(u!==G)for(const m in u)!Nt(m)&&!(m in p)&&i(c,m,u[m],null,g,b);for(const m in p){if(Nt(m))continue;const E=p[m],S=u[m];E!==S&&m!=="value"&&i(c,m,S,E,g,b)}"value"in p&&i(c,"value",u.value,p.value,g)}},Rt=(c,u,p,b,g,m,E,S,w)=>{const _=u.el=c?c.el:l(""),N=u.anchor=c?c.anchor:l("");let{patchFlag:C,dynamicChildren:M,slotScopeIds:j}=u;j&&(S=S?S.concat(j):j),c==null?(r(_,p,b),r(N,p,b),Re(u.children||[],p,N,g,m,E,S,w)):C>0&&C&64&&M&&c.dynamicChildren?(Me(c.dynamicChildren,M,p,g,m,E,S),(u.key!=null||g&&u===g.subTree)&&Ds(c,u,!0)):Y(c,u,p,N,g,m,E,S,w)},Pt=(c,u,p,b,g,m,E,S,w)=>{u.slotScopeIds=S,c==null?u.shapeFlag&512?g.ctx.activate(u,p,b,E,w):rn(u,p,b,g,m,E,w):Vn(c,u,w)},rn=(c,u,p,b,g,m,E)=>{const S=c.component=jl(c,b,g);if(ps(c)&&(S.ctx.renderer=oe),$l(S,!1,E),S.asyncDep){if(g&&g.registerDep(S,he,E),!c.el){const w=S.subTree=Te(gt);P(null,w,u,p)}}else he(S,c,u,p,g,m,E)},Vn=(c,u,p)=>{const b=u.component=c.component;if(Sl(c,u,p))if(b.asyncDep&&!b.asyncResolved){ie(b,u,p);return}else b.next=u,b.update();else u.el=c.el,b.vnode=u},he=(c,u,p,b,g,m,E)=>{const S=()=>{if(c.isMounted){let{next:C,bu:M,u:j,parent:U,vnode:se}=c;{const Ge=js(c);if(Ge){C&&(C.el=se.el,ie(c,C,E)),Ge.asyncDep.then(()=>{c.isUnmounted||S()});return}}let X=C,Pe;pt(c,!1),C?(C.el=se.el,ie(c,C,E)):C=se,M&&Gn(M),(Pe=C.props&&C.props.onVnodeBeforeUpdate)&&Ue(Pe,U,C,se),pt(c,!0);const we=Vs(c),ke=c.subTree;c.subTree=we,T(ke,we,h(ke.el),Q(ke),c,g,m),C.el=we.el,X===null&&El(c,we.el),j&&Oe(j,g),(Pe=C.props&&C.props.onVnodeUpdated)&&Oe(()=>Ue(Pe,U,C,se),g)}else{let C;const{el:M,props:j}=u,{bm:U,m:se,parent:X,root:Pe,type:we}=c,ke=Bt(u);pt(c,!1),U&&Gn(U),!ke&&(C=j&&j.onVnodeBeforeMount)&&Ue(C,X,u),pt(c,!0);{Pe.ce&&Pe.ce._injectChildStyle(we);const Ge=c.subTree=Vs(c);T(null,Ge,p,b,c,g,m),u.el=Ge.el}if(se&&Oe(se,g),!ke&&(C=j&&j.onVnodeMounted)){const Ge=u;Oe(()=>Ue(C,X,Ge),g)}(u.shapeFlag&256||X&&Bt(X.vnode)&&X.vnode.shapeFlag&256)&&c.a&&Oe(c.a,g),c.isMounted=!0,u=p=b=null}};c.scope.on();const w=c.effect=new Hr(S);c.scope.off();const _=c.update=w.run.bind(w),N=c.job=w.runIfDirty.bind(w);N.i=c,N.id=c.uid,w.scheduler=()=>cr(N),pt(c,!0),_()},ie=(c,u,p)=>{u.component=c;const b=c.vnode.props;c.vnode=u,c.next=null,ll(c,u.props,b,p),al(c,u.children,p),Je(),fs(c),ze()},Y=(c,u,p,b,g,m,E,S,w=!1)=>{const _=c&&c.children,N=c?c.shapeFlag:0,C=u.children,{patchFlag:M,shapeFlag:j}=u;if(M>0){if(M&128){It(_,C,p,b,g,m,E,S,w);return}else if(M&256){tt(_,C,p,b,g,m,E,S,w);return}}j&8?(N&16&&W(_,g,m),C!==_&&a(p,C)):N&16?j&16?It(_,C,p,b,g,m,E,S,w):W(_,g,m,!0):(N&8&&a(p,""),j&16&&Re(C,p,b,g,m,E,S,w))},tt=(c,u,p,b,g,m,E,S,w)=>{c=c||bt,u=u||bt;const _=c.length,N=u.length,C=Math.min(_,N);let M;for(M=0;M<C;M++){const j=u[M]=w?lt(u[M]):We(u[M]);T(c[M],j,p,null,g,m,E,S,w)}_>N?W(c,g,m,!0,!1,C):Re(u,p,b,g,m,E,S,w,C)},It=(c,u,p,b,g,m,E,S,w)=>{let _=0;const N=u.length;let C=c.length-1,M=N-1;for(;_<=C&&_<=M;){const j=c[_],U=u[_]=w?lt(u[_]):We(u[_]);if(zt(j,U))T(j,U,p,null,g,m,E,S,w);else break;_++}for(;_<=C&&_<=M;){const j=c[C],U=u[M]=w?lt(u[M]):We(u[M]);if(zt(j,U))T(j,U,p,null,g,m,E,S,w);else break;C--,M--}if(_>C){if(_<=M){const j=M+1,U=j<N?u[j].el:b;for(;_<=M;)T(null,u[_]=w?lt(u[_]):We(u[_]),p,U,g,m,E,S,w),_++}}else if(_>M)for(;_<=C;)y(c[_],g,m,!0),_++;else{const j=_,U=_,se=new Map;for(_=U;_<=M;_++){const Ie=u[_]=w?lt(u[_]):We(u[_]);Ie.key!=null&&se.set(Ie.key,_)}let X,Pe=0;const we=M-U+1;let ke=!1,Ge=0;const sn=new Array(we);for(_=0;_<we;_++)sn[_]=0;for(_=j;_<=C;_++){const Ie=c[_];if(Pe>=we){y(Ie,g,m,!0);continue}let qe;if(Ie.key!=null)qe=se.get(Ie.key);else for(X=U;X<=M;X++)if(sn[X-U]===0&&zt(Ie,u[X])){qe=X;break}qe===void 0?y(Ie,g,m,!0):(sn[qe-U]=_+1,qe>=Ge?Ge=qe:ke=!0,T(Ie,u[qe],p,null,g,m,E,S,w),Pe++)}const Pi=ke?gl(sn):bt;for(X=Pi.length-1,_=we-1;_>=0;_--){const Ie=U+_,qe=u[Ie],Ii=Ie+1<N?u[Ie+1].el:b;sn[_]===0?T(null,qe,p,Ii,g,m,E,S,w):ke&&(X<0||_!==Pi[X]?nt(qe,p,Ii,2):X--)}}},nt=(c,u,p,b,g=null)=>{const{el:m,type:E,transition:S,children:w,shapeFlag:_}=c;if(_&6){nt(c.component.subTree,u,p,b);return}if(_&128){c.suspense.move(u,p,b);return}if(_&64){E.move(c,u,p,oe);return}if(E===Ve){r(m,u,p);for(let C=0;C<w.length;C++)nt(w[C],u,p,b);r(c.anchor,u,p);return}if(E===br){V(c,u,p);return}if(b!==2&&_&1&&S)if(b===0)S.beforeEnter(m),r(m,u,p),Oe(()=>S.enter(m),g);else{const{leave:C,delayLeave:M,afterLeave:j}=S,U=()=>r(m,u,p),se=()=>{C(m,()=>{U(),j&&j()})};M?M(m,U,se):se()}else r(m,u,p)},y=(c,u,p,b=!1,g=!1)=>{const{type:m,props:E,ref:S,children:w,dynamicChildren:_,shapeFlag:N,patchFlag:C,dirs:M,cacheIndex:j}=c;if(C===-2&&(g=!1),S!=null&&xn(S,null,p,c,!0),j!=null&&(u.renderCache[j]=void 0),N&256){u.ctx.deactivate(c);return}const U=N&1&&M,se=!Bt(c);let X;if(se&&(X=E&&E.onVnodeBeforeUnmount)&&Ue(X,u,c),N&6)B(c.component,p,b);else{if(N&128){c.suspense.unmount(p,b);return}U&&ht(c,null,u,"beforeUnmount"),N&64?c.type.remove(c,u,p,oe,b):_&&!_.hasOnce&&(m!==Ve||C>0&&C&64)?W(_,u,p,!1,!0):(m===Ve&&C&384||!g&&N&16)&&W(w,u,p),b&&O(c)}(se&&(X=E&&E.onVnodeUnmounted)||U)&&Oe(()=>{X&&Ue(X,u,c),U&&ht(c,null,u,"unmounted")},p)},O=c=>{const{type:u,el:p,anchor:b,transition:g}=c;if(u===Ve){A(p,b);return}if(u===br){R(c);return}const m=()=>{s(p),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(c.shapeFlag&1&&g&&!g.persisted){const{leave:E,delayLeave:S}=g,w=()=>E(p,m);S?S(c.el,m,w):w()}else m()},A=(c,u)=>{let p;for(;c!==u;)p=x(c),s(c),c=p;s(u)},B=(c,u,p)=>{const{bum:b,scope:g,job:m,subTree:E,um:S,m:w,a:_}=c;$s(w),$s(_),b&&Gn(b),g.stop(),m&&(m.flags|=8,y(E,c,u,p)),S&&Oe(S,u),Oe(()=>{c.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},W=(c,u,p,b=!1,g=!1,m=0)=>{for(let E=m;E<c.length;E++)y(c[E],u,p,b,g)},Q=c=>{if(c.shapeFlag&6)return Q(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const u=x(c.anchor||c.el),p=u&&u[$o];return p?x(p):u};let ne=!1;const K=(c,u,p)=>{c==null?u._vnode&&y(u._vnode,null,null,!0):T(u._vnode||null,c,u,null,null,null,p),u._vnode=c,ne||(ne=!0,fs(),us(),ne=!1)},oe={p:T,um:y,m:nt,r:O,mt:rn,mc:Re,pc:Y,pbc:Me,n:Q,o:e};return{render:K,hydrate:void 0,createApp:sl(K)}}function mr({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function pt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function pl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ds(e,t,n=!1){const r=e.children,s=t.children;if(F(r)&&F(s))for(let i=0;i<r.length;i++){const o=r[i];let l=s[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[i]=lt(s[i]),l.el=o.el),!n&&l.patchFlag!==-2&&Ds(o,l)),l.type===Cn&&(l.el=o.el)}}function gl(e){const t=e.slice(),n=[0];let r,s,i,o,l;const f=e.length;for(r=0;r<f;r++){const d=e[r];if(d!==0){if(s=n[n.length-1],e[s]<d){t[r]=s,n.push(r);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<d?i=l+1:o=l;d<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function js(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:js(t)}function $s(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ml=Symbol.for("v-scx"),bl=()=>En(ml);function Gt(e,t,n){return Fs(e,t,n)}function Fs(e,t,n=G){const{immediate:r,deep:s,flush:i,once:o}=n,l=be({},n),f=t&&r||!t&&i!=="post";let d;if(Zt){if(i==="sync"){const v=bl();d=v.__watcherHandles||(v.__watcherHandles=[])}else if(!f){const v=()=>{};return v.stop=De,v.resume=De,v.pause=De,v}}const a=ae;l.call=(v,I,T)=>Fe(v,a,I,T);let h=!1;i==="post"?l.scheduler=v=>{Oe(v,a&&a.suspense)}:i!=="sync"&&(h=!0,l.scheduler=(v,I)=>{I?v():cr(v)}),l.augmentJob=v=>{t&&(v.flags|=4),h&&(v.flags|=2,a&&(v.id=a.uid,v.i=a))};const x=Oo(e,t,l);return Zt&&(d?d.push(x):f&&x()),x}function _l(e,t,n){const r=this.proxy,s=fe(e)?e.includes(".")?Ls(r,e):()=>r[e]:e.bind(r,r);let i;L(t)?i=t:(i=t.handler,n=t);const o=Yt(this),l=Fs(s,i.bind(r),n);return o(),l}function Ls(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const yl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${rt(t)}Modifiers`]||e[`${ft(t)}Modifiers`];function vl(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||G;let s=n;const i=t.startsWith("update:"),o=i&&yl(r,t.slice(7));o&&(o.trim&&(s=n.map(a=>fe(a)?a.trim():a)),o.number&&(s=n.map(Vi)));let l,f=r[l=kn(t)]||r[l=kn(rt(t))];!f&&i&&(f=r[l=kn(ft(t))]),f&&Fe(f,e,6,s);const d=r[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Fe(d,e,6,s)}}function Hs(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const i=e.emits;let o={},l=!1;if(!L(e)){const f=d=>{const a=Hs(d,t,!0);a&&(l=!0,be(o,a))};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!i&&!l?(le(e)&&r.set(e,null),null):(F(i)?i.forEach(f=>o[f]=null):be(o,i),le(e)&&r.set(e,o),o)}function On(e,t){return!e||!on(t)?!1:(t=t.slice(2).replace(/Once$/,""),Z(e,t[0].toLowerCase()+t.slice(1))||Z(e,ft(t))||Z(e,t))}function Sf(){}function Vs(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[i],slots:o,attrs:l,emit:f,render:d,renderCache:a,props:h,data:x,setupState:v,ctx:I,inheritAttrs:T}=e,H=vn(e);let P,D;try{if(n.shapeFlag&4){const R=s||r,k=it.NODE_ENV!=="production"&&v.__isScriptSetup?new Proxy(R,{get(ee,$,Re){return Co(`Property '${String($)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(ee,$,Re)}}):R;P=We(d.call(k,R,a,it.NODE_ENV!=="production"?pn(h):h,v,x,I)),D=l}else{const R=t;it.NODE_ENV,P=We(R.length>1?R(it.NODE_ENV!=="production"?pn(h):h,it.NODE_ENV!=="production"?{get attrs(){return pn(l)},slots:o,emit:f}:{attrs:l,slots:o,emit:f}):R(it.NODE_ENV!=="production"?pn(h):h,null)),D=t.props?l:xl(l)}}catch(R){qt.length=0,_n(R,e,1),P=Te(gt)}let V=P;if(D&&T!==!1){const R=Object.keys(D),{shapeFlag:k}=V;R.length&&k&7&&(i&&R.some(Un)&&(D=wl(D,i)),V=Ot(V,D,!1,!0))}return n.dirs&&(V=Ot(V,null,!1,!0),V.dirs=V.dirs?V.dirs.concat(n.dirs):n.dirs),n.transition&&fr(V,n.transition),P=V,vn(H),P}const xl=e=>{let t;for(const n in e)(n==="class"||n==="style"||on(n))&&((t||(t={}))[n]=e[n]);return t},wl=(e,t)=>{const n={};for(const r in e)(!Un(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Sl(e,t,n){const{props:r,children:s,component:i}=e,{props:o,children:l,patchFlag:f}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&f>=0){if(f&1024)return!0;if(f&16)return r?Ws(r,o,d):!!o;if(f&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const x=a[h];if(o[x]!==r[x]&&!On(d,x))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===o?!1:r?o?Ws(r,o,d):!0:!!o;return!1}function Ws(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const i=r[s];if(t[i]!==e[i]&&!On(n,i))return!0}return!1}function El({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Us=e=>e.__isSuspense;function Ol(e,t){t&&t.pendingBranch?F(e)?t.effects.push(...e):t.effects.push(e):Do(e)}const Ve=Symbol.for("v-fgt"),Cn=Symbol.for("v-txt"),gt=Symbol.for("v-cmt"),br=Symbol.for("v-stc"),qt=[];let Ce=null;function _r(e=!1){qt.push(Ce=e?null:[])}function Cl(){qt.pop(),Ce=qt[qt.length-1]||null}let Jt=1;function Bs(e,t=!1){Jt+=e,e<0&&Ce&&t&&(Ce.hasOnce=!0)}function Ks(e){return e.dynamicChildren=Jt>0?Ce||bt:null,Cl(),Jt>0&&Ce&&Ce.push(e),e}function ks(e,t,n,r,s,i){return Ks(mt(e,t,n,r,s,i,!0))}function Tl(e,t,n,r,s){return Ks(Te(e,t,n,r,s,!0))}function Tn(e){return e?e.__v_isVNode===!0:!1}function zt(e,t){return e.type===t.type&&e.key===t.key}const Gs=({key:e})=>e??null,An=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?fe(e)||ue(e)||L(e)?{i:He,r:e,k:t,f:!!n}:e:null);function mt(e,t=null,n=null,r=0,s=null,i=e===Ve?0:1,o=!1,l=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Gs(t),ref:t&&An(t),scopeId:ds,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:He};return l?(yr(f,n),i&128&&e.normalize(f)):n&&(f.shapeFlag|=fe(n)?8:16),Jt>0&&!o&&Ce&&(f.patchFlag>0||i&6)&&f.patchFlag!==32&&Ce.push(f),f}const Te=Al;function Al(e,t=null,n=null,r=0,s=null,i=!1){if((!e||e===Yo)&&(e=gt),Tn(e)){const l=Ot(e,t,!0);return n&&yr(l,n),Jt>0&&!i&&Ce&&(l.shapeFlag&6?Ce[Ce.indexOf(e)]=l:Ce.push(l)),l.patchFlag=-2,l}if(Bl(e)&&(e=e.__vccOpts),t){t=Rl(t);let{class:l,style:f}=t;l&&!fe(l)&&(t.class=Jn(l)),le(f)&&(ir(f)&&!F(f)&&(f=be({},f)),t.style=qn(f))}const o=fe(e)?1:Us(e)?128:Fo(e)?64:le(e)?4:L(e)?2:0;return mt(e,t,n,r,s,o,i,!0)}function Rl(e){return e?ir(e)||Cs(e)?be({},e):e:null}function Ot(e,t,n=!1,r=!1){const{props:s,ref:i,patchFlag:o,children:l,transition:f}=e,d=t?Ml(s||{},t):s,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Gs(d),ref:t&&t.ref?n&&i?F(i)?i.concat(An(t)):[i,An(t)]:An(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ve?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ot(e.ssContent),ssFallback:e.ssFallback&&Ot(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&r&&fr(a,f.clone(a)),a}function Pl(e=" ",t=0){return Te(Cn,null,e,t)}function Il(e="",t=!1){return t?(_r(),Tl(gt,null,e)):Te(gt,null,e)}function We(e){return e==null||typeof e=="boolean"?Te(gt):F(e)?Te(Ve,null,e.slice()):Tn(e)?lt(e):Te(Cn,null,String(e))}function lt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ot(e)}function yr(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(F(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),yr(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Cs(t)?t._ctx=He:s===3&&He&&(He.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else L(t)?(t={default:t,_ctx:He},n=32):(t=String(t),r&64?(n=16,t=[Pl(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ml(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Jn([t.class,r.class]));else if(s==="style")t.style=qn([t.style,r.style]);else if(on(s)){const i=t[s],o=r[s];o&&i!==o&&!(F(i)&&i.includes(o))&&(t[s]=i?[].concat(i,o):o)}else s!==""&&(t[s]=r[s])}return t}function Ue(e,t,n,r=null){Fe(e,t,7,[n,r])}const Nl=Ss();let Dl=0;function jl(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||Nl,i={uid:Dl++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new qi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:As(r,s),emitsOptions:Hs(r,s),emit:null,emitted:null,propsDefaults:G,inheritAttrs:r.inheritAttrs,ctx:G,data:G,props:G,attrs:G,slots:G,refs:G,setupState:G,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=vl.bind(null,i),e.ce&&e.ce(i),i}let ae=null,Rn,vr;{const e=fn(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),i=>{s.length>1?s.forEach(o=>o(i)):s[0](i)}};Rn=t("__VUE_INSTANCE_SETTERS__",n=>ae=n),vr=t("__VUE_SSR_SETTERS__",n=>Zt=n)}const Yt=e=>{const t=ae;return Rn(e),e.scope.on(),()=>{e.scope.off(),Rn(t)}},qs=()=>{ae&&ae.scope.off(),Rn(null)};function Js(e){return e.vnode.shapeFlag&4}let Zt=!1;function $l(e,t=!1,n=!1){t&&vr(t);const{props:r,children:s}=e.vnode,i=Js(e);ol(e,r,i,t),ul(e,s,n);const o=i?Fl(e,t):void 0;return t&&vr(!1),o}function Fl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Zo);const{setup:r}=n;if(r){Je();const s=e.setupContext=r.length>1?Hl(e):null,i=Yt(e),o=xt(r,e,0,[e.props,s]),l=Dr(o);if(ze(),i(),(l||e.sp)&&!Bt(e)&&hs(e),l){if(o.then(qs,qs),t)return o.then(f=>{zs(e,f)}).catch(f=>{_n(f,e,0)});e.asyncDep=o}else zs(e,o)}else Ys(e)}function zs(e,t,n){L(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:le(t)&&(e.setupState=is(t)),Ys(e)}function Ys(e,t,n){const r=e.type;e.render||(e.render=r.render||De);{const s=Yt(e);Je();try{Qo(e)}finally{ze(),s()}}}const Ll={get(e,t){return pe(e,"get",""),e[t]}};function Hl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ll),slots:e.slots,emit:e.emit,expose:t}}function xr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(is(mo(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Kt)return Kt[n](e)},has(t,n){return n in t||n in Kt}})):e.proxy}const Vl=/(?:^|[-_])(\w)/g,Wl=e=>e.replace(Vl,t=>t.toUpperCase()).replace(/[-_]/g,"");function Ul(e,t=!0){return L(e)?e.displayName||e.name:e.name||t&&e.__name}function Zs(e,t,n=!1){let r=Ul(t);if(!r&&t.__file){const s=t.__file.match(/([^/\\]+)\.\w+$/);s&&(r=s[1])}if(!r&&e&&e.parent){const s=i=>{for(const o in i)if(i[o]===t)return o};r=s(e.components||e.parent.type.components)||s(e.appContext.components)}return r?Wl(r):n?"App":"Anonymous"}function Bl(e){return L(e)&&"__vccOpts"in e}const Kl=(e,t)=>So(e,t,Zt);function kl(e,t,n){const r=arguments.length;return r===2?le(t)&&!F(t)?Tn(t)?Te(e,null,[t]):Te(e,t):Te(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&Tn(n)&&(n=[n]),Te(e,t,n))}const Gl="3.5.13";let wr;const Qs=typeof window<"u"&&window.trustedTypes;if(Qs)try{wr=Qs.createPolicy("vue",{createHTML:e=>e})}catch{}const Xs=wr?e=>wr.createHTML(e):e=>e,ql="http://www.w3.org/2000/svg",Jl="http://www.w3.org/1998/Math/MathML",Xe=typeof document<"u"?document:null,ei=Xe&&Xe.createElement("template"),zl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?Xe.createElementNS(ql,e):t==="mathml"?Xe.createElementNS(Jl,e):n?Xe.createElement(e,{is:n}):Xe.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>Xe.createTextNode(e),createComment:e=>Xe.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Xe.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,i){const o=n?n.previousSibling:t.lastChild;if(s&&(s===i||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===i||!(s=s.nextSibling)););else{ei.innerHTML=Xs(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=ei.content;if(r==="svg"||r==="mathml"){const f=l.firstChild;for(;f.firstChild;)l.appendChild(f.firstChild);l.removeChild(f)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Yl=Symbol("_vtc");function Zl(e,t,n){const r=e[Yl];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ti=Symbol("_vod"),Ql=Symbol("_vsh"),Xl=Symbol(""),ec=/(^|;)\s*display\s*:/;function tc(e,t,n){const r=e.style,s=fe(n);let i=!1;if(n&&!s){if(t)if(fe(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&Pn(r,l,"")}else for(const o in t)n[o]==null&&Pn(r,o,"");for(const o in n)o==="display"&&(i=!0),Pn(r,o,n[o])}else if(s){if(t!==n){const o=r[Xl];o&&(n+=";"+o),r.cssText=n,i=ec.test(n)}}else t&&e.removeAttribute("style");ti in e&&(e[ti]=i?r.display:"",e[Ql]&&(r.display="none"))}const ni=/\s*!important$/;function Pn(e,t,n){if(F(n))n.forEach(r=>Pn(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=nc(e,t);ni.test(n)?e.setProperty(ft(r),n.replace(ni,""),"important"):e[r]=n}}const ri=["Webkit","Moz","ms"],Sr={};function nc(e,t){const n=Sr[t];if(n)return n;let r=rt(t);if(r!=="filter"&&r in e)return Sr[t]=r;r=jr(r);for(let s=0;s<ri.length;s++){const i=ri[s]+r;if(i in e)return Sr[t]=i}return t}const si="http://www.w3.org/1999/xlink";function ii(e,t,n,r,s,i=ki(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(si,t.slice(6,t.length)):e.setAttributeNS(si,t,n):n==null||i&&!Lr(n)?e.removeAttribute(t):e.setAttribute(t,i?"":_t(n)?String(n):n)}function oi(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Xs(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,f=n==null?e.type==="checkbox"?"on":"":String(n);(l!==f||!("_value"in e))&&(e.value=f),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Lr(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(s||t)}function rc(e,t,n,r){e.addEventListener(t,n,r)}function sc(e,t,n,r){e.removeEventListener(t,n,r)}const li=Symbol("_vei");function ic(e,t,n,r,s=null){const i=e[li]||(e[li]={}),o=i[t];if(r&&o)o.value=r;else{const[l,f]=oc(t);if(r){const d=i[t]=fc(r,s);rc(e,l,d,f)}else o&&(sc(e,l,o,f),i[t]=void 0)}}const ci=/(?:Once|Passive|Capture)$/;function oc(e){let t;if(ci.test(e)){t={};let r;for(;r=e.match(ci);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ft(e.slice(2)),t]}let Er=0;const lc=Promise.resolve(),cc=()=>Er||(lc.then(()=>Er=0),Er=Date.now());function fc(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Fe(uc(r,n.value),t,5,[r])};return n.value=e,n.attached=cc(),n}function uc(e,t){if(F(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const fi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,ac=(e,t,n,r,s,i)=>{const o=s==="svg";t==="class"?Zl(e,r,o):t==="style"?tc(e,n,r):on(t)?Un(t)||ic(e,t,n,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):dc(e,t,r,o))?(oi(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ii(e,t,r,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!fe(r))?oi(e,rt(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),ii(e,t,r,o))};function dc(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&fi(t)&&L(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return fi(t)&&fe(n)?!1:t in e}const hc=be({patchProp:ac},zl);let ui;function pc(){return ui||(ui=dl(hc))}const gc=(...e)=>{const t=pc().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=bc(r);if(!s)return;const i=t._component;!L(i)&&!i.render&&!i.template&&(i.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const o=n(s,!1,mc(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),o},t};function mc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function bc(e){return fe(e)?document.querySelector(e):e}var Or={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var ai;function _c(){return ai||(ai=1,function(e){(function(){var t={}.hasOwnProperty;function n(){for(var i="",o=0;o<arguments.length;o++){var l=arguments[o];l&&(i=s(i,r(l)))}return i}function r(i){if(typeof i=="string"||typeof i=="number")return i;if(typeof i!="object")return"";if(Array.isArray(i))return n.apply(null,i);if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]"))return i.toString();var o="";for(var l in i)t.call(i,l)&&i[l]&&(o=s(o,l));return o}function s(i,o){return o?i?i+" "+o:i+o:i}e.exports?(n.default=n,e.exports=n):window.classNames=n})()}(Or)),Or.exports}_c();function yc(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}function vc(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),e.nonce!==void 0&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}var xc=function(){function e(n){var r=this;this._insertTag=function(s){var i;r.tags.length===0?r.insertionPoint?i=r.insertionPoint.nextSibling:r.prepend?i=r.container.firstChild:i=r.before:i=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(s,i),r.tags.push(s)},this.isSpeedy=n.speedy===void 0?!0:n.speedy,this.tags=[],this.ctr=0,this.nonce=n.nonce,this.key=n.key,this.container=n.container,this.prepend=n.prepend,this.insertionPoint=n.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(r){r.forEach(this._insertTag)},t.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(vc(this));var s=this.tags[this.tags.length-1];if(this.isSpeedy){var i=yc(s);try{i.insertRule(r,i.cssRules.length)}catch{}}else s.appendChild(document.createTextNode(r));this.ctr++},t.flush=function(){this.tags.forEach(function(r){var s;return(s=r.parentNode)==null?void 0:s.removeChild(r)}),this.tags=[],this.ctr=0},e}(),ge="-ms-",In="-moz-",J="-webkit-",di="comm",Cr="rule",Tr="decl",wc="@import",hi="@keyframes",Sc="@layer",Ec=Math.abs,Mn=String.fromCharCode,Oc=Object.assign;function Cc(e,t){return de(e,0)^45?(((t<<2^de(e,0))<<2^de(e,1))<<2^de(e,2))<<2^de(e,3):0}function pi(e){return e.trim()}function Tc(e,t){return(e=t.exec(e))?e[0]:e}function z(e,t,n){return e.replace(t,n)}function Ar(e,t){return e.indexOf(t)}function de(e,t){return e.charCodeAt(t)|0}function Qt(e,t,n){return e.slice(t,n)}function Be(e){return e.length}function Rr(e){return e.length}function Nn(e,t){return t.push(e),e}function Ac(e,t){return e.map(t).join("")}var Dn=1,Ct=1,gi=0,xe=0,ce=0,Tt="";function jn(e,t,n,r,s,i,o){return{value:e,root:t,parent:n,type:r,props:s,children:i,line:Dn,column:Ct,length:o,return:""}}function Xt(e,t){return Oc(jn("",null,null,"",null,null,0),e,{length:-e.length},t)}function Rc(){return ce}function Pc(){return ce=xe>0?de(Tt,--xe):0,Ct--,ce===10&&(Ct=1,Dn--),ce}function Ae(){return ce=xe<gi?de(Tt,xe++):0,Ct++,ce===10&&(Ct=1,Dn++),ce}function Ke(){return de(Tt,xe)}function $n(){return xe}function en(e,t){return Qt(Tt,e,t)}function tn(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function mi(e){return Dn=Ct=1,gi=Be(Tt=e),xe=0,[]}function bi(e){return Tt="",e}function Fn(e){return pi(en(xe-1,Pr(e===91?e+2:e===40?e+1:e)))}function Ic(e){for(;(ce=Ke())&&ce<33;)Ae();return tn(e)>2||tn(ce)>3?"":" "}function Mc(e,t){for(;--t&&Ae()&&!(ce<48||ce>102||ce>57&&ce<65||ce>70&&ce<97););return en(e,$n()+(t<6&&Ke()==32&&Ae()==32))}function Pr(e){for(;Ae();)switch(ce){case e:return xe;case 34:case 39:e!==34&&e!==39&&Pr(ce);break;case 40:e===41&&Pr(e);break;case 92:Ae();break}return xe}function Nc(e,t){for(;Ae()&&e+ce!==57;)if(e+ce===84&&Ke()===47)break;return"/*"+en(t,xe-1)+"*"+Mn(e===47?e:Ae())}function Dc(e){for(;!tn(Ke());)Ae();return en(e,xe)}function jc(e){return bi(Ln("",null,null,null,[""],e=mi(e),0,[0],e))}function Ln(e,t,n,r,s,i,o,l,f){for(var d=0,a=0,h=o,x=0,v=0,I=0,T=1,H=1,P=1,D=0,V="",R=s,k=i,ee=r,$=V;H;)switch(I=D,D=Ae()){case 40:if(I!=108&&de($,h-1)==58){Ar($+=z(Fn(D),"&","&\f"),"&\f")!=-1&&(P=-1);break}case 34:case 39:case 91:$+=Fn(D);break;case 9:case 10:case 13:case 32:$+=Ic(I);break;case 92:$+=Mc($n()-1,7);continue;case 47:switch(Ke()){case 42:case 47:Nn($c(Nc(Ae(),$n()),t,n),f);break;default:$+="/"}break;case 123*T:l[d++]=Be($)*P;case 125*T:case 59:case 0:switch(D){case 0:case 125:H=0;case 59+a:P==-1&&($=z($,/\f/g,"")),v>0&&Be($)-h&&Nn(v>32?yi($+";",r,n,h-1):yi(z($," ","")+";",r,n,h-2),f);break;case 59:$+=";";default:if(Nn(ee=_i($,t,n,d,a,s,l,V,R=[],k=[],h),i),D===123)if(a===0)Ln($,t,ee,ee,R,i,h,l,k);else switch(x===99&&de($,3)===110?100:x){case 100:case 108:case 109:case 115:Ln(e,ee,ee,r&&Nn(_i(e,ee,ee,0,0,s,l,V,s,R=[],h),k),s,k,h,l,r?R:k);break;default:Ln($,ee,ee,ee,[""],k,0,l,k)}}d=a=v=0,T=P=1,V=$="",h=o;break;case 58:h=1+Be($),v=I;default:if(T<1){if(D==123)--T;else if(D==125&&T++==0&&Pc()==125)continue}switch($+=Mn(D),D*T){case 38:P=a>0?1:($+="\f",-1);break;case 44:l[d++]=(Be($)-1)*P,P=1;break;case 64:Ke()===45&&($+=Fn(Ae())),x=Ke(),a=h=Be(V=$+=Dc($n())),D++;break;case 45:I===45&&Be($)==2&&(T=0)}}return i}function _i(e,t,n,r,s,i,o,l,f,d,a){for(var h=s-1,x=s===0?i:[""],v=Rr(x),I=0,T=0,H=0;I<r;++I)for(var P=0,D=Qt(e,h+1,h=Ec(T=o[I])),V=e;P<v;++P)(V=pi(T>0?x[P]+" "+D:z(D,/&\f/g,x[P])))&&(f[H++]=V);return jn(e,t,n,s===0?Cr:l,f,d,a)}function $c(e,t,n){return jn(e,t,n,di,Mn(Rc()),Qt(e,2,-2),0)}function yi(e,t,n,r){return jn(e,t,n,Tr,Qt(e,0,r),Qt(e,r+1,-1),r)}function At(e,t){for(var n="",r=Rr(e),s=0;s<r;s++)n+=t(e[s],s,e,t)||"";return n}function Fc(e,t,n,r){switch(e.type){case Sc:if(e.children.length)break;case wc:case Tr:return e.return=e.return||e.value;case di:return"";case hi:return e.return=e.value+"{"+At(e.children,r)+"}";case Cr:e.value=e.props.join(",")}return Be(n=At(e.children,r))?e.return=e.value+"{"+n+"}":""}function Lc(e){var t=Rr(e);return function(n,r,s,i){for(var o="",l=0;l<t;l++)o+=e[l](n,r,s,i)||"";return o}}function Hc(e){return function(t){t.root||(t=t.return)&&e(t)}}function Vc(e){var t=Object.create(null);return function(n){return t[n]===void 0&&(t[n]=e(n)),t[n]}}var Wc=function(t,n,r){for(var s=0,i=0;s=i,i=Ke(),s===38&&i===12&&(n[r]=1),!tn(i);)Ae();return en(t,xe)},Uc=function(t,n){var r=-1,s=44;do switch(tn(s)){case 0:s===38&&Ke()===12&&(n[r]=1),t[r]+=Wc(xe-1,n,r);break;case 2:t[r]+=Fn(s);break;case 4:if(s===44){t[++r]=Ke()===58?"&\f":"",n[r]=t[r].length;break}default:t[r]+=Mn(s)}while(s=Ae());return t},Bc=function(t,n){return bi(Uc(mi(t),n))},vi=new WeakMap,Kc=function(t){if(!(t.type!=="rule"||!t.parent||t.length<1)){for(var n=t.value,r=t.parent,s=t.column===r.column&&t.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(t.props.length===1&&n.charCodeAt(0)!==58&&!vi.get(r))&&!s){vi.set(t,!0);for(var i=[],o=Bc(n,i),l=r.props,f=0,d=0;f<o.length;f++)for(var a=0;a<l.length;a++,d++)t.props[d]=i[f]?o[f].replace(/&\f/g,l[a]):l[a]+" "+o[f]}}},kc=function(t){if(t.type==="decl"){var n=t.value;n.charCodeAt(0)===108&&n.charCodeAt(2)===98&&(t.return="",t.value="")}};function xi(e,t){switch(Cc(e,t)){case 5103:return J+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return J+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return J+e+In+e+ge+e+e;case 6828:case 4268:return J+e+ge+e+e;case 6165:return J+e+ge+"flex-"+e+e;case 5187:return J+e+z(e,/(\w+).+(:[^]+)/,J+"box-$1$2"+ge+"flex-$1$2")+e;case 5443:return J+e+ge+"flex-item-"+z(e,/flex-|-self/,"")+e;case 4675:return J+e+ge+"flex-line-pack"+z(e,/align-content|flex-|-self/,"")+e;case 5548:return J+e+ge+z(e,"shrink","negative")+e;case 5292:return J+e+ge+z(e,"basis","preferred-size")+e;case 6060:return J+"box-"+z(e,"-grow","")+J+e+ge+z(e,"grow","positive")+e;case 4554:return J+z(e,/([^-])(transform)/g,"$1"+J+"$2")+e;case 6187:return z(z(z(e,/(zoom-|grab)/,J+"$1"),/(image-set)/,J+"$1"),e,"")+e;case 5495:case 3959:return z(e,/(image-set\([^]*)/,J+"$1$`$1");case 4968:return z(z(e,/(.+:)(flex-)?(.*)/,J+"box-pack:$3"+ge+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+J+e+e;case 4095:case 3583:case 4068:case 2532:return z(e,/(.+)-inline(.+)/,J+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Be(e)-1-t>6)switch(de(e,t+1)){case 109:if(de(e,t+4)!==45)break;case 102:return z(e,/(.+:)(.+)-([^]+)/,"$1"+J+"$2-$3$1"+In+(de(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~Ar(e,"stretch")?xi(z(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(de(e,t+1)!==115)break;case 6444:switch(de(e,Be(e)-3-(~Ar(e,"!important")&&10))){case 107:return z(e,":",":"+J)+e;case 101:return z(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+J+(de(e,14)===45?"inline-":"")+"box$3$1"+J+"$2$3$1"+ge+"$2box$3")+e}break;case 5936:switch(de(e,t+11)){case 114:return J+e+ge+z(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return J+e+ge+z(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return J+e+ge+z(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return J+e+ge+e+e}return e}var Gc=function(t,n,r,s){if(t.length>-1&&!t.return)switch(t.type){case Tr:t.return=xi(t.value,t.length);break;case hi:return At([Xt(t,{value:z(t.value,"@","@"+J)})],s);case Cr:if(t.length)return Ac(t.props,function(i){switch(Tc(i,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return At([Xt(t,{props:[z(i,/:(read-\w+)/,":"+In+"$1")]})],s);case"::placeholder":return At([Xt(t,{props:[z(i,/:(plac\w+)/,":"+J+"input-$1")]}),Xt(t,{props:[z(i,/:(plac\w+)/,":"+In+"$1")]}),Xt(t,{props:[z(i,/:(plac\w+)/,ge+"input-$1")]})],s)}return""})}},qc=[Gc],Jc=function(t){var n=t.key;if(n==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(T){var H=T.getAttribute("data-emotion");H.indexOf(" ")!==-1&&(document.head.appendChild(T),T.setAttribute("data-s",""))})}var s=t.stylisPlugins||qc,i={},o,l=[];o=t.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+n+' "]'),function(T){for(var H=T.getAttribute("data-emotion").split(" "),P=1;P<H.length;P++)i[H[P]]=!0;l.push(T)});var f,d=[Kc,kc];{var a,h=[Fc,Hc(function(T){a.insert(T)})],x=Lc(d.concat(s,h)),v=function(H){return At(jc(H),x)};f=function(H,P,D,V){a=D,v(H?H+"{"+P.styles+"}":P.styles),V&&(I.inserted[P.name]=!0)}}var I={key:n,sheet:new xc({key:n,container:o,nonce:t.nonce,speedy:t.speedy,prepend:t.prepend,insertionPoint:t.insertionPoint}),nonce:t.nonce,inserted:i,registered:{},insert:f};return I.sheet.hydrate(l),I};function zc(e){for(var t=0,n,r=0,s=e.length;s>=4;++r,s-=4)n=e.charCodeAt(r)&255|(e.charCodeAt(++r)&255)<<8|(e.charCodeAt(++r)&255)<<16|(e.charCodeAt(++r)&255)<<24,n=(n&65535)*1540483477+((n>>>16)*59797<<16),n^=n>>>24,t=(n&65535)*1540483477+((n>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(s){case 3:t^=(e.charCodeAt(r+2)&255)<<16;case 2:t^=(e.charCodeAt(r+1)&255)<<8;case 1:t^=e.charCodeAt(r)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}var Yc={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Zc=/[A-Z]|^ms/g,Qc=/_EMO_([^_]+?)_([^]*?)_EMO_/g,wi=function(t){return t.charCodeAt(1)===45},Si=function(t){return t!=null&&typeof t!="boolean"},Ir=Vc(function(e){return wi(e)?e:e.replace(Zc,"-$&").toLowerCase()}),Ei=function(t,n){switch(t){case"animation":case"animationName":if(typeof n=="string")return n.replace(Qc,function(r,s,i){return ct={name:s,styles:i,next:ct},s})}return Yc[t]!==1&&!wi(t)&&typeof n=="number"&&n!==0?n+"px":n};function Hn(e,t,n){if(n==null)return"";var r=n;if(r.__emotion_styles!==void 0)return r;switch(typeof n){case"boolean":return"";case"object":{var s=n;if(s.anim===1)return ct={name:s.name,styles:s.styles,next:ct},s.name;var i=n;if(i.styles!==void 0){var o=i.next;if(o!==void 0)for(;o!==void 0;)ct={name:o.name,styles:o.styles,next:ct},o=o.next;var l=i.styles+";";return l}return Xc(e,t,n)}}var f=n;if(t==null)return f;var d=t[f];return d!==void 0?d:f}function Xc(e,t,n){var r="";if(Array.isArray(n))for(var s=0;s<n.length;s++)r+=Hn(e,t,n[s])+";";else for(var i in n){var o=n[i];if(typeof o!="object"){var l=o;t!=null&&t[l]!==void 0?r+=i+"{"+t[l]+"}":Si(l)&&(r+=Ir(i)+":"+Ei(i,l)+";")}else if(Array.isArray(o)&&typeof o[0]=="string"&&(t==null||t[o[0]]===void 0))for(var f=0;f<o.length;f++)Si(o[f])&&(r+=Ir(i)+":"+Ei(i,o[f])+";");else{var d=Hn(e,t,o);switch(i){case"animation":case"animationName":{r+=Ir(i)+":"+d+";";break}default:r+=i+"{"+d+"}"}}}return r}var Oi=/label:\s*([^\s;{]+)\s*(;|$)/g,ct;function Mr(e,t,n){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var r=!0,s="";ct=void 0;var i=e[0];if(i==null||i.raw===void 0)r=!1,s+=Hn(n,t,i);else{var o=i;s+=o[0]}for(var l=1;l<e.length;l++)if(s+=Hn(n,t,e[l]),r){var f=i;s+=f[l]}Oi.lastIndex=0;for(var d="",a;(a=Oi.exec(s))!==null;)d+="-"+a[1];var h=zc(s)+d;return{name:h,styles:s,next:ct}}function Ci(e,t,n){var r="";return n.split(" ").forEach(function(s){e[s]!==void 0?t.push(e[s]+";"):s&&(r+=s+" ")}),r}var ef=function(t,n,r){var s=t.key+"-"+n.name;t.registered[s]===void 0&&(t.registered[s]=n.styles)},tf=function(t,n,r){ef(t,n);var s=t.key+"-"+n.name;if(t.inserted[n.name]===void 0){var i=n;do t.insert(n===i?"."+s:"",i,t.sheet,!0),i=i.next;while(i!==void 0)}};function Ti(e,t){if(e.inserted[t.name]===void 0)return e.insert("",t,e.sheet,!0)}function Ai(e,t,n){var r=[],s=Ci(e,r,n);return r.length<2?n:s+t(r)}var nf=function(t){var n=Jc(t);n.sheet.speedy=function(l){this.isSpeedy=l},n.compat=!0;var r=function(){for(var f=arguments.length,d=new Array(f),a=0;a<f;a++)d[a]=arguments[a];var h=Mr(d,n.registered,void 0);return tf(n,h),n.key+"-"+h.name},s=function(){for(var f=arguments.length,d=new Array(f),a=0;a<f;a++)d[a]=arguments[a];var h=Mr(d,n.registered),x="animation-"+h.name;return Ti(n,{name:h.name,styles:"@keyframes "+x+"{"+h.styles+"}"}),x},i=function(){for(var f=arguments.length,d=new Array(f),a=0;a<f;a++)d[a]=arguments[a];var h=Mr(d,n.registered);Ti(n,h)},o=function(){for(var f=arguments.length,d=new Array(f),a=0;a<f;a++)d[a]=arguments[a];return Ai(n.registered,r,rf(d))};return{css:r,cx:o,injectGlobal:i,keyframes:s,hydrate:function(f){f.forEach(function(d){n.inserted[d]=!0})},flush:function(){n.registered={},n.inserted={},n.sheet.flush()},sheet:n.sheet,cache:n,getRegisteredStyles:Ci.bind(null,n.registered),merge:Ai.bind(null,n.registered,r)}},rf=function e(t){for(var n="",r=0;r<t.length;r++){var s=t[r];if(s!=null){var i=void 0;switch(typeof s){case"boolean":break;case"object":{if(Array.isArray(s))i=e(s);else{i="";for(var o in s)s[o]&&o&&(i&&(i+=" "),i+=o)}break}default:i=s}i&&(n&&(n+=" "),n+=i)}}return n};nf({key:"css"});var nn={exports:{}},sf=nn.exports,Ri;function of(){return Ri||(Ri=1,function(e,t){(function(n,r){r(t)})(sf,function(n){var r=Object.defineProperty,s=Object.defineProperties,i=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,d=(y,O,A)=>O in y?r(y,O,{enumerable:!0,configurable:!0,writable:!0,value:A}):y[O]=A,a=(y,O)=>{for(var A in O||(O={}))l.call(O,A)&&d(y,A,O[A]);if(o)for(var A of o(O))f.call(O,A)&&d(y,A,O[A]);return y},h=(y,O)=>s(y,i(O)),x=(y,O,A)=>new Promise((B,W)=>{var Q=oe=>{try{K(A.next(oe))}catch(Ne){W(Ne)}},ne=oe=>{try{K(A.throw(oe))}catch(Ne){W(Ne)}},K=oe=>oe.done?B(oe.value):Promise.resolve(oe.value).then(Q,ne);K((A=A.apply(y,O)).next())}),v=(y=>(y[y.NONE=0]="NONE",y[y.LOADING=1]="LOADING",y[y.LOADED=2]="LOADED",y[y.ERROR=3]="ERROR",y))(v||{});class I{constructor(O){this.items={},this.factory=O}getOrCreateItemByURL(O){let A=this.items[O];return A||(A=this.items[O]=this.factory(O)),A}tryGetItemByURL(O){var A;return(A=this.items[O])!=null?A:null}removeItemByURL(O){const A=this.items[O];return A&&(this.items[O]=null,A)}}const T="__RUNTIME_IMPORT__";function H(y,O){var A,B;const W=globalThis,Q=(A=W[T])!=null?A:W[T]={};return(B=Q[y])!=null?B:Q[y]=O()}const P=H("cssCache",()=>new I(y=>({url:y,status:v.NONE,el:null,error:null,reject:null})));function D(y,O,A){const B={handleLoad(){y.removeEventListener("load",B.handleLoad),y.removeEventListener("error",B.handleError),O()},handleError(W){y.removeEventListener("load",B.handleLoad),y.removeEventListener("error",B.handleError),A(W)}};y.addEventListener("load",B.handleLoad),y.addEventListener("error",B.handleError)}function V(y){const O=P.getOrCreateItemByURL(y),{status:A,error:B}=O;return A===v.LOADED?Promise.resolve():A===v.ERROR?Promise.reject(B):A===v.LOADING?new Promise((W,Q)=>{const{el:ne}=O;D(ne,()=>W(),K=>Q(K.error))}):(O.status=v.LOADING,new Promise((W,Q)=>{const ne=document.createElement("link");ne.rel="stylesheet",ne.href=y,D(ne,()=>{O.status=v.LOADED,W()},K=>{const oe=K.error||new Error(`Load css failed. href=${y}`);O.status=v.ERROR,O.error=oe,Q(oe)}),O.el=ne,ne.setAttribute("data-runtime-import-type","css"),document.head.appendChild(ne)}))}function R(y){return Promise.all(y.map(O=>V(O))).then(()=>Promise.resolve()).catch(O=>Promise.reject(O))}const k=H("jsCache",()=>new I(y=>({url:y,status:v.NONE,el:null,error:null,reject:null,exportThing:void 0}))),ee=globalThis,{define:$}=ee,{keys:Re}=Object;let te=!1;typeof $<"u"&&!$.runtime_import&&(console.warn("runtime-import should NOT coexist with requiesjs or seajs or any other AMD/CMD loader."),te=!0);const Me=H("pendingItemMap",()=>({})),et=function(...y){const O=y.pop(),{currentScript:A}=document;if(!A)throw new Error("currentScript is null.");const{src:B}=A,W=Me[B];if(!W)throw new Error(`Can NOT find item, src=${B}`);Me[B]=null;try{let Q=y[0]||[],ne=null;typeof Q=="string"&&(ne=Q,Q=y[1]||[]);const K=W.exportThing=(()=>{let oe=!1;const Ne={};let c=O(...Q.map(u=>u==="exports"?(oe=!0,Ne):ee[u]));return!c&&oe&&(c=Ne),c})();ne&&(ee[ne]=K),K&&Re(K).length===1&&K.default&&(W.exportThing=K.default,W.exportThing.default=K.default)}catch(Q){W.status=v.ERROR,Q instanceof Error&&(W.error=Q),W.reject(Q)}},Rt=()=>{const{currentScript:y}=document;if(y){const{src:O}=y;if(Me[O])return!0}return!1};["amd","cmd"].forEach(y=>{Object.defineProperty(et,y,{get:Rt})}),et.runtime_import=!0;function Pt(y,O){if(te)throw new Error("runtime-import UMD mode uses window.define, you should NOT have your own window.define.");ee.define||(ee.define=et),Me[y]=O}function rn(y){const O=/legao-comp\/(.*)\/[\d.]+\/web.js$/.exec(y),A=window;if(O&&O.length>0){const B=O[1];A.g_config=A.g_config||{},A.g_config.appKey=B}}function Vn(y,O,A){y.status=v.LOADING,rn(O);const{umd:B,crossOrigin:W}=A;return new Promise((Q,ne)=>{const K=document.createElement("script");if(K.src=O,K.async=!1,K.crossOrigin=W,B){K.setAttribute("data-runtime-import-type","javascript-umd"),y.reject=ne;const oe=K.src;Pt(oe,y)}else K.setAttribute("data-runtime-import-type","javascript");D(K,()=>{y.status=v.LOADED,y.el=null,Q(y.exportThing)},oe=>{const Ne=oe.error||new Error(`Load javascript failed. src=${O}`);y.status=v.ERROR,y.error=Ne,y.el=null,ne(Ne)}),y.el=K,document.body.appendChild(K)})}function he(y,O){const A=k.getOrCreateItemByURL(y),{status:B,exportThing:W,error:Q}=A;if(B===v.LOADED)return Promise.resolve(W);if(B===v.ERROR)return Promise.reject(Q);if(B===v.LOADING){const{el:ne}=A;return new Promise((K,oe)=>{D(ne,()=>K(A.exportThing),Ne=>oe(Ne.error))})}return Vn(A,y,O)}function ie(y,O){let A=Promise.resolve();const B=y.length-1,{umd:W}=O;return y.forEach((Q,ne)=>{const K=W&&ne===B;A=A.then(()=>he(Q,h(a({},O),{umd:K})))}),A}function Y(y){return x(this,null,function*(){const{scripts:O,styles:A}=y;if(A){const{urls:K}=A;yield R(K)}const{dependencies:B=[],entry:W,umd:Q=!0,crossOrigin:ne="anonymous"}=O;if((W?B.concat([W]):B).length)return yield ie(B.concat([W]),{umd:Q,crossOrigin:ne})})}function tt(y,O){return x(this,null,function*(){const A=O??{};return yield Y({scripts:{dependencies:[],entry:y,umd:A.umd,crossOrigin:A.crossOrigin}})})}function It(y){return x(this,null,function*(){return yield Y({scripts:{dependencies:[],entry:""},styles:{urls:[y]}})})}const nt=Y;n.importComponent=Y,n.importModule=nt,n.importScript=tt,n.importStyle=It,Object.defineProperty(n,Symbol.toStringTag,{value:"Module"})})}(nn,nn.exports)),nn.exports}of();const lf=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n};function cf(e){const t=[],n=/{([^{}]*|{([^{}]*|{[^{}]*})*})*}/g;let r;for(;(r=n.exec(e))!==null;)try{t.push(JSON.parse(r[0]))}catch{console.warn("无效的 JSON 片段",r[0])}return t.length>0?t:null}const ff={name:"rtf",props:{vueState:{type:Object,required:!0},data:{type:Object,required:!0}},data(){return{allText:""}},setup(e){const t=Ht({isapplystep:!1,dataid:"",amid:"",allText:"",amresknjh:""}),n=(s,i)=>{const o=window.top.AssistantIntgSDK||{},l=e.vueState.data.allText,f=e.vueState.data.dataid,d=atob(l),a=atob(f),h=new Uint8Array(a.length);for(let te=0;te<a.length;te++)h[te]=a.charCodeAt(te);const x=new TextDecoder("utf-8").decode(h);t.dataid=x;const v=e.vueState.data.amid,I=atob(v),T=new Uint8Array(I.length);for(let te=0;te<I.length;te++)T[te]=I.charCodeAt(te);const H=new TextDecoder("utf-8").decode(T);t.amid=H;const P=atob(e.vueState.data.amresknjh),D=new Uint8Array(P.length);for(let te=0;te<P.length;te++)D[te]=P.charCodeAt(te);const V=new TextDecoder("utf-8").decode(D);t.amresknjh=V;const R=new Uint8Array(d.length);for(let te=0;te<d.length;te++)R[te]=d.charCodeAt(te);const k=new TextDecoder("utf-8").decode(R);t.allText=k;const ee=cf(k),$=s.data.type,Re=o.llCreateMessage("repairstepwrite",{content:{content:ee,dataid:x,type:$}});o.llSendMessage(Re),t.isapplystep=!0},r=()=>{const s=window.top.AssistantIntgSDK||{},i={},o={},l={amid:t.amid,dataid:t.dataid,allText:t.allText,amresknjh:t.amresknjh},d={message:{text:"我需要查看备件信息",files:[]},contextValues:{framework:i,user:o,capture:l}};s.sendCommandViaCUI(d)};return{...yo(t),apply:n,sendmessagetosparepart:r}}},uf={class:"menu-button-widget"},af={key:0};function df(e,t,n,r,s,i){return _r(),ks(Ve,null,[mt("div",uf,[mt("button",{class:"menu-button-widget-button",style:{padding:"5px 10px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},onClick:t[0]||(t[0]=o=>r.apply(n.vueState,o))}," 应用 ")]),e.isapplystep?(_r(),ks("div",af,[t[2]||(t[2]=mt("div",{style:{"padding-top":"5px"}},[mt("span",null,"是否需要查看需要什么备件")],-1)),mt("button",{onClick:t[1]||(t[1]=o=>r.sendmessagetosparepart()),style:{padding:"5px","font-size":"medium",color:"blue",background:"none",border:"none","padding-left":"10px",cursor:"pointer"}},"我需要查看备件信息")])):Il("",!0)],64)}const hf=lf(ff,[["render",df],["__scopeId","data-v-f6684cf2"]]);function pf(e){return JSON.parse(JSON.stringify(e))}class gf{async initialize(){console.log("ExampleWidgetAPI initialized.")}async cleanup(){console.log("ExampleWidgetAPI cleanup.")}createWidget(){return new mf}}class mf{constructor(){Wn(this,"vueState_",Ht({options:{mode:"full",implOptions:{data:{}}},data:{}}));Wn(this,"vueApp_");Wn(this,"isMounted_",!1);const t=this.vueState_;this.vueApp_=gc({setup(){const n=t;return console.log("constuct"),Gt(()=>n.options,r=>{console.log("ExampleWidget options updated.",r)}),Gt(()=>n.data,r=>{console.log("ExampleWidget data updated.",r)}),{state:n}},render(){return console.log("render data",this.state.data),kl(hf,{vueState:t})}})}namespace(){return"sys"}name(){return"rtf"}options(){return this.vueState_.options}updateOptions(t){this.vueState_.options={...this.options(),...pf(t)}}data(){return console.log(this.options().implOptions.data),this.vueState_.data}updateData(t){console.log("ExampleWidget data updating.",t),this.vueState_.data=t,console.log("ExampleWidget data updated.",this.vueState_)}async mount(t){if(this.isMounted_)throw new Error("ExampleWidget already mounted.");this.vueApp_.mount(t),this.isMounted_=!0}async unmount(){if(!this.isMounted_)throw new Error("ExampleWidget is NOT mounted.");this.vueApp_.unmount(),this.isMounted_=!1}async rerender(){}async dispose(){this.isMounted_&&this.unmount()}addEventListener(t){return()=>{}}}const Nr=new gf;function bf(){return Nr.initialize()}function _f(){return Nr.cleanup()}function yf(){return Nr.createWidget()}ve.cleanup=_f,ve.createWidget=yf,ve.initialize=bf,Object.defineProperty(ve,Symbol.toStringTag,{value:"Module"})});
