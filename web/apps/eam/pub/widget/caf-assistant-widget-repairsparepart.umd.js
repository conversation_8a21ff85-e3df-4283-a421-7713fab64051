(function(He,qe){typeof exports=="object"&&typeof module<"u"?qe(exports):typeof define=="function"&&define.amd?define(["exports"],qe):(He=typeof globalThis<"u"?globalThis:He||self,qe(He.RTFWidget={}))})(this,function(He){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function qe(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const K={},nt=[],ge=()=>{},Vi=()=>!1,Ht=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCode<PERSON>t(2)<97),dn=e=>e.startsWith("onUpdate:"),te=Object.assign,hn=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Li=Object.prototype.hasOwnProperty,H=(e,t)=>Li.call(e,t),A=Array.isArray,st=e=>Vt(e)==="[object Map]",kn=e=>Vt(e)==="[object Set]",M=e=>typeof e=="function",Y=e=>typeof e=="string",Ve=e=>typeof e=="symbol",z=e=>e!==null&&typeof e=="object",Xn=e=>(z(e)||M(e))&&M(e.then)&&M(e.catch),Zn=Object.prototype.toString,Vt=e=>Zn.call(e),$i=e=>Vt(e).slice(8,-1),Qn=e=>Vt(e)==="[object Object]",pn=e=>Y(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,gt=qe(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Lt=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ui=/-(\w)/g,Le=Lt(e=>e.replace(Ui,(t,n)=>n?n.toUpperCase():"")),Wi=/\B([A-Z])/g,ze=Lt(e=>e.replace(Wi,"-$1").toLowerCase()),es=Lt(e=>e.charAt(0).toUpperCase()+e.slice(1)),gn=Lt(e=>e?`on${es(e)}`:""),Ge=(e,t)=>!Object.is(e,t),mn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ts=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Bi=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ns;const $t=()=>ns||(ns=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function _n(e){if(A(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],i=Y(s)?zi(s):_n(s);if(i)for(const r in i)t[r]=i[r]}return t}else if(Y(e)||z(e))return e}const Ki=/;(?![^(]*\))/g,Ji=/:([^]+)/,qi=/\/\*[^]*?\*\//g;function zi(e){const t={};return e.replace(qi,"").split(Ki).forEach(n=>{if(n){const s=n.split(Ji);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function bn(e){let t="";if(Y(e))t=e;else if(A(e))for(let n=0;n<e.length;n++){const s=bn(e[n]);s&&(t+=s+" ")}else if(z(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Gi=qe("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function ss(e){return!!e||e===""}const is=e=>!!(e&&e.__v_isRef===!0),it=e=>Y(e)?e:e==null?"":A(e)||z(e)&&(e.toString===Zn||!M(e.toString))?is(e)?it(e.value):JSON.stringify(e,rs,2):String(e),rs=(e,t)=>is(t)?rs(e,t.value):st(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,i],r)=>(n[yn(s,r)+" =>"]=i,n),{})}:kn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>yn(n))}:Ve(t)?yn(t):z(t)&&!A(t)&&!Qn(t)?String(t):t,yn=(e,t="")=>{var n;return Ve(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};var Yi={NODE_ENV:"production"};let oe;class ki{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=oe,!t&&oe&&(this.index=(oe.scopes||(oe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=oe;try{return oe=this,t()}finally{oe=n}}}on(){oe=this}off(){oe=this.parent}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function Xi(){return oe}let J;const xn=new WeakSet;class os{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,oe&&oe.active&&oe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,xn.has(this)&&(xn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||cs(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,hs(this),fs(this);const t=J,n=me;J=this,me=!0;try{return this.fn()}finally{us(this),J=t,me=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)En(t);this.deps=this.depsTail=void 0,hs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?xn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){wn(this)&&this.run()}get dirty(){return wn(this)}}let ls=0,mt,_t;function cs(e,t=!1){if(e.flags|=8,t){e.next=_t,_t=e;return}e.next=mt,mt=e}function vn(){ls++}function Sn(){if(--ls>0)return;if(_t){let t=_t;for(_t=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;mt;){let t=mt;for(mt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function fs(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function us(e){let t,n=e.depsTail,s=n;for(;s;){const i=s.prevDep;s.version===-1?(s===n&&(n=i),En(s),Zi(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=i}e.deps=t,e.depsTail=n}function wn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(as(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function as(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===bt))return;e.globalVersion=bt;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!wn(e)){e.flags&=-3;return}const n=J,s=me;J=e,me=!0;try{fs(e);const i=e.fn(e._value);(t.version===0||Ge(i,e._value))&&(e._value=i,t.version++)}catch(i){throw t.version++,i}finally{J=n,me=s,us(e),e.flags&=-3}}function En(e,t=!1){const{dep:n,prevSub:s,nextSub:i}=e;if(s&&(s.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)En(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Zi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let me=!0;const ds=[];function Me(){ds.push(me),me=!1}function Re(){const e=ds.pop();me=e===void 0?!0:e}function hs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=J;J=void 0;try{t()}finally{J=n}}}let bt=0;class Qi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ps{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!J||!me||J===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==J)n=this.activeLink=new Qi(J,this),J.deps?(n.prevDep=J.depsTail,J.depsTail.nextDep=n,J.depsTail=n):J.deps=J.depsTail=n,gs(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=J.depsTail,n.nextDep=void 0,J.depsTail.nextDep=n,J.depsTail=n,J.deps===n&&(J.deps=s)}return n}trigger(t){this.version++,bt++,this.notify(t)}notify(t){vn();try{Yi.NODE_ENV;for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Sn()}}}function gs(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)gs(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Ut=new WeakMap,Ye=Symbol(""),Tn=Symbol(""),yt=Symbol("");function ee(e,t,n){if(me&&J){let s=Ut.get(e);s||Ut.set(e,s=new Map);let i=s.get(n);i||(s.set(n,i=new ps),i.map=s,i.key=n),i.track()}}function Ie(e,t,n,s,i,r){const o=Ut.get(e);if(!o){bt++;return}const l=f=>{f&&f.trigger()};if(vn(),t==="clear")o.forEach(l);else{const f=A(e),d=f&&pn(n);if(f&&n==="length"){const a=Number(s);o.forEach((p,S)=>{(S==="length"||S===yt||!Ve(S)&&S>=a)&&l(p)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),d&&l(o.get(yt)),t){case"add":f?d&&l(o.get("length")):(l(o.get(Ye)),st(e)&&l(o.get(Tn)));break;case"delete":f||(l(o.get(Ye)),st(e)&&l(o.get(Tn)));break;case"set":st(e)&&l(o.get(Ye));break}}Sn()}function er(e,t){const n=Ut.get(e);return n&&n.get(t)}function rt(e){const t=F(e);return t===e?t:(ee(t,"iterate",yt),_e(e)?t:t.map(ie))}function Wt(e){return ee(e=F(e),"iterate",yt),e}const tr={__proto__:null,[Symbol.iterator](){return Cn(this,Symbol.iterator,ie)},concat(...e){return rt(this).concat(...e.map(t=>A(t)?rt(t):t))},entries(){return Cn(this,"entries",e=>(e[1]=ie(e[1]),e))},every(e,t){return De(this,"every",e,t,void 0,arguments)},filter(e,t){return De(this,"filter",e,t,n=>n.map(ie),arguments)},find(e,t){return De(this,"find",e,t,ie,arguments)},findIndex(e,t){return De(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return De(this,"findLast",e,t,ie,arguments)},findLastIndex(e,t){return De(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return De(this,"forEach",e,t,void 0,arguments)},includes(...e){return On(this,"includes",e)},indexOf(...e){return On(this,"indexOf",e)},join(e){return rt(this).join(e)},lastIndexOf(...e){return On(this,"lastIndexOf",e)},map(e,t){return De(this,"map",e,t,void 0,arguments)},pop(){return xt(this,"pop")},push(...e){return xt(this,"push",e)},reduce(e,...t){return ms(this,"reduce",e,t)},reduceRight(e,...t){return ms(this,"reduceRight",e,t)},shift(){return xt(this,"shift")},some(e,t){return De(this,"some",e,t,void 0,arguments)},splice(...e){return xt(this,"splice",e)},toReversed(){return rt(this).toReversed()},toSorted(e){return rt(this).toSorted(e)},toSpliced(...e){return rt(this).toSpliced(...e)},unshift(...e){return xt(this,"unshift",e)},values(){return Cn(this,"values",ie)}};function Cn(e,t,n){const s=Wt(e),i=s[t]();return s!==e&&!_e(e)&&(i._next=i.next,i.next=()=>{const r=i._next();return r.value&&(r.value=n(r.value)),r}),i}const nr=Array.prototype;function De(e,t,n,s,i,r){const o=Wt(e),l=o!==e&&!_e(e),f=o[t];if(f!==nr[t]){const p=f.apply(e,r);return l?ie(p):p}let d=n;o!==e&&(l?d=function(p,S){return n.call(this,ie(p),S,e)}:n.length>2&&(d=function(p,S){return n.call(this,p,S,e)}));const a=f.call(o,d,s);return l&&i?i(a):a}function ms(e,t,n,s){const i=Wt(e);let r=n;return i!==e&&(_e(e)?n.length>3&&(r=function(o,l,f){return n.call(this,o,l,f,e)}):r=function(o,l,f){return n.call(this,o,ie(l),f,e)}),i[t](r,...s)}function On(e,t,n){const s=F(e);ee(s,"iterate",yt);const i=s[t](...n);return(i===-1||i===!1)&&Pn(n[0])?(n[0]=F(n[0]),s[t](...n)):i}function xt(e,t,n=[]){Me(),vn();const s=F(e)[t].apply(e,n);return Sn(),Re(),s}const sr=qe("__proto__,__v_isRef,__isVue"),_s=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ve));function ir(e){Ve(e)||(e=String(e));const t=F(this);return ee(t,"has",e),t.hasOwnProperty(e)}class bs{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(i?r?Es:ws:r?Ss:vs).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=A(t);if(!i){let f;if(o&&(f=tr[n]))return f;if(n==="hasOwnProperty")return ir}const l=Reflect.get(t,n,Z(t)?t:s);return(Ve(n)?_s.has(n):sr(n))||(i||ee(t,"get",n),r)?l:Z(l)?o&&pn(n)?l:l.value:z(l)?i?Ts(l):vt(l):l}}class ys extends bs{constructor(t=!1){super(!1,t)}set(t,n,s,i){let r=t[n];if(!this._isShallow){const f=lt(r);if(!_e(s)&&!lt(s)&&(r=F(r),s=F(s)),!A(t)&&Z(r)&&!Z(s))return f?!1:(r.value=s,!0)}const o=A(t)&&pn(n)?Number(n)<t.length:H(t,n),l=Reflect.set(t,n,s,Z(t)?t:i);return t===F(i)&&(o?Ge(s,r)&&Ie(t,"set",n,s):Ie(t,"add",n,s)),l}deleteProperty(t,n){const s=H(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&s&&Ie(t,"delete",n,void 0),i}has(t,n){const s=Reflect.has(t,n);return(!Ve(n)||!_s.has(n))&&ee(t,"has",n),s}ownKeys(t){return ee(t,"iterate",A(t)?"length":Ye),Reflect.ownKeys(t)}}class xs extends bs{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const rr=new ys,or=new xs,lr=new ys(!0),cr=new xs(!0),An=e=>e,Bt=e=>Reflect.getPrototypeOf(e);function fr(e,t,n){return function(...s){const i=this.__v_raw,r=F(i),o=st(r),l=e==="entries"||e===Symbol.iterator&&o,f=e==="keys"&&o,d=i[e](...s),a=n?An:t?Mn:ie;return!t&&ee(r,"iterate",f?Tn:Ye),{next(){const{value:p,done:S}=d.next();return S?{value:p,done:S}:{value:l?[a(p[0]),a(p[1])]:a(p),done:S}},[Symbol.iterator](){return this}}}}function Kt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ur(e,t){const n={get(i){const r=this.__v_raw,o=F(r),l=F(i);e||(Ge(i,l)&&ee(o,"get",i),ee(o,"get",l));const{has:f}=Bt(o),d=t?An:e?Mn:ie;if(f.call(o,i))return d(r.get(i));if(f.call(o,l))return d(r.get(l));r!==o&&r.get(i)},get size(){const i=this.__v_raw;return!e&&ee(F(i),"iterate",Ye),Reflect.get(i,"size",i)},has(i){const r=this.__v_raw,o=F(r),l=F(i);return e||(Ge(i,l)&&ee(o,"has",i),ee(o,"has",l)),i===l?r.has(i):r.has(i)||r.has(l)},forEach(i,r){const o=this,l=o.__v_raw,f=F(l),d=t?An:e?Mn:ie;return!e&&ee(f,"iterate",Ye),l.forEach((a,p)=>i.call(r,d(a),d(p),o))}};return te(n,e?{add:Kt("add"),set:Kt("set"),delete:Kt("delete"),clear:Kt("clear")}:{add(i){!t&&!_e(i)&&!lt(i)&&(i=F(i));const r=F(this);return Bt(r).has.call(r,i)||(r.add(i),Ie(r,"add",i,i)),this},set(i,r){!t&&!_e(r)&&!lt(r)&&(r=F(r));const o=F(this),{has:l,get:f}=Bt(o);let d=l.call(o,i);d||(i=F(i),d=l.call(o,i));const a=f.call(o,i);return o.set(i,r),d?Ge(r,a)&&Ie(o,"set",i,r):Ie(o,"add",i,r),this},delete(i){const r=F(this),{has:o,get:l}=Bt(r);let f=o.call(r,i);f||(i=F(i),f=o.call(r,i)),l&&l.call(r,i);const d=r.delete(i);return f&&Ie(r,"delete",i,void 0),d},clear(){const i=F(this),r=i.size!==0,o=i.clear();return r&&Ie(i,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=fr(i,e,t)}),n}function Jt(e,t){const n=ur(e,t);return(s,i,r)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?s:Reflect.get(H(n,i)&&i in s?n:s,i,r)}const ar={get:Jt(!1,!1)},dr={get:Jt(!1,!0)},hr={get:Jt(!0,!1)},pr={get:Jt(!0,!0)},vs=new WeakMap,Ss=new WeakMap,ws=new WeakMap,Es=new WeakMap;function gr(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function mr(e){return e.__v_skip||!Object.isExtensible(e)?0:gr($i(e))}function vt(e){return lt(e)?e:zt(e,!1,rr,ar,vs)}function _r(e){return zt(e,!1,lr,dr,Ss)}function Ts(e){return zt(e,!0,or,hr,ws)}function qt(e){return zt(e,!0,cr,pr,Es)}function zt(e,t,n,s,i){if(!z(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=i.get(e);if(r)return r;const o=mr(e);if(o===0)return e;const l=new Proxy(e,o===2?s:n);return i.set(e,l),l}function ot(e){return lt(e)?ot(e.__v_raw):!!(e&&e.__v_isReactive)}function lt(e){return!!(e&&e.__v_isReadonly)}function _e(e){return!!(e&&e.__v_isShallow)}function Pn(e){return e?!!e.__v_raw:!1}function F(e){const t=e&&e.__v_raw;return t?F(t):e}function br(e){return!H(e,"__v_skip")&&Object.isExtensible(e)&&ts(e,"__v_skip",!0),e}const ie=e=>z(e)?vt(e):e,Mn=e=>z(e)?Ts(e):e;function Z(e){return e?e.__v_isRef===!0:!1}function yr(e){return Z(e)?e.value:e}const xr={get:(e,t,n)=>t==="__v_raw"?e:yr(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const i=e[t];return Z(i)&&!Z(n)?(i.value=n,!0):Reflect.set(e,t,n,s)}};function Cs(e){return ot(e)?e:new Proxy(e,xr)}function vr(e){const t=A(e)?new Array(e.length):{};for(const n in e)t[n]=wr(e,n);return t}class Sr{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return er(F(this._object),this._key)}}function wr(e,t,n){const s=e[t];return Z(s)?s:new Sr(e,t,n)}class Er{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ps(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=bt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&J!==this)return cs(this,!0),!0}get value(){const t=this.dep.track();return as(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Tr(e,t,n=!1){let s,i;return M(e)?s=e:(s=e.get,i=e.set),new Er(s,i,n)}const Gt={},Yt=new WeakMap;let ke;function Cr(e,t=!1,n=ke){if(n){let s=Yt.get(n);s||Yt.set(n,s=[]),s.push(e)}}function Or(e,t,n=K){const{immediate:s,deep:i,once:r,scheduler:o,augmentJob:l,call:f}=n,d=C=>i?C:_e(C)||i===!1||i===0?$e(C,1):$e(C);let a,p,S,E,I=!1,R=!1;if(Z(e)?(p=()=>e.value,I=_e(e)):ot(e)?(p=()=>d(e),I=!0):A(e)?(R=!0,I=e.some(C=>ot(C)||_e(C)),p=()=>e.map(C=>{if(Z(C))return C.value;if(ot(C))return d(C);if(M(C))return f?f(C,2):C()})):M(e)?t?p=f?()=>f(e,2):e:p=()=>{if(S){Me();try{S()}finally{Re()}}const C=ke;ke=a;try{return f?f(e,3,[E]):e(E)}finally{ke=C}}:p=ge,t&&i){const C=p,G=i===!0?1/0:i;p=()=>$e(C(),G)}const k=Xi(),j=()=>{a.stop(),k&&k.active&&hn(k.effects,a)};if(r&&t){const C=t;t=(...G)=>{C(...G),j()}}let $=R?new Array(e.length).fill(Gt):Gt;const W=C=>{if(!(!(a.flags&1)||!a.dirty&&!C))if(t){const G=a.run();if(i||I||(R?G.some((ue,V)=>Ge(ue,$[V])):Ge(G,$))){S&&S();const ue=ke;ke=a;try{const V=[G,$===Gt?void 0:R&&$[0]===Gt?[]:$,E];f?f(t,3,V):t(...V),$=G}finally{ke=ue}}}else a.run()};return l&&l(W),a=new os(p),a.scheduler=o?()=>o(W,!1):W,E=C=>Cr(C,!1,a),S=a.onStop=()=>{const C=Yt.get(a);if(C){if(f)f(C,4);else for(const G of C)G();Yt.delete(a)}},t?s?W(!0):$=a.run():o?o(W.bind(null,!0),!0):a.run(),j.pause=a.pause.bind(a),j.resume=a.resume.bind(a),j.stop=j,j}function $e(e,t=1/0,n){if(t<=0||!z(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Z(e))$e(e.value,t,n);else if(A(e))for(let s=0;s<e.length;s++)$e(e[s],t,n);else if(kn(e)||st(e))e.forEach(s=>{$e(s,t,n)});else if(Qn(e)){for(const s in e)$e(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&$e(e[s],t,n)}return e}var Ue={NODE_ENV:"production"};const St=[];let Rn=!1;function Ar(e,...t){if(Rn)return;Rn=!0,Me();const n=St.length?St[St.length-1].component:null,s=n&&n.appContext.config.warnHandler,i=Pr();if(s)ct(s,n,11,[e+t.map(r=>{var o,l;return(l=(o=r.toString)==null?void 0:o.call(r))!=null?l:JSON.stringify(r)}).join(""),n&&n.proxy,i.map(({vnode:r})=>`at <${yi(n,r.type)}>`).join(`
`),i]);else{const r=[`[Vue warn]: ${e}`,...t];i.length&&r.push(`
`,...Mr(i)),console.warn(...r)}Re(),Rn=!1}function Pr(){let e=St[St.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const s=e.component&&e.component.parent;e=s&&s.vnode}return t}function Mr(e){const t=[];return e.forEach((n,s)=>{t.push(...s===0?[]:[`
`],...Rr(n))}),t}function Rr({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",s=e.component?e.component.parent==null:!1,i=` at <${yi(e.component,e.type,s)}`,r=">"+n;return e.props?[i,...Ir(e.props),r]:[i+r]}function Ir(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(s=>{t.push(...Os(s,e[s]))}),n.length>3&&t.push(" ..."),t}function Os(e,t,n){return Y(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:Z(t)?(t=Os(e,F(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):M(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=F(t),n?t:[`${e}=`,t])}function ct(e,t,n,s){try{return s?e(...s):e()}catch(i){kt(i,t,n)}}function xe(e,t,n,s){if(M(e)){const i=ct(e,t,n,s);return i&&Xn(i)&&i.catch(r=>{kt(r,t,n)}),i}if(A(e)){const i=[];for(let r=0;r<e.length;r++)i.push(xe(e[r],t,n,s));return i}}function kt(e,t,n,s=!0){const i=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||K;if(t){let l=t.parent;const f=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let p=0;p<a.length;p++)if(a[p](e,f,d)===!1)return}l=l.parent}if(r){Me(),ct(r,null,10,[e,f,d]),Re();return}}Dr(e,n,i,s,o)}function Dr(e,t,n,s=!0,i=!1){if(i)throw e;console.error(e)}const ne=[];let ve=-1;const ft=[];let We=null,ut=0;const As=Promise.resolve();let Xt=null;function Nr(e){const t=Xt||As;return e?t.then(this?e.bind(this):e):t}function Fr(e){let t=ve+1,n=ne.length;for(;t<n;){const s=t+n>>>1,i=ne[s],r=wt(i);r<e||r===e&&i.flags&2?t=s+1:n=s}return t}function In(e){if(!(e.flags&1)){const t=wt(e),n=ne[ne.length-1];!n||!(e.flags&2)&&t>=wt(n)?ne.push(e):ne.splice(Fr(t),0,e),e.flags|=1,Ps()}}function Ps(){Xt||(Xt=As.then(Is))}function jr(e){A(e)?ft.push(...e):We&&e.id===-1?We.splice(ut+1,0,e):e.flags&1||(ft.push(e),e.flags|=1),Ps()}function Ms(e,t,n=ve+1){for(;n<ne.length;n++){const s=ne[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;ne.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Rs(e){if(ft.length){const t=[...new Set(ft)].sort((n,s)=>wt(n)-wt(s));if(ft.length=0,We){We.push(...t);return}for(We=t,ut=0;ut<We.length;ut++){const n=We[ut];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}We=null,ut=0}}const wt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Is(e){const t=ge;try{for(ve=0;ve<ne.length;ve++){const n=ne[ve];n&&!(n.flags&8)&&(Ue.NODE_ENV!=="production"&&t(n),n.flags&4&&(n.flags&=-2),ct(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;ve<ne.length;ve++){const n=ne[ve];n&&(n.flags&=-2)}ve=-1,ne.length=0,Rs(),Xt=null,(ne.length||ft.length)&&Is()}}let Se=null,Ds=null;function Zt(e){const t=Se;return Se=e,Ds=e&&e.type.__scopeId||null,t}function Hr(e,t=Se,n){if(!t||e._n)return e;const s=(...i)=>{s._d&&ai(-1);const r=Zt(t);let o;try{o=e(...i)}finally{Zt(r),s._d&&ai(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function Xe(e,t,n,s){const i=e.dirs,r=t&&t.dirs;for(let o=0;o<i.length;o++){const l=i[o];r&&(l.oldValue=r[o].value);let f=l.dir[s];f&&(Me(),xe(f,n,8,[e.el,l,e,t]),Re())}}const Vr=Symbol("_vte"),Lr=e=>e.__isTeleport;function Dn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Dn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ns(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Qt(e,t,n,s,i=!1){if(A(e)){e.forEach((I,R)=>Qt(I,t&&(A(t)?t[R]:t),n,s,i));return}if(Et(s)&&!i){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Qt(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?Kn(s.component):s.el,o=i?null:r,{i:l,r:f}=e,d=t&&t.r,a=l.refs===K?l.refs={}:l.refs,p=l.setupState,S=F(p),E=p===K?()=>!1:I=>H(S,I);if(d!=null&&d!==f&&(Y(d)?(a[d]=null,E(d)&&(p[d]=null)):Z(d)&&(d.value=null)),M(f))ct(f,l,12,[o,a]);else{const I=Y(f),R=Z(f);if(I||R){const k=()=>{if(e.f){const j=I?E(f)?p[f]:a[f]:f.value;i?A(j)&&hn(j,r):A(j)?j.includes(r)||j.push(r):I?(a[f]=[r],E(f)&&(p[f]=a[f])):(f.value=[r],e.k&&(a[e.k]=f.value))}else I?(a[f]=o,E(f)&&(p[f]=o)):R&&(f.value=o,e.k&&(a[e.k]=o))};o?(k.id=-1,le(k,n)):k()}}}$t().requestIdleCallback,$t().cancelIdleCallback;const Et=e=>!!e.type.__asyncLoader,Fs=e=>e.type.__isKeepAlive;function $r(e,t){js(e,"a",t)}function Ur(e,t){js(e,"da",t)}function js(e,t,n=Q){const s=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(en(t,s,n),n){let i=n.parent;for(;i&&i.parent;)Fs(i.parent.vnode)&&Wr(s,t,n,i),i=i.parent}}function Wr(e,t,n,s){const i=en(t,e,s,!0);Hs(()=>{hn(s[t],i)},n)}function en(e,t,n=Q,s=!1){if(n){const i=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...o)=>{Me();const l=Rt(n),f=xe(t,n,e,o);return l(),Re(),f});return s?i.unshift(r):i.push(r),r}}const Ne=e=>(t,n=Q)=>{(!It||e==="sp")&&en(e,(...s)=>t(...s),n)},Br=Ne("bm"),Kr=Ne("m"),Jr=Ne("bu"),qr=Ne("u"),zr=Ne("bum"),Hs=Ne("um"),Gr=Ne("sp"),Yr=Ne("rtg"),kr=Ne("rtc");function Xr(e,t=Q){en("ec",e,t)}const Zr=Symbol.for("v-ndc");function Vs(e,t,n,s){let i;const r=n,o=A(e);if(o||Y(e)){const l=o&&ot(e);let f=!1;l&&(f=!_e(e),e=Wt(e)),i=new Array(e.length);for(let d=0,a=e.length;d<a;d++)i[d]=t(f?ie(e[d]):e[d],d,void 0,r)}else if(typeof e=="number"){i=new Array(e);for(let l=0;l<e;l++)i[l]=t(l+1,l,void 0,r)}else if(z(e))if(e[Symbol.iterator])i=Array.from(e,(l,f)=>t(l,f,void 0,r));else{const l=Object.keys(e);i=new Array(l.length);for(let f=0,d=l.length;f<d;f++){const a=l[f];i[f]=t(e[a],a,f,r)}}else i=[];return i}const Nn=e=>e?mi(e)?Kn(e):Nn(e.parent):null,Tt=te(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Nn(e.parent),$root:e=>Nn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ws(e),$forceUpdate:e=>e.f||(e.f=()=>{In(e.update)}),$nextTick:e=>e.n||(e.n=Nr.bind(e.proxy)),$watch:e=>vo.bind(e)}),Fn=(e,t)=>e!==K&&!e.__isScriptSetup&&H(e,t),Qr={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:i,props:r,accessCache:o,type:l,appContext:f}=e;let d;if(t[0]!=="$"){const E=o[t];if(E!==void 0)switch(E){case 1:return s[t];case 2:return i[t];case 4:return n[t];case 3:return r[t]}else{if(Fn(s,t))return o[t]=1,s[t];if(i!==K&&H(i,t))return o[t]=2,i[t];if((d=e.propsOptions[0])&&H(d,t))return o[t]=3,r[t];if(n!==K&&H(n,t))return o[t]=4,n[t];jn&&(o[t]=0)}}const a=Tt[t];let p,S;if(a)return t==="$attrs"&&ee(e.attrs,"get",""),a(e);if((p=l.__cssModules)&&(p=p[t]))return p;if(n!==K&&H(n,t))return o[t]=4,n[t];if(S=f.config.globalProperties,H(S,t))return S[t]},set({_:e},t,n){const{data:s,setupState:i,ctx:r}=e;return Fn(i,t)?(i[t]=n,!0):s!==K&&H(s,t)?(s[t]=n,!0):H(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:i,propsOptions:r}},o){let l;return!!n[o]||e!==K&&H(e,o)||Fn(t,o)||(l=r[0])&&H(l,o)||H(s,o)||H(Tt,o)||H(i.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:H(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ls(e){return A(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let jn=!0;function eo(e){const t=Ws(e),n=e.proxy,s=e.ctx;jn=!1,t.beforeCreate&&$s(t.beforeCreate,e,"bc");const{data:i,computed:r,methods:o,watch:l,provide:f,inject:d,created:a,beforeMount:p,mounted:S,beforeUpdate:E,updated:I,activated:R,deactivated:k,beforeDestroy:j,beforeUnmount:$,destroyed:W,unmounted:C,render:G,renderTracked:ue,renderTriggered:V,errorCaptured:N,serverPrefetch:Je,expose:Te,inheritAttrs:tt,components:ht,directives:Ce,filters:un}=t;if(d&&to(d,s,null),o)for(const B in o){const L=o[B];M(L)&&(s[B]=L.bind(n))}if(i){const B=i.call(n,n);z(B)&&(e.data=vt(B))}if(jn=!0,r)for(const B in r){const L=r[B],ye=M(L)?L.bind(n,n):M(L.get)?L.get.bind(n,n):ge,pt=!M(L)&&M(L.set)?L.set.bind(n):ge,je=qo({get:ye,set:pt});Object.defineProperty(s,B,{enumerable:!0,configurable:!0,get:()=>je.value,set:pe=>je.value=pe})}if(l)for(const B in l)Us(l[B],s,n,B);if(f){const B=M(f)?f.call(n):f;Reflect.ownKeys(B).forEach(L=>{lo(L,B[L])})}a&&$s(a,e,"c");function X(B,L){A(L)?L.forEach(ye=>B(ye.bind(n))):L&&B(L.bind(n))}if(X(Br,p),X(Kr,S),X(Jr,E),X(qr,I),X($r,R),X(Ur,k),X(Xr,N),X(kr,ue),X(Yr,V),X(zr,$),X(Hs,C),X(Gr,Je),A(Te))if(Te.length){const B=e.exposed||(e.exposed={});Te.forEach(L=>{Object.defineProperty(B,L,{get:()=>n[L],set:ye=>n[L]=ye})})}else e.exposed||(e.exposed={});G&&e.render===ge&&(e.render=G),tt!=null&&(e.inheritAttrs=tt),ht&&(e.components=ht),Ce&&(e.directives=Ce),Je&&Ns(e)}function to(e,t,n=ge){A(e)&&(e=Hn(e));for(const s in e){const i=e[s];let r;z(i)?"default"in i?r=nn(i.from||s,i.default,!0):r=nn(i.from||s):r=nn(i),Z(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:o=>r.value=o}):t[s]=r}}function $s(e,t,n){xe(A(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Us(e,t,n,s){let i=s.includes(".")?oi(n,s):()=>n[s];if(Y(e)){const r=t[e];M(r)&&Ot(i,r)}else if(M(e))Ot(i,e.bind(n));else if(z(e))if(A(e))e.forEach(r=>Us(r,t,n,s));else{const r=M(e.handler)?e.handler.bind(n):t[e.handler];M(r)&&Ot(i,r,e)}}function Ws(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:i,optionsCache:r,config:{optionMergeStrategies:o}}=e.appContext,l=r.get(t);let f;return l?f=l:!i.length&&!n&&!s?f=t:(f={},i.length&&i.forEach(d=>tn(f,d,o,!0)),tn(f,t,o)),z(t)&&r.set(t,f),f}function tn(e,t,n,s=!1){const{mixins:i,extends:r}=t;r&&tn(e,r,n,!0),i&&i.forEach(o=>tn(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=no[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const no={data:Bs,props:Ks,emits:Ks,methods:Ct,computed:Ct,beforeCreate:se,created:se,beforeMount:se,mounted:se,beforeUpdate:se,updated:se,beforeDestroy:se,beforeUnmount:se,destroyed:se,unmounted:se,activated:se,deactivated:se,errorCaptured:se,serverPrefetch:se,components:Ct,directives:Ct,watch:io,provide:Bs,inject:so};function Bs(e,t){return t?e?function(){return te(M(e)?e.call(this,this):e,M(t)?t.call(this,this):t)}:t:e}function so(e,t){return Ct(Hn(e),Hn(t))}function Hn(e){if(A(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function se(e,t){return e?[...new Set([].concat(e,t))]:t}function Ct(e,t){return e?te(Object.create(null),e,t):t}function Ks(e,t){return e?A(e)&&A(t)?[...new Set([...e,...t])]:te(Object.create(null),Ls(e),Ls(t??{})):t}function io(e,t){if(!e)return t;if(!t)return e;const n=te(Object.create(null),e);for(const s in t)n[s]=se(e[s],t[s]);return n}function Js(){return{app:null,config:{isNativeTag:Vi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ro=0;function oo(e,t){return function(s,i=null){M(s)||(s=te({},s)),i!=null&&!z(i)&&(i=null);const r=Js(),o=new WeakSet,l=[];let f=!1;const d=r.app={_uid:ro++,_component:s,_props:i,_container:null,_context:r,_instance:null,version:Go,get config(){return r.config},set config(a){},use(a,...p){return o.has(a)||(a&&M(a.install)?(o.add(a),a.install(d,...p)):M(a)&&(o.add(a),a(d,...p))),d},mixin(a){return r.mixins.includes(a)||r.mixins.push(a),d},component(a,p){return p?(r.components[a]=p,d):r.components[a]},directive(a,p){return p?(r.directives[a]=p,d):r.directives[a]},mount(a,p,S){if(!f){const E=d._ceVNode||fe(s,i);return E.appContext=r,S===!0?S="svg":S===!1&&(S=void 0),e(E,a,S),f=!0,d._container=a,a.__vue_app__=d,Kn(E.component)}},onUnmount(a){l.push(a)},unmount(){f&&(xe(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,p){return r.provides[a]=p,d},runWithContext(a){const p=at;at=d;try{return a()}finally{at=p}}};return d}}let at=null;function lo(e,t){if(Q){let n=Q.provides;const s=Q.parent&&Q.parent.provides;s===n&&(n=Q.provides=Object.create(s)),n[e]=t}}function nn(e,t,n=!1){const s=Q||Se;if(s||at){const i=at?at._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&M(t)?t.call(s&&s.proxy):t}}const qs={},zs=()=>Object.create(qs),Gs=e=>Object.getPrototypeOf(e)===qs;function co(e,t,n,s=!1){const i={},r=zs();e.propsDefaults=Object.create(null),Ys(e,t,i,r);for(const o in e.propsOptions[0])o in i||(i[o]=void 0);n?e.props=s?i:_r(i):e.type.props?e.props=i:e.props=r,e.attrs=r}function fo(e,t,n,s){const{props:i,attrs:r,vnode:{patchFlag:o}}=e,l=F(i),[f]=e.propsOptions;let d=!1;if((s||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let p=0;p<a.length;p++){let S=a[p];if(sn(e.emitsOptions,S))continue;const E=t[S];if(f)if(H(r,S))E!==r[S]&&(r[S]=E,d=!0);else{const I=Le(S);i[I]=Vn(f,l,I,E,e,!1)}else E!==r[S]&&(r[S]=E,d=!0)}}}else{Ys(e,t,i,r)&&(d=!0);let a;for(const p in l)(!t||!H(t,p)&&((a=ze(p))===p||!H(t,a)))&&(f?n&&(n[p]!==void 0||n[a]!==void 0)&&(i[p]=Vn(f,l,p,void 0,e,!0)):delete i[p]);if(r!==l)for(const p in r)(!t||!H(t,p))&&(delete r[p],d=!0)}d&&Ie(e.attrs,"set","")}function Ys(e,t,n,s){const[i,r]=e.propsOptions;let o=!1,l;if(t)for(let f in t){if(gt(f))continue;const d=t[f];let a;i&&H(i,a=Le(f))?!r||!r.includes(a)?n[a]=d:(l||(l={}))[a]=d:sn(e.emitsOptions,f)||(!(f in s)||d!==s[f])&&(s[f]=d,o=!0)}if(r){const f=F(n),d=l||K;for(let a=0;a<r.length;a++){const p=r[a];n[p]=Vn(i,f,p,d[p],e,!H(d,p))}}return o}function Vn(e,t,n,s,i,r){const o=e[n];if(o!=null){const l=H(o,"default");if(l&&s===void 0){const f=o.default;if(o.type!==Function&&!o.skipFactory&&M(f)){const{propsDefaults:d}=i;if(n in d)s=d[n];else{const a=Rt(i);s=d[n]=f.call(null,t),a()}}else s=f;i.ce&&i.ce._setProp(n,s)}o[0]&&(r&&!l?s=!1:o[1]&&(s===""||s===ze(n))&&(s=!0))}return s}const uo=new WeakMap;function ks(e,t,n=!1){const s=n?uo:t.propsCache,i=s.get(e);if(i)return i;const r=e.props,o={},l=[];let f=!1;if(!M(e)){const a=p=>{f=!0;const[S,E]=ks(p,t,!0);te(o,S),E&&l.push(...E)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!r&&!f)return z(e)&&s.set(e,nt),nt;if(A(r))for(let a=0;a<r.length;a++){const p=Le(r[a]);Xs(p)&&(o[p]=K)}else if(r)for(const a in r){const p=Le(a);if(Xs(p)){const S=r[a],E=o[p]=A(S)||M(S)?{type:S}:te({},S),I=E.type;let R=!1,k=!0;if(A(I))for(let j=0;j<I.length;++j){const $=I[j],W=M($)&&$.name;if(W==="Boolean"){R=!0;break}else W==="String"&&(k=!1)}else R=M(I)&&I.name==="Boolean";E[0]=R,E[1]=k,(R||H(E,"default"))&&l.push(p)}}const d=[o,l];return z(e)&&s.set(e,d),d}function Xs(e){return e[0]!=="$"&&!gt(e)}const Zs=e=>e[0]==="_"||e==="$stable",Ln=e=>A(e)?e.map(we):[we(e)],ao=(e,t,n)=>{if(t._n)return t;const s=Hr((...i)=>(Ue.NODE_ENV!=="production"&&Q&&(!n||(n.root,Q.root)),Ln(t(...i))),n);return s._c=!1,s},Qs=(e,t,n)=>{const s=e._ctx;for(const i in e){if(Zs(i))continue;const r=e[i];if(M(r))t[i]=ao(i,r,s);else if(r!=null){const o=Ln(r);t[i]=()=>o}}},ei=(e,t)=>{const n=Ln(t);e.slots.default=()=>n},ti=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},ho=(e,t,n)=>{const s=e.slots=zs();if(e.vnode.shapeFlag&32){const i=t._;i?(ti(s,t,n),n&&ts(s,"_",i,!0)):Qs(t,s)}else t&&ei(e,t)},po=(e,t,n)=>{const{vnode:s,slots:i}=e;let r=!0,o=K;if(s.shapeFlag&32){const l=t._;l?n&&l===1?r=!1:ti(i,t,n):(r=!t.$stable,Qs(t,i)),o=t}else t&&(ei(e,t),o={default:1});if(r)for(const l in i)!Zs(l)&&o[l]==null&&delete i[l]},le=Ao;function go(e){return mo(e)}function mo(e,t){const n=$t();n.__VUE__=!0;const{insert:s,remove:i,patchProp:r,createElement:o,createText:l,createComment:f,setText:d,setElementText:a,parentNode:p,nextSibling:S,setScopeId:E=ge,insertStaticContent:I}=e,R=(c,u,h,_=null,g=null,m=null,v=void 0,x=null,y=!!u.dynamicChildren)=>{if(c===u)return;c&&!Mt(c,u)&&(_=an(c),pe(c,g,m,!0),c=null),u.patchFlag===-2&&(y=!1,u.dynamicChildren=null);const{type:b,ref:O,shapeFlag:w}=u;switch(b){case rn:k(c,u,h,_);break;case Qe:j(c,u,h,_);break;case Un:c==null&&$(u,h,_,v);break;case be:ht(c,u,h,_,g,m,v,x,y);break;default:w&1?G(c,u,h,_,g,m,v,x,y):w&6?Ce(c,u,h,_,g,m,v,x,y):(w&64||w&128)&&b.process(c,u,h,_,g,m,v,x,y,Ft)}O!=null&&g&&Qt(O,c&&c.ref,m,u||c,!u)},k=(c,u,h,_)=>{if(c==null)s(u.el=l(u.children),h,_);else{const g=u.el=c.el;u.children!==c.children&&d(g,u.children)}},j=(c,u,h,_)=>{c==null?s(u.el=f(u.children||""),h,_):u.el=c.el},$=(c,u,h,_)=>{[c.el,c.anchor]=I(c.children,u,h,_,c.el,c.anchor)},W=({el:c,anchor:u},h,_)=>{let g;for(;c&&c!==u;)g=S(c),s(c,h,_),c=g;s(u,h,_)},C=({el:c,anchor:u})=>{let h;for(;c&&c!==u;)h=S(c),i(c),c=h;i(u)},G=(c,u,h,_,g,m,v,x,y)=>{u.type==="svg"?v="svg":u.type==="math"&&(v="mathml"),c==null?ue(u,h,_,g,m,v,x,y):Je(c,u,g,m,v,x,y)},ue=(c,u,h,_,g,m,v,x)=>{let y,b;const{props:O,shapeFlag:w,transition:T,dirs:P}=c;if(y=c.el=o(c.type,m,O&&O.is,O),w&8?a(y,c.children):w&16&&N(c.children,y,null,_,g,$n(c,m),v,x),P&&Xe(c,null,_,"created"),V(y,c,c.scopeId,v,_),O){for(const q in O)q!=="value"&&!gt(q)&&r(y,q,null,O[q],m,_);"value"in O&&r(y,"value",null,O.value,m),(b=O.onVnodeBeforeMount)&&Ee(b,_,c)}P&&Xe(c,null,_,"beforeMount");const D=_o(g,T);D&&T.beforeEnter(y),s(y,u,h),((b=O&&O.onVnodeMounted)||D||P)&&le(()=>{b&&Ee(b,_,c),D&&T.enter(y),P&&Xe(c,null,_,"mounted")},g)},V=(c,u,h,_,g)=>{if(h&&E(c,h),_)for(let m=0;m<_.length;m++)E(c,_[m]);if(g){let m=g.subTree;if(u===m||ui(m.type)&&(m.ssContent===u||m.ssFallback===u)){const v=g.vnode;V(c,v,v.scopeId,v.slotScopeIds,g.parent)}}},N=(c,u,h,_,g,m,v,x,y=0)=>{for(let b=y;b<c.length;b++){const O=c[b]=x?Ke(c[b]):we(c[b]);R(null,O,u,h,_,g,m,v,x)}},Je=(c,u,h,_,g,m,v)=>{const x=u.el=c.el;let{patchFlag:y,dynamicChildren:b,dirs:O}=u;y|=c.patchFlag&16;const w=c.props||K,T=u.props||K;let P;if(h&&Ze(h,!1),(P=T.onVnodeBeforeUpdate)&&Ee(P,h,u,c),O&&Xe(u,c,h,"beforeUpdate"),h&&Ze(h,!0),(w.innerHTML&&T.innerHTML==null||w.textContent&&T.textContent==null)&&a(x,""),b?Te(c.dynamicChildren,b,x,h,_,$n(u,g),m):v||L(c,u,x,null,h,_,$n(u,g),m,!1),y>0){if(y&16)tt(x,w,T,h,g);else if(y&2&&w.class!==T.class&&r(x,"class",null,T.class,g),y&4&&r(x,"style",w.style,T.style,g),y&8){const D=u.dynamicProps;for(let q=0;q<D.length;q++){const U=D[q],ae=w[U],re=T[U];(re!==ae||U==="value")&&r(x,U,ae,re,g,h)}}y&1&&c.children!==u.children&&a(x,u.children)}else!v&&b==null&&tt(x,w,T,h,g);((P=T.onVnodeUpdated)||O)&&le(()=>{P&&Ee(P,h,u,c),O&&Xe(u,c,h,"updated")},_)},Te=(c,u,h,_,g,m,v)=>{for(let x=0;x<u.length;x++){const y=c[x],b=u[x],O=y.el&&(y.type===be||!Mt(y,b)||y.shapeFlag&70)?p(y.el):h;R(y,b,O,null,_,g,m,v,!0)}},tt=(c,u,h,_,g)=>{if(u!==h){if(u!==K)for(const m in u)!gt(m)&&!(m in h)&&r(c,m,u[m],null,g,_);for(const m in h){if(gt(m))continue;const v=h[m],x=u[m];v!==x&&m!=="value"&&r(c,m,x,v,g,_)}"value"in h&&r(c,"value",u.value,h.value,g)}},ht=(c,u,h,_,g,m,v,x,y)=>{const b=u.el=c?c.el:l(""),O=u.anchor=c?c.anchor:l("");let{patchFlag:w,dynamicChildren:T,slotScopeIds:P}=u;P&&(x=x?x.concat(P):P),c==null?(s(b,h,_),s(O,h,_),N(u.children||[],h,O,g,m,v,x,y)):w>0&&w&64&&T&&c.dynamicChildren?(Te(c.dynamicChildren,T,h,g,m,v,x),(u.key!=null||g&&u===g.subTree)&&ni(c,u,!0)):L(c,u,h,O,g,m,v,x,y)},Ce=(c,u,h,_,g,m,v,x,y)=>{u.slotScopeIds=x,c==null?u.shapeFlag&512?g.ctx.activate(u,h,_,v,y):un(u,h,_,g,m,v,y):Dt(c,u,y)},un=(c,u,h,_,g,m,v)=>{const x=c.component=Ho(c,_,g);if(Fs(c)&&(x.ctx.renderer=Ft),Vo(x,!1,v),x.asyncDep){if(g&&g.registerDep(x,X,v),!c.el){const y=x.subTree=fe(Qe);j(null,y,u,h)}}else X(x,c,u,h,g,m,v)},Dt=(c,u,h)=>{const _=u.component=c.component;if(Co(c,u,h))if(_.asyncDep&&!_.asyncResolved){B(_,u,h);return}else _.next=u,_.update();else u.el=c.el,_.vnode=u},X=(c,u,h,_,g,m,v)=>{const x=()=>{if(c.isMounted){let{next:w,bu:T,u:P,parent:D,vnode:q}=c;{const Ae=si(c);if(Ae){w&&(w.el=q.el,B(c,w,v)),Ae.asyncDep.then(()=>{c.isUnmounted||x()});return}}let U=w,ae;Ze(c,!1),w?(w.el=q.el,B(c,w,v)):w=q,T&&mn(T),(ae=w.props&&w.props.onVnodeBeforeUpdate)&&Ee(ae,D,w,q),Ze(c,!0);const re=ci(c),Oe=c.subTree;c.subTree=re,R(Oe,re,p(Oe.el),an(Oe),c,g,m),w.el=re.el,U===null&&Oo(c,re.el),P&&le(P,g),(ae=w.props&&w.props.onVnodeUpdated)&&le(()=>Ee(ae,D,w,q),g)}else{let w;const{el:T,props:P}=u,{bm:D,m:q,parent:U,root:ae,type:re}=c,Oe=Et(u);Ze(c,!1),D&&mn(D),!Oe&&(w=P&&P.onVnodeBeforeMount)&&Ee(w,U,u),Ze(c,!0);{ae.ce&&ae.ce._injectChildStyle(re);const Ae=c.subTree=ci(c);R(null,Ae,h,_,c,g,m),u.el=Ae.el}if(q&&le(q,g),!Oe&&(w=P&&P.onVnodeMounted)){const Ae=u;le(()=>Ee(w,U,Ae),g)}(u.shapeFlag&256||U&&Et(U.vnode)&&U.vnode.shapeFlag&256)&&c.a&&le(c.a,g),c.isMounted=!0,u=h=_=null}};c.scope.on();const y=c.effect=new os(x);c.scope.off();const b=c.update=y.run.bind(y),O=c.job=y.runIfDirty.bind(y);O.i=c,O.id=c.uid,y.scheduler=()=>In(O),Ze(c,!0),b()},B=(c,u,h)=>{u.component=c;const _=c.vnode.props;c.vnode=u,c.next=null,fo(c,u.props,_,h),po(c,u.children,h),Me(),Ms(c),Re()},L=(c,u,h,_,g,m,v,x,y=!1)=>{const b=c&&c.children,O=c?c.shapeFlag:0,w=u.children,{patchFlag:T,shapeFlag:P}=u;if(T>0){if(T&128){pt(b,w,h,_,g,m,v,x,y);return}else if(T&256){ye(b,w,h,_,g,m,v,x,y);return}}P&8?(O&16&&Nt(b,g,m),w!==b&&a(h,w)):O&16?P&16?pt(b,w,h,_,g,m,v,x,y):Nt(b,g,m,!0):(O&8&&a(h,""),P&16&&N(w,h,_,g,m,v,x,y))},ye=(c,u,h,_,g,m,v,x,y)=>{c=c||nt,u=u||nt;const b=c.length,O=u.length,w=Math.min(b,O);let T;for(T=0;T<w;T++){const P=u[T]=y?Ke(u[T]):we(u[T]);R(c[T],P,h,null,g,m,v,x,y)}b>O?Nt(c,g,m,!0,!1,w):N(u,h,_,g,m,v,x,y,w)},pt=(c,u,h,_,g,m,v,x,y)=>{let b=0;const O=u.length;let w=c.length-1,T=O-1;for(;b<=w&&b<=T;){const P=c[b],D=u[b]=y?Ke(u[b]):we(u[b]);if(Mt(P,D))R(P,D,h,null,g,m,v,x,y);else break;b++}for(;b<=w&&b<=T;){const P=c[w],D=u[T]=y?Ke(u[T]):we(u[T]);if(Mt(P,D))R(P,D,h,null,g,m,v,x,y);else break;w--,T--}if(b>w){if(b<=T){const P=T+1,D=P<O?u[P].el:_;for(;b<=T;)R(null,u[b]=y?Ke(u[b]):we(u[b]),h,D,g,m,v,x,y),b++}}else if(b>T)for(;b<=w;)pe(c[b],g,m,!0),b++;else{const P=b,D=b,q=new Map;for(b=D;b<=T;b++){const de=u[b]=y?Ke(u[b]):we(u[b]);de.key!=null&&q.set(de.key,b)}let U,ae=0;const re=T-D+1;let Oe=!1,Ae=0;const jt=new Array(re);for(b=0;b<re;b++)jt[b]=0;for(b=P;b<=w;b++){const de=c[b];if(ae>=re){pe(de,g,m,!0);continue}let Pe;if(de.key!=null)Pe=q.get(de.key);else for(U=D;U<=T;U++)if(jt[U-D]===0&&Mt(de,u[U])){Pe=U;break}Pe===void 0?pe(de,g,m,!0):(jt[Pe-D]=b+1,Pe>=Ae?Ae=Pe:Oe=!0,R(de,u[Pe],h,null,g,m,v,x,y),ae++)}const ji=Oe?bo(jt):nt;for(U=ji.length-1,b=re-1;b>=0;b--){const de=D+b,Pe=u[de],Hi=de+1<O?u[de+1].el:_;jt[b]===0?R(null,Pe,h,Hi,g,m,v,x,y):Oe&&(U<0||b!==ji[U]?je(Pe,h,Hi,2):U--)}}},je=(c,u,h,_,g=null)=>{const{el:m,type:v,transition:x,children:y,shapeFlag:b}=c;if(b&6){je(c.component.subTree,u,h,_);return}if(b&128){c.suspense.move(u,h,_);return}if(b&64){v.move(c,u,h,Ft);return}if(v===be){s(m,u,h);for(let w=0;w<y.length;w++)je(y[w],u,h,_);s(c.anchor,u,h);return}if(v===Un){W(c,u,h);return}if(_!==2&&b&1&&x)if(_===0)x.beforeEnter(m),s(m,u,h),le(()=>x.enter(m),g);else{const{leave:w,delayLeave:T,afterLeave:P}=x,D=()=>s(m,u,h),q=()=>{w(m,()=>{D(),P&&P()})};T?T(m,D,q):q()}else s(m,u,h)},pe=(c,u,h,_=!1,g=!1)=>{const{type:m,props:v,ref:x,children:y,dynamicChildren:b,shapeFlag:O,patchFlag:w,dirs:T,cacheIndex:P}=c;if(w===-2&&(g=!1),x!=null&&Qt(x,null,h,c,!0),P!=null&&(u.renderCache[P]=void 0),O&256){u.ctx.deactivate(c);return}const D=O&1&&T,q=!Et(c);let U;if(q&&(U=v&&v.onVnodeBeforeUnmount)&&Ee(U,u,c),O&6)Fl(c.component,h,_);else{if(O&128){c.suspense.unmount(h,_);return}D&&Xe(c,null,u,"beforeUnmount"),O&64?c.type.remove(c,u,h,Ft,_):b&&!b.hasOnce&&(m!==be||w>0&&w&64)?Nt(b,u,h,!1,!0):(m===be&&w&384||!g&&O&16)&&Nt(y,u,h),_&&Ni(c)}(q&&(U=v&&v.onVnodeUnmounted)||D)&&le(()=>{U&&Ee(U,u,c),D&&Xe(c,null,u,"unmounted")},h)},Ni=c=>{const{type:u,el:h,anchor:_,transition:g}=c;if(u===be){Nl(h,_);return}if(u===Un){C(c);return}const m=()=>{i(h),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(c.shapeFlag&1&&g&&!g.persisted){const{leave:v,delayLeave:x}=g,y=()=>v(h,m);x?x(c.el,m,y):y()}else m()},Nl=(c,u)=>{let h;for(;c!==u;)h=S(c),i(c),c=h;i(u)},Fl=(c,u,h)=>{const{bum:_,scope:g,job:m,subTree:v,um:x,m:y,a:b}=c;ii(y),ii(b),_&&mn(_),g.stop(),m&&(m.flags|=8,pe(v,c,u,h)),x&&le(x,u),le(()=>{c.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},Nt=(c,u,h,_=!1,g=!1,m=0)=>{for(let v=m;v<c.length;v++)pe(c[v],u,h,_,g)},an=c=>{if(c.shapeFlag&6)return an(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const u=S(c.anchor||c.el),h=u&&u[Vr];return h?S(h):u};let Yn=!1;const Fi=(c,u,h)=>{c==null?u._vnode&&pe(u._vnode,null,null,!0):R(u._vnode||null,c,u,null,null,null,h),u._vnode=c,Yn||(Yn=!0,Ms(),Rs(),Yn=!1)},Ft={p:R,um:pe,m:je,r:Ni,mt:un,mc:N,pc:L,pbc:Te,n:an,o:e};return{render:Fi,hydrate:void 0,createApp:oo(Fi)}}function $n({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ze({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function _o(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ni(e,t,n=!1){const s=e.children,i=t.children;if(A(s)&&A(i))for(let r=0;r<s.length;r++){const o=s[r];let l=i[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=i[r]=Ke(i[r]),l.el=o.el),!n&&l.patchFlag!==-2&&ni(o,l)),l.type===rn&&(l.el=o.el)}}function bo(e){const t=e.slice(),n=[0];let s,i,r,o,l;const f=e.length;for(s=0;s<f;s++){const d=e[s];if(d!==0){if(i=n[n.length-1],e[i]<d){t[s]=i,n.push(s);continue}for(r=0,o=n.length-1;r<o;)l=r+o>>1,e[n[l]]<d?r=l+1:o=l;d<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,o=n[r-1];r-- >0;)n[r]=o,o=t[o];return n}function si(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:si(t)}function ii(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const yo=Symbol.for("v-scx"),xo=()=>nn(yo);function Ot(e,t,n){return ri(e,t,n)}function ri(e,t,n=K){const{immediate:s,deep:i,flush:r,once:o}=n,l=te({},n),f=t&&s||!t&&r!=="post";let d;if(It){if(r==="sync"){const E=xo();d=E.__watcherHandles||(E.__watcherHandles=[])}else if(!f){const E=()=>{};return E.stop=ge,E.resume=ge,E.pause=ge,E}}const a=Q;l.call=(E,I,R)=>xe(E,a,I,R);let p=!1;r==="post"?l.scheduler=E=>{le(E,a&&a.suspense)}:r!=="sync"&&(p=!0,l.scheduler=(E,I)=>{I?E():In(E)}),l.augmentJob=E=>{t&&(E.flags|=4),p&&(E.flags|=2,a&&(E.id=a.uid,E.i=a))};const S=Or(e,t,l);return It&&(d?d.push(S):f&&S()),S}function vo(e,t,n){const s=this.proxy,i=Y(e)?e.includes(".")?oi(s,e):()=>s[e]:e.bind(s,s);let r;M(t)?r=t:(r=t.handler,n=t);const o=Rt(this),l=ri(i,r.bind(s),n);return o(),l}function oi(e,t){const n=t.split(".");return()=>{let s=e;for(let i=0;i<n.length&&s;i++)s=s[n[i]];return s}}const So=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Le(t)}Modifiers`]||e[`${ze(t)}Modifiers`];function wo(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||K;let i=n;const r=t.startsWith("update:"),o=r&&So(s,t.slice(7));o&&(o.trim&&(i=n.map(a=>Y(a)?a.trim():a)),o.number&&(i=n.map(Bi)));let l,f=s[l=gn(t)]||s[l=gn(Le(t))];!f&&r&&(f=s[l=gn(ze(t))]),f&&xe(f,e,6,i);const d=s[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,xe(d,e,6,i)}}function li(e,t,n=!1){const s=t.emitsCache,i=s.get(e);if(i!==void 0)return i;const r=e.emits;let o={},l=!1;if(!M(e)){const f=d=>{const a=li(d,t,!0);a&&(l=!0,te(o,a))};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!r&&!l?(z(e)&&s.set(e,null),null):(A(r)?r.forEach(f=>o[f]=null):te(o,r),z(e)&&s.set(e,o),o)}function sn(e,t){return!e||!Ht(t)?!1:(t=t.slice(2).replace(/Once$/,""),H(e,t[0].toLowerCase()+t.slice(1))||H(e,ze(t))||H(e,t))}function Hl(){}function ci(e){const{type:t,vnode:n,proxy:s,withProxy:i,propsOptions:[r],slots:o,attrs:l,emit:f,render:d,renderCache:a,props:p,data:S,setupState:E,ctx:I,inheritAttrs:R}=e,k=Zt(e);let j,$;try{if(n.shapeFlag&4){const C=i||s,G=Ue.NODE_ENV!=="production"&&E.__isScriptSetup?new Proxy(C,{get(ue,V,N){return Ar(`Property '${String(V)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(ue,V,N)}}):C;j=we(d.call(G,C,a,Ue.NODE_ENV!=="production"?qt(p):p,E,S,I)),$=l}else{const C=t;Ue.NODE_ENV,j=we(C.length>1?C(Ue.NODE_ENV!=="production"?qt(p):p,Ue.NODE_ENV!=="production"?{get attrs(){return qt(l)},slots:o,emit:f}:{attrs:l,slots:o,emit:f}):C(Ue.NODE_ENV!=="production"?qt(p):p,null)),$=t.props?l:Eo(l)}}catch(C){At.length=0,kt(C,e,1),j=fe(Qe)}let W=j;if($&&R!==!1){const C=Object.keys($),{shapeFlag:G}=W;C.length&&G&7&&(r&&C.some(dn)&&($=To($,r)),W=dt(W,$,!1,!0))}return n.dirs&&(W=dt(W,null,!1,!0),W.dirs=W.dirs?W.dirs.concat(n.dirs):n.dirs),n.transition&&Dn(W,n.transition),j=W,Zt(k),j}const Eo=e=>{let t;for(const n in e)(n==="class"||n==="style"||Ht(n))&&((t||(t={}))[n]=e[n]);return t},To=(e,t)=>{const n={};for(const s in e)(!dn(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Co(e,t,n){const{props:s,children:i,component:r}=e,{props:o,children:l,patchFlag:f}=t,d=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&f>=0){if(f&1024)return!0;if(f&16)return s?fi(s,o,d):!!o;if(f&8){const a=t.dynamicProps;for(let p=0;p<a.length;p++){const S=a[p];if(o[S]!==s[S]&&!sn(d,S))return!0}}}else return(i||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?fi(s,o,d):!0:!!o;return!1}function fi(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let i=0;i<s.length;i++){const r=s[i];if(t[r]!==e[r]&&!sn(n,r))return!0}return!1}function Oo({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const ui=e=>e.__isSuspense;function Ao(e,t){t&&t.pendingBranch?A(e)?t.effects.push(...e):t.effects.push(e):jr(e)}const be=Symbol.for("v-fgt"),rn=Symbol.for("v-txt"),Qe=Symbol.for("v-cmt"),Un=Symbol.for("v-stc"),At=[];let ce=null;function Be(e=!1){At.push(ce=e?null:[])}function Po(){At.pop(),ce=At[At.length-1]||null}let Pt=1;function ai(e,t=!1){Pt+=e,e<0&&ce&&t&&(ce.hasOnce=!0)}function di(e){return e.dynamicChildren=Pt>0?ce||nt:null,Po(),Pt>0&&ce&&ce.push(e),e}function et(e,t,n,s,i,r){return di(he(e,t,n,s,i,r,!0))}function Mo(e,t,n,s,i){return di(fe(e,t,n,s,i,!0))}function on(e){return e?e.__v_isVNode===!0:!1}function Mt(e,t){return e.type===t.type&&e.key===t.key}const hi=({key:e})=>e??null,ln=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Y(e)||Z(e)||M(e)?{i:Se,r:e,k:t,f:!!n}:e:null);function he(e,t=null,n=null,s=0,i=null,r=e===be?0:1,o=!1,l=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&hi(t),ref:t&&ln(t),scopeId:Ds,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Se};return l?(Wn(f,n),r&128&&e.normalize(f)):n&&(f.shapeFlag|=Y(n)?8:16),Pt>0&&!o&&ce&&(f.patchFlag>0||r&6)&&f.patchFlag!==32&&ce.push(f),f}const fe=Ro;function Ro(e,t=null,n=null,s=0,i=null,r=!1){if((!e||e===Zr)&&(e=Qe),on(e)){const l=dt(e,t,!0);return n&&Wn(l,n),Pt>0&&!r&&ce&&(l.shapeFlag&6?ce[ce.indexOf(e)]=l:ce.push(l)),l.patchFlag=-2,l}if(Jo(e)&&(e=e.__vccOpts),t){t=Io(t);let{class:l,style:f}=t;l&&!Y(l)&&(t.class=bn(l)),z(f)&&(Pn(f)&&!A(f)&&(f=te({},f)),t.style=_n(f))}const o=Y(e)?1:ui(e)?128:Lr(e)?64:z(e)?4:M(e)?2:0;return he(e,t,n,s,i,o,r,!0)}function Io(e){return e?Pn(e)||Gs(e)?te({},e):e:null}function dt(e,t,n=!1,s=!1){const{props:i,ref:r,patchFlag:o,children:l,transition:f}=e,d=t?No(i||{},t):i,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&hi(d),ref:t&&t.ref?n&&r?A(r)?r.concat(ln(t)):[r,ln(t)]:ln(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==be?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&dt(e.ssContent),ssFallback:e.ssFallback&&dt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&s&&Dn(a,f.clone(a)),a}function Do(e=" ",t=0){return fe(rn,null,e,t)}function pi(e="",t=!1){return t?(Be(),Mo(Qe,null,e)):fe(Qe,null,e)}function we(e){return e==null||typeof e=="boolean"?fe(Qe):A(e)?fe(be,null,e.slice()):on(e)?Ke(e):fe(rn,null,String(e))}function Ke(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:dt(e)}function Wn(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(A(t))n=16;else if(typeof t=="object")if(s&65){const i=t.default;i&&(i._c&&(i._d=!1),Wn(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!Gs(t)?t._ctx=Se:i===3&&Se&&(Se.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else M(t)?(t={default:t,_ctx:Se},n=32):(t=String(t),s&64?(n=16,t=[Do(t)]):n=8);e.children=t,e.shapeFlag|=n}function No(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const i in s)if(i==="class")t.class!==s.class&&(t.class=bn([t.class,s.class]));else if(i==="style")t.style=_n([t.style,s.style]);else if(Ht(i)){const r=t[i],o=s[i];o&&r!==o&&!(A(r)&&r.includes(o))&&(t[i]=r?[].concat(r,o):o)}else i!==""&&(t[i]=s[i])}return t}function Ee(e,t,n,s=null){xe(e,t,7,[n,s])}const Fo=Js();let jo=0;function Ho(e,t,n){const s=e.type,i=(t?t.appContext:e.appContext)||Fo,r={uid:jo++,vnode:e,type:s,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ki(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ks(s,i),emitsOptions:li(s,i),emit:null,emitted:null,propsDefaults:K,inheritAttrs:s.inheritAttrs,ctx:K,data:K,props:K,attrs:K,slots:K,refs:K,setupState:K,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=wo.bind(null,r),e.ce&&e.ce(r),r}let Q=null,cn,Bn;{const e=$t(),t=(n,s)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(s),r=>{i.length>1?i.forEach(o=>o(r)):i[0](r)}};cn=t("__VUE_INSTANCE_SETTERS__",n=>Q=n),Bn=t("__VUE_SSR_SETTERS__",n=>It=n)}const Rt=e=>{const t=Q;return cn(e),e.scope.on(),()=>{e.scope.off(),cn(t)}},gi=()=>{Q&&Q.scope.off(),cn(null)};function mi(e){return e.vnode.shapeFlag&4}let It=!1;function Vo(e,t=!1,n=!1){t&&Bn(t);const{props:s,children:i}=e.vnode,r=mi(e);co(e,s,r,t),ho(e,i,n);const o=r?Lo(e,t):void 0;return t&&Bn(!1),o}function Lo(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Qr);const{setup:s}=n;if(s){Me();const i=e.setupContext=s.length>1?Uo(e):null,r=Rt(e),o=ct(s,e,0,[e.props,i]),l=Xn(o);if(Re(),r(),(l||e.sp)&&!Et(e)&&Ns(e),l){if(o.then(gi,gi),t)return o.then(f=>{_i(e,f)}).catch(f=>{kt(f,e,0)});e.asyncDep=o}else _i(e,o)}else bi(e)}function _i(e,t,n){M(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:z(t)&&(e.setupState=Cs(t)),bi(e)}function bi(e,t,n){const s=e.type;e.render||(e.render=s.render||ge);{const i=Rt(e);Me();try{eo(e)}finally{Re(),i()}}}const $o={get(e,t){return ee(e,"get",""),e[t]}};function Uo(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,$o),slots:e.slots,emit:e.emit,expose:t}}function Kn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Cs(br(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Tt)return Tt[n](e)},has(t,n){return n in t||n in Tt}})):e.proxy}const Wo=/(?:^|[-_])(\w)/g,Bo=e=>e.replace(Wo,t=>t.toUpperCase()).replace(/[-_]/g,"");function Ko(e,t=!0){return M(e)?e.displayName||e.name:e.name||t&&e.__name}function yi(e,t,n=!1){let s=Ko(t);if(!s&&t.__file){const i=t.__file.match(/([^/\\]+)\.\w+$/);i&&(s=i[1])}if(!s&&e&&e.parent){const i=r=>{for(const o in r)if(r[o]===t)return o};s=i(e.components||e.parent.type.components)||i(e.appContext.components)}return s?Bo(s):n?"App":"Anonymous"}function Jo(e){return M(e)&&"__vccOpts"in e}const qo=(e,t)=>Tr(e,t,It);function zo(e,t,n){const s=arguments.length;return s===2?z(t)&&!A(t)?on(t)?fe(e,null,[t]):fe(e,t):fe(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&on(n)&&(n=[n]),fe(e,t,n))}const Go="3.5.13";let Jn;const xi=typeof window<"u"&&window.trustedTypes;if(xi)try{Jn=xi.createPolicy("vue",{createHTML:e=>e})}catch{}const vi=Jn?e=>Jn.createHTML(e):e=>e,Yo="http://www.w3.org/2000/svg",ko="http://www.w3.org/1998/Math/MathML",Fe=typeof document<"u"?document:null,Si=Fe&&Fe.createElement("template"),Xo={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const i=t==="svg"?Fe.createElementNS(Yo,e):t==="mathml"?Fe.createElementNS(ko,e):n?Fe.createElement(e,{is:n}):Fe.createElement(e);return e==="select"&&s&&s.multiple!=null&&i.setAttribute("multiple",s.multiple),i},createText:e=>Fe.createTextNode(e),createComment:e=>Fe.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Fe.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,i,r){const o=n?n.previousSibling:t.lastChild;if(i&&(i===r||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===r||!(i=i.nextSibling)););else{Si.innerHTML=vi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Si.content;if(s==="svg"||s==="mathml"){const f=l.firstChild;for(;f.firstChild;)l.appendChild(f.firstChild);l.removeChild(f)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Zo=Symbol("_vtc");function Qo(e,t,n){const s=e[Zo];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const wi=Symbol("_vod"),el=Symbol("_vsh"),tl=Symbol(""),nl=/(^|;)\s*display\s*:/;function sl(e,t,n){const s=e.style,i=Y(n);let r=!1;if(n&&!i){if(t)if(Y(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&fn(s,l,"")}else for(const o in t)n[o]==null&&fn(s,o,"");for(const o in n)o==="display"&&(r=!0),fn(s,o,n[o])}else if(i){if(t!==n){const o=s[tl];o&&(n+=";"+o),s.cssText=n,r=nl.test(n)}}else t&&e.removeAttribute("style");wi in e&&(e[wi]=r?s.display:"",e[el]&&(s.display="none"))}const Ei=/\s*!important$/;function fn(e,t,n){if(A(n))n.forEach(s=>fn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=il(e,t);Ei.test(n)?e.setProperty(ze(s),n.replace(Ei,""),"important"):e[s]=n}}const Ti=["Webkit","Moz","ms"],qn={};function il(e,t){const n=qn[t];if(n)return n;let s=Le(t);if(s!=="filter"&&s in e)return qn[t]=s;s=es(s);for(let i=0;i<Ti.length;i++){const r=Ti[i]+s;if(r in e)return qn[t]=r}return t}const Ci="http://www.w3.org/1999/xlink";function Oi(e,t,n,s,i,r=Gi(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Ci,t.slice(6,t.length)):e.setAttributeNS(Ci,t,n):n==null||r&&!ss(n)?e.removeAttribute(t):e.setAttribute(t,r?"":Ve(n)?String(n):n)}function Ai(e,t,n,s,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?vi(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,f=n==null?e.type==="checkbox"?"on":"":String(n);(l!==f||!("_value"in e))&&(e.value=f),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ss(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(i||t)}function rl(e,t,n,s){e.addEventListener(t,n,s)}function ol(e,t,n,s){e.removeEventListener(t,n,s)}const Pi=Symbol("_vei");function ll(e,t,n,s,i=null){const r=e[Pi]||(e[Pi]={}),o=r[t];if(s&&o)o.value=s;else{const[l,f]=cl(t);if(s){const d=r[t]=al(s,i);rl(e,l,d,f)}else o&&(ol(e,l,o,f),r[t]=void 0)}}const Mi=/(?:Once|Passive|Capture)$/;function cl(e){let t;if(Mi.test(e)){t={};let s;for(;s=e.match(Mi);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ze(e.slice(2)),t]}let zn=0;const fl=Promise.resolve(),ul=()=>zn||(fl.then(()=>zn=0),zn=Date.now());function al(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;xe(dl(s,n.value),t,5,[s])};return n.value=e,n.attached=ul(),n}function dl(e,t){if(A(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>i=>!i._stopped&&s&&s(i))}else return t}const Ri=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,hl=(e,t,n,s,i,r)=>{const o=i==="svg";t==="class"?Qo(e,s,o):t==="style"?sl(e,n,s):Ht(t)?dn(t)||ll(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):pl(e,t,s,o))?(Ai(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Oi(e,t,s,o,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Y(s))?Ai(e,Le(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Oi(e,t,s,o))};function pl(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ri(t)&&M(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Ri(t)&&Y(n)?!1:t in e}const gl=te({patchProp:hl},Xo);let Ii;function ml(){return Ii||(Ii=go(gl))}const _l=(...e)=>{const t=ml().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=yl(s);if(!i)return;const r=t._component;!M(r)&&!r.render&&!r.template&&(r.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const o=n(i,!1,bl(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),o},t};function bl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function yl(e){return Y(e)?document.querySelector(e):e}const xl=(e,t)=>{const n=e.__vccOpts||e;for(const[s,i]of t)n[s]=i;return n};function Di(e){const t=[],n=/{([^{}]*|{([^{}]*|{[^{}]*})*})*}/g;let s;for(;(s=n.exec(e))!==null;)try{t.push(JSON.parse(s[0]))}catch{console.warn("无效的 JSON 片段",s[0])}return t.length>0?t:null}const vl={name:"rtf",props:{vueState:{type:Object,required:!0},data:{type:Object,required:!0}},data(){return{allText:""}},setup(e){const t=vt({isapplystep:!1,dataid:"",amid:"",ischeckinventory:!1,sparepartsinventory:[],sparepartsinfo:[]}),n=(r,o)=>{const l=window.top.AssistantIntgSDK||{},f=e.vueState.data.allText,d=e.vueState.data.dataid,a=atob(f),p=atob(d),S=new Uint8Array(p.length);for(let V=0;V<p.length;V++)S[V]=p.charCodeAt(V);const E=new TextDecoder("utf-8").decode(S);t.dataid=E;const I=e.vueState.data.amid,R=atob(I),k=new Uint8Array(R.length);for(let V=0;V<R.length;V++)k[V]=R.charCodeAt(V);const j=new TextDecoder("utf-8").decode(k);t.amid=j;const $=new Uint8Array(a.length);for(let V=0;V<a.length;V++)$[V]=a.charCodeAt(V);const W=new TextDecoder("utf-8").decode($),C=Di(W),G=r.data.type,ue=l.llCreateMessage("repairsparepartwrite",{content:{content:C,dataid:E,type:G}});l.llSendMessage(ue)},s=async(r,o)=>{t.ischeckinventory=!0,t.sparepartsinventory=[],t.sparepartsinfo=[];const l=e.vueState.data.allText,f=e.vueState.data.dataid,d=atob(l),a=atob(f),p=new Uint8Array(a.length);for(let N=0;N<a.length;N++)p[N]=a.charCodeAt(N);const S=new TextDecoder("utf-8").decode(p);t.dataid=S;const E=e.vueState.data.amorgid,I=atob(E),R=new Uint8Array(I.length);for(let N=0;N<I.length;N++)R[N]=I.charCodeAt(N);const k=new TextDecoder("utf-8").decode(R);t.amorgid=k;const j=e.vueState.data.amid,$=atob(j),W=new Uint8Array($.length);for(let N=0;N<$.length;N++)W[N]=$.charCodeAt(N);const C=new TextDecoder("utf-8").decode(W);t.amid=C;const G=new Uint8Array(d.length);for(let N=0;N<d.length;N++)G[N]=d.charCodeAt(N);const ue=new TextDecoder("utf-8").decode(G),V=Di(ue);for(let N=0;N<V.length;N++){const Je=V[N],Te=Je.id,tt=Je.sparepartname,ht=Je.sparepartcode,Ce={};Ce.id=Te,Ce.name_chs=tt,Ce.code=ht;const Dt=await(await fetch("/api/eam/eampm/v1.0/sp/kccs/inventory/getsparepartinv",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({parm1:t.amorgid,parm2:Te})})).json();if(Dt.resultType=="SUCCESS"){const X=Dt.appendData,B=[];for(let L=0;L<X.length;L++){const ye=X[L],pt=ye.批号,je=ye.当前数量,pe=ye.资产位置;B.push({serialnumber:pt,inventory:je,ampos:pe})}B.length>0&&(Ce.sparepartinv=B,t.sparepartsinfo.push(Ce))}}},i=()=>{const r={},o={},l={},f="我需要查看备件信息",d=window.top.AssistantIntgSDK||{},a={message:{text:f,files:[]},contextValues:{framework:r,user:o,capture:l},target:""};d.sendCommandViaCUI(a)};return{...vr(t),apply:n,sendmessagetosparepart:i,checkinventory:s}}},Sl={class:"menu-button-widget"},wl={key:0},El={style:{display:"flex",gap:"14px",border:"1px solid black","background-image":"linear-gradient(135deg, #fdf2ff 0%, #fbefff 100%)"}},Tl={style:{display:"flex",fontSize:"14px",flexDirection:"column",border:"1px solid grey",padding:"2px 2px 2px 10px",gap:"5px"}};function Cl(e,t,n,s,i,r){return Be(),et("div",Sl,[e.ischeckinventory?pi("",!0):(Be(),et("button",{key:0,class:"menu-button-widget-button",style:{padding:"5px 10px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},onClick:t[0]||(t[0]=o=>s.checkinventory(n.vueState,o))}," 查看备件库存 ")),he("div",null,[e.ischeckinventory?(Be(),et("div",wl,[t[2]||(t[2]=he("div",null,null,-1)),(Be(!0),et(be,null,Vs(e.sparepartsinfo,(o,l)=>(Be(),et("div",{key:l},[he("div",El,[he("span",null,"备件名称："+it(o.name_chs),1),he("span",null,"备件编号："+it(o.code),1)]),(Be(!0),et(be,null,Vs(o.sparepartinv,(f,d)=>(Be(),et("div",{key:d},[he("div",Tl,[he("span",null,"资产位置: "+it(f.ampos),1),he("span",null,"批号："+it(f.serialnumber),1),he("span",null,"备件数量："+it(f.inventory),1)])]))),128))]))),128)),he("button",{class:"menu-button-widget-button",style:{padding:"10px 10px 10px 10px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},onClick:t[1]||(t[1]=o=>s.apply(n.vueState,o))}," 应用备件 ")])):pi("",!0)])])}const Ol=xl(vl,[["render",Cl],["__scopeId","data-v-25838123"]]);function Al(e){return JSON.parse(JSON.stringify(e))}class Pl{async initialize(){console.log("ExampleWidgetAPI initialized.")}async cleanup(){console.log("ExampleWidgetAPI cleanup.")}createWidget(){return new Ml}}class Ml{constructor(){this.vueState_=vt({options:{mode:"full",implOptions:{data:{}}},data:{}}),this.isMounted_=!1;const t=this.vueState_;this.vueApp_=_l({setup(){const n=t;return console.log("constuct"),Ot(()=>n.options,s=>{console.log("ExampleWidget options updated.",s)}),Ot(()=>n.data,s=>{console.log("ExampleWidget data updated.",s)}),{state:n}},render(){return console.log("render data",this.state.data),zo(Ol,{vueState:t})}})}namespace(){return"sys"}name(){return"rtf"}options(){return this.vueState_.options}updateOptions(t){this.vueState_.options={...this.options(),...Al(t)}}data(){return console.log(this.options().implOptions.data),this.vueState_.data}updateData(t){console.log("ExampleWidget data updating.",t),this.vueState_.data=t,console.log("ExampleWidget data updated.",this.vueState_)}async mount(t){if(this.isMounted_)throw new Error("ExampleWidget already mounted.");this.vueApp_.mount(t),this.isMounted_=!0}async unmount(){if(!this.isMounted_)throw new Error("ExampleWidget is NOT mounted.");this.vueApp_.unmount(),this.isMounted_=!1}async rerender(){}async dispose(){this.isMounted_&&this.unmount()}addEventListener(t){return()=>{}}}const Gn=new Pl;function Rl(){return Gn.initialize()}function Il(){return Gn.cleanup()}function Dl(){return Gn.createWidget()}He.cleanup=Il,He.createWidget=Dl,He.initialize=Rl,Object.defineProperty(He,Symbol.toStringTag,{value:"Module"})});
