(function(we,ye){typeof exports=="object"&&typeof module<"u"?ye(exports):typeof define=="function"&&define.amd?define(["exports"],ye):(we=typeof globalThis<"u"?globalThis:we||self,ye(we.RTFWidget={}))})(this,function(we){"use strict";var ha=Object.defineProperty;var pa=(we,ye,Z)=>ye in we?ha(we,ye,{enumerable:!0,configurable:!0,writable:!0,value:Z}):we[ye]=Z;var Dr=(we,ye,Z)=>pa(we,typeof ye!="symbol"?ye+"":ye,Z);/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ye(e){const t=Object.create(null);for(const u of e.split(","))t[u]=1;return u=>u in t}const Z={},Rt=[],He=()=>{},Fi=()=>!1,xu=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),dr=e=>e.startsWith("onUpdate:"),Be=Object.assign,hr=(e,t)=>{const u=e.indexOf(t);u>-1&&e.splice(u,1)},Ei=Object.prototype.hasOwnProperty,ue=(e,t)=>Ei.call(e,t),L=Array.isArray,$t=e=>wu(e)==="[object Map]",fn=e=>wu(e)==="[object Set]",W=e=>typeof e=="function",de=e=>typeof e=="string",Ct=e=>typeof e=="symbol",De=e=>e!==null&&typeof e=="object",Dn=e=>(De(e)||W(e))&&W(e.then)&&W(e.catch),dn=Object.prototype.toString,wu=e=>dn.call(e),gi=e=>wu(e).slice(8,-1),hn=e=>wu(e)==="[object Object]",pr=e=>de(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Qt=ye(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Su=e=>{const t=Object.create(null);return u=>t[u]||(t[u]=e(u))},mi=/-(\w)/g,At=Su(e=>e.replace(mi,(t,u)=>u?u.toUpperCase():"")),bi=/\B([A-Z])/g,vt=Su(e=>e.replace(bi,"-$1").toLowerCase()),pn=Su(e=>e.charAt(0).toUpperCase()+e.slice(1)),Cr=Su(e=>e?`on${pn(e)}`:""),_t=(e,t)=>!Object.is(e,t),Ar=(e,...t)=>{for(let u=0;u<e.length;u++)e[u](...t)},Cn=(e,t,u,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:u})},yi=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let An;const Ou=()=>An||(An=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Fr(e){if(L(e)){const t={};for(let u=0;u<e.length;u++){const r=e[u],n=de(r)?xi(r):Fr(r);if(n)for(const s in n)t[s]=n[s]}return t}else if(de(e)||De(e))return e}const Bi=/;(?![^(]*\))/g,vi=/:([^]+)/,_i=/\/\*[^]*?\*\//g;function xi(e){const t={};return e.replace(_i,"").split(Bi).forEach(u=>{if(u){const r=u.split(vi);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Er(e){let t="";if(de(e))t=e;else if(L(e))for(let u=0;u<e.length;u++){const r=Er(e[u]);r&&(t+=r+" ")}else if(De(e))for(const u in e)e[u]&&(t+=u+" ");return t.trim()}const wi=ye("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Fn(e){return!!e||e===""}const En=e=>!!(e&&e.__v_isRef===!0),Mt=e=>de(e)?e:e==null?"":L(e)||De(e)&&(e.toString===dn||!W(e.toString))?En(e)?Mt(e.value):JSON.stringify(e,gn,2):String(e),gn=(e,t)=>En(t)?gn(e,t.value):$t(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((u,[r,n],s)=>(u[gr(r,s)+" =>"]=n,u),{})}:fn(t)?{[`Set(${t.size})`]:[...t.values()].map(u=>gr(u))}:Ct(t)?gr(t):De(t)&&!L(t)&&!hn(t)?String(t):t,gr=(e,t="")=>{var u;return Ct(e)?`Symbol(${(u=e.description)!=null?u:t})`:e};var Si={NODE_ENV:"production"};let Pe;class Oi{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Pe,!t&&Pe&&(this.index=(Pe.scopes||(Pe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,u;if(this.scopes)for(t=0,u=this.scopes.length;t<u;t++)this.scopes[t].pause();for(t=0,u=this.effects.length;t<u;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,u;if(this.scopes)for(t=0,u=this.scopes.length;t<u;t++)this.scopes[t].resume();for(t=0,u=this.effects.length;t<u;t++)this.effects[t].resume()}}run(t){if(this._active){const u=Pe;try{return Pe=this,t()}finally{Pe=u}}}on(){Pe=this}off(){Pe=this.parent}stop(t){if(this._active){this._active=!1;let u,r;for(u=0,r=this.effects.length;u<r;u++)this.effects[u].stop();for(this.effects.length=0,u=0,r=this.cleanups.length;u<r;u++)this.cleanups[u]();if(this.cleanups.length=0,this.scopes){for(u=0,r=this.scopes.length;u<r;u++)this.scopes[u].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function Ti(){return Pe}let ie;const mr=new WeakSet;class mn{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Pe&&Pe.active&&Pe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,mr.has(this)&&(mr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||yn(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,wn(this),Bn(this);const t=ie,u=We;ie=this,We=!0;try{return this.fn()}finally{vn(this),ie=t,We=u,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)vr(t);this.deps=this.depsTail=void 0,wn(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?mr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Br(this)&&this.run()}get dirty(){return Br(this)}}let bn=0,Xt,eu;function yn(e,t=!1){if(e.flags|=8,t){e.next=eu,eu=e;return}e.next=Xt,Xt=e}function br(){bn++}function yr(){if(--bn>0)return;if(eu){let t=eu;for(eu=void 0;t;){const u=t.next;t.next=void 0,t.flags&=-9,t=u}}let e;for(;Xt;){let t=Xt;for(Xt=void 0;t;){const u=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=u}}if(e)throw e}function Bn(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function vn(e){let t,u=e.depsTail,r=u;for(;r;){const n=r.prevDep;r.version===-1?(r===u&&(u=n),vr(r),Pi(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=n}e.deps=t,e.depsTail=u}function Br(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(_n(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function _n(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===tu))return;e.globalVersion=tu;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Br(e)){e.flags&=-3;return}const u=ie,r=We;ie=e,We=!0;try{Bn(e);const n=e.fn(e._value);(t.version===0||_t(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{ie=u,We=r,vn(e),e.flags&=-3}}function vr(e,t=!1){const{dep:u,prevSub:r,nextSub:n}=e;if(r&&(r.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=r,e.nextSub=void 0),u.subs===e&&(u.subs=r,!r&&u.computed)){u.computed.flags&=-5;for(let s=u.computed.deps;s;s=s.nextDep)vr(s,!0)}!t&&!--u.sc&&u.map&&u.map.delete(u.key)}function Pi(e){const{prevDep:t,nextDep:u}=e;t&&(t.nextDep=u,e.prevDep=void 0),u&&(u.prevDep=t,e.nextDep=void 0)}let We=!0;const xn=[];function rt(){xn.push(We),We=!1}function nt(){const e=xn.pop();We=e===void 0?!0:e}function wn(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const u=ie;ie=void 0;try{t()}finally{ie=u}}}let tu=0;class Ni{constructor(t,u){this.sub=t,this.dep=u,this.version=u.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Sn{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ie||!We||ie===this.computed)return;let u=this.activeLink;if(u===void 0||u.sub!==ie)u=this.activeLink=new Ni(ie,this),ie.deps?(u.prevDep=ie.depsTail,ie.depsTail.nextDep=u,ie.depsTail=u):ie.deps=ie.depsTail=u,On(u);else if(u.version===-1&&(u.version=this.version,u.nextDep)){const r=u.nextDep;r.prevDep=u.prevDep,u.prevDep&&(u.prevDep.nextDep=r),u.prevDep=ie.depsTail,u.nextDep=void 0,ie.depsTail.nextDep=u,ie.depsTail=u,ie.deps===u&&(ie.deps=r)}return u}trigger(t){this.version++,tu++,this.notify(t)}notify(t){br();try{Si.NODE_ENV;for(let u=this.subs;u;u=u.prevSub)u.sub.notify()&&u.sub.dep.notify()}finally{yr()}}}function On(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)On(r)}const u=e.dep.subs;u!==e&&(e.prevSub=u,u&&(u.nextSub=e)),e.dep.subs=e}}const Tu=new WeakMap,xt=Symbol(""),_r=Symbol(""),uu=Symbol("");function me(e,t,u){if(We&&ie){let r=Tu.get(e);r||Tu.set(e,r=new Map);let n=r.get(u);n||(r.set(u,n=new Sn),n.map=r,n.key=u),n.track()}}function st(e,t,u,r,n,s){const i=Tu.get(e);if(!i){tu++;return}const o=c=>{c&&c.trigger()};if(br(),t==="clear")i.forEach(o);else{const c=L(e),D=c&&pr(u);if(c&&u==="length"){const a=Number(r);i.forEach((d,C)=>{(C==="length"||C===uu||!Ct(C)&&C>=a)&&o(d)})}else switch((u!==void 0||i.has(void 0))&&o(i.get(u)),D&&o(i.get(uu)),t){case"add":c?D&&o(i.get("length")):(o(i.get(xt)),$t(e)&&o(i.get(_r)));break;case"delete":c||(o(i.get(xt)),$t(e)&&o(i.get(_r)));break;case"set":$t(e)&&o(i.get(xt));break}}yr()}function Ii(e,t){const u=Tu.get(e);return u&&u.get(t)}function jt(e){const t=Q(e);return t===e?t:(me(t,"iterate",uu),ke(e)?t:t.map(Se))}function Pu(e){return me(e=Q(e),"iterate",uu),e}const Ri={__proto__:null,[Symbol.iterator](){return xr(this,Symbol.iterator,Se)},concat(...e){return jt(this).concat(...e.map(t=>L(t)?jt(t):t))},entries(){return xr(this,"entries",e=>(e[1]=Se(e[1]),e))},every(e,t){return it(this,"every",e,t,void 0,arguments)},filter(e,t){return it(this,"filter",e,t,u=>u.map(Se),arguments)},find(e,t){return it(this,"find",e,t,Se,arguments)},findIndex(e,t){return it(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return it(this,"findLast",e,t,Se,arguments)},findLastIndex(e,t){return it(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return it(this,"forEach",e,t,void 0,arguments)},includes(...e){return wr(this,"includes",e)},indexOf(...e){return wr(this,"indexOf",e)},join(e){return jt(this).join(e)},lastIndexOf(...e){return wr(this,"lastIndexOf",e)},map(e,t){return it(this,"map",e,t,void 0,arguments)},pop(){return ru(this,"pop")},push(...e){return ru(this,"push",e)},reduce(e,...t){return Tn(this,"reduce",e,t)},reduceRight(e,...t){return Tn(this,"reduceRight",e,t)},shift(){return ru(this,"shift")},some(e,t){return it(this,"some",e,t,void 0,arguments)},splice(...e){return ru(this,"splice",e)},toReversed(){return jt(this).toReversed()},toSorted(e){return jt(this).toSorted(e)},toSpliced(...e){return jt(this).toSpliced(...e)},unshift(...e){return ru(this,"unshift",e)},values(){return xr(this,"values",Se)}};function xr(e,t,u){const r=Pu(e),n=r[t]();return r!==e&&!ke(e)&&(n._next=n.next,n.next=()=>{const s=n._next();return s.value&&(s.value=u(s.value)),s}),n}const $i=Array.prototype;function it(e,t,u,r,n,s){const i=Pu(e),o=i!==e&&!ke(e),c=i[t];if(c!==$i[t]){const d=c.apply(e,s);return o?Se(d):d}let D=u;i!==e&&(o?D=function(d,C){return u.call(this,Se(d),C,e)}:u.length>2&&(D=function(d,C){return u.call(this,d,C,e)}));const a=c.call(i,D,r);return o&&n?n(a):a}function Tn(e,t,u,r){const n=Pu(e);let s=u;return n!==e&&(ke(e)?u.length>3&&(s=function(i,o,c){return u.call(this,i,o,c,e)}):s=function(i,o,c){return u.call(this,i,Se(o),c,e)}),n[t](s,...r)}function wr(e,t,u){const r=Q(e);me(r,"iterate",uu);const n=r[t](...u);return(n===-1||n===!1)&&Or(u[0])?(u[0]=Q(u[0]),r[t](...u)):n}function ru(e,t,u=[]){rt(),br();const r=Q(e)[t].apply(e,u);return yr(),nt(),r}const Mi=ye("__proto__,__v_isRef,__isVue"),Pn=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ct));function ji(e){Ct(e)||(e=String(e));const t=Q(this);return me(t,"has",e),t.hasOwnProperty(e)}class Nn{constructor(t=!1,u=!1){this._isReadonly=t,this._isShallow=u}get(t,u,r){if(u==="__v_skip")return t.__v_skip;const n=this._isReadonly,s=this._isShallow;if(u==="__v_isReactive")return!n;if(u==="__v_isReadonly")return n;if(u==="__v_isShallow")return s;if(u==="__v_raw")return r===(n?s?Ln:jn:s?Mn:$n).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=L(t);if(!n){let c;if(i&&(c=Ri[u]))return c;if(u==="hasOwnProperty")return ji}const o=Reflect.get(t,u,Ae(t)?t:r);return(Ct(u)?Pn.has(u):Mi(u))||(n||me(t,"get",u),s)?o:Ae(o)?i&&pr(u)?o:o.value:De(o)?n?Vn(o):nu(o):o}}class In extends Nn{constructor(t=!1){super(!1,t)}set(t,u,r,n){let s=t[u];if(!this._isShallow){const c=Vt(s);if(!ke(r)&&!Vt(r)&&(s=Q(s),r=Q(r)),!L(t)&&Ae(s)&&!Ae(r))return c?!1:(s.value=r,!0)}const i=L(t)&&pr(u)?Number(u)<t.length:ue(t,u),o=Reflect.set(t,u,r,Ae(t)?t:n);return t===Q(n)&&(i?_t(r,s)&&st(t,"set",u,r):st(t,"add",u,r)),o}deleteProperty(t,u){const r=ue(t,u);t[u];const n=Reflect.deleteProperty(t,u);return n&&r&&st(t,"delete",u,void 0),n}has(t,u){const r=Reflect.has(t,u);return(!Ct(u)||!Pn.has(u))&&me(t,"has",u),r}ownKeys(t){return me(t,"iterate",L(t)?"length":xt),Reflect.ownKeys(t)}}class Rn extends Nn{constructor(t=!1){super(!0,t)}set(t,u){return!0}deleteProperty(t,u){return!0}}const Li=new In,Vi=new Rn,Hi=new In(!0),Wi=new Rn(!0),Sr=e=>e,Nu=e=>Reflect.getPrototypeOf(e);function ki(e,t,u){return function(...r){const n=this.__v_raw,s=Q(n),i=$t(s),o=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,D=n[e](...r),a=u?Sr:t?Tr:Se;return!t&&me(s,"iterate",c?_r:xt),{next(){const{value:d,done:C}=D.next();return C?{value:d,done:C}:{value:o?[a(d[0]),a(d[1])]:a(d),done:C}},[Symbol.iterator](){return this}}}}function Iu(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Ui(e,t){const u={get(n){const s=this.__v_raw,i=Q(s),o=Q(n);e||(_t(n,o)&&me(i,"get",n),me(i,"get",o));const{has:c}=Nu(i),D=t?Sr:e?Tr:Se;if(c.call(i,n))return D(s.get(n));if(c.call(i,o))return D(s.get(o));s!==i&&s.get(n)},get size(){const n=this.__v_raw;return!e&&me(Q(n),"iterate",xt),Reflect.get(n,"size",n)},has(n){const s=this.__v_raw,i=Q(s),o=Q(n);return e||(_t(n,o)&&me(i,"has",n),me(i,"has",o)),n===o?s.has(n):s.has(n)||s.has(o)},forEach(n,s){const i=this,o=i.__v_raw,c=Q(o),D=t?Sr:e?Tr:Se;return!e&&me(c,"iterate",xt),o.forEach((a,d)=>n.call(s,D(a),D(d),i))}};return Be(u,e?{add:Iu("add"),set:Iu("set"),delete:Iu("delete"),clear:Iu("clear")}:{add(n){!t&&!ke(n)&&!Vt(n)&&(n=Q(n));const s=Q(this);return Nu(s).has.call(s,n)||(s.add(n),st(s,"add",n,n)),this},set(n,s){!t&&!ke(s)&&!Vt(s)&&(s=Q(s));const i=Q(this),{has:o,get:c}=Nu(i);let D=o.call(i,n);D||(n=Q(n),D=o.call(i,n));const a=c.call(i,n);return i.set(n,s),D?_t(s,a)&&st(i,"set",n,s):st(i,"add",n,s),this},delete(n){const s=Q(this),{has:i,get:o}=Nu(s);let c=i.call(s,n);c||(n=Q(n),c=i.call(s,n)),o&&o.call(s,n);const D=s.delete(n);return c&&st(s,"delete",n,void 0),D},clear(){const n=Q(this),s=n.size!==0,i=n.clear();return s&&st(n,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(n=>{u[n]=ki(n,e,t)}),u}function Ru(e,t){const u=Ui(e,t);return(r,n,s)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?r:Reflect.get(ue(u,n)&&n in r?u:r,n,s)}const Ki={get:Ru(!1,!1)},zi={get:Ru(!1,!0)},Gi={get:Ru(!0,!1)},Ji={get:Ru(!0,!0)},$n=new WeakMap,Mn=new WeakMap,jn=new WeakMap,Ln=new WeakMap;function qi(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Yi(e){return e.__v_skip||!Object.isExtensible(e)?0:qi(gi(e))}function nu(e){return Vt(e)?e:Mu(e,!1,Li,Ki,$n)}function Zi(e){return Mu(e,!1,Hi,zi,Mn)}function Vn(e){return Mu(e,!0,Vi,Gi,jn)}function $u(e){return Mu(e,!0,Wi,Ji,Ln)}function Mu(e,t,u,r,n){if(!De(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=n.get(e);if(s)return s;const i=Yi(e);if(i===0)return e;const o=new Proxy(e,i===2?r:u);return n.set(e,o),o}function Lt(e){return Vt(e)?Lt(e.__v_raw):!!(e&&e.__v_isReactive)}function Vt(e){return!!(e&&e.__v_isReadonly)}function ke(e){return!!(e&&e.__v_isShallow)}function Or(e){return e?!!e.__v_raw:!1}function Q(e){const t=e&&e.__v_raw;return t?Q(t):e}function Qi(e){return!ue(e,"__v_skip")&&Object.isExtensible(e)&&Cn(e,"__v_skip",!0),e}const Se=e=>De(e)?nu(e):e,Tr=e=>De(e)?Vn(e):e;function Ae(e){return e?e.__v_isRef===!0:!1}function Xi(e){return Ae(e)?e.value:e}const eo={get:(e,t,u)=>t==="__v_raw"?e:Xi(Reflect.get(e,t,u)),set:(e,t,u,r)=>{const n=e[t];return Ae(n)&&!Ae(u)?(n.value=u,!0):Reflect.set(e,t,u,r)}};function Hn(e){return Lt(e)?e:new Proxy(e,eo)}function to(e){const t=L(e)?new Array(e.length):{};for(const u in e)t[u]=ro(e,u);return t}class uo{constructor(t,u,r){this._object=t,this._key=u,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Ii(Q(this._object),this._key)}}function ro(e,t,u){const r=e[t];return Ae(r)?r:new uo(e,t,u)}class no{constructor(t,u,r){this.fn=t,this.setter=u,this._value=void 0,this.dep=new Sn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=tu-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!u,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ie!==this)return yn(this,!0),!0}get value(){const t=this.dep.track();return _n(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function so(e,t,u=!1){let r,n;return W(e)?r=e:(r=e.get,n=e.set),new no(r,n,u)}const ju={},Lu=new WeakMap;let wt;function io(e,t=!1,u=wt){if(u){let r=Lu.get(u);r||Lu.set(u,r=[]),r.push(e)}}function oo(e,t,u=Z){const{immediate:r,deep:n,once:s,scheduler:i,augmentJob:o,call:c}=u,D=x=>n?x:ke(x)||n===!1||n===0?Ft(x,1):Ft(x);let a,d,C,F,T=!1,A=!1;if(Ae(e)?(d=()=>e.value,T=ke(e)):Lt(e)?(d=()=>D(e),T=!0):L(e)?(A=!0,T=e.some(x=>Lt(x)||ke(x)),d=()=>e.map(x=>{if(Ae(x))return x.value;if(Lt(x))return D(x);if(W(x))return c?c(x,2):x()})):W(e)?t?d=c?()=>c(e,2):e:d=()=>{if(C){rt();try{C()}finally{nt()}}const x=wt;wt=a;try{return c?c(e,3,[F]):e(F)}finally{wt=x}}:d=He,t&&n){const x=d,j=n===!0?1/0:n;d=()=>Ft(x(),j)}const I=Ti(),m=()=>{a.stop(),I&&I.active&&hr(I.effects,a)};if(s&&t){const x=t;t=(...j)=>{x(...j),m()}}let P=A?new Array(e.length).fill(ju):ju;const R=x=>{if(!(!(a.flags&1)||!a.dirty&&!x))if(t){const j=a.run();if(n||T||(A?j.some((J,k)=>_t(J,P[k])):_t(j,P))){C&&C();const J=wt;wt=a;try{const k=[j,P===ju?void 0:A&&P[0]===ju?[]:P,F];c?c(t,3,k):t(...k),P=j}finally{wt=J}}}else a.run()};return o&&o(R),a=new mn(d),a.scheduler=i?()=>i(R,!1):R,F=x=>io(x,!1,a),C=a.onStop=()=>{const x=Lu.get(a);if(x){if(c)c(x,4);else for(const j of x)j();Lu.delete(a)}},t?r?R(!0):P=a.run():i?i(R.bind(null,!0),!0):a.run(),m.pause=a.pause.bind(a),m.resume=a.resume.bind(a),m.stop=m,m}function Ft(e,t=1/0,u){if(t<=0||!De(e)||e.__v_skip||(u=u||new Set,u.has(e)))return e;if(u.add(e),t--,Ae(e))Ft(e.value,t,u);else if(L(e))for(let r=0;r<e.length;r++)Ft(e[r],t,u);else if(fn(e)||$t(e))e.forEach(r=>{Ft(r,t,u)});else if(hn(e)){for(const r in e)Ft(e[r],t,u);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Ft(e[r],t,u)}return e}var Et={NODE_ENV:"production"};const su=[];let Pr=!1;function co(e,...t){if(Pr)return;Pr=!0,rt();const u=su.length?su[su.length-1].component:null,r=u&&u.appContext.config.warnHandler,n=lo();if(r)Ht(r,u,11,[e+t.map(s=>{var i,o;return(o=(i=s.toString)==null?void 0:i.call(s))!=null?o:JSON.stringify(s)}).join(""),u&&u.proxy,n.map(({vnode:s})=>`at <${Is(u,s.type)}>`).join(`
`),n]);else{const s=[`[Vue warn]: ${e}`,...t];n.length&&s.push(`
`,...ao(n)),console.warn(...s)}nt(),Pr=!1}function lo(){let e=su[su.length-1];if(!e)return[];const t=[];for(;e;){const u=t[0];u&&u.vnode===e?u.recurseCount++:t.push({vnode:e,recurseCount:0});const r=e.component&&e.component.parent;e=r&&r.vnode}return t}function ao(e){const t=[];return e.forEach((u,r)=>{t.push(...r===0?[]:[`
`],...fo(u))}),t}function fo({vnode:e,recurseCount:t}){const u=t>0?`... (${t} recursive calls)`:"",r=e.component?e.component.parent==null:!1,n=` at <${Is(e.component,e.type,r)}`,s=">"+u;return e.props?[n,...Do(e.props),s]:[n+s]}function Do(e){const t=[],u=Object.keys(e);return u.slice(0,3).forEach(r=>{t.push(...Wn(r,e[r]))}),u.length>3&&t.push(" ..."),t}function Wn(e,t,u){return de(t)?(t=JSON.stringify(t),u?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?u?t:[`${e}=${t}`]:Ae(t)?(t=Wn(e,Q(t.value),!0),u?t:[`${e}=Ref<`,t,">"]):W(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Q(t),u?t:[`${e}=`,t])}function Ht(e,t,u,r){try{return r?e(...r):e()}catch(n){Vu(n,t,u)}}function ze(e,t,u,r){if(W(e)){const n=Ht(e,t,u,r);return n&&Dn(n)&&n.catch(s=>{Vu(s,t,u)}),n}if(L(e)){const n=[];for(let s=0;s<e.length;s++)n.push(ze(e[s],t,u,r));return n}}function Vu(e,t,u,r=!0){const n=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Z;if(t){let o=t.parent;const c=t.proxy,D=`https://vuejs.org/error-reference/#runtime-${u}`;for(;o;){const a=o.ec;if(a){for(let d=0;d<a.length;d++)if(a[d](e,c,D)===!1)return}o=o.parent}if(s){rt(),Ht(s,null,10,[e,c,D]),nt();return}}ho(e,u,n,r,i)}function ho(e,t,u,r=!0,n=!1){if(n)throw e;console.error(e)}const ve=[];let Ge=-1;const Wt=[];let gt=null,kt=0;const kn=Promise.resolve();let Hu=null;function po(e){const t=Hu||kn;return e?t.then(this?e.bind(this):e):t}function Co(e){let t=Ge+1,u=ve.length;for(;t<u;){const r=t+u>>>1,n=ve[r],s=iu(n);s<e||s===e&&n.flags&2?t=r+1:u=r}return t}function Nr(e){if(!(e.flags&1)){const t=iu(e),u=ve[ve.length-1];!u||!(e.flags&2)&&t>=iu(u)?ve.push(e):ve.splice(Co(t),0,e),e.flags|=1,Un()}}function Un(){Hu||(Hu=kn.then(Gn))}function Ao(e){L(e)?Wt.push(...e):gt&&e.id===-1?gt.splice(kt+1,0,e):e.flags&1||(Wt.push(e),e.flags|=1),Un()}function Kn(e,t,u=Ge+1){for(;u<ve.length;u++){const r=ve[u];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;ve.splice(u,1),u--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function zn(e){if(Wt.length){const t=[...new Set(Wt)].sort((u,r)=>iu(u)-iu(r));if(Wt.length=0,gt){gt.push(...t);return}for(gt=t,kt=0;kt<gt.length;kt++){const u=gt[kt];u.flags&4&&(u.flags&=-2),u.flags&8||u(),u.flags&=-2}gt=null,kt=0}}const iu=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Gn(e){const t=He;try{for(Ge=0;Ge<ve.length;Ge++){const u=ve[Ge];u&&!(u.flags&8)&&(Et.NODE_ENV!=="production"&&t(u),u.flags&4&&(u.flags&=-2),Ht(u,u.i,u.i?15:14),u.flags&4||(u.flags&=-2))}}finally{for(;Ge<ve.length;Ge++){const u=ve[Ge];u&&(u.flags&=-2)}Ge=-1,ve.length=0,zn(),Hu=null,(ve.length||Wt.length)&&Gn()}}let Je=null,Jn=null;function Wu(e){const t=Je;return Je=e,Jn=e&&e.type.__scopeId||null,t}function Fo(e,t=Je,u){if(!t||e._n)return e;const r=(...n)=>{r._d&&_s(-1);const s=Wu(t);let i;try{i=e(...n)}finally{Wu(s),r._d&&_s(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function St(e,t,u,r){const n=e.dirs,s=t&&t.dirs;for(let i=0;i<n.length;i++){const o=n[i];s&&(o.oldValue=s[i].value);let c=o.dir[r];c&&(rt(),ze(c,u,8,[e.el,o,e,t]),nt())}}const Eo=Symbol("_vte"),go=e=>e.__isTeleport;function Ir(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ir(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function qn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function ku(e,t,u,r,n=!1){if(L(e)){e.forEach((T,A)=>ku(T,t&&(L(t)?t[A]:t),u,r,n));return}if(ou(r)&&!n){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&ku(e,t,u,r.component.subTree);return}const s=r.shapeFlag&4?Kr(r.component):r.el,i=n?null:s,{i:o,r:c}=e,D=t&&t.r,a=o.refs===Z?o.refs={}:o.refs,d=o.setupState,C=Q(d),F=d===Z?()=>!1:T=>ue(C,T);if(D!=null&&D!==c&&(de(D)?(a[D]=null,F(D)&&(d[D]=null)):Ae(D)&&(D.value=null)),W(c))Ht(c,o,12,[i,a]);else{const T=de(c),A=Ae(c);if(T||A){const I=()=>{if(e.f){const m=T?F(c)?d[c]:a[c]:c.value;n?L(m)&&hr(m,s):L(m)?m.includes(s)||m.push(s):T?(a[c]=[s],F(c)&&(d[c]=a[c])):(c.value=[s],e.k&&(a[e.k]=c.value))}else T?(a[c]=i,F(c)&&(d[c]=i)):A&&(c.value=i,e.k&&(a[e.k]=i))};i?(I.id=-1,Ne(I,u)):I()}}}Ou().requestIdleCallback,Ou().cancelIdleCallback;const ou=e=>!!e.type.__asyncLoader,Yn=e=>e.type.__isKeepAlive;function mo(e,t){Zn(e,"a",t)}function bo(e,t){Zn(e,"da",t)}function Zn(e,t,u=Fe){const r=e.__wdc||(e.__wdc=()=>{let n=u;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Uu(t,r,u),u){let n=u.parent;for(;n&&n.parent;)Yn(n.parent.vnode)&&yo(r,t,u,n),n=n.parent}}function yo(e,t,u,r){const n=Uu(t,e,r,!0);Xn(()=>{hr(r[t],n)},u)}function Uu(e,t,u=Fe,r=!1){if(u){const n=u[e]||(u[e]=[]),s=t.__weh||(t.__weh=(...i)=>{rt();const o=Cu(u),c=ze(t,u,e,i);return o(),nt(),c});return r?n.unshift(s):n.push(s),s}}const ot=e=>(t,u=Fe)=>{(!Au||e==="sp")&&Uu(e,(...r)=>t(...r),u)},Bo=ot("bm"),Qn=ot("m"),vo=ot("bu"),_o=ot("u"),xo=ot("bum"),Xn=ot("um"),wo=ot("sp"),So=ot("rtg"),Oo=ot("rtc");function To(e,t=Fe){Uu("ec",e,t)}const Po=Symbol.for("v-ndc");function No(e,t,u,r){let n;const s=u,i=L(e);if(i||de(e)){const o=i&&Lt(e);let c=!1;o&&(c=!ke(e),e=Pu(e)),n=new Array(e.length);for(let D=0,a=e.length;D<a;D++)n[D]=t(c?Se(e[D]):e[D],D,void 0,s)}else if(typeof e=="number"){n=new Array(e);for(let o=0;o<e;o++)n[o]=t(o+1,o,void 0,s)}else if(De(e))if(e[Symbol.iterator])n=Array.from(e,(o,c)=>t(o,c,void 0,s));else{const o=Object.keys(e);n=new Array(o.length);for(let c=0,D=o.length;c<D;c++){const a=o[c];n[c]=t(e[a],a,c,s)}}else n=[];return n}const Rr=e=>e?Ts(e)?Kr(e):Rr(e.parent):null,cu=Be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Rr(e.parent),$root:e=>Rr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>rs(e),$forceUpdate:e=>e.f||(e.f=()=>{Nr(e.update)}),$nextTick:e=>e.n||(e.n=po.bind(e.proxy)),$watch:e=>tc.bind(e)}),$r=(e,t)=>e!==Z&&!e.__isScriptSetup&&ue(e,t),Io={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:u,setupState:r,data:n,props:s,accessCache:i,type:o,appContext:c}=e;let D;if(t[0]!=="$"){const F=i[t];if(F!==void 0)switch(F){case 1:return r[t];case 2:return n[t];case 4:return u[t];case 3:return s[t]}else{if($r(r,t))return i[t]=1,r[t];if(n!==Z&&ue(n,t))return i[t]=2,n[t];if((D=e.propsOptions[0])&&ue(D,t))return i[t]=3,s[t];if(u!==Z&&ue(u,t))return i[t]=4,u[t];Mr&&(i[t]=0)}}const a=cu[t];let d,C;if(a)return t==="$attrs"&&me(e.attrs,"get",""),a(e);if((d=o.__cssModules)&&(d=d[t]))return d;if(u!==Z&&ue(u,t))return i[t]=4,u[t];if(C=c.config.globalProperties,ue(C,t))return C[t]},set({_:e},t,u){const{data:r,setupState:n,ctx:s}=e;return $r(n,t)?(n[t]=u,!0):r!==Z&&ue(r,t)?(r[t]=u,!0):ue(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=u,!0)},has({_:{data:e,setupState:t,accessCache:u,ctx:r,appContext:n,propsOptions:s}},i){let o;return!!u[i]||e!==Z&&ue(e,i)||$r(t,i)||(o=s[0])&&ue(o,i)||ue(r,i)||ue(cu,i)||ue(n.config.globalProperties,i)},defineProperty(e,t,u){return u.get!=null?e._.accessCache[t]=0:ue(u,"value")&&this.set(e,t,u.value,null),Reflect.defineProperty(e,t,u)}};function es(e){return L(e)?e.reduce((t,u)=>(t[u]=null,t),{}):e}let Mr=!0;function Ro(e){const t=rs(e),u=e.proxy,r=e.ctx;Mr=!1,t.beforeCreate&&ts(t.beforeCreate,e,"bc");const{data:n,computed:s,methods:i,watch:o,provide:c,inject:D,created:a,beforeMount:d,mounted:C,beforeUpdate:F,updated:T,activated:A,deactivated:I,beforeDestroy:m,beforeUnmount:P,destroyed:R,unmounted:x,render:j,renderTracked:J,renderTriggered:k,errorCaptured:Ke,serverPrefetch:It,expose:Le,inheritAttrs:dt,components:qt,directives:Yt,filters:vu}=t;if(D&&$o(D,r,null),i)for(const ae in i){const te=i[ae];W(te)&&(r[ae]=te.bind(u))}if(n){const ae=n.call(u,u);De(ae)&&(e.data=nu(ae))}if(Mr=!0,s)for(const ae in s){const te=s[ae],ht=W(te)?te.bind(u,u):W(te.get)?te.get.bind(u,u):He,Zt=!W(te)&&W(te.set)?te.set.bind(u):He,pt=vc({get:ht,set:Zt});Object.defineProperty(r,ae,{enumerable:!0,configurable:!0,get:()=>pt.value,set:B=>pt.value=B})}if(o)for(const ae in o)us(o[ae],r,u,ae);if(c){const ae=W(c)?c.call(u):c;Reflect.ownKeys(ae).forEach(te=>{Wo(te,ae[te])})}a&&ts(a,e,"c");function ge(ae,te){L(te)?te.forEach(ht=>ae(ht.bind(u))):te&&ae(te.bind(u))}if(ge(Bo,d),ge(Qn,C),ge(vo,F),ge(_o,T),ge(mo,A),ge(bo,I),ge(To,Ke),ge(Oo,J),ge(So,k),ge(xo,P),ge(Xn,x),ge(wo,It),L(Le))if(Le.length){const ae=e.exposed||(e.exposed={});Le.forEach(te=>{Object.defineProperty(ae,te,{get:()=>u[te],set:ht=>u[te]=ht})})}else e.exposed||(e.exposed={});j&&e.render===He&&(e.render=j),dt!=null&&(e.inheritAttrs=dt),qt&&(e.components=qt),Yt&&(e.directives=Yt),It&&qn(e)}function $o(e,t,u=He){L(e)&&(e=jr(e));for(const r in e){const n=e[r];let s;De(n)?"default"in n?s=zu(n.from||r,n.default,!0):s=zu(n.from||r):s=zu(n),Ae(s)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[r]=s}}function ts(e,t,u){ze(L(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,u)}function us(e,t,u,r){let n=r.includes(".")?ms(u,r):()=>u[r];if(de(e)){const s=t[e];W(s)&&au(n,s)}else if(W(e))au(n,e.bind(u));else if(De(e))if(L(e))e.forEach(s=>us(s,t,u,r));else{const s=W(e.handler)?e.handler.bind(u):t[e.handler];W(s)&&au(n,s,e)}}function rs(e){const t=e.type,{mixins:u,extends:r}=t,{mixins:n,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,o=s.get(t);let c;return o?c=o:!n.length&&!u&&!r?c=t:(c={},n.length&&n.forEach(D=>Ku(c,D,i,!0)),Ku(c,t,i)),De(t)&&s.set(t,c),c}function Ku(e,t,u,r=!1){const{mixins:n,extends:s}=t;s&&Ku(e,s,u,!0),n&&n.forEach(i=>Ku(e,i,u,!0));for(const i in t)if(!(r&&i==="expose")){const o=Mo[i]||u&&u[i];e[i]=o?o(e[i],t[i]):t[i]}return e}const Mo={data:ns,props:ss,emits:ss,methods:lu,computed:lu,beforeCreate:_e,created:_e,beforeMount:_e,mounted:_e,beforeUpdate:_e,updated:_e,beforeDestroy:_e,beforeUnmount:_e,destroyed:_e,unmounted:_e,activated:_e,deactivated:_e,errorCaptured:_e,serverPrefetch:_e,components:lu,directives:lu,watch:Lo,provide:ns,inject:jo};function ns(e,t){return t?e?function(){return Be(W(e)?e.call(this,this):e,W(t)?t.call(this,this):t)}:t:e}function jo(e,t){return lu(jr(e),jr(t))}function jr(e){if(L(e)){const t={};for(let u=0;u<e.length;u++)t[e[u]]=e[u];return t}return e}function _e(e,t){return e?[...new Set([].concat(e,t))]:t}function lu(e,t){return e?Be(Object.create(null),e,t):t}function ss(e,t){return e?L(e)&&L(t)?[...new Set([...e,...t])]:Be(Object.create(null),es(e),es(t??{})):t}function Lo(e,t){if(!e)return t;if(!t)return e;const u=Be(Object.create(null),e);for(const r in t)u[r]=_e(e[r],t[r]);return u}function is(){return{app:null,config:{isNativeTag:Fi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Vo=0;function Ho(e,t){return function(r,n=null){W(r)||(r=Be({},r)),n!=null&&!De(n)&&(n=null);const s=is(),i=new WeakSet,o=[];let c=!1;const D=s.app={_uid:Vo++,_component:r,_props:n,_container:null,_context:s,_instance:null,version:xc,get config(){return s.config},set config(a){},use(a,...d){return i.has(a)||(a&&W(a.install)?(i.add(a),a.install(D,...d)):W(a)&&(i.add(a),a(D,...d))),D},mixin(a){return s.mixins.includes(a)||s.mixins.push(a),D},component(a,d){return d?(s.components[a]=d,D):s.components[a]},directive(a,d){return d?(s.directives[a]=d,D):s.directives[a]},mount(a,d,C){if(!c){const F=D._ceVNode||Re(r,n);return F.appContext=s,C===!0?C="svg":C===!1&&(C=void 0),e(F,a,C),c=!0,D._container=a,a.__vue_app__=D,Kr(F.component)}},onUnmount(a){o.push(a)},unmount(){c&&(ze(o,D._instance,16),e(null,D._container),delete D._container.__vue_app__)},provide(a,d){return s.provides[a]=d,D},runWithContext(a){const d=Ut;Ut=D;try{return a()}finally{Ut=d}}};return D}}let Ut=null;function Wo(e,t){if(Fe){let u=Fe.provides;const r=Fe.parent&&Fe.parent.provides;r===u&&(u=Fe.provides=Object.create(r)),u[e]=t}}function zu(e,t,u=!1){const r=Fe||Je;if(r||Ut){const n=Ut?Ut._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return u&&W(t)?t.call(r&&r.proxy):t}}const os={},cs=()=>Object.create(os),ls=e=>Object.getPrototypeOf(e)===os;function ko(e,t,u,r=!1){const n={},s=cs();e.propsDefaults=Object.create(null),as(e,t,n,s);for(const i in e.propsOptions[0])i in n||(n[i]=void 0);u?e.props=r?n:Zi(n):e.type.props?e.props=n:e.props=s,e.attrs=s}function Uo(e,t,u,r){const{props:n,attrs:s,vnode:{patchFlag:i}}=e,o=Q(n),[c]=e.propsOptions;let D=!1;if((r||i>0)&&!(i&16)){if(i&8){const a=e.vnode.dynamicProps;for(let d=0;d<a.length;d++){let C=a[d];if(Gu(e.emitsOptions,C))continue;const F=t[C];if(c)if(ue(s,C))F!==s[C]&&(s[C]=F,D=!0);else{const T=At(C);n[T]=Lr(c,o,T,F,e,!1)}else F!==s[C]&&(s[C]=F,D=!0)}}}else{as(e,t,n,s)&&(D=!0);let a;for(const d in o)(!t||!ue(t,d)&&((a=vt(d))===d||!ue(t,a)))&&(c?u&&(u[d]!==void 0||u[a]!==void 0)&&(n[d]=Lr(c,o,d,void 0,e,!0)):delete n[d]);if(s!==o)for(const d in s)(!t||!ue(t,d))&&(delete s[d],D=!0)}D&&st(e.attrs,"set","")}function as(e,t,u,r){const[n,s]=e.propsOptions;let i=!1,o;if(t)for(let c in t){if(Qt(c))continue;const D=t[c];let a;n&&ue(n,a=At(c))?!s||!s.includes(a)?u[a]=D:(o||(o={}))[a]=D:Gu(e.emitsOptions,c)||(!(c in r)||D!==r[c])&&(r[c]=D,i=!0)}if(s){const c=Q(u),D=o||Z;for(let a=0;a<s.length;a++){const d=s[a];u[d]=Lr(n,c,d,D[d],e,!ue(D,d))}}return i}function Lr(e,t,u,r,n,s){const i=e[u];if(i!=null){const o=ue(i,"default");if(o&&r===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&W(c)){const{propsDefaults:D}=n;if(u in D)r=D[u];else{const a=Cu(n);r=D[u]=c.call(null,t),a()}}else r=c;n.ce&&n.ce._setProp(u,r)}i[0]&&(s&&!o?r=!1:i[1]&&(r===""||r===vt(u))&&(r=!0))}return r}const Ko=new WeakMap;function fs(e,t,u=!1){const r=u?Ko:t.propsCache,n=r.get(e);if(n)return n;const s=e.props,i={},o=[];let c=!1;if(!W(e)){const a=d=>{c=!0;const[C,F]=fs(d,t,!0);Be(i,C),F&&o.push(...F)};!u&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!s&&!c)return De(e)&&r.set(e,Rt),Rt;if(L(s))for(let a=0;a<s.length;a++){const d=At(s[a]);Ds(d)&&(i[d]=Z)}else if(s)for(const a in s){const d=At(a);if(Ds(d)){const C=s[a],F=i[d]=L(C)||W(C)?{type:C}:Be({},C),T=F.type;let A=!1,I=!0;if(L(T))for(let m=0;m<T.length;++m){const P=T[m],R=W(P)&&P.name;if(R==="Boolean"){A=!0;break}else R==="String"&&(I=!1)}else A=W(T)&&T.name==="Boolean";F[0]=A,F[1]=I,(A||ue(F,"default"))&&o.push(d)}}const D=[i,o];return De(e)&&r.set(e,D),D}function Ds(e){return e[0]!=="$"&&!Qt(e)}const ds=e=>e[0]==="_"||e==="$stable",Vr=e=>L(e)?e.map(Ye):[Ye(e)],zo=(e,t,u)=>{if(t._n)return t;const r=Fo((...n)=>(Et.NODE_ENV!=="production"&&Fe&&(!u||(u.root,Fe.root)),Vr(t(...n))),u);return r._c=!1,r},hs=(e,t,u)=>{const r=e._ctx;for(const n in e){if(ds(n))continue;const s=e[n];if(W(s))t[n]=zo(n,s,r);else if(s!=null){const i=Vr(s);t[n]=()=>i}}},ps=(e,t)=>{const u=Vr(t);e.slots.default=()=>u},Cs=(e,t,u)=>{for(const r in t)(u||r!=="_")&&(e[r]=t[r])},Go=(e,t,u)=>{const r=e.slots=cs();if(e.vnode.shapeFlag&32){const n=t._;n?(Cs(r,t,u),u&&Cn(r,"_",n,!0)):hs(t,r)}else t&&ps(e,t)},Jo=(e,t,u)=>{const{vnode:r,slots:n}=e;let s=!0,i=Z;if(r.shapeFlag&32){const o=t._;o?u&&o===1?s=!1:Cs(n,t,u):(s=!t.$stable,hs(t,n)),i=t}else t&&(ps(e,t),i={default:1});if(s)for(const o in n)!ds(o)&&i[o]==null&&delete n[o]},Ne=cc;function qo(e){return Yo(e)}function Yo(e,t){const u=Ou();u.__VUE__=!0;const{insert:r,remove:n,patchProp:s,createElement:i,createText:o,createComment:c,setText:D,setElementText:a,parentNode:d,nextSibling:C,setScopeId:F=He,insertStaticContent:T}=e,A=(l,f,h,b=null,E=null,g=null,w=void 0,_=null,v=!!f.dynamicChildren)=>{if(l===f)return;l&&!du(l,f)&&(b=re(l),B(l,E,g,!0),l=null),f.patchFlag===-2&&(v=!1,f.dynamicChildren=null);const{type:y,ref:M,shapeFlag:O}=f;switch(y){case Ju:I(l,f,h,b);break;case Tt:m(l,f,h,b);break;case Wr:l==null&&P(f,h,b,w);break;case qe:qt(l,f,h,b,E,g,w,_,v);break;default:O&1?j(l,f,h,b,E,g,w,_,v):O&6?Yt(l,f,h,b,E,g,w,_,v):(O&64||O&128)&&y.process(l,f,h,b,E,g,w,_,v,fe)}M!=null&&E&&ku(M,l&&l.ref,g,f||l,!f)},I=(l,f,h,b)=>{if(l==null)r(f.el=o(f.children),h,b);else{const E=f.el=l.el;f.children!==l.children&&D(E,f.children)}},m=(l,f,h,b)=>{l==null?r(f.el=c(f.children||""),h,b):f.el=l.el},P=(l,f,h,b)=>{[l.el,l.anchor]=T(l.children,f,h,b,l.el,l.anchor)},R=({el:l,anchor:f},h,b)=>{let E;for(;l&&l!==f;)E=C(l),r(l,h,b),l=E;r(f,h,b)},x=({el:l,anchor:f})=>{let h;for(;l&&l!==f;)h=C(l),n(l),l=h;n(f)},j=(l,f,h,b,E,g,w,_,v)=>{f.type==="svg"?w="svg":f.type==="math"&&(w="mathml"),l==null?J(f,h,b,E,g,w,_,v):It(l,f,E,g,w,_,v)},J=(l,f,h,b,E,g,w,_)=>{let v,y;const{props:M,shapeFlag:O,transition:$,dirs:V}=l;if(v=l.el=i(l.type,g,M&&M.is,M),O&8?a(v,l.children):O&16&&Ke(l.children,v,null,b,E,Hr(l,g),w,_),V&&St(l,null,b,"created"),k(v,l,l.scopeId,w,b),M){for(const oe in M)oe!=="value"&&!Qt(oe)&&s(v,oe,null,M[oe],g,b);"value"in M&&s(v,"value",null,M.value,g),(y=M.onVnodeBeforeMount)&&Ze(y,b,l)}V&&St(l,null,b,"beforeMount");const G=Zo(E,$);G&&$.beforeEnter(v),r(v,f,h),((y=M&&M.onVnodeMounted)||G||V)&&Ne(()=>{y&&Ze(y,b,l),G&&$.enter(v),V&&St(l,null,b,"mounted")},E)},k=(l,f,h,b,E)=>{if(h&&F(l,h),b)for(let g=0;g<b.length;g++)F(l,b[g]);if(E){let g=E.subTree;if(f===g||vs(g.type)&&(g.ssContent===f||g.ssFallback===f)){const w=E.vnode;k(l,w,w.scopeId,w.slotScopeIds,E.parent)}}},Ke=(l,f,h,b,E,g,w,_,v=0)=>{for(let y=v;y<l.length;y++){const M=l[y]=_?bt(l[y]):Ye(l[y]);A(null,M,f,h,b,E,g,w,_)}},It=(l,f,h,b,E,g,w)=>{const _=f.el=l.el;let{patchFlag:v,dynamicChildren:y,dirs:M}=f;v|=l.patchFlag&16;const O=l.props||Z,$=f.props||Z;let V;if(h&&Ot(h,!1),(V=$.onVnodeBeforeUpdate)&&Ze(V,h,f,l),M&&St(f,l,h,"beforeUpdate"),h&&Ot(h,!0),(O.innerHTML&&$.innerHTML==null||O.textContent&&$.textContent==null)&&a(_,""),y?Le(l.dynamicChildren,y,_,h,b,Hr(f,E),g):w||te(l,f,_,null,h,b,Hr(f,E),g,!1),v>0){if(v&16)dt(_,O,$,h,E);else if(v&2&&O.class!==$.class&&s(_,"class",null,$.class,E),v&4&&s(_,"style",O.style,$.style,E),v&8){const G=f.dynamicProps;for(let oe=0;oe<G.length;oe++){const ne=G[oe],Me=O[ne],Te=$[ne];(Te!==Me||ne==="value")&&s(_,ne,Me,Te,E,h)}}v&1&&l.children!==f.children&&a(_,f.children)}else!w&&y==null&&dt(_,O,$,h,E);((V=$.onVnodeUpdated)||M)&&Ne(()=>{V&&Ze(V,h,f,l),M&&St(f,l,h,"updated")},b)},Le=(l,f,h,b,E,g,w)=>{for(let _=0;_<f.length;_++){const v=l[_],y=f[_],M=v.el&&(v.type===qe||!du(v,y)||v.shapeFlag&70)?d(v.el):h;A(v,y,M,null,b,E,g,w,!0)}},dt=(l,f,h,b,E)=>{if(f!==h){if(f!==Z)for(const g in f)!Qt(g)&&!(g in h)&&s(l,g,f[g],null,E,b);for(const g in h){if(Qt(g))continue;const w=h[g],_=f[g];w!==_&&g!=="value"&&s(l,g,_,w,E,b)}"value"in h&&s(l,"value",f.value,h.value,E)}},qt=(l,f,h,b,E,g,w,_,v)=>{const y=f.el=l?l.el:o(""),M=f.anchor=l?l.anchor:o("");let{patchFlag:O,dynamicChildren:$,slotScopeIds:V}=f;V&&(_=_?_.concat(V):V),l==null?(r(y,h,b),r(M,h,b),Ke(f.children||[],h,M,E,g,w,_,v)):O>0&&O&64&&$&&l.dynamicChildren?(Le(l.dynamicChildren,$,h,E,g,w,_),(f.key!=null||E&&f===E.subTree)&&As(l,f,!0)):te(l,f,h,M,E,g,w,_,v)},Yt=(l,f,h,b,E,g,w,_,v)=>{f.slotScopeIds=_,l==null?f.shapeFlag&512?E.ctx.activate(f,h,b,w,v):vu(f,h,b,E,g,w,v):fr(l,f,v)},vu=(l,f,h,b,E,g,w)=>{const _=l.component=Cc(l,b,E);if(Yn(l)&&(_.ctx.renderer=fe),Ac(_,!1,w),_.asyncDep){if(E&&E.registerDep(_,ge,w),!l.el){const v=_.subTree=Re(Tt);m(null,v,f,h)}}else ge(_,l,f,h,E,g,w)},fr=(l,f,h)=>{const b=f.component=l.component;if(ic(l,f,h))if(b.asyncDep&&!b.asyncResolved){ae(b,f,h);return}else b.next=f,b.update();else f.el=l.el,b.vnode=f},ge=(l,f,h,b,E,g,w)=>{const _=()=>{if(l.isMounted){let{next:O,bu:$,u:V,parent:G,vnode:oe}=l;{const tt=Fs(l);if(tt){O&&(O.el=oe.el,ae(l,O,w)),tt.asyncDep.then(()=>{l.isUnmounted||_()});return}}let ne=O,Me;Ot(l,!1),O?(O.el=oe.el,ae(l,O,w)):O=oe,$&&Ar($),(Me=O.props&&O.props.onVnodeBeforeUpdate)&&Ze(Me,G,O,oe),Ot(l,!0);const Te=ys(l),et=l.subTree;l.subTree=Te,A(et,Te,d(et.el),re(et),l,E,g),O.el=Te.el,ne===null&&oc(l,Te.el),V&&Ne(V,E),(Me=O.props&&O.props.onVnodeUpdated)&&Ne(()=>Ze(Me,G,O,oe),E)}else{let O;const{el:$,props:V}=f,{bm:G,m:oe,parent:ne,root:Me,type:Te}=l,et=ou(f);Ot(l,!1),G&&Ar(G),!et&&(O=V&&V.onVnodeBeforeMount)&&Ze(O,ne,f),Ot(l,!0);{Me.ce&&Me.ce._injectChildStyle(Te);const tt=l.subTree=ys(l);A(null,tt,h,b,l,E,g),f.el=tt.el}if(oe&&Ne(oe,E),!et&&(O=V&&V.onVnodeMounted)){const tt=f;Ne(()=>Ze(O,ne,tt),E)}(f.shapeFlag&256||ne&&ou(ne.vnode)&&ne.vnode.shapeFlag&256)&&l.a&&Ne(l.a,E),l.isMounted=!0,f=h=b=null}};l.scope.on();const v=l.effect=new mn(_);l.scope.off();const y=l.update=v.run.bind(v),M=l.job=v.runIfDirty.bind(v);M.i=l,M.id=l.uid,v.scheduler=()=>Nr(M),Ot(l,!0),y()},ae=(l,f,h)=>{f.component=l;const b=l.vnode.props;l.vnode=f,l.next=null,Uo(l,f.props,b,h),Jo(l,f.children,h),rt(),Kn(l),nt()},te=(l,f,h,b,E,g,w,_,v=!1)=>{const y=l&&l.children,M=l?l.shapeFlag:0,O=f.children,{patchFlag:$,shapeFlag:V}=f;if($>0){if($&128){Zt(y,O,h,b,E,g,w,_,v);return}else if($&256){ht(y,O,h,b,E,g,w,_,v);return}}V&8?(M&16&&z(y,E,g),O!==y&&a(h,O)):M&16?V&16?Zt(y,O,h,b,E,g,w,_,v):z(y,E,g,!0):(M&8&&a(h,""),V&16&&Ke(O,h,b,E,g,w,_,v))},ht=(l,f,h,b,E,g,w,_,v)=>{l=l||Rt,f=f||Rt;const y=l.length,M=f.length,O=Math.min(y,M);let $;for($=0;$<O;$++){const V=f[$]=v?bt(f[$]):Ye(f[$]);A(l[$],V,h,null,E,g,w,_,v)}y>M?z(l,E,g,!0,!1,O):Ke(f,h,b,E,g,w,_,v,O)},Zt=(l,f,h,b,E,g,w,_,v)=>{let y=0;const M=f.length;let O=l.length-1,$=M-1;for(;y<=O&&y<=$;){const V=l[y],G=f[y]=v?bt(f[y]):Ye(f[y]);if(du(V,G))A(V,G,h,null,E,g,w,_,v);else break;y++}for(;y<=O&&y<=$;){const V=l[O],G=f[$]=v?bt(f[$]):Ye(f[$]);if(du(V,G))A(V,G,h,null,E,g,w,_,v);else break;O--,$--}if(y>O){if(y<=$){const V=$+1,G=V<M?f[V].el:b;for(;y<=$;)A(null,f[y]=v?bt(f[y]):Ye(f[y]),h,G,E,g,w,_,v),y++}}else if(y>$)for(;y<=O;)B(l[y],E,g,!0),y++;else{const V=y,G=y,oe=new Map;for(y=G;y<=$;y++){const je=f[y]=v?bt(f[y]):Ye(f[y]);je.key!=null&&oe.set(je.key,y)}let ne,Me=0;const Te=$-G+1;let et=!1,tt=0;const _u=new Array(Te);for(y=0;y<Te;y++)_u[y]=0;for(y=V;y<=O;y++){const je=l[y];if(Me>=Te){B(je,E,g,!0);continue}let ut;if(je.key!=null)ut=oe.get(je.key);else for(ne=G;ne<=$;ne++)if(_u[ne-G]===0&&du(je,f[ne])){ut=ne;break}ut===void 0?B(je,E,g,!0):(_u[ut-G]=y+1,ut>=tt?tt=ut:et=!0,A(je,f[ut],h,null,E,g,w,_,v),Me++)}const Ci=et?Qo(_u):Rt;for(ne=Ci.length-1,y=Te-1;y>=0;y--){const je=G+y,ut=f[je],Ai=je+1<M?f[je+1].el:b;_u[y]===0?A(null,ut,h,Ai,E,g,w,_,v):et&&(ne<0||y!==Ci[ne]?pt(ut,h,Ai,2):ne--)}}},pt=(l,f,h,b,E=null)=>{const{el:g,type:w,transition:_,children:v,shapeFlag:y}=l;if(y&6){pt(l.component.subTree,f,h,b);return}if(y&128){l.suspense.move(f,h,b);return}if(y&64){w.move(l,f,h,fe);return}if(w===qe){r(g,f,h);for(let O=0;O<v.length;O++)pt(v[O],f,h,b);r(l.anchor,f,h);return}if(w===Wr){R(l,f,h);return}if(b!==2&&y&1&&_)if(b===0)_.beforeEnter(g),r(g,f,h),Ne(()=>_.enter(g),E);else{const{leave:O,delayLeave:$,afterLeave:V}=_,G=()=>r(g,f,h),oe=()=>{O(g,()=>{G(),V&&V()})};$?$(g,G,oe):oe()}else r(g,f,h)},B=(l,f,h,b=!1,E=!1)=>{const{type:g,props:w,ref:_,children:v,dynamicChildren:y,shapeFlag:M,patchFlag:O,dirs:$,cacheIndex:V}=l;if(O===-2&&(E=!1),_!=null&&ku(_,null,h,l,!0),V!=null&&(f.renderCache[V]=void 0),M&256){f.ctx.deactivate(l);return}const G=M&1&&$,oe=!ou(l);let ne;if(oe&&(ne=w&&w.onVnodeBeforeUnmount)&&Ze(ne,f,l),M&6)q(l.component,h,b);else{if(M&128){l.suspense.unmount(h,b);return}G&&St(l,null,f,"beforeUnmount"),M&64?l.type.remove(l,f,h,fe,b):y&&!y.hasOnce&&(g!==qe||O>0&&O&64)?z(y,f,h,!1,!0):(g===qe&&O&384||!E&&M&16)&&z(v,f,h),b&&S(l)}(oe&&(ne=w&&w.onVnodeUnmounted)||G)&&Ne(()=>{ne&&Ze(ne,f,l),G&&St(l,null,f,"unmounted")},h)},S=l=>{const{type:f,el:h,anchor:b,transition:E}=l;if(f===qe){N(h,b);return}if(f===Wr){x(l);return}const g=()=>{n(h),E&&!E.persisted&&E.afterLeave&&E.afterLeave()};if(l.shapeFlag&1&&E&&!E.persisted){const{leave:w,delayLeave:_}=E,v=()=>w(h,g);_?_(l.el,g,v):v()}else g()},N=(l,f)=>{let h;for(;l!==f;)h=C(l),n(l),l=h;n(f)},q=(l,f,h)=>{const{bum:b,scope:E,job:g,subTree:w,um:_,m:v,a:y}=l;Es(v),Es(y),b&&Ar(b),E.stop(),g&&(g.flags|=8,B(w,l,f,h)),_&&Ne(_,f),Ne(()=>{l.isUnmounted=!0},f),f&&f.pendingBranch&&!f.isUnmounted&&l.asyncDep&&!l.asyncResolved&&l.suspenseId===f.pendingId&&(f.deps--,f.deps===0&&f.resolve())},z=(l,f,h,b=!1,E=!1,g=0)=>{for(let w=g;w<l.length;w++)B(l[w],f,h,b,E)},re=l=>{if(l.shapeFlag&6)return re(l.component.subTree);if(l.shapeFlag&128)return l.suspense.next();const f=C(l.anchor||l.el),h=f&&f[Eo];return h?C(h):f};let se=!1;const Y=(l,f,h)=>{l==null?f._vnode&&B(f._vnode,null,null,!0):A(f._vnode||null,l,f,null,null,null,h),f._vnode=l,se||(se=!0,Kn(),zn(),se=!1)},fe={p:A,um:B,m:pt,r:S,mt:vu,mc:Ke,pc:te,pbc:Le,n:re,o:e};return{render:Y,hydrate:void 0,createApp:Ho(Y)}}function Hr({type:e,props:t},u){return u==="svg"&&e==="foreignObject"||u==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:u}function Ot({effect:e,job:t},u){u?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Zo(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function As(e,t,u=!1){const r=e.children,n=t.children;if(L(r)&&L(n))for(let s=0;s<r.length;s++){const i=r[s];let o=n[s];o.shapeFlag&1&&!o.dynamicChildren&&((o.patchFlag<=0||o.patchFlag===32)&&(o=n[s]=bt(n[s]),o.el=i.el),!u&&o.patchFlag!==-2&&As(i,o)),o.type===Ju&&(o.el=i.el)}}function Qo(e){const t=e.slice(),u=[0];let r,n,s,i,o;const c=e.length;for(r=0;r<c;r++){const D=e[r];if(D!==0){if(n=u[u.length-1],e[n]<D){t[r]=n,u.push(r);continue}for(s=0,i=u.length-1;s<i;)o=s+i>>1,e[u[o]]<D?s=o+1:i=o;D<e[u[s]]&&(s>0&&(t[r]=u[s-1]),u[s]=r)}}for(s=u.length,i=u[s-1];s-- >0;)u[s]=i,i=t[i];return u}function Fs(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Fs(t)}function Es(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Xo=Symbol.for("v-scx"),ec=()=>zu(Xo);function au(e,t,u){return gs(e,t,u)}function gs(e,t,u=Z){const{immediate:r,deep:n,flush:s,once:i}=u,o=Be({},u),c=t&&r||!t&&s!=="post";let D;if(Au){if(s==="sync"){const F=ec();D=F.__watcherHandles||(F.__watcherHandles=[])}else if(!c){const F=()=>{};return F.stop=He,F.resume=He,F.pause=He,F}}const a=Fe;o.call=(F,T,A)=>ze(F,a,T,A);let d=!1;s==="post"?o.scheduler=F=>{Ne(F,a&&a.suspense)}:s!=="sync"&&(d=!0,o.scheduler=(F,T)=>{T?F():Nr(F)}),o.augmentJob=F=>{t&&(F.flags|=4),d&&(F.flags|=2,a&&(F.id=a.uid,F.i=a))};const C=oo(e,t,o);return Au&&(D?D.push(C):c&&C()),C}function tc(e,t,u){const r=this.proxy,n=de(e)?e.includes(".")?ms(r,e):()=>r[e]:e.bind(r,r);let s;W(t)?s=t:(s=t.handler,u=t);const i=Cu(this),o=gs(n,s.bind(r),u);return i(),o}function ms(e,t){const u=t.split(".");return()=>{let r=e;for(let n=0;n<u.length&&r;n++)r=r[u[n]];return r}}const uc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${At(t)}Modifiers`]||e[`${vt(t)}Modifiers`];function rc(e,t,...u){if(e.isUnmounted)return;const r=e.vnode.props||Z;let n=u;const s=t.startsWith("update:"),i=s&&uc(r,t.slice(7));i&&(i.trim&&(n=u.map(a=>de(a)?a.trim():a)),i.number&&(n=u.map(yi)));let o,c=r[o=Cr(t)]||r[o=Cr(At(t))];!c&&s&&(c=r[o=Cr(vt(t))]),c&&ze(c,e,6,n);const D=r[o+"Once"];if(D){if(!e.emitted)e.emitted={};else if(e.emitted[o])return;e.emitted[o]=!0,ze(D,e,6,n)}}function bs(e,t,u=!1){const r=t.emitsCache,n=r.get(e);if(n!==void 0)return n;const s=e.emits;let i={},o=!1;if(!W(e)){const c=D=>{const a=bs(D,t,!0);a&&(o=!0,Be(i,a))};!u&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!s&&!o?(De(e)&&r.set(e,null),null):(L(s)?s.forEach(c=>i[c]=null):Be(i,s),De(e)&&r.set(e,i),i)}function Gu(e,t){return!e||!xu(t)?!1:(t=t.slice(2).replace(/Once$/,""),ue(e,t[0].toLowerCase()+t.slice(1))||ue(e,vt(t))||ue(e,t))}function Aa(){}function ys(e){const{type:t,vnode:u,proxy:r,withProxy:n,propsOptions:[s],slots:i,attrs:o,emit:c,render:D,renderCache:a,props:d,data:C,setupState:F,ctx:T,inheritAttrs:A}=e,I=Wu(e);let m,P;try{if(u.shapeFlag&4){const x=n||r,j=Et.NODE_ENV!=="production"&&F.__isScriptSetup?new Proxy(x,{get(J,k,Ke){return co(`Property '${String(k)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(J,k,Ke)}}):x;m=Ye(D.call(j,x,a,Et.NODE_ENV!=="production"?$u(d):d,F,C,T)),P=o}else{const x=t;Et.NODE_ENV,m=Ye(x.length>1?x(Et.NODE_ENV!=="production"?$u(d):d,Et.NODE_ENV!=="production"?{get attrs(){return $u(o)},slots:i,emit:c}:{attrs:o,slots:i,emit:c}):x(Et.NODE_ENV!=="production"?$u(d):d,null)),P=t.props?o:nc(o)}}catch(x){fu.length=0,Vu(x,e,1),m=Re(Tt)}let R=m;if(P&&A!==!1){const x=Object.keys(P),{shapeFlag:j}=R;x.length&&j&7&&(s&&x.some(dr)&&(P=sc(P,s)),R=Kt(R,P,!1,!0))}return u.dirs&&(R=Kt(R,null,!1,!0),R.dirs=R.dirs?R.dirs.concat(u.dirs):u.dirs),u.transition&&Ir(R,u.transition),m=R,Wu(I),m}const nc=e=>{let t;for(const u in e)(u==="class"||u==="style"||xu(u))&&((t||(t={}))[u]=e[u]);return t},sc=(e,t)=>{const u={};for(const r in e)(!dr(r)||!(r.slice(9)in t))&&(u[r]=e[r]);return u};function ic(e,t,u){const{props:r,children:n,component:s}=e,{props:i,children:o,patchFlag:c}=t,D=s.emitsOptions;if(t.dirs||t.transition)return!0;if(u&&c>=0){if(c&1024)return!0;if(c&16)return r?Bs(r,i,D):!!i;if(c&8){const a=t.dynamicProps;for(let d=0;d<a.length;d++){const C=a[d];if(i[C]!==r[C]&&!Gu(D,C))return!0}}}else return(n||o)&&(!o||!o.$stable)?!0:r===i?!1:r?i?Bs(r,i,D):!0:!!i;return!1}function Bs(e,t,u){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let n=0;n<r.length;n++){const s=r[n];if(t[s]!==e[s]&&!Gu(u,s))return!0}return!1}function oc({vnode:e,parent:t},u){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=u,t=t.parent;else break}}const vs=e=>e.__isSuspense;function cc(e,t){t&&t.pendingBranch?L(e)?t.effects.push(...e):t.effects.push(e):Ao(e)}const qe=Symbol.for("v-fgt"),Ju=Symbol.for("v-txt"),Tt=Symbol.for("v-cmt"),Wr=Symbol.for("v-stc"),fu=[];let Ie=null;function ct(e=!1){fu.push(Ie=e?null:[])}function lc(){fu.pop(),Ie=fu[fu.length-1]||null}let Du=1;function _s(e,t=!1){Du+=e,e<0&&Ie&&t&&(Ie.hasOnce=!0)}function xs(e){return e.dynamicChildren=Du>0?Ie||Rt:null,lc(),Du>0&&Ie&&Ie.push(e),e}function mt(e,t,u,r,n,s){return xs(hu(e,t,u,r,n,s,!0))}function ac(e,t,u,r,n){return xs(Re(e,t,u,r,n,!0))}function qu(e){return e?e.__v_isVNode===!0:!1}function du(e,t){return e.type===t.type&&e.key===t.key}const ws=({key:e})=>e??null,Yu=({ref:e,ref_key:t,ref_for:u})=>(typeof e=="number"&&(e=""+e),e!=null?de(e)||Ae(e)||W(e)?{i:Je,r:e,k:t,f:!!u}:e:null);function hu(e,t=null,u=null,r=0,n=null,s=e===qe?0:1,i=!1,o=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ws(t),ref:t&&Yu(t),scopeId:Jn,slotScopeIds:null,children:u,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Je};return o?(kr(c,u),s&128&&e.normalize(c)):u&&(c.shapeFlag|=de(u)?8:16),Du>0&&!i&&Ie&&(c.patchFlag>0||s&6)&&c.patchFlag!==32&&Ie.push(c),c}const Re=fc;function fc(e,t=null,u=null,r=0,n=null,s=!1){if((!e||e===Po)&&(e=Tt),qu(e)){const o=Kt(e,t,!0);return u&&kr(o,u),Du>0&&!s&&Ie&&(o.shapeFlag&6?Ie[Ie.indexOf(e)]=o:Ie.push(o)),o.patchFlag=-2,o}if(Bc(e)&&(e=e.__vccOpts),t){t=Dc(t);let{class:o,style:c}=t;o&&!de(o)&&(t.class=Er(o)),De(c)&&(Or(c)&&!L(c)&&(c=Be({},c)),t.style=Fr(c))}const i=de(e)?1:vs(e)?128:go(e)?64:De(e)?4:W(e)?2:0;return hu(e,t,u,r,n,i,s,!0)}function Dc(e){return e?Or(e)||ls(e)?Be({},e):e:null}function Kt(e,t,u=!1,r=!1){const{props:n,ref:s,patchFlag:i,children:o,transition:c}=e,D=t?dc(n||{},t):n,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:D,key:D&&ws(D),ref:t&&t.ref?u&&s?L(s)?s.concat(Yu(t)):[s,Yu(t)]:Yu(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==qe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Kt(e.ssContent),ssFallback:e.ssFallback&&Kt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&Ir(a,c.clone(a)),a}function Ss(e=" ",t=0){return Re(Ju,null,e,t)}function pu(e="",t=!1){return t?(ct(),ac(Tt,null,e)):Re(Tt,null,e)}function Ye(e){return e==null||typeof e=="boolean"?Re(Tt):L(e)?Re(qe,null,e.slice()):qu(e)?bt(e):Re(Ju,null,String(e))}function bt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Kt(e)}function kr(e,t){let u=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(L(t))u=16;else if(typeof t=="object")if(r&65){const n=t.default;n&&(n._c&&(n._d=!1),kr(e,n()),n._c&&(n._d=!0));return}else{u=32;const n=t._;!n&&!ls(t)?t._ctx=Je:n===3&&Je&&(Je.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else W(t)?(t={default:t,_ctx:Je},u=32):(t=String(t),r&64?(u=16,t=[Ss(t)]):u=8);e.children=t,e.shapeFlag|=u}function dc(...e){const t={};for(let u=0;u<e.length;u++){const r=e[u];for(const n in r)if(n==="class")t.class!==r.class&&(t.class=Er([t.class,r.class]));else if(n==="style")t.style=Fr([t.style,r.style]);else if(xu(n)){const s=t[n],i=r[n];i&&s!==i&&!(L(s)&&s.includes(i))&&(t[n]=s?[].concat(s,i):i)}else n!==""&&(t[n]=r[n])}return t}function Ze(e,t,u,r=null){ze(e,t,7,[u,r])}const hc=is();let pc=0;function Cc(e,t,u){const r=e.type,n=(t?t.appContext:e.appContext)||hc,s={uid:pc++,vnode:e,type:r,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Oi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:fs(r,n),emitsOptions:bs(r,n),emit:null,emitted:null,propsDefaults:Z,inheritAttrs:r.inheritAttrs,ctx:Z,data:Z,props:Z,attrs:Z,slots:Z,refs:Z,setupState:Z,setupContext:null,suspense:u,suspenseId:u?u.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=rc.bind(null,s),e.ce&&e.ce(s),s}let Fe=null,Zu,Ur;{const e=Ou(),t=(u,r)=>{let n;return(n=e[u])||(n=e[u]=[]),n.push(r),s=>{n.length>1?n.forEach(i=>i(s)):n[0](s)}};Zu=t("__VUE_INSTANCE_SETTERS__",u=>Fe=u),Ur=t("__VUE_SSR_SETTERS__",u=>Au=u)}const Cu=e=>{const t=Fe;return Zu(e),e.scope.on(),()=>{e.scope.off(),Zu(t)}},Os=()=>{Fe&&Fe.scope.off(),Zu(null)};function Ts(e){return e.vnode.shapeFlag&4}let Au=!1;function Ac(e,t=!1,u=!1){t&&Ur(t);const{props:r,children:n}=e.vnode,s=Ts(e);ko(e,r,s,t),Go(e,n,u);const i=s?Fc(e,t):void 0;return t&&Ur(!1),i}function Fc(e,t){const u=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Io);const{setup:r}=u;if(r){rt();const n=e.setupContext=r.length>1?gc(e):null,s=Cu(e),i=Ht(r,e,0,[e.props,n]),o=Dn(i);if(nt(),s(),(o||e.sp)&&!ou(e)&&qn(e),o){if(i.then(Os,Os),t)return i.then(c=>{Ps(e,c)}).catch(c=>{Vu(c,e,0)});e.asyncDep=i}else Ps(e,i)}else Ns(e)}function Ps(e,t,u){W(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:De(t)&&(e.setupState=Hn(t)),Ns(e)}function Ns(e,t,u){const r=e.type;e.render||(e.render=r.render||He);{const n=Cu(e);rt();try{Ro(e)}finally{nt(),n()}}}const Ec={get(e,t){return me(e,"get",""),e[t]}};function gc(e){const t=u=>{e.exposed=u||{}};return{attrs:new Proxy(e.attrs,Ec),slots:e.slots,emit:e.emit,expose:t}}function Kr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Hn(Qi(e.exposed)),{get(t,u){if(u in t)return t[u];if(u in cu)return cu[u](e)},has(t,u){return u in t||u in cu}})):e.proxy}const mc=/(?:^|[-_])(\w)/g,bc=e=>e.replace(mc,t=>t.toUpperCase()).replace(/[-_]/g,"");function yc(e,t=!0){return W(e)?e.displayName||e.name:e.name||t&&e.__name}function Is(e,t,u=!1){let r=yc(t);if(!r&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(r=n[1])}if(!r&&e&&e.parent){const n=s=>{for(const i in s)if(s[i]===t)return i};r=n(e.components||e.parent.type.components)||n(e.appContext.components)}return r?bc(r):u?"App":"Anonymous"}function Bc(e){return W(e)&&"__vccOpts"in e}const vc=(e,t)=>so(e,t,Au);function _c(e,t,u){const r=arguments.length;return r===2?De(t)&&!L(t)?qu(t)?Re(e,null,[t]):Re(e,t):Re(e,null,t):(r>3?u=Array.prototype.slice.call(arguments,2):r===3&&qu(u)&&(u=[u]),Re(e,t,u))}const xc="3.5.13";let zr;const Rs=typeof window<"u"&&window.trustedTypes;if(Rs)try{zr=Rs.createPolicy("vue",{createHTML:e=>e})}catch{}const $s=zr?e=>zr.createHTML(e):e=>e,wc="http://www.w3.org/2000/svg",Sc="http://www.w3.org/1998/Math/MathML",lt=typeof document<"u"?document:null,Ms=lt&&lt.createElement("template"),Oc={insert:(e,t,u)=>{t.insertBefore(e,u||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,u,r)=>{const n=t==="svg"?lt.createElementNS(wc,e):t==="mathml"?lt.createElementNS(Sc,e):u?lt.createElement(e,{is:u}):lt.createElement(e);return e==="select"&&r&&r.multiple!=null&&n.setAttribute("multiple",r.multiple),n},createText:e=>lt.createTextNode(e),createComment:e=>lt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>lt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,u,r,n,s){const i=u?u.previousSibling:t.lastChild;if(n&&(n===s||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),u),!(n===s||!(n=n.nextSibling)););else{Ms.innerHTML=$s(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const o=Ms.content;if(r==="svg"||r==="mathml"){const c=o.firstChild;for(;c.firstChild;)o.appendChild(c.firstChild);o.removeChild(c)}t.insertBefore(o,u)}return[i?i.nextSibling:t.firstChild,u?u.previousSibling:t.lastChild]}},Tc=Symbol("_vtc");function Pc(e,t,u){const r=e[Tc];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):u?e.setAttribute("class",t):e.className=t}const js=Symbol("_vod"),Nc=Symbol("_vsh"),Ic=Symbol(""),Rc=/(^|;)\s*display\s*:/;function $c(e,t,u){const r=e.style,n=de(u);let s=!1;if(u&&!n){if(t)if(de(t))for(const i of t.split(";")){const o=i.slice(0,i.indexOf(":")).trim();u[o]==null&&Qu(r,o,"")}else for(const i in t)u[i]==null&&Qu(r,i,"");for(const i in u)i==="display"&&(s=!0),Qu(r,i,u[i])}else if(n){if(t!==u){const i=r[Ic];i&&(u+=";"+i),r.cssText=u,s=Rc.test(u)}}else t&&e.removeAttribute("style");js in e&&(e[js]=s?r.display:"",e[Nc]&&(r.display="none"))}const Ls=/\s*!important$/;function Qu(e,t,u){if(L(u))u.forEach(r=>Qu(e,t,r));else if(u==null&&(u=""),t.startsWith("--"))e.setProperty(t,u);else{const r=Mc(e,t);Ls.test(u)?e.setProperty(vt(r),u.replace(Ls,""),"important"):e[r]=u}}const Vs=["Webkit","Moz","ms"],Gr={};function Mc(e,t){const u=Gr[t];if(u)return u;let r=At(t);if(r!=="filter"&&r in e)return Gr[t]=r;r=pn(r);for(let n=0;n<Vs.length;n++){const s=Vs[n]+r;if(s in e)return Gr[t]=s}return t}const Hs="http://www.w3.org/1999/xlink";function Ws(e,t,u,r,n,s=wi(t)){r&&t.startsWith("xlink:")?u==null?e.removeAttributeNS(Hs,t.slice(6,t.length)):e.setAttributeNS(Hs,t,u):u==null||s&&!Fn(u)?e.removeAttribute(t):e.setAttribute(t,s?"":Ct(u)?String(u):u)}function ks(e,t,u,r,n){if(t==="innerHTML"||t==="textContent"){u!=null&&(e[t]=t==="innerHTML"?$s(u):u);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const o=s==="OPTION"?e.getAttribute("value")||"":e.value,c=u==null?e.type==="checkbox"?"on":"":String(u);(o!==c||!("_value"in e))&&(e.value=c),u==null&&e.removeAttribute(t),e._value=u;return}let i=!1;if(u===""||u==null){const o=typeof e[t];o==="boolean"?u=Fn(u):u==null&&o==="string"?(u="",i=!0):o==="number"&&(u=0,i=!0)}try{e[t]=u}catch{}i&&e.removeAttribute(n||t)}function jc(e,t,u,r){e.addEventListener(t,u,r)}function Lc(e,t,u,r){e.removeEventListener(t,u,r)}const Us=Symbol("_vei");function Vc(e,t,u,r,n=null){const s=e[Us]||(e[Us]={}),i=s[t];if(r&&i)i.value=r;else{const[o,c]=Hc(t);if(r){const D=s[t]=Uc(r,n);jc(e,o,D,c)}else i&&(Lc(e,o,i,c),s[t]=void 0)}}const Ks=/(?:Once|Passive|Capture)$/;function Hc(e){let t;if(Ks.test(e)){t={};let r;for(;r=e.match(Ks);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):vt(e.slice(2)),t]}let Jr=0;const Wc=Promise.resolve(),kc=()=>Jr||(Wc.then(()=>Jr=0),Jr=Date.now());function Uc(e,t){const u=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=u.attached)return;ze(Kc(r,u.value),t,5,[r])};return u.value=e,u.attached=kc(),u}function Kc(e,t){if(L(t)){const u=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{u.call(e),e._stopped=!0},t.map(r=>n=>!n._stopped&&r&&r(n))}else return t}const zs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,zc=(e,t,u,r,n,s)=>{const i=n==="svg";t==="class"?Pc(e,r,i):t==="style"?$c(e,u,r):xu(t)?dr(t)||Vc(e,t,u,r,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Gc(e,t,r,i))?(ks(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ws(e,t,r,i,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!de(r))?ks(e,At(t),r,s,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Ws(e,t,r,i))};function Gc(e,t,u,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&zs(t)&&W(u));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return zs(t)&&de(u)?!1:t in e}const Jc=Be({patchProp:zc},Oc);let Gs;function qc(){return Gs||(Gs=qo(Jc))}const Yc=(...e)=>{const t=qc().createApp(...e),{mount:u}=t;return t.mount=r=>{const n=Qc(r);if(!n)return;const s=t._component;!W(s)&&!s.render&&!s.template&&(s.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const i=u(n,!1,Zc(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),i},t};function Zc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Qc(e){return de(e)?document.querySelector(e):e}var qr={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var Js;function Xc(){return Js||(Js=1,function(e){(function(){var t={}.hasOwnProperty;function u(){for(var s="",i=0;i<arguments.length;i++){var o=arguments[i];o&&(s=n(s,r(o)))}return s}function r(s){if(typeof s=="string"||typeof s=="number")return s;if(typeof s!="object")return"";if(Array.isArray(s))return u.apply(null,s);if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]"))return s.toString();var i="";for(var o in s)t.call(s,o)&&s[o]&&(i=n(i,o));return i}function n(s,i){return i?s?s+" "+i:s+i:s}e.exports?(u.default=u,e.exports=u):window.classNames=u})()}(qr)),qr.exports}Xc();function el(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}function tl(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),e.nonce!==void 0&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}var ul=function(){function e(u){var r=this;this._insertTag=function(n){var s;r.tags.length===0?r.insertionPoint?s=r.insertionPoint.nextSibling:r.prepend?s=r.container.firstChild:s=r.before:s=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(n,s),r.tags.push(n)},this.isSpeedy=u.speedy===void 0?!0:u.speedy,this.tags=[],this.ctr=0,this.nonce=u.nonce,this.key=u.key,this.container=u.container,this.prepend=u.prepend,this.insertionPoint=u.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(r){r.forEach(this._insertTag)},t.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(tl(this));var n=this.tags[this.tags.length-1];if(this.isSpeedy){var s=el(n);try{s.insertRule(r,s.cssRules.length)}catch{}}else n.appendChild(document.createTextNode(r));this.ctr++},t.flush=function(){this.tags.forEach(function(r){var n;return(n=r.parentNode)==null?void 0:n.removeChild(r)}),this.tags=[],this.ctr=0},e}(),be="-ms-",Xu="-moz-",X="-webkit-",qs="comm",Yr="rule",Zr="decl",rl="@import",Ys="@keyframes",nl="@layer",sl=Math.abs,er=String.fromCharCode,il=Object.assign;function ol(e,t){return Ee(e,0)^45?(((t<<2^Ee(e,0))<<2^Ee(e,1))<<2^Ee(e,2))<<2^Ee(e,3):0}function Zs(e){return e.trim()}function cl(e,t){return(e=t.exec(e))?e[0]:e}function ee(e,t,u){return e.replace(t,u)}function Qr(e,t){return e.indexOf(t)}function Ee(e,t){return e.charCodeAt(t)|0}function Fu(e,t,u){return e.slice(t,u)}function Qe(e){return e.length}function Xr(e){return e.length}function tr(e,t){return t.push(e),e}function ll(e,t){return e.map(t).join("")}var ur=1,zt=1,Qs=0,Oe=0,pe=0,Gt="";function rr(e,t,u,r,n,s,i){return{value:e,root:t,parent:u,type:r,props:n,children:s,line:ur,column:zt,length:i,return:""}}function Eu(e,t){return il(rr("",null,null,"",null,null,0),e,{length:-e.length},t)}function al(){return pe}function fl(){return pe=Oe>0?Ee(Gt,--Oe):0,zt--,pe===10&&(zt=1,ur--),pe}function $e(){return pe=Oe<Qs?Ee(Gt,Oe++):0,zt++,pe===10&&(zt=1,ur++),pe}function Xe(){return Ee(Gt,Oe)}function nr(){return Oe}function gu(e,t){return Fu(Gt,e,t)}function mu(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Xs(e){return ur=zt=1,Qs=Qe(Gt=e),Oe=0,[]}function ei(e){return Gt="",e}function sr(e){return Zs(gu(Oe-1,en(e===91?e+2:e===40?e+1:e)))}function Dl(e){for(;(pe=Xe())&&pe<33;)$e();return mu(e)>2||mu(pe)>3?"":" "}function dl(e,t){for(;--t&&$e()&&!(pe<48||pe>102||pe>57&&pe<65||pe>70&&pe<97););return gu(e,nr()+(t<6&&Xe()==32&&$e()==32))}function en(e){for(;$e();)switch(pe){case e:return Oe;case 34:case 39:e!==34&&e!==39&&en(pe);break;case 40:e===41&&en(e);break;case 92:$e();break}return Oe}function hl(e,t){for(;$e()&&e+pe!==57;)if(e+pe===84&&Xe()===47)break;return"/*"+gu(t,Oe-1)+"*"+er(e===47?e:$e())}function pl(e){for(;!mu(Xe());)$e();return gu(e,Oe)}function Cl(e){return ei(ir("",null,null,null,[""],e=Xs(e),0,[0],e))}function ir(e,t,u,r,n,s,i,o,c){for(var D=0,a=0,d=i,C=0,F=0,T=0,A=1,I=1,m=1,P=0,R="",x=n,j=s,J=r,k=R;I;)switch(T=P,P=$e()){case 40:if(T!=108&&Ee(k,d-1)==58){Qr(k+=ee(sr(P),"&","&\f"),"&\f")!=-1&&(m=-1);break}case 34:case 39:case 91:k+=sr(P);break;case 9:case 10:case 13:case 32:k+=Dl(T);break;case 92:k+=dl(nr()-1,7);continue;case 47:switch(Xe()){case 42:case 47:tr(Al(hl($e(),nr()),t,u),c);break;default:k+="/"}break;case 123*A:o[D++]=Qe(k)*m;case 125*A:case 59:case 0:switch(P){case 0:case 125:I=0;case 59+a:m==-1&&(k=ee(k,/\f/g,"")),F>0&&Qe(k)-d&&tr(F>32?ui(k+";",r,u,d-1):ui(ee(k," ","")+";",r,u,d-2),c);break;case 59:k+=";";default:if(tr(J=ti(k,t,u,D,a,n,o,R,x=[],j=[],d),s),P===123)if(a===0)ir(k,t,J,J,x,s,d,o,j);else switch(C===99&&Ee(k,3)===110?100:C){case 100:case 108:case 109:case 115:ir(e,J,J,r&&tr(ti(e,J,J,0,0,n,o,R,n,x=[],d),j),n,j,d,o,r?x:j);break;default:ir(k,J,J,J,[""],j,0,o,j)}}D=a=F=0,A=m=1,R=k="",d=i;break;case 58:d=1+Qe(k),F=T;default:if(A<1){if(P==123)--A;else if(P==125&&A++==0&&fl()==125)continue}switch(k+=er(P),P*A){case 38:m=a>0?1:(k+="\f",-1);break;case 44:o[D++]=(Qe(k)-1)*m,m=1;break;case 64:Xe()===45&&(k+=sr($e())),C=Xe(),a=d=Qe(R=k+=pl(nr())),P++;break;case 45:T===45&&Qe(k)==2&&(A=0)}}return s}function ti(e,t,u,r,n,s,i,o,c,D,a){for(var d=n-1,C=n===0?s:[""],F=Xr(C),T=0,A=0,I=0;T<r;++T)for(var m=0,P=Fu(e,d+1,d=sl(A=i[T])),R=e;m<F;++m)(R=Zs(A>0?C[m]+" "+P:ee(P,/&\f/g,C[m])))&&(c[I++]=R);return rr(e,t,u,n===0?Yr:o,c,D,a)}function Al(e,t,u){return rr(e,t,u,qs,er(al()),Fu(e,2,-2),0)}function ui(e,t,u,r){return rr(e,t,u,Zr,Fu(e,0,r),Fu(e,r+1,-1),r)}function Jt(e,t){for(var u="",r=Xr(e),n=0;n<r;n++)u+=t(e[n],n,e,t)||"";return u}function Fl(e,t,u,r){switch(e.type){case nl:if(e.children.length)break;case rl:case Zr:return e.return=e.return||e.value;case qs:return"";case Ys:return e.return=e.value+"{"+Jt(e.children,r)+"}";case Yr:e.value=e.props.join(",")}return Qe(u=Jt(e.children,r))?e.return=e.value+"{"+u+"}":""}function El(e){var t=Xr(e);return function(u,r,n,s){for(var i="",o=0;o<t;o++)i+=e[o](u,r,n,s)||"";return i}}function gl(e){return function(t){t.root||(t=t.return)&&e(t)}}function ml(e){var t=Object.create(null);return function(u){return t[u]===void 0&&(t[u]=e(u)),t[u]}}var bl=function(t,u,r){for(var n=0,s=0;n=s,s=Xe(),n===38&&s===12&&(u[r]=1),!mu(s);)$e();return gu(t,Oe)},yl=function(t,u){var r=-1,n=44;do switch(mu(n)){case 0:n===38&&Xe()===12&&(u[r]=1),t[r]+=bl(Oe-1,u,r);break;case 2:t[r]+=sr(n);break;case 4:if(n===44){t[++r]=Xe()===58?"&\f":"",u[r]=t[r].length;break}default:t[r]+=er(n)}while(n=$e());return t},Bl=function(t,u){return ei(yl(Xs(t),u))},ri=new WeakMap,vl=function(t){if(!(t.type!=="rule"||!t.parent||t.length<1)){for(var u=t.value,r=t.parent,n=t.column===r.column&&t.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(t.props.length===1&&u.charCodeAt(0)!==58&&!ri.get(r))&&!n){ri.set(t,!0);for(var s=[],i=Bl(u,s),o=r.props,c=0,D=0;c<i.length;c++)for(var a=0;a<o.length;a++,D++)t.props[D]=s[c]?i[c].replace(/&\f/g,o[a]):o[a]+" "+i[c]}}},_l=function(t){if(t.type==="decl"){var u=t.value;u.charCodeAt(0)===108&&u.charCodeAt(2)===98&&(t.return="",t.value="")}};function ni(e,t){switch(ol(e,t)){case 5103:return X+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return X+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return X+e+Xu+e+be+e+e;case 6828:case 4268:return X+e+be+e+e;case 6165:return X+e+be+"flex-"+e+e;case 5187:return X+e+ee(e,/(\w+).+(:[^]+)/,X+"box-$1$2"+be+"flex-$1$2")+e;case 5443:return X+e+be+"flex-item-"+ee(e,/flex-|-self/,"")+e;case 4675:return X+e+be+"flex-line-pack"+ee(e,/align-content|flex-|-self/,"")+e;case 5548:return X+e+be+ee(e,"shrink","negative")+e;case 5292:return X+e+be+ee(e,"basis","preferred-size")+e;case 6060:return X+"box-"+ee(e,"-grow","")+X+e+be+ee(e,"grow","positive")+e;case 4554:return X+ee(e,/([^-])(transform)/g,"$1"+X+"$2")+e;case 6187:return ee(ee(ee(e,/(zoom-|grab)/,X+"$1"),/(image-set)/,X+"$1"),e,"")+e;case 5495:case 3959:return ee(e,/(image-set\([^]*)/,X+"$1$`$1");case 4968:return ee(ee(e,/(.+:)(flex-)?(.*)/,X+"box-pack:$3"+be+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+X+e+e;case 4095:case 3583:case 4068:case 2532:return ee(e,/(.+)-inline(.+)/,X+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Qe(e)-1-t>6)switch(Ee(e,t+1)){case 109:if(Ee(e,t+4)!==45)break;case 102:return ee(e,/(.+:)(.+)-([^]+)/,"$1"+X+"$2-$3$1"+Xu+(Ee(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~Qr(e,"stretch")?ni(ee(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(Ee(e,t+1)!==115)break;case 6444:switch(Ee(e,Qe(e)-3-(~Qr(e,"!important")&&10))){case 107:return ee(e,":",":"+X)+e;case 101:return ee(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+X+(Ee(e,14)===45?"inline-":"")+"box$3$1"+X+"$2$3$1"+be+"$2box$3")+e}break;case 5936:switch(Ee(e,t+11)){case 114:return X+e+be+ee(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return X+e+be+ee(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return X+e+be+ee(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return X+e+be+e+e}return e}var xl=function(t,u,r,n){if(t.length>-1&&!t.return)switch(t.type){case Zr:t.return=ni(t.value,t.length);break;case Ys:return Jt([Eu(t,{value:ee(t.value,"@","@"+X)})],n);case Yr:if(t.length)return ll(t.props,function(s){switch(cl(s,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Jt([Eu(t,{props:[ee(s,/:(read-\w+)/,":"+Xu+"$1")]})],n);case"::placeholder":return Jt([Eu(t,{props:[ee(s,/:(plac\w+)/,":"+X+"input-$1")]}),Eu(t,{props:[ee(s,/:(plac\w+)/,":"+Xu+"$1")]}),Eu(t,{props:[ee(s,/:(plac\w+)/,be+"input-$1")]})],n)}return""})}},wl=[xl],Sl=function(t){var u=t.key;if(u==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(A){var I=A.getAttribute("data-emotion");I.indexOf(" ")!==-1&&(document.head.appendChild(A),A.setAttribute("data-s",""))})}var n=t.stylisPlugins||wl,s={},i,o=[];i=t.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+u+' "]'),function(A){for(var I=A.getAttribute("data-emotion").split(" "),m=1;m<I.length;m++)s[I[m]]=!0;o.push(A)});var c,D=[vl,_l];{var a,d=[Fl,gl(function(A){a.insert(A)})],C=El(D.concat(n,d)),F=function(I){return Jt(Cl(I),C)};c=function(I,m,P,R){a=P,F(I?I+"{"+m.styles+"}":m.styles),R&&(T.inserted[m.name]=!0)}}var T={key:u,sheet:new ul({key:u,container:i,nonce:t.nonce,speedy:t.speedy,prepend:t.prepend,insertionPoint:t.insertionPoint}),nonce:t.nonce,inserted:s,registered:{},insert:c};return T.sheet.hydrate(o),T};function Ol(e){for(var t=0,u,r=0,n=e.length;n>=4;++r,n-=4)u=e.charCodeAt(r)&255|(e.charCodeAt(++r)&255)<<8|(e.charCodeAt(++r)&255)<<16|(e.charCodeAt(++r)&255)<<24,u=(u&65535)*1540483477+((u>>>16)*59797<<16),u^=u>>>24,t=(u&65535)*1540483477+((u>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(n){case 3:t^=(e.charCodeAt(r+2)&255)<<16;case 2:t^=(e.charCodeAt(r+1)&255)<<8;case 1:t^=e.charCodeAt(r)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}var Tl={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Pl=/[A-Z]|^ms/g,Nl=/_EMO_([^_]+?)_([^]*?)_EMO_/g,si=function(t){return t.charCodeAt(1)===45},ii=function(t){return t!=null&&typeof t!="boolean"},tn=ml(function(e){return si(e)?e:e.replace(Pl,"-$&").toLowerCase()}),oi=function(t,u){switch(t){case"animation":case"animationName":if(typeof u=="string")return u.replace(Nl,function(r,n,s){return yt={name:n,styles:s,next:yt},n})}return Tl[t]!==1&&!si(t)&&typeof u=="number"&&u!==0?u+"px":u};function or(e,t,u){if(u==null)return"";var r=u;if(r.__emotion_styles!==void 0)return r;switch(typeof u){case"boolean":return"";case"object":{var n=u;if(n.anim===1)return yt={name:n.name,styles:n.styles,next:yt},n.name;var s=u;if(s.styles!==void 0){var i=s.next;if(i!==void 0)for(;i!==void 0;)yt={name:i.name,styles:i.styles,next:yt},i=i.next;var o=s.styles+";";return o}return Il(e,t,u)}}var c=u;if(t==null)return c;var D=t[c];return D!==void 0?D:c}function Il(e,t,u){var r="";if(Array.isArray(u))for(var n=0;n<u.length;n++)r+=or(e,t,u[n])+";";else for(var s in u){var i=u[s];if(typeof i!="object"){var o=i;t!=null&&t[o]!==void 0?r+=s+"{"+t[o]+"}":ii(o)&&(r+=tn(s)+":"+oi(s,o)+";")}else if(Array.isArray(i)&&typeof i[0]=="string"&&(t==null||t[i[0]]===void 0))for(var c=0;c<i.length;c++)ii(i[c])&&(r+=tn(s)+":"+oi(s,i[c])+";");else{var D=or(e,t,i);switch(s){case"animation":case"animationName":{r+=tn(s)+":"+D+";";break}default:r+=s+"{"+D+"}"}}}return r}var ci=/label:\s*([^\s;{]+)\s*(;|$)/g,yt;function un(e,t,u){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var r=!0,n="";yt=void 0;var s=e[0];if(s==null||s.raw===void 0)r=!1,n+=or(u,t,s);else{var i=s;n+=i[0]}for(var o=1;o<e.length;o++)if(n+=or(u,t,e[o]),r){var c=s;n+=c[o]}ci.lastIndex=0;for(var D="",a;(a=ci.exec(n))!==null;)D+="-"+a[1];var d=Ol(n)+D;return{name:d,styles:n,next:yt}}function li(e,t,u){var r="";return u.split(" ").forEach(function(n){e[n]!==void 0?t.push(e[n]+";"):n&&(r+=n+" ")}),r}var Rl=function(t,u,r){var n=t.key+"-"+u.name;t.registered[n]===void 0&&(t.registered[n]=u.styles)},$l=function(t,u,r){Rl(t,u);var n=t.key+"-"+u.name;if(t.inserted[u.name]===void 0){var s=u;do t.insert(u===s?"."+n:"",s,t.sheet,!0),s=s.next;while(s!==void 0)}};function ai(e,t){if(e.inserted[t.name]===void 0)return e.insert("",t,e.sheet,!0)}function fi(e,t,u){var r=[],n=li(e,r,u);return r.length<2?u:n+t(r)}var Ml=function(t){var u=Sl(t);u.sheet.speedy=function(o){this.isSpeedy=o},u.compat=!0;var r=function(){for(var c=arguments.length,D=new Array(c),a=0;a<c;a++)D[a]=arguments[a];var d=un(D,u.registered,void 0);return $l(u,d),u.key+"-"+d.name},n=function(){for(var c=arguments.length,D=new Array(c),a=0;a<c;a++)D[a]=arguments[a];var d=un(D,u.registered),C="animation-"+d.name;return ai(u,{name:d.name,styles:"@keyframes "+C+"{"+d.styles+"}"}),C},s=function(){for(var c=arguments.length,D=new Array(c),a=0;a<c;a++)D[a]=arguments[a];var d=un(D,u.registered);ai(u,d)},i=function(){for(var c=arguments.length,D=new Array(c),a=0;a<c;a++)D[a]=arguments[a];return fi(u.registered,r,jl(D))};return{css:r,cx:i,injectGlobal:s,keyframes:n,hydrate:function(c){c.forEach(function(D){u.inserted[D]=!0})},flush:function(){u.registered={},u.inserted={},u.sheet.flush()},sheet:u.sheet,cache:u,getRegisteredStyles:li.bind(null,u.registered),merge:fi.bind(null,u.registered,r)}},jl=function e(t){for(var u="",r=0;r<t.length;r++){var n=t[r];if(n!=null){var s=void 0;switch(typeof n){case"boolean":break;case"object":{if(Array.isArray(n))s=e(n);else{s="";for(var i in n)n[i]&&i&&(s&&(s+=" "),s+=i)}break}default:s=n}s&&(u&&(u+=" "),u+=s)}}return u};Ml({key:"css"});var bu={exports:{}},Ll=bu.exports,Di;function Vl(){return Di||(Di=1,function(e,t){(function(u,r){r(t)})(Ll,function(u){var r=Object.defineProperty,n=Object.defineProperties,s=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,D=(B,S,N)=>S in B?r(B,S,{enumerable:!0,configurable:!0,writable:!0,value:N}):B[S]=N,a=(B,S)=>{for(var N in S||(S={}))o.call(S,N)&&D(B,N,S[N]);if(i)for(var N of i(S))c.call(S,N)&&D(B,N,S[N]);return B},d=(B,S)=>n(B,s(S)),C=(B,S,N)=>new Promise((q,z)=>{var re=fe=>{try{Y(N.next(fe))}catch(Ve){z(Ve)}},se=fe=>{try{Y(N.throw(fe))}catch(Ve){z(Ve)}},Y=fe=>fe.done?q(fe.value):Promise.resolve(fe.value).then(re,se);Y((N=N.apply(B,S)).next())}),F=(B=>(B[B.NONE=0]="NONE",B[B.LOADING=1]="LOADING",B[B.LOADED=2]="LOADED",B[B.ERROR=3]="ERROR",B))(F||{});class T{constructor(S){this.items={},this.factory=S}getOrCreateItemByURL(S){let N=this.items[S];return N||(N=this.items[S]=this.factory(S)),N}tryGetItemByURL(S){var N;return(N=this.items[S])!=null?N:null}removeItemByURL(S){const N=this.items[S];return N&&(this.items[S]=null,N)}}const A="__RUNTIME_IMPORT__";function I(B,S){var N,q;const z=globalThis,re=(N=z[A])!=null?N:z[A]={};return(q=re[B])!=null?q:re[B]=S()}const m=I("cssCache",()=>new T(B=>({url:B,status:F.NONE,el:null,error:null,reject:null})));function P(B,S,N){const q={handleLoad(){B.removeEventListener("load",q.handleLoad),B.removeEventListener("error",q.handleError),S()},handleError(z){B.removeEventListener("load",q.handleLoad),B.removeEventListener("error",q.handleError),N(z)}};B.addEventListener("load",q.handleLoad),B.addEventListener("error",q.handleError)}function R(B){const S=m.getOrCreateItemByURL(B),{status:N,error:q}=S;return N===F.LOADED?Promise.resolve():N===F.ERROR?Promise.reject(q):N===F.LOADING?new Promise((z,re)=>{const{el:se}=S;P(se,()=>z(),Y=>re(Y.error))}):(S.status=F.LOADING,new Promise((z,re)=>{const se=document.createElement("link");se.rel="stylesheet",se.href=B,P(se,()=>{S.status=F.LOADED,z()},Y=>{const fe=Y.error||new Error(`Load css failed. href=${B}`);S.status=F.ERROR,S.error=fe,re(fe)}),S.el=se,se.setAttribute("data-runtime-import-type","css"),document.head.appendChild(se)}))}function x(B){return Promise.all(B.map(S=>R(S))).then(()=>Promise.resolve()).catch(S=>Promise.reject(S))}const j=I("jsCache",()=>new T(B=>({url:B,status:F.NONE,el:null,error:null,reject:null,exportThing:void 0}))),J=globalThis,{define:k}=J,{keys:Ke}=Object;let It=!1;typeof k<"u"&&!k.runtime_import&&(console.warn("runtime-import should NOT coexist with requiesjs or seajs or any other AMD/CMD loader."),It=!0);const Le=I("pendingItemMap",()=>({})),dt=function(...B){const S=B.pop(),{currentScript:N}=document;if(!N)throw new Error("currentScript is null.");const{src:q}=N,z=Le[q];if(!z)throw new Error(`Can NOT find item, src=${q}`);Le[q]=null;try{let re=B[0]||[],se=null;typeof re=="string"&&(se=re,re=B[1]||[]);const Y=z.exportThing=(()=>{let fe=!1;const Ve={};let l=S(...re.map(f=>f==="exports"?(fe=!0,Ve):J[f]));return!l&&fe&&(l=Ve),l})();se&&(J[se]=Y),Y&&Ke(Y).length===1&&Y.default&&(z.exportThing=Y.default,z.exportThing.default=Y.default)}catch(re){z.status=F.ERROR,re instanceof Error&&(z.error=re),z.reject(re)}},qt=()=>{const{currentScript:B}=document;if(B){const{src:S}=B;if(Le[S])return!0}return!1};["amd","cmd"].forEach(B=>{Object.defineProperty(dt,B,{get:qt})}),dt.runtime_import=!0;function Yt(B,S){if(It)throw new Error("runtime-import UMD mode uses window.define, you should NOT have your own window.define.");J.define||(J.define=dt),Le[B]=S}function vu(B){const S=/legao-comp\/(.*)\/[\d.]+\/web.js$/.exec(B),N=window;if(S&&S.length>0){const q=S[1];N.g_config=N.g_config||{},N.g_config.appKey=q}}function fr(B,S,N){B.status=F.LOADING,vu(S);const{umd:q,crossOrigin:z}=N;return new Promise((re,se)=>{const Y=document.createElement("script");if(Y.src=S,Y.async=!1,Y.crossOrigin=z,q){Y.setAttribute("data-runtime-import-type","javascript-umd"),B.reject=se;const fe=Y.src;Yt(fe,B)}else Y.setAttribute("data-runtime-import-type","javascript");P(Y,()=>{B.status=F.LOADED,B.el=null,re(B.exportThing)},fe=>{const Ve=fe.error||new Error(`Load javascript failed. src=${S}`);B.status=F.ERROR,B.error=Ve,B.el=null,se(Ve)}),B.el=Y,document.body.appendChild(Y)})}function ge(B,S){const N=j.getOrCreateItemByURL(B),{status:q,exportThing:z,error:re}=N;if(q===F.LOADED)return Promise.resolve(z);if(q===F.ERROR)return Promise.reject(re);if(q===F.LOADING){const{el:se}=N;return new Promise((Y,fe)=>{P(se,()=>Y(N.exportThing),Ve=>fe(Ve.error))})}return fr(N,B,S)}function ae(B,S){let N=Promise.resolve();const q=B.length-1,{umd:z}=S;return B.forEach((re,se)=>{const Y=z&&se===q;N=N.then(()=>ge(re,d(a({},S),{umd:Y})))}),N}function te(B){return C(this,null,function*(){const{scripts:S,styles:N}=B;if(N){const{urls:Y}=N;yield x(Y)}const{dependencies:q=[],entry:z,umd:re=!0,crossOrigin:se="anonymous"}=S;if((z?q.concat([z]):q).length)return yield ae(q.concat([z]),{umd:re,crossOrigin:se})})}function ht(B,S){return C(this,null,function*(){const N=S??{};return yield te({scripts:{dependencies:[],entry:B,umd:N.umd,crossOrigin:N.crossOrigin}})})}function Zt(B){return C(this,null,function*(){return yield te({scripts:{dependencies:[],entry:""},styles:{urls:[B]}})})}const pt=te;u.importComponent=te,u.importModule=pt,u.importScript=ht,u.importStyle=Zt,Object.defineProperty(u,Symbol.toStringTag,{value:"Module"})})}(bu,bu.exports)),bu.exports}Vl();var Hl=/[\u1680\u2000-\u200A\u202F\u205F\u3000]/,Wl=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,kl=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/,rn={Space_Separator:Hl,ID_Start:Wl,ID_Continue:kl},he={isSpaceSeparator(e){return typeof e=="string"&&rn.Space_Separator.test(e)},isIdStartChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="$"||e==="_"||rn.ID_Start.test(e))},isIdContinueChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="$"||e==="_"||e==="‌"||e==="‍"||rn.ID_Continue.test(e))},isDigit(e){return typeof e=="string"&&/[0-9]/.test(e)},isHexDigit(e){return typeof e=="string"&&/[0-9A-Fa-f]/.test(e)}};let nn,xe,at,cr,Bt,Ue,Ce,sn,yu;var Ul=function(t,u){nn=String(t),xe="start",at=[],cr=0,Bt=1,Ue=0,Ce=void 0,sn=void 0,yu=void 0;do Ce=Kl(),Jl[xe]();while(Ce.type!=="eof");return typeof u=="function"?on({"":yu},"",u):yu};function on(e,t,u){const r=e[t];if(r!=null&&typeof r=="object")if(Array.isArray(r))for(let n=0;n<r.length;n++){const s=String(n),i=on(r,s,u);i===void 0?delete r[s]:Object.defineProperty(r,s,{value:i,writable:!0,enumerable:!0,configurable:!0})}else for(const n in r){const s=on(r,n,u);s===void 0?delete r[n]:Object.defineProperty(r,n,{value:s,writable:!0,enumerable:!0,configurable:!0})}return u.call(e,t,r)}let U,H,Bu,ft,K;function Kl(){for(U="default",H="",Bu=!1,ft=1;;){K=Dt();const e=di[U]();if(e)return e}}function Dt(){if(nn[cr])return String.fromCodePoint(nn.codePointAt(cr))}function p(){const e=Dt();return e===`
`?(Bt++,Ue=0):e?Ue+=e.length:Ue++,e&&(cr+=e.length),e}const di={default(){switch(K){case"	":case"\v":case"\f":case" ":case" ":case"\uFEFF":case`
`:case"\r":case"\u2028":case"\u2029":p();return;case"/":p(),U="comment";return;case void 0:return p(),ce("eof")}if(he.isSpaceSeparator(K)){p();return}return di[xe]()},comment(){switch(K){case"*":p(),U="multiLineComment";return;case"/":p(),U="singleLineComment";return}throw le(p())},multiLineComment(){switch(K){case"*":p(),U="multiLineCommentAsterisk";return;case void 0:throw le(p())}p()},multiLineCommentAsterisk(){switch(K){case"*":p();return;case"/":p(),U="default";return;case void 0:throw le(p())}p(),U="multiLineComment"},singleLineComment(){switch(K){case`
`:case"\r":case"\u2028":case"\u2029":p(),U="default";return;case void 0:return p(),ce("eof")}p()},value(){switch(K){case"{":case"[":return ce("punctuator",p());case"n":return p(),Pt("ull"),ce("null",null);case"t":return p(),Pt("rue"),ce("boolean",!0);case"f":return p(),Pt("alse"),ce("boolean",!1);case"-":case"+":p()==="-"&&(ft=-1),U="sign";return;case".":H=p(),U="decimalPointLeading";return;case"0":H=p(),U="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":H=p(),U="decimalInteger";return;case"I":return p(),Pt("nfinity"),ce("numeric",1/0);case"N":return p(),Pt("aN"),ce("numeric",NaN);case'"':case"'":Bu=p()==='"',H="",U="string";return}throw le(p())},identifierNameStartEscape(){if(K!=="u")throw le(p());p();const e=cn();switch(e){case"$":case"_":break;default:if(!he.isIdStartChar(e))throw hi();break}H+=e,U="identifierName"},identifierName(){switch(K){case"$":case"_":case"‌":case"‍":H+=p();return;case"\\":p(),U="identifierNameEscape";return}if(he.isIdContinueChar(K)){H+=p();return}return ce("identifier",H)},identifierNameEscape(){if(K!=="u")throw le(p());p();const e=cn();switch(e){case"$":case"_":case"‌":case"‍":break;default:if(!he.isIdContinueChar(e))throw hi();break}H+=e,U="identifierName"},sign(){switch(K){case".":H=p(),U="decimalPointLeading";return;case"0":H=p(),U="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":H=p(),U="decimalInteger";return;case"I":return p(),Pt("nfinity"),ce("numeric",ft*(1/0));case"N":return p(),Pt("aN"),ce("numeric",NaN)}throw le(p())},zero(){switch(K){case".":H+=p(),U="decimalPoint";return;case"e":case"E":H+=p(),U="decimalExponent";return;case"x":case"X":H+=p(),U="hexadecimal";return}return ce("numeric",ft*0)},decimalInteger(){switch(K){case".":H+=p(),U="decimalPoint";return;case"e":case"E":H+=p(),U="decimalExponent";return}if(he.isDigit(K)){H+=p();return}return ce("numeric",ft*Number(H))},decimalPointLeading(){if(he.isDigit(K)){H+=p(),U="decimalFraction";return}throw le(p())},decimalPoint(){switch(K){case"e":case"E":H+=p(),U="decimalExponent";return}if(he.isDigit(K)){H+=p(),U="decimalFraction";return}return ce("numeric",ft*Number(H))},decimalFraction(){switch(K){case"e":case"E":H+=p(),U="decimalExponent";return}if(he.isDigit(K)){H+=p();return}return ce("numeric",ft*Number(H))},decimalExponent(){switch(K){case"+":case"-":H+=p(),U="decimalExponentSign";return}if(he.isDigit(K)){H+=p(),U="decimalExponentInteger";return}throw le(p())},decimalExponentSign(){if(he.isDigit(K)){H+=p(),U="decimalExponentInteger";return}throw le(p())},decimalExponentInteger(){if(he.isDigit(K)){H+=p();return}return ce("numeric",ft*Number(H))},hexadecimal(){if(he.isHexDigit(K)){H+=p(),U="hexadecimalInteger";return}throw le(p())},hexadecimalInteger(){if(he.isHexDigit(K)){H+=p();return}return ce("numeric",ft*Number(H))},string(){switch(K){case"\\":p(),H+=zl();return;case'"':if(Bu)return p(),ce("string",H);H+=p();return;case"'":if(!Bu)return p(),ce("string",H);H+=p();return;case`
`:case"\r":throw le(p());case"\u2028":case"\u2029":ql(K);break;case void 0:throw le(p())}H+=p()},start(){switch(K){case"{":case"[":return ce("punctuator",p())}U="value"},beforePropertyName(){switch(K){case"$":case"_":H=p(),U="identifierName";return;case"\\":p(),U="identifierNameStartEscape";return;case"}":return ce("punctuator",p());case'"':case"'":Bu=p()==='"',U="string";return}if(he.isIdStartChar(K)){H+=p(),U="identifierName";return}throw le(p())},afterPropertyName(){if(K===":")return ce("punctuator",p());throw le(p())},beforePropertyValue(){U="value"},afterPropertyValue(){switch(K){case",":case"}":return ce("punctuator",p())}throw le(p())},beforeArrayValue(){if(K==="]")return ce("punctuator",p());U="value"},afterArrayValue(){switch(K){case",":case"]":return ce("punctuator",p())}throw le(p())},end(){throw le(p())}};function ce(e,t){return{type:e,value:t,line:Bt,column:Ue}}function Pt(e){for(const t of e){if(Dt()!==t)throw le(p());p()}}function zl(){switch(Dt()){case"b":return p(),"\b";case"f":return p(),"\f";case"n":return p(),`
`;case"r":return p(),"\r";case"t":return p(),"	";case"v":return p(),"\v";case"0":if(p(),he.isDigit(Dt()))throw le(p());return"\0";case"x":return p(),Gl();case"u":return p(),cn();case`
`:case"\u2028":case"\u2029":return p(),"";case"\r":return p(),Dt()===`
`&&p(),"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":throw le(p());case void 0:throw le(p())}return p()}function Gl(){let e="",t=Dt();if(!he.isHexDigit(t)||(e+=p(),t=Dt(),!he.isHexDigit(t)))throw le(p());return e+=p(),String.fromCodePoint(parseInt(e,16))}function cn(){let e="",t=4;for(;t-- >0;){const u=Dt();if(!he.isHexDigit(u))throw le(p());e+=p()}return String.fromCodePoint(parseInt(e,16))}const Jl={start(){if(Ce.type==="eof")throw Nt();ln()},beforePropertyName(){switch(Ce.type){case"identifier":case"string":sn=Ce.value,xe="afterPropertyName";return;case"punctuator":lr();return;case"eof":throw Nt()}},afterPropertyName(){if(Ce.type==="eof")throw Nt();xe="beforePropertyValue"},beforePropertyValue(){if(Ce.type==="eof")throw Nt();ln()},beforeArrayValue(){if(Ce.type==="eof")throw Nt();if(Ce.type==="punctuator"&&Ce.value==="]"){lr();return}ln()},afterPropertyValue(){if(Ce.type==="eof")throw Nt();switch(Ce.value){case",":xe="beforePropertyName";return;case"}":lr()}},afterArrayValue(){if(Ce.type==="eof")throw Nt();switch(Ce.value){case",":xe="beforeArrayValue";return;case"]":lr()}},end(){}};function ln(){let e;switch(Ce.type){case"punctuator":switch(Ce.value){case"{":e={};break;case"[":e=[];break}break;case"null":case"boolean":case"numeric":case"string":e=Ce.value;break}if(yu===void 0)yu=e;else{const t=at[at.length-1];Array.isArray(t)?t.push(e):Object.defineProperty(t,sn,{value:e,writable:!0,enumerable:!0,configurable:!0})}if(e!==null&&typeof e=="object")at.push(e),Array.isArray(e)?xe="beforeArrayValue":xe="beforePropertyName";else{const t=at[at.length-1];t==null?xe="end":Array.isArray(t)?xe="afterArrayValue":xe="afterPropertyValue"}}function lr(){at.pop();const e=at[at.length-1];e==null?xe="end":Array.isArray(e)?xe="afterArrayValue":xe="afterPropertyValue"}function le(e){return ar(e===void 0?`JSON5: invalid end of input at ${Bt}:${Ue}`:`JSON5: invalid character '${pi(e)}' at ${Bt}:${Ue}`)}function Nt(){return ar(`JSON5: invalid end of input at ${Bt}:${Ue}`)}function hi(){return Ue-=5,ar(`JSON5: invalid identifier character at ${Bt}:${Ue}`)}function ql(e){console.warn(`JSON5: '${pi(e)}' in strings is not valid ECMAScript; consider escaping`)}function pi(e){const t={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(t[e])return t[e];if(e<" "){const u=e.charCodeAt(0).toString(16);return"\\x"+("00"+u).substring(u.length)}return e}function ar(e){const t=new SyntaxError(e);return t.lineNumber=Bt,t.columnNumber=Ue,t}var Yl=function(t,u,r){const n=[];let s="",i,o,c="",D;if(u!=null&&typeof u=="object"&&!Array.isArray(u)&&(r=u.space,D=u.quote,u=u.replacer),typeof u=="function")o=u;else if(Array.isArray(u)){i=[];for(const A of u){let I;typeof A=="string"?I=A:(typeof A=="number"||A instanceof String||A instanceof Number)&&(I=String(A)),I!==void 0&&i.indexOf(I)<0&&i.push(I)}}return r instanceof Number?r=Number(r):r instanceof String&&(r=String(r)),typeof r=="number"?r>0&&(r=Math.min(10,Math.floor(r)),c="          ".substr(0,r)):typeof r=="string"&&(c=r.substr(0,10)),a("",{"":t});function a(A,I){let m=I[A];switch(m!=null&&(typeof m.toJSON5=="function"?m=m.toJSON5(A):typeof m.toJSON=="function"&&(m=m.toJSON(A))),o&&(m=o.call(I,A,m)),m instanceof Number?m=Number(m):m instanceof String?m=String(m):m instanceof Boolean&&(m=m.valueOf()),m){case null:return"null";case!0:return"true";case!1:return"false"}if(typeof m=="string")return d(m);if(typeof m=="number")return String(m);if(typeof m=="object")return Array.isArray(m)?T(m):C(m)}function d(A){const I={"'":.1,'"':.2},m={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};let P="";for(let x=0;x<A.length;x++){const j=A[x];switch(j){case"'":case'"':I[j]++,P+=j;continue;case"\0":if(he.isDigit(A[x+1])){P+="\\x00";continue}}if(m[j]){P+=m[j];continue}if(j<" "){let J=j.charCodeAt(0).toString(16);P+="\\x"+("00"+J).substring(J.length);continue}P+=j}const R=D||Object.keys(I).reduce((x,j)=>I[x]<I[j]?x:j);return P=P.replace(new RegExp(R,"g"),m[R]),R+P+R}function C(A){if(n.indexOf(A)>=0)throw TypeError("Converting circular structure to JSON5");n.push(A);let I=s;s=s+c;let m=i||Object.keys(A),P=[];for(const x of m){const j=a(x,A);if(j!==void 0){let J=F(x)+":";c!==""&&(J+=" "),J+=j,P.push(J)}}let R;if(P.length===0)R="{}";else{let x;if(c==="")x=P.join(","),R="{"+x+"}";else{let j=`,
`+s;x=P.join(j),R=`{
`+s+x+`,
`+I+"}"}}return n.pop(),s=I,R}function F(A){if(A.length===0)return d(A);const I=String.fromCodePoint(A.codePointAt(0));if(!he.isIdStartChar(I))return d(A);for(let m=I.length;m<A.length;m++)if(!he.isIdContinueChar(String.fromCodePoint(A.codePointAt(m))))return d(A);return A}function T(A){if(n.indexOf(A)>=0)throw TypeError("Converting circular structure to JSON5");n.push(A);let I=s;s=s+c;let m=[];for(let R=0;R<A.length;R++){const x=a(String(R),A);m.push(x!==void 0?x:"null")}let P;if(m.length===0)P="[]";else if(c==="")P="["+m.join(",")+"]";else{let R=`,
`+s,x=m.join(R);P=`[
`+s+x+`,
`+I+"]"}return n.pop(),s=I,P}},Zl={parse:Ul,stringify:Yl};const Ql=(e,t)=>{const u=e.__vccOpts||e;for(const[r,n]of t)u[r]=n;return u},Xl={name:"rtf",props:{vueState:{type:Object,required:!0},data:{type:Object,required:!0}},setup(e){const t=nu({amcode:"",amname:"",amtype:"",faultdesc:"",applydatamap:{},bujianid:"",iscreateapply:!1,buttonname:"生成维修申请",billcode:"",repairapplyid:"",type:"",isLoading:!1,options:[],parseData:[],iszhankai:!1}),u=()=>{try{const a=e.vueState.data.allText,d=atob(a),C=new Uint8Array(d.length);for(let A=0;A<d.length;A++)C[A]=d.charCodeAt(A);const F=new TextDecoder("utf-8").decode(C),T=Zl.parse(F);t.parseData=T,T.length<=3?(t.options=T,t.iszhankai=!1):(t.options=[T[0],T[1],T[2]],t.parseData.shift(),t.parseData.shift(),t.parseData.shift(),t.iszhankai=!0)}catch(a){console.error("解析JSON数据时出错：",a)}};Qn(()=>{u()});const r=async(a,d)=>{if(t.iscreateapply||t.isLoading)return;t.isLoading=!0,t.buttonname="正在生成维修申请";const F=await(await fetch("/api/eam/eampm/v1.0/rep/repair/repairapply/createRepairApplyDuellm",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t.applydatamap)})).json();F.resultType=="SUCCESS"?(t.buttonname="已生成维修申请",t.iscreateapply=!0,t.billcode=F.appendData.billcode,t.repairapplyid=F.appendData.id):(t.buttonname="生成维修申请",t.iscreateapply=!1)},n=a=>{var d=a.amworksheetid,C=a.amorgid,F=a.activetype,T="/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid="+d+"&status=wf&runtime=true&styleid=cfb320f6-c5f8-e29a-994c-417a7f0bb911&&runtime=true&fdim="+F+"&sdim="+C+"&mapActiveTypeId=&liststyle=9a1c667d-2c4c-4071-e77f-5ae1a28b81cc&sceneid=;&formState=;styletype:worksheet";D(d,"维修工单",T)},s=()=>{var a=t.repairapplyid,d="/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid="+a+"&status=view&runtime=true&styleid=b8894174-be37-b305-7be5-83743a29babf";D(a,"维修申请",d)},i=()=>{t.iszhankai=!1;var a=t.parseData.length,d=3;if(a>3)for(var C=0;C<d;C++)t.options.push(t.parseData[0]),t.parseData.shift();else t.options=t.options.concat(t.parseData),t.parseData=[];t.parseData.length>0&&(t.iszhankai=!0)},o=()=>{try{return window.parent&&window.parent.gspframeworkAdapterService?window.parent:window.top&&window.top.gspframeworkAdapterService?window.top:window}catch{return window}},c=a=>{const d=new RegExp("(^|&)"+a+"=([^&]*)(&|$)","i");let C;return window.location.search&&(C=window.location.search.substr(1).match(d)),C!=null||window.location.hash&&(C=window.location.hash.substr(3).match(d),C!=null)?unescape(C[2]):""},D=(a,d,C)=>{const F=o();if(F){F.allgspfuncs||F.gspframeworkAdapterService.funcSvc.getAllCachedFuncs();const T=c("pfuncid")||c("funcId")||c("funcid")||"49855bdc-0230-f667-2a3e-7a5710b18168",A="funcid="+a+"&pfuncid="+T;if(C.indexOf("?")===-1?C+="?"+A:C+="&"+A,F.gspframeworkAdapterService)return F.gspframeworkAdapterService.appSvc.getFeb().post("farrisapp-click",{FuncName:d,active:!1,appType:"menu",code:a,tabId:a,funcId:T,id:a,su:"views",isjquery:!0,reload:void 0,sessionid:localStorage.session,src:C,url:C}),a}};return{...to(t),apply:r,view:s,adddata:i,jumptows:n}}},ea={style:{margin:"10px"}},ta=["onClick"],ua={key:0,style:{"margin-top":"5px"}},ra={key:1,style:{"margin-top":"5px"}},na={key:2,style:{"margin-top":"5px"}},sa={key:3,style:{"margin-top":"5px"}};function ia(e,t,u,r,n,s){return ct(),mt("div",null,[(ct(!0),mt(qe,null,No(e.options,i=>(ct(),mt("div",ea,[hu("div",null,[t[1]||(t[1]=hu("span",null,"维修工单编号：",-1)),t[2]||(t[2]=Ss()),hu("span",{style:{color:"blue",cursor:"pointer"},onClick:o=>r.jumptows(i)},Mt(i.billcode),9,ta)]),i.isamcard=="1"?(ct(),mt("div",ua,"资产名称："+Mt(i.amcardname),1)):pu("",!0),i.isamcard=="0"?(ct(),mt("div",ra,"资产位置："+Mt(i.posname),1)):pu("",!0),i.defectdesc!=""?(ct(),mt("div",na,"故障描述："+Mt(i.defectdesc),1)):pu("",!0),i.defectfinddate!=""?(ct(),mt("div",sa,"缺陷发现时间 "+Mt(i.defectfinddate),1)):pu("",!0)]))),256)),e.iszhankai?(ct(),mt("div",{key:0,onClick:t[0]||(t[0]=i=>r.adddata()),style:{display:"flex","justify-content":"center",margin:"5px","font-size":"16px",cursor:"pointer"}}," 查看更多 ")):pu("",!0)])}const oa=Ql(Xl,[["render",ia],["__scopeId","data-v-0d055c7d"]]);function ca(e){return JSON.parse(JSON.stringify(e))}class la{async initialize(){console.log("ExampleWidgetAPI initialized.")}async cleanup(){console.log("ExampleWidgetAPI cleanup.")}createWidget(){return new aa}}class aa{constructor(){Dr(this,"vueState_",nu({options:{mode:"full",implOptions:{data:{}}},data:{}}));Dr(this,"vueApp_");Dr(this,"isMounted_",!1);const t=this.vueState_;this.vueApp_=Yc({setup(){const u=t;return console.log("constuct"),au(()=>u.options,r=>{console.log("ExampleWidget options updated.",r)}),au(()=>u.data,r=>{console.log("ExampleWidget data updated.",r)}),{state:u}},render(){return console.log("render data",this.state.data),_c(oa,{vueState:t})}})}namespace(){return"sys"}name(){return"rtf"}options(){return this.vueState_.options}updateOptions(t){this.vueState_.options={...this.options(),...ca(t)}}data(){return console.log(this.options().implOptions.data),this.vueState_.data}updateData(t){console.log("ExampleWidget data updating.",t),this.vueState_.data=t,console.log("ExampleWidget data updated.",this.vueState_)}async mount(t){if(this.isMounted_)throw new Error("ExampleWidget already mounted.");this.vueApp_.mount(t),this.isMounted_=!0}async unmount(){if(!this.isMounted_)throw new Error("ExampleWidget is NOT mounted.");this.vueApp_.unmount(),this.isMounted_=!1}async rerender(){}async dispose(){this.isMounted_&&this.unmount()}addEventListener(t){return()=>{}}}const an=new la;function fa(){return an.initialize()}function Da(){return an.cleanup()}function da(){return an.createWidget()}we.cleanup=Da,we.createWidget=da,we.initialize=fa,Object.defineProperty(we,Symbol.toStringTag,{value:"Module"})});
