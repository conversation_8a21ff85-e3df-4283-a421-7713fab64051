(function(Te,we){typeof exports=="object"&&typeof module<"u"?we(exports):typeof define=="function"&&define.amd?define(["exports"],we):(Te=typeof globalThis<"u"?globalThis:Te||self,we(Te.RTFWidget={}))})(this,function(Te){"use strict";var A0=Object.defineProperty;var F0=(Te,we,ne)=>we in Te?A0(Te,we,{enumerable:!0,configurable:!0,writable:!0,value:ne}):Te[we]=ne;var _n=(Te,we,ne)=>F0(Te,typeof we!="symbol"?we+"":we,ne);/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function we(e){const t=Object.create(null);for(const u of e.split(","))t[u]=1;return u=>u in t}const ne={},Gt=[],ze=()=>{},bo=()=>!1,Hu=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),xn=e=>e.startsWith("onUpdate:"),be=Object.assign,Sn=(e,t)=>{const u=e.indexOf(t);u>-1&&e.splice(u,1)},vo=Object.prototype.hasOwnProperty,ie=(e,t)=>vo.call(e,t),K=Array.isArray,Jt=e=>Vu(e)==="[object Map]",Mr=e=>Vu(e)==="[object Set]",z=e=>typeof e=="function",ge=e=>typeof e=="string",Bt=e=>typeof e=="symbol",he=e=>e!==null&&typeof e=="object",$r=e=>(he(e)||z(e))&&z(e.then)&&z(e.catch),Lr=Object.prototype.toString,Vu=e=>Lr.call(e),Bo=e=>Vu(e).slice(8,-1),jr=e=>Vu(e)==="[object Object]",Rn=e=>ge(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,fu=we(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Uu=e=>{const t=Object.create(null);return u=>t[u]||(t[u]=e(u))},wo=/-(\w)/g,wt=Uu(e=>e.replace(wo,(t,u)=>u?u.toUpperCase():"")),_o=/\B([A-Z])/g,$t=Uu(e=>e.replace(_o,"-$1").toLowerCase()),kr=Uu(e=>e.charAt(0).toUpperCase()+e.slice(1)),On=Uu(e=>e?`on${kr(e)}`:""),_t=(e,t)=>!Object.is(e,t),Pn=(e,...t)=>{for(let u=0;u<e.length;u++)e[u](...t)},Hr=(e,t,u,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:u})},xo=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Vr;const Wu=()=>Vr||(Vr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Tn(e){if(K(e)){const t={};for(let u=0;u<e.length;u++){const n=e[u],r=ge(n)?Po(n):Tn(n);if(r)for(const s in r)t[s]=r[s]}return t}else if(ge(e)||he(e))return e}const So=/;(?![^(]*\))/g,Ro=/:([^]+)/,Oo=/\/\*[^]*?\*\//g;function Po(e){const t={};return e.replace(Oo,"").split(So).forEach(u=>{if(u){const n=u.split(Ro);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Nn(e){let t="";if(ge(e))t=e;else if(K(e))for(let u=0;u<e.length;u++){const n=Nn(e[u]);n&&(t+=n+" ")}else if(he(e))for(const u in e)e[u]&&(t+=u+" ");return t.trim()}const To=we("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Ur(e){return!!e||e===""}const Wr=e=>!!(e&&e.__v_isRef===!0),Lt=e=>ge(e)?e:e==null?"":K(e)||he(e)&&(e.toString===Lr||!z(e.toString))?Wr(e)?Lt(e.value):JSON.stringify(e,Kr,2):String(e),Kr=(e,t)=>Wr(t)?Kr(e,t.value):Jt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((u,[n,r],s)=>(u[In(n,s)+" =>"]=r,u),{})}:Mr(t)?{[`Set(${t.size})`]:[...t.values()].map(u=>In(u))}:Bt(t)?In(t):he(t)&&!K(t)&&!jr(t)?String(t):t,In=(e,t="")=>{var u;return Bt(e)?`Symbol(${(u=e.description)!=null?u:t})`:e};var No={NODE_ENV:"production"};let Me;class Io{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Me,!t&&Me&&(this.index=(Me.scopes||(Me.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,u;if(this.scopes)for(t=0,u=this.scopes.length;t<u;t++)this.scopes[t].pause();for(t=0,u=this.effects.length;t<u;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,u;if(this.scopes)for(t=0,u=this.scopes.length;t<u;t++)this.scopes[t].resume();for(t=0,u=this.effects.length;t<u;t++)this.effects[t].resume()}}run(t){if(this._active){const u=Me;try{return Me=this,t()}finally{Me=u}}}on(){Me=this}off(){Me=this.parent}stop(t){if(this._active){this._active=!1;let u,n;for(u=0,n=this.effects.length;u<n;u++)this.effects[u].stop();for(this.effects.length=0,u=0,n=this.cleanups.length;u<n;u++)this.cleanups[u]();if(this.cleanups.length=0,this.scopes){for(u=0,n=this.scopes.length;u<n;u++)this.scopes[u].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Mo(){return Me}let ae;const Mn=new WeakSet;class qr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Me&&Me.active&&Me.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Mn.has(this)&&(Mn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Gr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Xr(this),Jr(this);const t=ae,u=Ge;ae=this,Ge=!0;try{return this.fn()}finally{Qr(this),ae=t,Ge=u,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)kn(t);this.deps=this.depsTail=void 0,Xr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Mn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){jn(this)&&this.run()}get dirty(){return jn(this)}}let zr=0,du,Du;function Gr(e,t=!1){if(e.flags|=8,t){e.next=Du,Du=e;return}e.next=du,du=e}function $n(){zr++}function Ln(){if(--zr>0)return;if(Du){let t=Du;for(Du=void 0;t;){const u=t.next;t.next=void 0,t.flags&=-9,t=u}}let e;for(;du;){let t=du;for(du=void 0;t;){const u=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=u}}if(e)throw e}function Jr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Qr(e){let t,u=e.depsTail,n=u;for(;n;){const r=n.prevDep;n.version===-1?(n===u&&(u=r),kn(n),$o(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=r}e.deps=t,e.depsTail=u}function jn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Yr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Yr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===hu))return;e.globalVersion=hu;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!jn(e)){e.flags&=-3;return}const u=ae,n=Ge;ae=e,Ge=!0;try{Jr(e);const r=e.fn(e._value);(t.version===0||_t(r,e._value))&&(e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ae=u,Ge=n,Qr(e),e.flags&=-3}}function kn(e,t=!1){const{dep:u,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),u.subs===e&&(u.subs=n,!n&&u.computed)){u.computed.flags&=-5;for(let s=u.computed.deps;s;s=s.nextDep)kn(s,!0)}!t&&!--u.sc&&u.map&&u.map.delete(u.key)}function $o(e){const{prevDep:t,nextDep:u}=e;t&&(t.nextDep=u,e.prevDep=void 0),u&&(u.prevDep=t,e.nextDep=void 0)}let Ge=!0;const Zr=[];function ht(){Zr.push(Ge),Ge=!1}function pt(){const e=Zr.pop();Ge=e===void 0?!0:e}function Xr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const u=ae;ae=void 0;try{t()}finally{ae=u}}}let hu=0;class Lo{constructor(t,u){this.sub=t,this.dep=u,this.version=u.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Hn{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ae||!Ge||ae===this.computed)return;let u=this.activeLink;if(u===void 0||u.sub!==ae)u=this.activeLink=new Lo(ae,this),ae.deps?(u.prevDep=ae.depsTail,ae.depsTail.nextDep=u,ae.depsTail=u):ae.deps=ae.depsTail=u,es(u);else if(u.version===-1&&(u.version=this.version,u.nextDep)){const n=u.nextDep;n.prevDep=u.prevDep,u.prevDep&&(u.prevDep.nextDep=n),u.prevDep=ae.depsTail,u.nextDep=void 0,ae.depsTail.nextDep=u,ae.depsTail=u,ae.deps===u&&(ae.deps=n)}return u}trigger(t){this.version++,hu++,this.notify(t)}notify(t){$n();try{No.NODE_ENV;for(let u=this.subs;u;u=u.prevSub)u.sub.notify()&&u.sub.dep.notify()}finally{Ln()}}}function es(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)es(n)}const u=e.dep.subs;u!==e&&(e.prevSub=u,u&&(u.nextSub=e)),e.dep.subs=e}}const Ku=new WeakMap,jt=Symbol(""),Vn=Symbol(""),pu=Symbol("");function ve(e,t,u){if(Ge&&ae){let n=Ku.get(e);n||Ku.set(e,n=new Map);let r=n.get(u);r||(n.set(u,r=new Hn),r.map=n,r.key=u),r.track()}}function gt(e,t,u,n,r,s){const i=Ku.get(e);if(!i){hu++;return}const c=o=>{o&&o.trigger()};if($n(),t==="clear")i.forEach(c);else{const o=K(e),d=o&&Rn(u);if(o&&u==="length"){const l=Number(n);i.forEach((f,p)=>{(p==="length"||p===pu||!Bt(p)&&p>=l)&&c(f)})}else switch((u!==void 0||i.has(void 0))&&c(i.get(u)),d&&c(i.get(pu)),t){case"add":o?d&&c(i.get("length")):(c(i.get(jt)),Jt(e)&&c(i.get(Vn)));break;case"delete":o||(c(i.get(jt)),Jt(e)&&c(i.get(Vn)));break;case"set":Jt(e)&&c(i.get(jt));break}}Ln()}function jo(e,t){const u=Ku.get(e);return u&&u.get(t)}function Qt(e){const t=ue(e);return t===e?t:(ve(t,"iterate",pu),Je(e)?t:t.map(_e))}function Un(e){return ve(e=ue(e),"iterate",pu),e}const ko={__proto__:null,[Symbol.iterator](){return Wn(this,Symbol.iterator,_e)},concat(...e){return Qt(this).concat(...e.map(t=>K(t)?Qt(t):t))},entries(){return Wn(this,"entries",e=>(e[1]=_e(e[1]),e))},every(e,t){return Ct(this,"every",e,t,void 0,arguments)},filter(e,t){return Ct(this,"filter",e,t,u=>u.map(_e),arguments)},find(e,t){return Ct(this,"find",e,t,_e,arguments)},findIndex(e,t){return Ct(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ct(this,"findLast",e,t,_e,arguments)},findLastIndex(e,t){return Ct(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ct(this,"forEach",e,t,void 0,arguments)},includes(...e){return Kn(this,"includes",e)},indexOf(...e){return Kn(this,"indexOf",e)},join(e){return Qt(this).join(e)},lastIndexOf(...e){return Kn(this,"lastIndexOf",e)},map(e,t){return Ct(this,"map",e,t,void 0,arguments)},pop(){return gu(this,"pop")},push(...e){return gu(this,"push",e)},reduce(e,...t){return ts(this,"reduce",e,t)},reduceRight(e,...t){return ts(this,"reduceRight",e,t)},shift(){return gu(this,"shift")},some(e,t){return Ct(this,"some",e,t,void 0,arguments)},splice(...e){return gu(this,"splice",e)},toReversed(){return Qt(this).toReversed()},toSorted(e){return Qt(this).toSorted(e)},toSpliced(...e){return Qt(this).toSpliced(...e)},unshift(...e){return gu(this,"unshift",e)},values(){return Wn(this,"values",_e)}};function Wn(e,t,u){const n=Un(e),r=n[t]();return n!==e&&!Je(e)&&(r._next=r.next,r.next=()=>{const s=r._next();return s.value&&(s.value=u(s.value)),s}),r}const Ho=Array.prototype;function Ct(e,t,u,n,r,s){const i=Un(e),c=i!==e&&!Je(e),o=i[t];if(o!==Ho[t]){const f=o.apply(e,s);return c?_e(f):f}let d=u;i!==e&&(c?d=function(f,p){return u.call(this,_e(f),p,e)}:u.length>2&&(d=function(f,p){return u.call(this,f,p,e)}));const l=o.call(i,d,n);return c&&r?r(l):l}function ts(e,t,u,n){const r=Un(e);let s=u;return r!==e&&(Je(e)?u.length>3&&(s=function(i,c,o){return u.call(this,i,c,o,e)}):s=function(i,c,o){return u.call(this,i,_e(c),o,e)}),r[t](s,...n)}function Kn(e,t,u){const n=ue(e);ve(n,"iterate",pu);const r=n[t](...u);return(r===-1||r===!1)&&zn(u[0])?(u[0]=ue(u[0]),n[t](...u)):r}function gu(e,t,u=[]){ht(),$n();const n=ue(e)[t].apply(e,u);return Ln(),pt(),n}const Vo=we("__proto__,__v_isRef,__isVue"),us=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Bt));function Uo(e){Bt(e)||(e=String(e));const t=ue(this);return ve(t,"has",e),t.hasOwnProperty(e)}class ns{constructor(t=!1,u=!1){this._isReadonly=t,this._isShallow=u}get(t,u,n){if(u==="__v_skip")return t.__v_skip;const r=this._isReadonly,s=this._isShallow;if(u==="__v_isReactive")return!r;if(u==="__v_isReadonly")return r;if(u==="__v_isShallow")return s;if(u==="__v_raw")return n===(r?s?ls:cs:s?os:is).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const i=K(t);if(!r){let o;if(i&&(o=ko[u]))return o;if(u==="hasOwnProperty")return Uo}const c=Reflect.get(t,u,Ee(t)?t:n);return(Bt(u)?us.has(u):Vo(u))||(r||ve(t,"get",u),s)?c:Ee(c)?i&&Rn(u)?c:c.value:he(c)?r?fs(c):Yt(c):c}}class rs extends ns{constructor(t=!1){super(!1,t)}set(t,u,n,r){let s=t[u];if(!this._isShallow){const o=kt(s);if(!Je(n)&&!kt(n)&&(s=ue(s),n=ue(n)),!K(t)&&Ee(s)&&!Ee(n))return o?!1:(s.value=n,!0)}const i=K(t)&&Rn(u)?Number(u)<t.length:ie(t,u),c=Reflect.set(t,u,n,Ee(t)?t:r);return t===ue(r)&&(i?_t(n,s)&&gt(t,"set",u,n):gt(t,"add",u,n)),c}deleteProperty(t,u){const n=ie(t,u);t[u];const r=Reflect.deleteProperty(t,u);return r&&n&&gt(t,"delete",u,void 0),r}has(t,u){const n=Reflect.has(t,u);return(!Bt(u)||!us.has(u))&&ve(t,"has",u),n}ownKeys(t){return ve(t,"iterate",K(t)?"length":jt),Reflect.ownKeys(t)}}class ss extends ns{constructor(t=!1){super(!0,t)}set(t,u){return!0}deleteProperty(t,u){return!0}}const Wo=new rs,Ko=new ss,qo=new rs(!0),zo=new ss(!0),qn=e=>e,qu=e=>Reflect.getPrototypeOf(e);function Go(e,t,u){return function(...n){const r=this.__v_raw,s=ue(r),i=Jt(s),c=e==="entries"||e===Symbol.iterator&&i,o=e==="keys"&&i,d=r[e](...n),l=u?qn:t?Gn:_e;return!t&&ve(s,"iterate",o?Vn:jt),{next(){const{value:f,done:p}=d.next();return p?{value:f,done:p}:{value:c?[l(f[0]),l(f[1])]:l(f),done:p}},[Symbol.iterator](){return this}}}}function zu(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Jo(e,t){const u={get(r){const s=this.__v_raw,i=ue(s),c=ue(r);e||(_t(r,c)&&ve(i,"get",r),ve(i,"get",c));const{has:o}=qu(i),d=t?qn:e?Gn:_e;if(o.call(i,r))return d(s.get(r));if(o.call(i,c))return d(s.get(c));s!==i&&s.get(r)},get size(){const r=this.__v_raw;return!e&&ve(ue(r),"iterate",jt),Reflect.get(r,"size",r)},has(r){const s=this.__v_raw,i=ue(s),c=ue(r);return e||(_t(r,c)&&ve(i,"has",r),ve(i,"has",c)),r===c?s.has(r):s.has(r)||s.has(c)},forEach(r,s){const i=this,c=i.__v_raw,o=ue(c),d=t?qn:e?Gn:_e;return!e&&ve(o,"iterate",jt),c.forEach((l,f)=>r.call(s,d(l),d(f),i))}};return be(u,e?{add:zu("add"),set:zu("set"),delete:zu("delete"),clear:zu("clear")}:{add(r){!t&&!Je(r)&&!kt(r)&&(r=ue(r));const s=ue(this);return qu(s).has.call(s,r)||(s.add(r),gt(s,"add",r,r)),this},set(r,s){!t&&!Je(s)&&!kt(s)&&(s=ue(s));const i=ue(this),{has:c,get:o}=qu(i);let d=c.call(i,r);d||(r=ue(r),d=c.call(i,r));const l=o.call(i,r);return i.set(r,s),d?_t(s,l)&&gt(i,"set",r,s):gt(i,"add",r,s),this},delete(r){const s=ue(this),{has:i,get:c}=qu(s);let o=i.call(s,r);o||(r=ue(r),o=i.call(s,r)),c&&c.call(s,r);const d=s.delete(r);return o&&gt(s,"delete",r,void 0),d},clear(){const r=ue(this),s=r.size!==0,i=r.clear();return s&&gt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{u[r]=Go(r,e,t)}),u}function Gu(e,t){const u=Jo(e,t);return(n,r,s)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?n:Reflect.get(ie(u,r)&&r in n?u:n,r,s)}const Qo={get:Gu(!1,!1)},Yo={get:Gu(!1,!0)},Zo={get:Gu(!0,!1)},Xo={get:Gu(!0,!0)},is=new WeakMap,os=new WeakMap,cs=new WeakMap,ls=new WeakMap;function ec(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function tc(e){return e.__v_skip||!Object.isExtensible(e)?0:ec(Bo(e))}function Yt(e){return kt(e)?e:Qu(e,!1,Wo,Qo,is)}function as(e){return Qu(e,!1,qo,Yo,os)}function fs(e){return Qu(e,!0,Ko,Zo,cs)}function Ju(e){return Qu(e,!0,zo,Xo,ls)}function Qu(e,t,u,n,r){if(!he(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=r.get(e);if(s)return s;const i=tc(e);if(i===0)return e;const c=new Proxy(e,i===2?n:u);return r.set(e,c),c}function Cu(e){return kt(e)?Cu(e.__v_raw):!!(e&&e.__v_isReactive)}function kt(e){return!!(e&&e.__v_isReadonly)}function Je(e){return!!(e&&e.__v_isShallow)}function zn(e){return e?!!e.__v_raw:!1}function ue(e){const t=e&&e.__v_raw;return t?ue(t):e}function uc(e){return!ie(e,"__v_skip")&&Object.isExtensible(e)&&Hr(e,"__v_skip",!0),e}const _e=e=>he(e)?Yt(e):e,Gn=e=>he(e)?fs(e):e;function Ee(e){return e?e.__v_isRef===!0:!1}function nc(e){return ds(e,!1)}function rc(e){return ds(e,!0)}function ds(e,t){return Ee(e)?e:new sc(e,t)}class sc{constructor(t,u){this.dep=new Hn,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=u?t:ue(t),this._value=u?t:_e(t),this.__v_isShallow=u}get value(){return this.dep.track(),this._value}set value(t){const u=this._rawValue,n=this.__v_isShallow||Je(t)||kt(t);t=n?t:ue(t),_t(t,u)&&(this._rawValue=t,this._value=n?t:_e(t),this.dep.trigger())}}function Zt(e){return Ee(e)?e.value:e}const ic={get:(e,t,u)=>t==="__v_raw"?e:Zt(Reflect.get(e,t,u)),set:(e,t,u,n)=>{const r=e[t];return Ee(r)&&!Ee(u)?(r.value=u,!0):Reflect.set(e,t,u,n)}};function Ds(e){return Cu(e)?e:new Proxy(e,ic)}function oc(e){const t=K(e)?new Array(e.length):{};for(const u in e)t[u]=lc(e,u);return t}class cc{constructor(t,u,n){this._object=t,this._key=u,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return jo(ue(this._object),this._key)}}function lc(e,t,u){const n=e[t];return Ee(n)?n:new cc(e,t,u)}class ac{constructor(t,u,n){this.fn=t,this.setter=u,this._value=void 0,this.dep=new Hn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=hu-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!u,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return Gr(this,!0),!0}get value(){const t=this.dep.track();return Yr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function fc(e,t,u=!1){let n,r;return z(e)?n=e:(n=e.get,r=e.set),new ac(n,r,u)}const Yu={},Zu=new WeakMap;let Ht;function dc(e,t=!1,u=Ht){if(u){let n=Zu.get(u);n||Zu.set(u,n=[]),n.push(e)}}function Dc(e,t,u=ne){const{immediate:n,deep:r,once:s,scheduler:i,augmentJob:c,call:o}=u,d=x=>r?x:Je(x)||r===!1||r===0?xt(x,1):xt(x);let l,f,p,h,A=!1,C=!1;if(Ee(e)?(f=()=>e.value,A=Je(e)):Cu(e)?(f=()=>d(e),A=!0):K(e)?(C=!0,A=e.some(x=>Cu(x)||Je(x)),f=()=>e.map(x=>{if(Ee(x))return x.value;if(Cu(x))return d(x);if(z(x))return o?o(x,2):x()})):z(e)?t?f=o?()=>o(e,2):e:f=()=>{if(p){ht();try{p()}finally{pt()}}const x=Ht;Ht=l;try{return o?o(e,3,[h]):e(h)}finally{Ht=x}}:f=ze,t&&r){const x=f,k=r===!0?1/0:r;f=()=>xt(x(),k)}const v=Mo(),E=()=>{l.stop(),v&&v.active&&Sn(v.effects,l)};if(s&&t){const x=t;t=(...k)=>{x(...k),E()}}let B=C?new Array(e.length).fill(Yu):Yu;const P=x=>{if(!(!(l.flags&1)||!l.dirty&&!x))if(t){const k=l.run();if(r||A||(C?k.some((q,H)=>_t(q,B[H])):_t(k,B))){p&&p();const q=Ht;Ht=l;try{const H=[k,B===Yu?void 0:C&&B[0]===Yu?[]:B,h];o?o(t,3,H):t(...H),B=k}finally{Ht=q}}}else l.run()};return c&&c(P),l=new qr(f),l.scheduler=i?()=>i(P,!1):P,h=x=>dc(x,!1,l),p=l.onStop=()=>{const x=Zu.get(l);if(x){if(o)o(x,4);else for(const k of x)k();Zu.delete(l)}},t?n?P(!0):B=l.run():i?i(P.bind(null,!0),!0):l.run(),E.pause=l.pause.bind(l),E.resume=l.resume.bind(l),E.stop=E,E}function xt(e,t=1/0,u){if(t<=0||!he(e)||e.__v_skip||(u=u||new Set,u.has(e)))return e;if(u.add(e),t--,Ee(e))xt(e.value,t,u);else if(K(e))for(let n=0;n<e.length;n++)xt(e[n],t,u);else if(Mr(e)||Jt(e))e.forEach(n=>{xt(n,t,u)});else if(jr(e)){for(const n in e)xt(e[n],t,u);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&xt(e[n],t,u)}return e}var St={NODE_ENV:"production"};const mu=[];let Jn=!1;function hc(e,...t){if(Jn)return;Jn=!0,ht();const u=mu.length?mu[mu.length-1].component:null,n=u&&u.appContext.config.warnHandler,r=pc();if(n)Xt(n,u,11,[e+t.map(s=>{var i,c;return(c=(i=s.toString)==null?void 0:i.call(s))!=null?c:JSON.stringify(s)}).join(""),u&&u.proxy,r.map(({vnode:s})=>`at <${ci(u,s.type)}>`).join(`
`),r]);else{const s=[`[Vue warn]: ${e}`,...t];r.length&&s.push(`
`,...gc(r)),console.warn(...s)}pt(),Jn=!1}function pc(){let e=mu[mu.length-1];if(!e)return[];const t=[];for(;e;){const u=t[0];u&&u.vnode===e?u.recurseCount++:t.push({vnode:e,recurseCount:0});const n=e.component&&e.component.parent;e=n&&n.vnode}return t}function gc(e){const t=[];return e.forEach((u,n)=>{t.push(...n===0?[]:[`
`],...Cc(u))}),t}function Cc({vnode:e,recurseCount:t}){const u=t>0?`... (${t} recursive calls)`:"",n=e.component?e.component.parent==null:!1,r=` at <${ci(e.component,e.type,n)}`,s=">"+u;return e.props?[r,...mc(e.props),s]:[r+s]}function mc(e){const t=[],u=Object.keys(e);return u.slice(0,3).forEach(n=>{t.push(...hs(n,e[n]))}),u.length>3&&t.push(" ..."),t}function hs(e,t,u){return ge(t)?(t=JSON.stringify(t),u?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?u?t:[`${e}=${t}`]:Ee(t)?(t=hs(e,ue(t.value),!0),u?t:[`${e}=Ref<`,t,">"]):z(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=ue(t),u?t:[`${e}=`,t])}function Xt(e,t,u,n){try{return n?e(...n):e()}catch(r){Xu(r,t,u)}}function ut(e,t,u,n){if(z(e)){const r=Xt(e,t,u,n);return r&&$r(r)&&r.catch(s=>{Xu(s,t,u)}),r}if(K(e)){const r=[];for(let s=0;s<e.length;s++)r.push(ut(e[s],t,u,n));return r}}function Xu(e,t,u,n=!0){const r=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ne;if(t){let c=t.parent;const o=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${u}`;for(;c;){const l=c.ec;if(l){for(let f=0;f<l.length;f++)if(l[f](e,o,d)===!1)return}c=c.parent}if(s){ht(),Xt(s,null,10,[e,o,d]),pt();return}}Ec(e,u,r,n,i)}function Ec(e,t,u,n=!0,r=!1){if(r)throw e;console.error(e)}const xe=[];let nt=-1;const eu=[];let Rt=null,tu=0;const ps=Promise.resolve();let en=null;function gs(e){const t=en||ps;return e?t.then(this?e.bind(this):e):t}function Ac(e){let t=nt+1,u=xe.length;for(;t<u;){const n=t+u>>>1,r=xe[n],s=Eu(r);s<e||s===e&&r.flags&2?t=n+1:u=n}return t}function Qn(e){if(!(e.flags&1)){const t=Eu(e),u=xe[xe.length-1];!u||!(e.flags&2)&&t>=Eu(u)?xe.push(e):xe.splice(Ac(t),0,e),e.flags|=1,Cs()}}function Cs(){en||(en=ps.then(As))}function Fc(e){K(e)?eu.push(...e):Rt&&e.id===-1?Rt.splice(tu+1,0,e):e.flags&1||(eu.push(e),e.flags|=1),Cs()}function ms(e,t,u=nt+1){for(;u<xe.length;u++){const n=xe[u];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;xe.splice(u,1),u--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function Es(e){if(eu.length){const t=[...new Set(eu)].sort((u,n)=>Eu(u)-Eu(n));if(eu.length=0,Rt){Rt.push(...t);return}for(Rt=t,tu=0;tu<Rt.length;tu++){const u=Rt[tu];u.flags&4&&(u.flags&=-2),u.flags&8||u(),u.flags&=-2}Rt=null,tu=0}}const Eu=e=>e.id==null?e.flags&2?-1:1/0:e.id;function As(e){const t=ze;try{for(nt=0;nt<xe.length;nt++){const u=xe[nt];u&&!(u.flags&8)&&(St.NODE_ENV!=="production"&&t(u),u.flags&4&&(u.flags&=-2),Xt(u,u.i,u.i?15:14),u.flags&4||(u.flags&=-2))}}finally{for(;nt<xe.length;nt++){const u=xe[nt];u&&(u.flags&=-2)}nt=-1,xe.length=0,Es(),en=null,(xe.length||eu.length)&&As()}}let rt=null,Fs=null;function tn(e){const t=rt;return rt=e,Fs=e&&e.type.__scopeId||null,t}function yc(e,t=rt,u){if(!t||e._n)return e;const n=(...r)=>{n._d&&ei(-1);const s=tn(t);let i;try{i=e(...r)}finally{tn(s),n._d&&ei(1)}return i};return n._n=!0,n._c=!0,n._d=!0,n}function Vt(e,t,u,n){const r=e.dirs,s=t&&t.dirs;for(let i=0;i<r.length;i++){const c=r[i];s&&(c.oldValue=s[i].value);let o=c.dir[n];o&&(ht(),ut(o,u,8,[e.el,c,e,t]),pt())}}const bc=Symbol("_vte"),vc=e=>e.__isTeleport;function Yn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Yn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function ys(e,t){return z(e)?be({name:e.name},t,{setup:e}):e}function bs(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function un(e,t,u,n,r=!1){if(K(e)){e.forEach((A,C)=>un(A,t&&(K(t)?t[C]:t),u,n,r));return}if(Au(n)&&!r){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&un(e,t,u,n.component.subTree);return}const s=n.shapeFlag&4?lr(n.component):n.el,i=r?null:s,{i:c,r:o}=e,d=t&&t.r,l=c.refs===ne?c.refs={}:c.refs,f=c.setupState,p=ue(f),h=f===ne?()=>!1:A=>ie(p,A);if(d!=null&&d!==o&&(ge(d)?(l[d]=null,h(d)&&(f[d]=null)):Ee(d)&&(d.value=null)),z(o))Xt(o,c,12,[i,l]);else{const A=ge(o),C=Ee(o);if(A||C){const v=()=>{if(e.f){const E=A?h(o)?f[o]:l[o]:o.value;r?K(E)&&Sn(E,s):K(E)?E.includes(s)||E.push(s):A?(l[o]=[s],h(o)&&(f[o]=l[o])):(o.value=[s],e.k&&(l[e.k]=o.value))}else A?(l[o]=i,h(o)&&(f[o]=i)):C&&(o.value=i,e.k&&(l[e.k]=i))};i?(v.id=-1,$e(v,u)):v()}}}Wu().requestIdleCallback,Wu().cancelIdleCallback;const Au=e=>!!e.type.__asyncLoader,vs=e=>e.type.__isKeepAlive;function Bc(e,t){Bs(e,"a",t)}function wc(e,t){Bs(e,"da",t)}function Bs(e,t,u=Fe){const n=e.__wdc||(e.__wdc=()=>{let r=u;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(nn(t,n,u),u){let r=u.parent;for(;r&&r.parent;)vs(r.parent.vnode)&&_c(n,t,u,r),r=r.parent}}function _c(e,t,u,n){const r=nn(t,e,n,!0);_s(()=>{Sn(n[t],r)},u)}function nn(e,t,u=Fe,n=!1){if(u){const r=u[e]||(u[e]=[]),s=t.__weh||(t.__weh=(...i)=>{ht();const c=wu(u),o=ut(t,u,e,i);return c(),pt(),o});return n?r.unshift(s):r.push(s),s}}const mt=e=>(t,u=Fe)=>{(!_u||e==="sp")&&nn(e,(...n)=>t(...n),u)},xc=mt("bm"),ws=mt("m"),Sc=mt("bu"),Rc=mt("u"),Oc=mt("bum"),_s=mt("um"),Pc=mt("sp"),Tc=mt("rtg"),Nc=mt("rtc");function Ic(e,t=Fe){nn("ec",e,t)}const Mc=Symbol.for("v-ndc"),Zn=e=>e?si(e)?lr(e):Zn(e.parent):null,Fu=be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Zn(e.parent),$root:e=>Zn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Os(e),$forceUpdate:e=>e.f||(e.f=()=>{Qn(e.update)}),$nextTick:e=>e.n||(e.n=gs.bind(e.proxy)),$watch:e=>nl.bind(e)}),Xn=(e,t)=>e!==ne&&!e.__isScriptSetup&&ie(e,t),$c={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:u,setupState:n,data:r,props:s,accessCache:i,type:c,appContext:o}=e;let d;if(t[0]!=="$"){const h=i[t];if(h!==void 0)switch(h){case 1:return n[t];case 2:return r[t];case 4:return u[t];case 3:return s[t]}else{if(Xn(n,t))return i[t]=1,n[t];if(r!==ne&&ie(r,t))return i[t]=2,r[t];if((d=e.propsOptions[0])&&ie(d,t))return i[t]=3,s[t];if(u!==ne&&ie(u,t))return i[t]=4,u[t];er&&(i[t]=0)}}const l=Fu[t];let f,p;if(l)return t==="$attrs"&&ve(e.attrs,"get",""),l(e);if((f=c.__cssModules)&&(f=f[t]))return f;if(u!==ne&&ie(u,t))return i[t]=4,u[t];if(p=o.config.globalProperties,ie(p,t))return p[t]},set({_:e},t,u){const{data:n,setupState:r,ctx:s}=e;return Xn(r,t)?(r[t]=u,!0):n!==ne&&ie(n,t)?(n[t]=u,!0):ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=u,!0)},has({_:{data:e,setupState:t,accessCache:u,ctx:n,appContext:r,propsOptions:s}},i){let c;return!!u[i]||e!==ne&&ie(e,i)||Xn(t,i)||(c=s[0])&&ie(c,i)||ie(n,i)||ie(Fu,i)||ie(r.config.globalProperties,i)},defineProperty(e,t,u){return u.get!=null?e._.accessCache[t]=0:ie(u,"value")&&this.set(e,t,u.value,null),Reflect.defineProperty(e,t,u)}};function xs(e){return K(e)?e.reduce((t,u)=>(t[u]=null,t),{}):e}let er=!0;function Lc(e){const t=Os(e),u=e.proxy,n=e.ctx;er=!1,t.beforeCreate&&Ss(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:i,watch:c,provide:o,inject:d,created:l,beforeMount:f,mounted:p,beforeUpdate:h,updated:A,activated:C,deactivated:v,beforeDestroy:E,beforeUnmount:B,destroyed:P,unmounted:x,render:k,renderTracked:q,renderTriggered:H,errorCaptured:Oe,serverPrefetch:et,expose:Pe,inheritAttrs:We,components:vt,directives:Ke,filters:zt}=t;if(d&&jc(d,n,null),i)for(const ce in i){const X=i[ce];z(X)&&(n[ce]=X.bind(u))}if(r){const ce=r.call(u,u);he(ce)&&(e.data=Yt(ce))}if(er=!0,s)for(const ce in s){const X=s[ce],tt=z(X)?X.bind(u,u):z(X.get)?X.get.bind(u,u):ze,at=!z(X)&&z(X.set)?X.set.bind(u):ze,qe=Ye({get:tt,set:at});Object.defineProperty(n,ce,{enumerable:!0,configurable:!0,get:()=>qe.value,set:S=>qe.value=S})}if(c)for(const ce in c)Rs(c[ce],n,u,ce);if(o){const ce=z(o)?o.call(u):o;Reflect.ownKeys(ce).forEach(X=>{sn(X,ce[X])})}l&&Ss(l,e,"c");function me(ce,X){K(X)?X.forEach(tt=>ce(tt.bind(u))):X&&ce(X.bind(u))}if(me(xc,f),me(ws,p),me(Sc,h),me(Rc,A),me(Bc,C),me(wc,v),me(Ic,Oe),me(Nc,q),me(Tc,H),me(Oc,B),me(_s,x),me(Pc,et),K(Pe))if(Pe.length){const ce=e.exposed||(e.exposed={});Pe.forEach(X=>{Object.defineProperty(ce,X,{get:()=>u[X],set:tt=>u[X]=tt})})}else e.exposed||(e.exposed={});k&&e.render===ze&&(e.render=k),We!=null&&(e.inheritAttrs=We),vt&&(e.components=vt),Ke&&(e.directives=Ke),et&&bs(e)}function jc(e,t,u=ze){K(e)&&(e=tr(e));for(const n in e){const r=e[n];let s;he(r)?"default"in r?s=Qe(r.from||n,r.default,!0):s=Qe(r.from||n):s=Qe(r),Ee(s)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[n]=s}}function Ss(e,t,u){ut(K(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,u)}function Rs(e,t,u,n){let r=n.includes(".")?Js(u,n):()=>u[n];if(ge(e)){const s=t[e];z(s)&&nu(r,s)}else if(z(e))nu(r,e.bind(u));else if(he(e))if(K(e))e.forEach(s=>Rs(s,t,u,n));else{const s=z(e.handler)?e.handler.bind(u):t[e.handler];z(s)&&nu(r,s,e)}}function Os(e){const t=e.type,{mixins:u,extends:n}=t,{mixins:r,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,c=s.get(t);let o;return c?o=c:!r.length&&!u&&!n?o=t:(o={},r.length&&r.forEach(d=>rn(o,d,i,!0)),rn(o,t,i)),he(t)&&s.set(t,o),o}function rn(e,t,u,n=!1){const{mixins:r,extends:s}=t;s&&rn(e,s,u,!0),r&&r.forEach(i=>rn(e,i,u,!0));for(const i in t)if(!(n&&i==="expose")){const c=kc[i]||u&&u[i];e[i]=c?c(e[i],t[i]):t[i]}return e}const kc={data:Ps,props:Ts,emits:Ts,methods:yu,computed:yu,beforeCreate:Se,created:Se,beforeMount:Se,mounted:Se,beforeUpdate:Se,updated:Se,beforeDestroy:Se,beforeUnmount:Se,destroyed:Se,unmounted:Se,activated:Se,deactivated:Se,errorCaptured:Se,serverPrefetch:Se,components:yu,directives:yu,watch:Vc,provide:Ps,inject:Hc};function Ps(e,t){return t?e?function(){return be(z(e)?e.call(this,this):e,z(t)?t.call(this,this):t)}:t:e}function Hc(e,t){return yu(tr(e),tr(t))}function tr(e){if(K(e)){const t={};for(let u=0;u<e.length;u++)t[e[u]]=e[u];return t}return e}function Se(e,t){return e?[...new Set([].concat(e,t))]:t}function yu(e,t){return e?be(Object.create(null),e,t):t}function Ts(e,t){return e?K(e)&&K(t)?[...new Set([...e,...t])]:be(Object.create(null),xs(e),xs(t??{})):t}function Vc(e,t){if(!e)return t;if(!t)return e;const u=be(Object.create(null),e);for(const n in t)u[n]=Se(e[n],t[n]);return u}function Ns(){return{app:null,config:{isNativeTag:bo,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Uc=0;function Wc(e,t){return function(n,r=null){z(n)||(n=be({},n)),r!=null&&!he(r)&&(r=null);const s=Ns(),i=new WeakSet,c=[];let o=!1;const d=s.app={_uid:Uc++,_component:n,_props:r,_container:null,_context:s,_instance:null,version:Sl,get config(){return s.config},set config(l){},use(l,...f){return i.has(l)||(l&&z(l.install)?(i.add(l),l.install(d,...f)):z(l)&&(i.add(l),l(d,...f))),d},mixin(l){return s.mixins.includes(l)||s.mixins.push(l),d},component(l,f){return f?(s.components[l]=f,d):s.components[l]},directive(l,f){return f?(s.directives[l]=f,d):s.directives[l]},mount(l,f,p){if(!o){const h=d._ceVNode||ke(n,r);return h.appContext=s,p===!0?p="svg":p===!1&&(p=void 0),e(h,l,p),o=!0,d._container=l,l.__vue_app__=d,lr(h.component)}},onUnmount(l){c.push(l)},unmount(){o&&(ut(c,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(l,f){return s.provides[l]=f,d},runWithContext(l){const f=uu;uu=d;try{return l()}finally{uu=f}}};return d}}let uu=null;function sn(e,t){if(Fe){let u=Fe.provides;const n=Fe.parent&&Fe.parent.provides;n===u&&(u=Fe.provides=Object.create(n)),u[e]=t}}function Qe(e,t,u=!1){const n=Fe||rt;if(n||uu){const r=uu?uu._context.provides:n?n.parent==null?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return u&&z(t)?t.call(n&&n.proxy):t}}const Is={},Ms=()=>Object.create(Is),$s=e=>Object.getPrototypeOf(e)===Is;function Kc(e,t,u,n=!1){const r={},s=Ms();e.propsDefaults=Object.create(null),Ls(e,t,r,s);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);u?e.props=n?r:as(r):e.type.props?e.props=r:e.props=s,e.attrs=s}function qc(e,t,u,n){const{props:r,attrs:s,vnode:{patchFlag:i}}=e,c=ue(r),[o]=e.propsOptions;let d=!1;if((n||i>0)&&!(i&16)){if(i&8){const l=e.vnode.dynamicProps;for(let f=0;f<l.length;f++){let p=l[f];if(on(e.emitsOptions,p))continue;const h=t[p];if(o)if(ie(s,p))h!==s[p]&&(s[p]=h,d=!0);else{const A=wt(p);r[A]=ur(o,c,A,h,e,!1)}else h!==s[p]&&(s[p]=h,d=!0)}}}else{Ls(e,t,r,s)&&(d=!0);let l;for(const f in c)(!t||!ie(t,f)&&((l=$t(f))===f||!ie(t,l)))&&(o?u&&(u[f]!==void 0||u[l]!==void 0)&&(r[f]=ur(o,c,f,void 0,e,!0)):delete r[f]);if(s!==c)for(const f in s)(!t||!ie(t,f))&&(delete s[f],d=!0)}d&&gt(e.attrs,"set","")}function Ls(e,t,u,n){const[r,s]=e.propsOptions;let i=!1,c;if(t)for(let o in t){if(fu(o))continue;const d=t[o];let l;r&&ie(r,l=wt(o))?!s||!s.includes(l)?u[l]=d:(c||(c={}))[l]=d:on(e.emitsOptions,o)||(!(o in n)||d!==n[o])&&(n[o]=d,i=!0)}if(s){const o=ue(u),d=c||ne;for(let l=0;l<s.length;l++){const f=s[l];u[f]=ur(r,o,f,d[f],e,!ie(d,f))}}return i}function ur(e,t,u,n,r,s){const i=e[u];if(i!=null){const c=ie(i,"default");if(c&&n===void 0){const o=i.default;if(i.type!==Function&&!i.skipFactory&&z(o)){const{propsDefaults:d}=r;if(u in d)n=d[u];else{const l=wu(r);n=d[u]=o.call(null,t),l()}}else n=o;r.ce&&r.ce._setProp(u,n)}i[0]&&(s&&!c?n=!1:i[1]&&(n===""||n===$t(u))&&(n=!0))}return n}const zc=new WeakMap;function js(e,t,u=!1){const n=u?zc:t.propsCache,r=n.get(e);if(r)return r;const s=e.props,i={},c=[];let o=!1;if(!z(e)){const l=f=>{o=!0;const[p,h]=js(f,t,!0);be(i,p),h&&c.push(...h)};!u&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}if(!s&&!o)return he(e)&&n.set(e,Gt),Gt;if(K(s))for(let l=0;l<s.length;l++){const f=wt(s[l]);ks(f)&&(i[f]=ne)}else if(s)for(const l in s){const f=wt(l);if(ks(f)){const p=s[l],h=i[f]=K(p)||z(p)?{type:p}:be({},p),A=h.type;let C=!1,v=!0;if(K(A))for(let E=0;E<A.length;++E){const B=A[E],P=z(B)&&B.name;if(P==="Boolean"){C=!0;break}else P==="String"&&(v=!1)}else C=z(A)&&A.name==="Boolean";h[0]=C,h[1]=v,(C||ie(h,"default"))&&c.push(f)}}const d=[i,c];return he(e)&&n.set(e,d),d}function ks(e){return e[0]!=="$"&&!fu(e)}const Hs=e=>e[0]==="_"||e==="$stable",nr=e=>K(e)?e.map(it):[it(e)],Gc=(e,t,u)=>{if(t._n)return t;const n=yc((...r)=>(St.NODE_ENV!=="production"&&Fe&&(!u||(u.root,Fe.root)),nr(t(...r))),u);return n._c=!1,n},Vs=(e,t,u)=>{const n=e._ctx;for(const r in e){if(Hs(r))continue;const s=e[r];if(z(s))t[r]=Gc(r,s,n);else if(s!=null){const i=nr(s);t[r]=()=>i}}},Us=(e,t)=>{const u=nr(t);e.slots.default=()=>u},Ws=(e,t,u)=>{for(const n in t)(u||n!=="_")&&(e[n]=t[n])},Jc=(e,t,u)=>{const n=e.slots=Ms();if(e.vnode.shapeFlag&32){const r=t._;r?(Ws(n,t,u),u&&Hr(n,"_",r,!0)):Vs(t,n)}else t&&Us(e,t)},Qc=(e,t,u)=>{const{vnode:n,slots:r}=e;let s=!0,i=ne;if(n.shapeFlag&32){const c=t._;c?u&&c===1?s=!1:Ws(r,t,u):(s=!t.$stable,Vs(t,r)),i=t}else t&&(Us(e,t),i={default:1});if(s)for(const c in r)!Hs(c)&&i[c]==null&&delete r[c]},$e=al;function Yc(e){return Zc(e)}function Zc(e,t){const u=Wu();u.__VUE__=!0;const{insert:n,remove:r,patchProp:s,createElement:i,createText:c,createComment:o,setText:d,setElementText:l,parentNode:f,nextSibling:p,setScopeId:h=ze,insertStaticContent:A}=e,C=(a,D,g,F=null,w=null,b=null,$=void 0,N=null,T=!!D.dynamicChildren)=>{if(a===D)return;a&&!Bu(a,D)&&(F=m(a),S(a,w,b,!0),a=null),D.patchFlag===-2&&(T=!1,D.dynamicChildren=null);const{type:R,ref:U,shapeFlag:L}=D;switch(R){case cn:v(a,D,g,F);break;case Wt:E(a,D,g,F);break;case sr:a==null&&B(D,g,F,$);break;case st:vt(a,D,g,F,w,b,$,N,T);break;default:L&1?k(a,D,g,F,w,b,$,N,T):L&6?Ke(a,D,g,F,w,b,$,N,T):(L&64||L&128)&&R.process(a,D,g,F,w,b,$,N,T,I)}U!=null&&w&&un(U,a&&a.ref,b,D||a,!D)},v=(a,D,g,F)=>{if(a==null)n(D.el=c(D.children),g,F);else{const w=D.el=a.el;D.children!==a.children&&d(w,D.children)}},E=(a,D,g,F)=>{a==null?n(D.el=o(D.children||""),g,F):D.el=a.el},B=(a,D,g,F)=>{[a.el,a.anchor]=A(a.children,D,g,F,a.el,a.anchor)},P=({el:a,anchor:D},g,F)=>{let w;for(;a&&a!==D;)w=p(a),n(a,g,F),a=w;n(D,g,F)},x=({el:a,anchor:D})=>{let g;for(;a&&a!==D;)g=p(a),r(a),a=g;r(D)},k=(a,D,g,F,w,b,$,N,T)=>{D.type==="svg"?$="svg":D.type==="math"&&($="mathml"),a==null?q(D,g,F,w,b,$,N,T):et(a,D,w,b,$,N,T)},q=(a,D,g,F,w,b,$,N)=>{let T,R;const{props:U,shapeFlag:L,transition:V,dirs:W}=a;if(T=a.el=i(a.type,b,U&&U.is,U),L&8?l(T,a.children):L&16&&Oe(a.children,T,null,F,w,rr(a,b),$,N),W&&Vt(a,null,F,"created"),H(T,a,a.scopeId,$,F),U){for(const fe in U)fe!=="value"&&!fu(fe)&&s(T,fe,null,U[fe],b,F);"value"in U&&s(T,"value",null,U.value,b),(R=U.onVnodeBeforeMount)&&ot(R,F,a)}W&&Vt(a,null,F,"beforeMount");const te=Xc(w,V);te&&V.beforeEnter(T),n(T,D,g),((R=U&&U.onVnodeMounted)||te||W)&&$e(()=>{R&&ot(R,F,a),te&&V.enter(T),W&&Vt(a,null,F,"mounted")},w)},H=(a,D,g,F,w)=>{if(g&&h(a,g),F)for(let b=0;b<F.length;b++)h(a,F[b]);if(w){let b=w.subTree;if(D===b||Xs(b.type)&&(b.ssContent===D||b.ssFallback===D)){const $=w.vnode;H(a,$,$.scopeId,$.slotScopeIds,w.parent)}}},Oe=(a,D,g,F,w,b,$,N,T=0)=>{for(let R=T;R<a.length;R++){const U=a[R]=N?Ot(a[R]):it(a[R]);C(null,U,D,g,F,w,b,$,N)}},et=(a,D,g,F,w,b,$)=>{const N=D.el=a.el;let{patchFlag:T,dynamicChildren:R,dirs:U}=D;T|=a.patchFlag&16;const L=a.props||ne,V=D.props||ne;let W;if(g&&Ut(g,!1),(W=V.onVnodeBeforeUpdate)&&ot(W,g,D,a),U&&Vt(D,a,g,"beforeUpdate"),g&&Ut(g,!0),(L.innerHTML&&V.innerHTML==null||L.textContent&&V.textContent==null)&&l(N,""),R?Pe(a.dynamicChildren,R,N,g,F,rr(D,w),b):$||X(a,D,N,null,g,F,rr(D,w),b,!1),T>0){if(T&16)We(N,L,V,g,w);else if(T&2&&L.class!==V.class&&s(N,"class",null,V.class,w),T&4&&s(N,"style",L.style,V.style,w),T&8){const te=D.dynamicProps;for(let fe=0;fe<te.length;fe++){const le=te[fe],Ve=L[le],Ie=V[le];(Ie!==Ve||le==="value")&&s(N,le,Ve,Ie,w,g)}}T&1&&a.children!==D.children&&l(N,D.children)}else!$&&R==null&&We(N,L,V,g,w);((W=V.onVnodeUpdated)||U)&&$e(()=>{W&&ot(W,g,D,a),U&&Vt(D,a,g,"updated")},F)},Pe=(a,D,g,F,w,b,$)=>{for(let N=0;N<D.length;N++){const T=a[N],R=D[N],U=T.el&&(T.type===st||!Bu(T,R)||T.shapeFlag&70)?f(T.el):g;C(T,R,U,null,F,w,b,$,!0)}},We=(a,D,g,F,w)=>{if(D!==g){if(D!==ne)for(const b in D)!fu(b)&&!(b in g)&&s(a,b,D[b],null,w,F);for(const b in g){if(fu(b))continue;const $=g[b],N=D[b];$!==N&&b!=="value"&&s(a,b,N,$,w,F)}"value"in g&&s(a,"value",D.value,g.value,w)}},vt=(a,D,g,F,w,b,$,N,T)=>{const R=D.el=a?a.el:c(""),U=D.anchor=a?a.anchor:c("");let{patchFlag:L,dynamicChildren:V,slotScopeIds:W}=D;W&&(N=N?N.concat(W):W),a==null?(n(R,g,F),n(U,g,F),Oe(D.children||[],g,U,w,b,$,N,T)):L>0&&L&64&&V&&a.dynamicChildren?(Pe(a.dynamicChildren,V,g,w,b,$,N),(D.key!=null||w&&D===w.subTree)&&Ks(a,D,!0)):X(a,D,g,U,w,b,$,N,T)},Ke=(a,D,g,F,w,b,$,N,T)=>{D.slotScopeIds=N,a==null?D.shapeFlag&512?w.ctx.activate(D,g,F,$,T):zt(D,g,F,w,b,$,T):Mt(a,D,T)},zt=(a,D,g,F,w,b,$)=>{const N=a.component=Al(a,F,w);if(vs(a)&&(N.ctx.renderer=I),Fl(N,!1,$),N.asyncDep){if(w&&w.registerDep(N,me,$),!a.el){const T=N.subTree=ke(Wt);E(null,T,D,g)}}else me(N,a,D,g,w,b,$)},Mt=(a,D,g)=>{const F=D.component=a.component;if(cl(a,D,g))if(F.asyncDep&&!F.asyncResolved){ce(F,D,g);return}else F.next=D,F.update();else D.el=a.el,F.vnode=D},me=(a,D,g,F,w,b,$)=>{const N=()=>{if(a.isMounted){let{next:L,bu:V,u:W,parent:te,vnode:fe}=a;{const dt=qs(a);if(dt){L&&(L.el=fe.el,ce(a,L,$)),dt.asyncDep.then(()=>{a.isUnmounted||N()});return}}let le=L,Ve;Ut(a,!1),L?(L.el=fe.el,ce(a,L,$)):L=fe,V&&Pn(V),(Ve=L.props&&L.props.onVnodeBeforeUpdate)&&ot(Ve,te,L,fe),Ut(a,!0);const Ie=Ys(a),ft=a.subTree;a.subTree=Ie,C(ft,Ie,f(ft.el),m(ft),a,w,b),L.el=Ie.el,le===null&&ll(a,Ie.el),W&&$e(W,w),(Ve=L.props&&L.props.onVnodeUpdated)&&$e(()=>ot(Ve,te,L,fe),w)}else{let L;const{el:V,props:W}=D,{bm:te,m:fe,parent:le,root:Ve,type:Ie}=a,ft=Au(D);Ut(a,!1),te&&Pn(te),!ft&&(L=W&&W.onVnodeBeforeMount)&&ot(L,le,D),Ut(a,!0);{Ve.ce&&Ve.ce._injectChildStyle(Ie);const dt=a.subTree=Ys(a);C(null,dt,g,F,a,w,b),D.el=dt.el}if(fe&&$e(fe,w),!ft&&(L=W&&W.onVnodeMounted)){const dt=D;$e(()=>ot(L,le,dt),w)}(D.shapeFlag&256||le&&Au(le.vnode)&&le.vnode.shapeFlag&256)&&a.a&&$e(a.a,w),a.isMounted=!0,D=g=F=null}};a.scope.on();const T=a.effect=new qr(N);a.scope.off();const R=a.update=T.run.bind(T),U=a.job=T.runIfDirty.bind(T);U.i=a,U.id=a.uid,T.scheduler=()=>Qn(U),Ut(a,!0),R()},ce=(a,D,g)=>{D.component=a;const F=a.vnode.props;a.vnode=D,a.next=null,qc(a,D.props,F,g),Qc(a,D.children,g),ht(),ms(a),pt()},X=(a,D,g,F,w,b,$,N,T=!1)=>{const R=a&&a.children,U=a?a.shapeFlag:0,L=D.children,{patchFlag:V,shapeFlag:W}=D;if(V>0){if(V&128){at(R,L,g,F,w,b,$,N,T);return}else if(V&256){tt(R,L,g,F,w,b,$,N,T);return}}W&8?(U&16&&J(R,w,b),L!==R&&l(g,L)):U&16?W&16?at(R,L,g,F,w,b,$,N,T):J(R,w,b,!0):(U&8&&l(g,""),W&16&&Oe(L,g,F,w,b,$,N,T))},tt=(a,D,g,F,w,b,$,N,T)=>{a=a||Gt,D=D||Gt;const R=a.length,U=D.length,L=Math.min(R,U);let V;for(V=0;V<L;V++){const W=D[V]=T?Ot(D[V]):it(D[V]);C(a[V],W,g,null,w,b,$,N,T)}R>U?J(a,w,b,!0,!1,L):Oe(D,g,F,w,b,$,N,T,L)},at=(a,D,g,F,w,b,$,N,T)=>{let R=0;const U=D.length;let L=a.length-1,V=U-1;for(;R<=L&&R<=V;){const W=a[R],te=D[R]=T?Ot(D[R]):it(D[R]);if(Bu(W,te))C(W,te,g,null,w,b,$,N,T);else break;R++}for(;R<=L&&R<=V;){const W=a[L],te=D[V]=T?Ot(D[V]):it(D[V]);if(Bu(W,te))C(W,te,g,null,w,b,$,N,T);else break;L--,V--}if(R>L){if(R<=V){const W=V+1,te=W<U?D[W].el:F;for(;R<=V;)C(null,D[R]=T?Ot(D[R]):it(D[R]),g,te,w,b,$,N,T),R++}}else if(R>V)for(;R<=L;)S(a[R],w,b,!0),R++;else{const W=R,te=R,fe=new Map;for(R=te;R<=V;R++){const Ue=D[R]=T?Ot(D[R]):it(D[R]);Ue.key!=null&&fe.set(Ue.key,R)}let le,Ve=0;const Ie=V-te+1;let ft=!1,dt=0;const ku=new Array(Ie);for(R=0;R<Ie;R++)ku[R]=0;for(R=W;R<=L;R++){const Ue=a[R];if(Ve>=Ie){S(Ue,w,b,!0);continue}let Dt;if(Ue.key!=null)Dt=fe.get(Ue.key);else for(le=te;le<=V;le++)if(ku[le-te]===0&&Bu(Ue,D[le])){Dt=le;break}Dt===void 0?S(Ue,w,b,!0):(ku[Dt-te]=R+1,Dt>=dt?dt=Dt:ft=!0,C(Ue,D[Dt],g,null,w,b,$,N,T),Ve++)}const Fo=ft?el(ku):Gt;for(le=Fo.length-1,R=Ie-1;R>=0;R--){const Ue=te+R,Dt=D[Ue],yo=Ue+1<U?D[Ue+1].el:F;ku[R]===0?C(null,Dt,g,yo,w,b,$,N,T):ft&&(le<0||R!==Fo[le]?qe(Dt,g,yo,2):le--)}}},qe=(a,D,g,F,w=null)=>{const{el:b,type:$,transition:N,children:T,shapeFlag:R}=a;if(R&6){qe(a.component.subTree,D,g,F);return}if(R&128){a.suspense.move(D,g,F);return}if(R&64){$.move(a,D,g,I);return}if($===st){n(b,D,g);for(let L=0;L<T.length;L++)qe(T[L],D,g,F);n(a.anchor,D,g);return}if($===sr){P(a,D,g);return}if(F!==2&&R&1&&N)if(F===0)N.beforeEnter(b),n(b,D,g),$e(()=>N.enter(b),w);else{const{leave:L,delayLeave:V,afterLeave:W}=N,te=()=>n(b,D,g),fe=()=>{L(b,()=>{te(),W&&W()})};V?V(b,te,fe):fe()}else n(b,D,g)},S=(a,D,g,F=!1,w=!1)=>{const{type:b,props:$,ref:N,children:T,dynamicChildren:R,shapeFlag:U,patchFlag:L,dirs:V,cacheIndex:W}=a;if(L===-2&&(w=!1),N!=null&&un(N,null,g,a,!0),W!=null&&(D.renderCache[W]=void 0),U&256){D.ctx.deactivate(a);return}const te=U&1&&V,fe=!Au(a);let le;if(fe&&(le=$&&$.onVnodeBeforeUnmount)&&ot(le,D,a),U&6)ee(a.component,g,F);else{if(U&128){a.suspense.unmount(g,F);return}te&&Vt(a,null,D,"beforeUnmount"),U&64?a.type.remove(a,D,g,I,F):R&&!R.hasOnce&&(b!==st||L>0&&L&64)?J(R,D,g,!1,!0):(b===st&&L&384||!w&&U&16)&&J(T,D,g),F&&M(a)}(fe&&(le=$&&$.onVnodeUnmounted)||te)&&$e(()=>{le&&ot(le,D,a),te&&Vt(a,null,D,"unmounted")},g)},M=a=>{const{type:D,el:g,anchor:F,transition:w}=a;if(D===st){j(g,F);return}if(D===sr){x(a);return}const b=()=>{r(g),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(a.shapeFlag&1&&w&&!w.persisted){const{leave:$,delayLeave:N}=w,T=()=>$(g,b);N?N(a.el,b,T):T()}else b()},j=(a,D)=>{let g;for(;a!==D;)g=p(a),r(a),a=g;r(D)},ee=(a,D,g)=>{const{bum:F,scope:w,job:b,subTree:$,um:N,m:T,a:R}=a;zs(T),zs(R),F&&Pn(F),w.stop(),b&&(b.flags|=8,S($,a,D,g)),N&&$e(N,D),$e(()=>{a.isUnmounted=!0},D),D&&D.pendingBranch&&!D.isUnmounted&&a.asyncDep&&!a.asyncResolved&&a.suspenseId===D.pendingId&&(D.deps--,D.deps===0&&D.resolve())},J=(a,D,g,F=!1,w=!1,b=0)=>{for(let $=b;$<a.length;$++)S(a[$],D,g,F,w)},m=a=>{if(a.shapeFlag&6)return m(a.component.subTree);if(a.shapeFlag&128)return a.suspense.next();const D=p(a.anchor||a.el),g=D&&D[bc];return g?p(g):D};let O=!1;const _=(a,D,g)=>{a==null?D._vnode&&S(D._vnode,null,null,!0):C(D._vnode||null,a,D,null,null,null,g),D._vnode=a,O||(O=!0,ms(),Es(),O=!1)},I={p:C,um:S,m:qe,r:M,mt:zt,mc:Oe,pc:X,pbc:Pe,n:m,o:e};return{render:_,hydrate:void 0,createApp:Wc(_)}}function rr({type:e,props:t},u){return u==="svg"&&e==="foreignObject"||u==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:u}function Ut({effect:e,job:t},u){u?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Xc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ks(e,t,u=!1){const n=e.children,r=t.children;if(K(n)&&K(r))for(let s=0;s<n.length;s++){const i=n[s];let c=r[s];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=r[s]=Ot(r[s]),c.el=i.el),!u&&c.patchFlag!==-2&&Ks(i,c)),c.type===cn&&(c.el=i.el)}}function el(e){const t=e.slice(),u=[0];let n,r,s,i,c;const o=e.length;for(n=0;n<o;n++){const d=e[n];if(d!==0){if(r=u[u.length-1],e[r]<d){t[n]=r,u.push(n);continue}for(s=0,i=u.length-1;s<i;)c=s+i>>1,e[u[c]]<d?s=c+1:i=c;d<e[u[s]]&&(s>0&&(t[n]=u[s-1]),u[s]=n)}}for(s=u.length,i=u[s-1];s-- >0;)u[s]=i,i=t[i];return u}function qs(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:qs(t)}function zs(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const tl=Symbol.for("v-scx"),ul=()=>Qe(tl);function nu(e,t,u){return Gs(e,t,u)}function Gs(e,t,u=ne){const{immediate:n,deep:r,flush:s,once:i}=u,c=be({},u),o=t&&n||!t&&s!=="post";let d;if(_u){if(s==="sync"){const h=ul();d=h.__watcherHandles||(h.__watcherHandles=[])}else if(!o){const h=()=>{};return h.stop=ze,h.resume=ze,h.pause=ze,h}}const l=Fe;c.call=(h,A,C)=>ut(h,l,A,C);let f=!1;s==="post"?c.scheduler=h=>{$e(h,l&&l.suspense)}:s!=="sync"&&(f=!0,c.scheduler=(h,A)=>{A?h():Qn(h)}),c.augmentJob=h=>{t&&(h.flags|=4),f&&(h.flags|=2,l&&(h.id=l.uid,h.i=l))};const p=Dc(e,t,c);return _u&&(d?d.push(p):o&&p()),p}function nl(e,t,u){const n=this.proxy,r=ge(e)?e.includes(".")?Js(n,e):()=>n[e]:e.bind(n,n);let s;z(t)?s=t:(s=t.handler,u=t);const i=wu(this),c=Gs(r,s.bind(n),u);return i(),c}function Js(e,t){const u=t.split(".");return()=>{let n=e;for(let r=0;r<u.length&&n;r++)n=n[u[r]];return n}}const rl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${wt(t)}Modifiers`]||e[`${$t(t)}Modifiers`];function sl(e,t,...u){if(e.isUnmounted)return;const n=e.vnode.props||ne;let r=u;const s=t.startsWith("update:"),i=s&&rl(n,t.slice(7));i&&(i.trim&&(r=u.map(l=>ge(l)?l.trim():l)),i.number&&(r=u.map(xo)));let c,o=n[c=On(t)]||n[c=On(wt(t))];!o&&s&&(o=n[c=On($t(t))]),o&&ut(o,e,6,r);const d=n[c+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,ut(d,e,6,r)}}function Qs(e,t,u=!1){const n=t.emitsCache,r=n.get(e);if(r!==void 0)return r;const s=e.emits;let i={},c=!1;if(!z(e)){const o=d=>{const l=Qs(d,t,!0);l&&(c=!0,be(i,l))};!u&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return!s&&!c?(he(e)&&n.set(e,null),null):(K(s)?s.forEach(o=>i[o]=null):be(i,s),he(e)&&n.set(e,i),i)}function on(e,t){return!e||!Hu(t)?!1:(t=t.slice(2).replace(/Once$/,""),ie(e,t[0].toLowerCase()+t.slice(1))||ie(e,$t(t))||ie(e,t))}function b0(){}function Ys(e){const{type:t,vnode:u,proxy:n,withProxy:r,propsOptions:[s],slots:i,attrs:c,emit:o,render:d,renderCache:l,props:f,data:p,setupState:h,ctx:A,inheritAttrs:C}=e,v=tn(e);let E,B;try{if(u.shapeFlag&4){const x=r||n,k=St.NODE_ENV!=="production"&&h.__isScriptSetup?new Proxy(x,{get(q,H,Oe){return hc(`Property '${String(H)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(q,H,Oe)}}):x;E=it(d.call(k,x,l,St.NODE_ENV!=="production"?Ju(f):f,h,p,A)),B=c}else{const x=t;St.NODE_ENV,E=it(x.length>1?x(St.NODE_ENV!=="production"?Ju(f):f,St.NODE_ENV!=="production"?{get attrs(){return Ju(c)},slots:i,emit:o}:{attrs:c,slots:i,emit:o}):x(St.NODE_ENV!=="production"?Ju(f):f,null)),B=t.props?c:il(c)}}catch(x){bu.length=0,Xu(x,e,1),E=ke(Wt)}let P=E;if(B&&C!==!1){const x=Object.keys(B),{shapeFlag:k}=P;x.length&&k&7&&(s&&x.some(xn)&&(B=ol(B,s)),P=ru(P,B,!1,!0))}return u.dirs&&(P=ru(P,null,!1,!0),P.dirs=P.dirs?P.dirs.concat(u.dirs):u.dirs),u.transition&&Yn(P,u.transition),E=P,tn(v),E}const il=e=>{let t;for(const u in e)(u==="class"||u==="style"||Hu(u))&&((t||(t={}))[u]=e[u]);return t},ol=(e,t)=>{const u={};for(const n in e)(!xn(n)||!(n.slice(9)in t))&&(u[n]=e[n]);return u};function cl(e,t,u){const{props:n,children:r,component:s}=e,{props:i,children:c,patchFlag:o}=t,d=s.emitsOptions;if(t.dirs||t.transition)return!0;if(u&&o>=0){if(o&1024)return!0;if(o&16)return n?Zs(n,i,d):!!i;if(o&8){const l=t.dynamicProps;for(let f=0;f<l.length;f++){const p=l[f];if(i[p]!==n[p]&&!on(d,p))return!0}}}else return(r||c)&&(!c||!c.$stable)?!0:n===i?!1:n?i?Zs(n,i,d):!0:!!i;return!1}function Zs(e,t,u){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let r=0;r<n.length;r++){const s=n[r];if(t[s]!==e[s]&&!on(u,s))return!0}return!1}function ll({vnode:e,parent:t},u){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=u,t=t.parent;else break}}const Xs=e=>e.__isSuspense;function al(e,t){t&&t.pendingBranch?K(e)?t.effects.push(...e):t.effects.push(e):Fc(e)}const st=Symbol.for("v-fgt"),cn=Symbol.for("v-txt"),Wt=Symbol.for("v-cmt"),sr=Symbol.for("v-stc"),bu=[];let Le=null;function ir(e=!1){bu.push(Le=e?null:[])}function fl(){bu.pop(),Le=bu[bu.length-1]||null}let vu=1;function ei(e,t=!1){vu+=e,e<0&&Le&&t&&(Le.hasOnce=!0)}function ti(e){return e.dynamicChildren=vu>0?Le||Gt:null,fl(),vu>0&&Le&&Le.push(e),e}function ui(e,t,u,n,r,s){return ti(je(e,t,u,n,r,s,!0))}function dl(e,t,u,n,r){return ti(ke(e,t,u,n,r,!0))}function ln(e){return e?e.__v_isVNode===!0:!1}function Bu(e,t){return e.type===t.type&&e.key===t.key}const ni=({key:e})=>e??null,an=({ref:e,ref_key:t,ref_for:u})=>(typeof e=="number"&&(e=""+e),e!=null?ge(e)||Ee(e)||z(e)?{i:rt,r:e,k:t,f:!!u}:e:null);function je(e,t=null,u=null,n=0,r=null,s=e===st?0:1,i=!1,c=!1){const o={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ni(t),ref:t&&an(t),scopeId:Fs,slotScopeIds:null,children:u,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:n,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:rt};return c?(or(o,u),s&128&&e.normalize(o)):u&&(o.shapeFlag|=ge(u)?8:16),vu>0&&!i&&Le&&(o.patchFlag>0||s&6)&&o.patchFlag!==32&&Le.push(o),o}const ke=Dl;function Dl(e,t=null,u=null,n=0,r=null,s=!1){if((!e||e===Mc)&&(e=Wt),ln(e)){const c=ru(e,t,!0);return u&&or(c,u),vu>0&&!s&&Le&&(c.shapeFlag&6?Le[Le.indexOf(e)]=c:Le.push(c)),c.patchFlag=-2,c}if(xl(e)&&(e=e.__vccOpts),t){t=hl(t);let{class:c,style:o}=t;c&&!ge(c)&&(t.class=Nn(c)),he(o)&&(zn(o)&&!K(o)&&(o=be({},o)),t.style=Tn(o))}const i=ge(e)?1:Xs(e)?128:vc(e)?64:he(e)?4:z(e)?2:0;return je(e,t,u,n,r,i,s,!0)}function hl(e){return e?zn(e)||$s(e)?be({},e):e:null}function ru(e,t,u=!1,n=!1){const{props:r,ref:s,patchFlag:i,children:c,transition:o}=e,d=t?Cl(r||{},t):r,l={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&ni(d),ref:t&&t.ref?u&&s?K(s)?s.concat(an(t)):[s,an(t)]:an(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==st?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:o,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ru(e.ssContent),ssFallback:e.ssFallback&&ru(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return o&&n&&Yn(l,o.clone(l)),l}function pl(e=" ",t=0){return ke(cn,null,e,t)}function gl(e="",t=!1){return t?(ir(),dl(Wt,null,e)):ke(Wt,null,e)}function it(e){return e==null||typeof e=="boolean"?ke(Wt):K(e)?ke(st,null,e.slice()):ln(e)?Ot(e):ke(cn,null,String(e))}function Ot(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:ru(e)}function or(e,t){let u=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(K(t))u=16;else if(typeof t=="object")if(n&65){const r=t.default;r&&(r._c&&(r._d=!1),or(e,r()),r._c&&(r._d=!0));return}else{u=32;const r=t._;!r&&!$s(t)?t._ctx=rt:r===3&&rt&&(rt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else z(t)?(t={default:t,_ctx:rt},u=32):(t=String(t),n&64?(u=16,t=[pl(t)]):u=8);e.children=t,e.shapeFlag|=u}function Cl(...e){const t={};for(let u=0;u<e.length;u++){const n=e[u];for(const r in n)if(r==="class")t.class!==n.class&&(t.class=Nn([t.class,n.class]));else if(r==="style")t.style=Tn([t.style,n.style]);else if(Hu(r)){const s=t[r],i=n[r];i&&s!==i&&!(K(s)&&s.includes(i))&&(t[r]=s?[].concat(s,i):i)}else r!==""&&(t[r]=n[r])}return t}function ot(e,t,u,n=null){ut(e,t,7,[u,n])}const ml=Ns();let El=0;function Al(e,t,u){const n=e.type,r=(t?t.appContext:e.appContext)||ml,s={uid:El++,vnode:e,type:n,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Io(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:js(n,r),emitsOptions:Qs(n,r),emit:null,emitted:null,propsDefaults:ne,inheritAttrs:n.inheritAttrs,ctx:ne,data:ne,props:ne,attrs:ne,slots:ne,refs:ne,setupState:ne,setupContext:null,suspense:u,suspenseId:u?u.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=sl.bind(null,s),e.ce&&e.ce(s),s}let Fe=null,fn,cr;{const e=Wu(),t=(u,n)=>{let r;return(r=e[u])||(r=e[u]=[]),r.push(n),s=>{r.length>1?r.forEach(i=>i(s)):r[0](s)}};fn=t("__VUE_INSTANCE_SETTERS__",u=>Fe=u),cr=t("__VUE_SSR_SETTERS__",u=>_u=u)}const wu=e=>{const t=Fe;return fn(e),e.scope.on(),()=>{e.scope.off(),fn(t)}},ri=()=>{Fe&&Fe.scope.off(),fn(null)};function si(e){return e.vnode.shapeFlag&4}let _u=!1;function Fl(e,t=!1,u=!1){t&&cr(t);const{props:n,children:r}=e.vnode,s=si(e);Kc(e,n,s,t),Jc(e,r,u);const i=s?yl(e,t):void 0;return t&&cr(!1),i}function yl(e,t){const u=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,$c);const{setup:n}=u;if(n){ht();const r=e.setupContext=n.length>1?vl(e):null,s=wu(e),i=Xt(n,e,0,[e.props,r]),c=$r(i);if(pt(),s(),(c||e.sp)&&!Au(e)&&bs(e),c){if(i.then(ri,ri),t)return i.then(o=>{ii(e,o)}).catch(o=>{Xu(o,e,0)});e.asyncDep=i}else ii(e,i)}else oi(e)}function ii(e,t,u){z(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:he(t)&&(e.setupState=Ds(t)),oi(e)}function oi(e,t,u){const n=e.type;e.render||(e.render=n.render||ze);{const r=wu(e);ht();try{Lc(e)}finally{pt(),r()}}}const bl={get(e,t){return ve(e,"get",""),e[t]}};function vl(e){const t=u=>{e.exposed=u||{}};return{attrs:new Proxy(e.attrs,bl),slots:e.slots,emit:e.emit,expose:t}}function lr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Ds(uc(e.exposed)),{get(t,u){if(u in t)return t[u];if(u in Fu)return Fu[u](e)},has(t,u){return u in t||u in Fu}})):e.proxy}const Bl=/(?:^|[-_])(\w)/g,wl=e=>e.replace(Bl,t=>t.toUpperCase()).replace(/[-_]/g,"");function _l(e,t=!0){return z(e)?e.displayName||e.name:e.name||t&&e.__name}function ci(e,t,u=!1){let n=_l(t);if(!n&&t.__file){const r=t.__file.match(/([^/\\]+)\.\w+$/);r&&(n=r[1])}if(!n&&e&&e.parent){const r=s=>{for(const i in s)if(s[i]===t)return i};n=r(e.components||e.parent.type.components)||r(e.appContext.components)}return n?wl(n):u?"App":"Anonymous"}function xl(e){return z(e)&&"__vccOpts"in e}const Ye=(e,t)=>fc(e,t,_u);function ar(e,t,u){const n=arguments.length;return n===2?he(t)&&!K(t)?ln(t)?ke(e,null,[t]):ke(e,t):ke(e,null,t):(n>3?u=Array.prototype.slice.call(arguments,2):n===3&&ln(u)&&(u=[u]),ke(e,t,u))}const Sl="3.5.13";let fr;const li=typeof window<"u"&&window.trustedTypes;if(li)try{fr=li.createPolicy("vue",{createHTML:e=>e})}catch{}const ai=fr?e=>fr.createHTML(e):e=>e,Rl="http://www.w3.org/2000/svg",Ol="http://www.w3.org/1998/Math/MathML",Et=typeof document<"u"?document:null,fi=Et&&Et.createElement("template"),Pl={insert:(e,t,u)=>{t.insertBefore(e,u||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,u,n)=>{const r=t==="svg"?Et.createElementNS(Rl,e):t==="mathml"?Et.createElementNS(Ol,e):u?Et.createElement(e,{is:u}):Et.createElement(e);return e==="select"&&n&&n.multiple!=null&&r.setAttribute("multiple",n.multiple),r},createText:e=>Et.createTextNode(e),createComment:e=>Et.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Et.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,u,n,r,s){const i=u?u.previousSibling:t.lastChild;if(r&&(r===s||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),u),!(r===s||!(r=r.nextSibling)););else{fi.innerHTML=ai(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const c=fi.content;if(n==="svg"||n==="mathml"){const o=c.firstChild;for(;o.firstChild;)c.appendChild(o.firstChild);c.removeChild(o)}t.insertBefore(c,u)}return[i?i.nextSibling:t.firstChild,u?u.previousSibling:t.lastChild]}},Tl=Symbol("_vtc");function Nl(e,t,u){const n=e[Tl];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):u?e.setAttribute("class",t):e.className=t}const di=Symbol("_vod"),Il=Symbol("_vsh"),Ml=Symbol(""),$l=/(^|;)\s*display\s*:/;function Ll(e,t,u){const n=e.style,r=ge(u);let s=!1;if(u&&!r){if(t)if(ge(t))for(const i of t.split(";")){const c=i.slice(0,i.indexOf(":")).trim();u[c]==null&&dn(n,c,"")}else for(const i in t)u[i]==null&&dn(n,i,"");for(const i in u)i==="display"&&(s=!0),dn(n,i,u[i])}else if(r){if(t!==u){const i=n[Ml];i&&(u+=";"+i),n.cssText=u,s=$l.test(u)}}else t&&e.removeAttribute("style");di in e&&(e[di]=s?n.display:"",e[Il]&&(n.display="none"))}const Di=/\s*!important$/;function dn(e,t,u){if(K(u))u.forEach(n=>dn(e,t,n));else if(u==null&&(u=""),t.startsWith("--"))e.setProperty(t,u);else{const n=jl(e,t);Di.test(u)?e.setProperty($t(n),u.replace(Di,""),"important"):e[n]=u}}const hi=["Webkit","Moz","ms"],dr={};function jl(e,t){const u=dr[t];if(u)return u;let n=wt(t);if(n!=="filter"&&n in e)return dr[t]=n;n=kr(n);for(let r=0;r<hi.length;r++){const s=hi[r]+n;if(s in e)return dr[t]=s}return t}const pi="http://www.w3.org/1999/xlink";function gi(e,t,u,n,r,s=To(t)){n&&t.startsWith("xlink:")?u==null?e.removeAttributeNS(pi,t.slice(6,t.length)):e.setAttributeNS(pi,t,u):u==null||s&&!Ur(u)?e.removeAttribute(t):e.setAttribute(t,s?"":Bt(u)?String(u):u)}function Ci(e,t,u,n,r){if(t==="innerHTML"||t==="textContent"){u!=null&&(e[t]=t==="innerHTML"?ai(u):u);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const c=s==="OPTION"?e.getAttribute("value")||"":e.value,o=u==null?e.type==="checkbox"?"on":"":String(u);(c!==o||!("_value"in e))&&(e.value=o),u==null&&e.removeAttribute(t),e._value=u;return}let i=!1;if(u===""||u==null){const c=typeof e[t];c==="boolean"?u=Ur(u):u==null&&c==="string"?(u="",i=!0):c==="number"&&(u=0,i=!0)}try{e[t]=u}catch{}i&&e.removeAttribute(r||t)}function kl(e,t,u,n){e.addEventListener(t,u,n)}function Hl(e,t,u,n){e.removeEventListener(t,u,n)}const mi=Symbol("_vei");function Vl(e,t,u,n,r=null){const s=e[mi]||(e[mi]={}),i=s[t];if(n&&i)i.value=n;else{const[c,o]=Ul(t);if(n){const d=s[t]=ql(n,r);kl(e,c,d,o)}else i&&(Hl(e,c,i,o),s[t]=void 0)}}const Ei=/(?:Once|Passive|Capture)$/;function Ul(e){let t;if(Ei.test(e)){t={};let n;for(;n=e.match(Ei);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):$t(e.slice(2)),t]}let Dr=0;const Wl=Promise.resolve(),Kl=()=>Dr||(Wl.then(()=>Dr=0),Dr=Date.now());function ql(e,t){const u=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=u.attached)return;ut(zl(n,u.value),t,5,[n])};return u.value=e,u.attached=Kl(),u}function zl(e,t){if(K(t)){const u=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{u.call(e),e._stopped=!0},t.map(n=>r=>!r._stopped&&n&&n(r))}else return t}const Ai=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Gl=(e,t,u,n,r,s)=>{const i=r==="svg";t==="class"?Nl(e,n,i):t==="style"?Ll(e,u,n):Hu(t)?xn(t)||Vl(e,t,u,n,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Jl(e,t,n,i))?(Ci(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&gi(e,t,n,i,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ge(n))?Ci(e,wt(t),n,s,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),gi(e,t,n,i))};function Jl(e,t,u,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ai(t)&&z(u));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Ai(t)&&ge(u)?!1:t in e}const Ql=be({patchProp:Gl},Pl);let Fi;function Yl(){return Fi||(Fi=Yc(Ql))}const Zl=(...e)=>{const t=Yl().createApp(...e),{mount:u}=t;return t.mount=n=>{const r=ea(n);if(!r)return;const s=t._component;!z(s)&&!s.render&&!s.template&&(s.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=u(r,!1,Xl(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Xl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ea(e){return ge(e)?document.querySelector(e):e}var hr={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var yi;function ta(){return yi||(yi=1,function(e){(function(){var t={}.hasOwnProperty;function u(){for(var s="",i=0;i<arguments.length;i++){var c=arguments[i];c&&(s=r(s,n(c)))}return s}function n(s){if(typeof s=="string"||typeof s=="number")return s;if(typeof s!="object")return"";if(Array.isArray(s))return u.apply(null,s);if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]"))return s.toString();var i="";for(var c in s)t.call(s,c)&&s[c]&&(i=r(i,c));return i}function r(s,i){return i?s?s+" "+i:s+i:s}e.exports?(u.default=u,e.exports=u):window.classNames=u})()}(hr)),hr.exports}ta();function ua(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}function na(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),e.nonce!==void 0&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}var ra=function(){function e(u){var n=this;this._insertTag=function(r){var s;n.tags.length===0?n.insertionPoint?s=n.insertionPoint.nextSibling:n.prepend?s=n.container.firstChild:s=n.before:s=n.tags[n.tags.length-1].nextSibling,n.container.insertBefore(r,s),n.tags.push(r)},this.isSpeedy=u.speedy===void 0?!0:u.speedy,this.tags=[],this.ctr=0,this.nonce=u.nonce,this.key=u.key,this.container=u.container,this.prepend=u.prepend,this.insertionPoint=u.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(n){n.forEach(this._insertTag)},t.insert=function(n){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(na(this));var r=this.tags[this.tags.length-1];if(this.isSpeedy){var s=ua(r);try{s.insertRule(n,s.cssRules.length)}catch{}}else r.appendChild(document.createTextNode(n));this.ctr++},t.flush=function(){this.tags.forEach(function(n){var r;return(r=n.parentNode)==null?void 0:r.removeChild(n)}),this.tags=[],this.ctr=0},e}(),Be="-ms-",Dn="-moz-",re="-webkit-",bi="comm",pr="rule",gr="decl",sa="@import",vi="@keyframes",ia="@layer",oa=Math.abs,hn=String.fromCharCode,ca=Object.assign;function la(e,t){return ye(e,0)^45?(((t<<2^ye(e,0))<<2^ye(e,1))<<2^ye(e,2))<<2^ye(e,3):0}function Bi(e){return e.trim()}function aa(e,t){return(e=t.exec(e))?e[0]:e}function se(e,t,u){return e.replace(t,u)}function Cr(e,t){return e.indexOf(t)}function ye(e,t){return e.charCodeAt(t)|0}function xu(e,t,u){return e.slice(t,u)}function ct(e){return e.length}function mr(e){return e.length}function pn(e,t){return t.push(e),e}function fa(e,t){return e.map(t).join("")}var gn=1,su=1,wi=0,Ne=0,Ce=0,iu="";function Cn(e,t,u,n,r,s,i){return{value:e,root:t,parent:u,type:n,props:r,children:s,line:gn,column:su,length:i,return:""}}function Su(e,t){return ca(Cn("",null,null,"",null,null,0),e,{length:-e.length},t)}function da(){return Ce}function Da(){return Ce=Ne>0?ye(iu,--Ne):0,su--,Ce===10&&(su=1,gn--),Ce}function He(){return Ce=Ne<wi?ye(iu,Ne++):0,su++,Ce===10&&(su=1,gn++),Ce}function lt(){return ye(iu,Ne)}function mn(){return Ne}function Ru(e,t){return xu(iu,e,t)}function Ou(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function _i(e){return gn=su=1,wi=ct(iu=e),Ne=0,[]}function xi(e){return iu="",e}function En(e){return Bi(Ru(Ne-1,Er(e===91?e+2:e===40?e+1:e)))}function ha(e){for(;(Ce=lt())&&Ce<33;)He();return Ou(e)>2||Ou(Ce)>3?"":" "}function pa(e,t){for(;--t&&He()&&!(Ce<48||Ce>102||Ce>57&&Ce<65||Ce>70&&Ce<97););return Ru(e,mn()+(t<6&&lt()==32&&He()==32))}function Er(e){for(;He();)switch(Ce){case e:return Ne;case 34:case 39:e!==34&&e!==39&&Er(Ce);break;case 40:e===41&&Er(e);break;case 92:He();break}return Ne}function ga(e,t){for(;He()&&e+Ce!==57;)if(e+Ce===84&&lt()===47)break;return"/*"+Ru(t,Ne-1)+"*"+hn(e===47?e:He())}function Ca(e){for(;!Ou(lt());)He();return Ru(e,Ne)}function ma(e){return xi(An("",null,null,null,[""],e=_i(e),0,[0],e))}function An(e,t,u,n,r,s,i,c,o){for(var d=0,l=0,f=i,p=0,h=0,A=0,C=1,v=1,E=1,B=0,P="",x=r,k=s,q=n,H=P;v;)switch(A=B,B=He()){case 40:if(A!=108&&ye(H,f-1)==58){Cr(H+=se(En(B),"&","&\f"),"&\f")!=-1&&(E=-1);break}case 34:case 39:case 91:H+=En(B);break;case 9:case 10:case 13:case 32:H+=ha(A);break;case 92:H+=pa(mn()-1,7);continue;case 47:switch(lt()){case 42:case 47:pn(Ea(ga(He(),mn()),t,u),o);break;default:H+="/"}break;case 123*C:c[d++]=ct(H)*E;case 125*C:case 59:case 0:switch(B){case 0:case 125:v=0;case 59+l:E==-1&&(H=se(H,/\f/g,"")),h>0&&ct(H)-f&&pn(h>32?Ri(H+";",n,u,f-1):Ri(se(H," ","")+";",n,u,f-2),o);break;case 59:H+=";";default:if(pn(q=Si(H,t,u,d,l,r,c,P,x=[],k=[],f),s),B===123)if(l===0)An(H,t,q,q,x,s,f,c,k);else switch(p===99&&ye(H,3)===110?100:p){case 100:case 108:case 109:case 115:An(e,q,q,n&&pn(Si(e,q,q,0,0,r,c,P,r,x=[],f),k),r,k,f,c,n?x:k);break;default:An(H,q,q,q,[""],k,0,c,k)}}d=l=h=0,C=E=1,P=H="",f=i;break;case 58:f=1+ct(H),h=A;default:if(C<1){if(B==123)--C;else if(B==125&&C++==0&&Da()==125)continue}switch(H+=hn(B),B*C){case 38:E=l>0?1:(H+="\f",-1);break;case 44:c[d++]=(ct(H)-1)*E,E=1;break;case 64:lt()===45&&(H+=En(He())),p=lt(),l=f=ct(P=H+=Ca(mn())),B++;break;case 45:A===45&&ct(H)==2&&(C=0)}}return s}function Si(e,t,u,n,r,s,i,c,o,d,l){for(var f=r-1,p=r===0?s:[""],h=mr(p),A=0,C=0,v=0;A<n;++A)for(var E=0,B=xu(e,f+1,f=oa(C=i[A])),P=e;E<h;++E)(P=Bi(C>0?p[E]+" "+B:se(B,/&\f/g,p[E])))&&(o[v++]=P);return Cn(e,t,u,r===0?pr:c,o,d,l)}function Ea(e,t,u){return Cn(e,t,u,bi,hn(da()),xu(e,2,-2),0)}function Ri(e,t,u,n){return Cn(e,t,u,gr,xu(e,0,n),xu(e,n+1,-1),n)}function ou(e,t){for(var u="",n=mr(e),r=0;r<n;r++)u+=t(e[r],r,e,t)||"";return u}function Aa(e,t,u,n){switch(e.type){case ia:if(e.children.length)break;case sa:case gr:return e.return=e.return||e.value;case bi:return"";case vi:return e.return=e.value+"{"+ou(e.children,n)+"}";case pr:e.value=e.props.join(",")}return ct(u=ou(e.children,n))?e.return=e.value+"{"+u+"}":""}function Fa(e){var t=mr(e);return function(u,n,r,s){for(var i="",c=0;c<t;c++)i+=e[c](u,n,r,s)||"";return i}}function ya(e){return function(t){t.root||(t=t.return)&&e(t)}}function ba(e){var t=Object.create(null);return function(u){return t[u]===void 0&&(t[u]=e(u)),t[u]}}var va=function(t,u,n){for(var r=0,s=0;r=s,s=lt(),r===38&&s===12&&(u[n]=1),!Ou(s);)He();return Ru(t,Ne)},Ba=function(t,u){var n=-1,r=44;do switch(Ou(r)){case 0:r===38&&lt()===12&&(u[n]=1),t[n]+=va(Ne-1,u,n);break;case 2:t[n]+=En(r);break;case 4:if(r===44){t[++n]=lt()===58?"&\f":"",u[n]=t[n].length;break}default:t[n]+=hn(r)}while(r=He());return t},wa=function(t,u){return xi(Ba(_i(t),u))},Oi=new WeakMap,_a=function(t){if(!(t.type!=="rule"||!t.parent||t.length<1)){for(var u=t.value,n=t.parent,r=t.column===n.column&&t.line===n.line;n.type!=="rule";)if(n=n.parent,!n)return;if(!(t.props.length===1&&u.charCodeAt(0)!==58&&!Oi.get(n))&&!r){Oi.set(t,!0);for(var s=[],i=wa(u,s),c=n.props,o=0,d=0;o<i.length;o++)for(var l=0;l<c.length;l++,d++)t.props[d]=s[o]?i[o].replace(/&\f/g,c[l]):c[l]+" "+i[o]}}},xa=function(t){if(t.type==="decl"){var u=t.value;u.charCodeAt(0)===108&&u.charCodeAt(2)===98&&(t.return="",t.value="")}};function Pi(e,t){switch(la(e,t)){case 5103:return re+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return re+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return re+e+Dn+e+Be+e+e;case 6828:case 4268:return re+e+Be+e+e;case 6165:return re+e+Be+"flex-"+e+e;case 5187:return re+e+se(e,/(\w+).+(:[^]+)/,re+"box-$1$2"+Be+"flex-$1$2")+e;case 5443:return re+e+Be+"flex-item-"+se(e,/flex-|-self/,"")+e;case 4675:return re+e+Be+"flex-line-pack"+se(e,/align-content|flex-|-self/,"")+e;case 5548:return re+e+Be+se(e,"shrink","negative")+e;case 5292:return re+e+Be+se(e,"basis","preferred-size")+e;case 6060:return re+"box-"+se(e,"-grow","")+re+e+Be+se(e,"grow","positive")+e;case 4554:return re+se(e,/([^-])(transform)/g,"$1"+re+"$2")+e;case 6187:return se(se(se(e,/(zoom-|grab)/,re+"$1"),/(image-set)/,re+"$1"),e,"")+e;case 5495:case 3959:return se(e,/(image-set\([^]*)/,re+"$1$`$1");case 4968:return se(se(e,/(.+:)(flex-)?(.*)/,re+"box-pack:$3"+Be+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+re+e+e;case 4095:case 3583:case 4068:case 2532:return se(e,/(.+)-inline(.+)/,re+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(ct(e)-1-t>6)switch(ye(e,t+1)){case 109:if(ye(e,t+4)!==45)break;case 102:return se(e,/(.+:)(.+)-([^]+)/,"$1"+re+"$2-$3$1"+Dn+(ye(e,t+3)==108?"$3":"$2-$3"))+e;case 115:return~Cr(e,"stretch")?Pi(se(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(ye(e,t+1)!==115)break;case 6444:switch(ye(e,ct(e)-3-(~Cr(e,"!important")&&10))){case 107:return se(e,":",":"+re)+e;case 101:return se(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+re+(ye(e,14)===45?"inline-":"")+"box$3$1"+re+"$2$3$1"+Be+"$2box$3")+e}break;case 5936:switch(ye(e,t+11)){case 114:return re+e+Be+se(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return re+e+Be+se(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return re+e+Be+se(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return re+e+Be+e+e}return e}var Sa=function(t,u,n,r){if(t.length>-1&&!t.return)switch(t.type){case gr:t.return=Pi(t.value,t.length);break;case vi:return ou([Su(t,{value:se(t.value,"@","@"+re)})],r);case pr:if(t.length)return fa(t.props,function(s){switch(aa(s,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return ou([Su(t,{props:[se(s,/:(read-\w+)/,":"+Dn+"$1")]})],r);case"::placeholder":return ou([Su(t,{props:[se(s,/:(plac\w+)/,":"+re+"input-$1")]}),Su(t,{props:[se(s,/:(plac\w+)/,":"+Dn+"$1")]}),Su(t,{props:[se(s,/:(plac\w+)/,Be+"input-$1")]})],r)}return""})}},Ra=[Sa],Oa=function(t){var u=t.key;if(u==="css"){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,function(C){var v=C.getAttribute("data-emotion");v.indexOf(" ")!==-1&&(document.head.appendChild(C),C.setAttribute("data-s",""))})}var r=t.stylisPlugins||Ra,s={},i,c=[];i=t.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+u+' "]'),function(C){for(var v=C.getAttribute("data-emotion").split(" "),E=1;E<v.length;E++)s[v[E]]=!0;c.push(C)});var o,d=[_a,xa];{var l,f=[Aa,ya(function(C){l.insert(C)})],p=Fa(d.concat(r,f)),h=function(v){return ou(ma(v),p)};o=function(v,E,B,P){l=B,h(v?v+"{"+E.styles+"}":E.styles),P&&(A.inserted[E.name]=!0)}}var A={key:u,sheet:new ra({key:u,container:i,nonce:t.nonce,speedy:t.speedy,prepend:t.prepend,insertionPoint:t.insertionPoint}),nonce:t.nonce,inserted:s,registered:{},insert:o};return A.sheet.hydrate(c),A};function Pa(e){for(var t=0,u,n=0,r=e.length;r>=4;++n,r-=4)u=e.charCodeAt(n)&255|(e.charCodeAt(++n)&255)<<8|(e.charCodeAt(++n)&255)<<16|(e.charCodeAt(++n)&255)<<24,u=(u&65535)*1540483477+((u>>>16)*59797<<16),u^=u>>>24,t=(u&65535)*1540483477+((u>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(r){case 3:t^=(e.charCodeAt(n+2)&255)<<16;case 2:t^=(e.charCodeAt(n+1)&255)<<8;case 1:t^=e.charCodeAt(n)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}var Ta={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Na=/[A-Z]|^ms/g,Ia=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Ti=function(t){return t.charCodeAt(1)===45},Ni=function(t){return t!=null&&typeof t!="boolean"},Ar=ba(function(e){return Ti(e)?e:e.replace(Na,"-$&").toLowerCase()}),Ii=function(t,u){switch(t){case"animation":case"animationName":if(typeof u=="string")return u.replace(Ia,function(n,r,s){return Pt={name:r,styles:s,next:Pt},r})}return Ta[t]!==1&&!Ti(t)&&typeof u=="number"&&u!==0?u+"px":u};function Fn(e,t,u){if(u==null)return"";var n=u;if(n.__emotion_styles!==void 0)return n;switch(typeof u){case"boolean":return"";case"object":{var r=u;if(r.anim===1)return Pt={name:r.name,styles:r.styles,next:Pt},r.name;var s=u;if(s.styles!==void 0){var i=s.next;if(i!==void 0)for(;i!==void 0;)Pt={name:i.name,styles:i.styles,next:Pt},i=i.next;var c=s.styles+";";return c}return Ma(e,t,u)}}var o=u;if(t==null)return o;var d=t[o];return d!==void 0?d:o}function Ma(e,t,u){var n="";if(Array.isArray(u))for(var r=0;r<u.length;r++)n+=Fn(e,t,u[r])+";";else for(var s in u){var i=u[s];if(typeof i!="object"){var c=i;t!=null&&t[c]!==void 0?n+=s+"{"+t[c]+"}":Ni(c)&&(n+=Ar(s)+":"+Ii(s,c)+";")}else if(Array.isArray(i)&&typeof i[0]=="string"&&(t==null||t[i[0]]===void 0))for(var o=0;o<i.length;o++)Ni(i[o])&&(n+=Ar(s)+":"+Ii(s,i[o])+";");else{var d=Fn(e,t,i);switch(s){case"animation":case"animationName":{n+=Ar(s)+":"+d+";";break}default:n+=s+"{"+d+"}"}}}return n}var Mi=/label:\s*([^\s;{]+)\s*(;|$)/g,Pt;function Fr(e,t,u){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var n=!0,r="";Pt=void 0;var s=e[0];if(s==null||s.raw===void 0)n=!1,r+=Fn(u,t,s);else{var i=s;r+=i[0]}for(var c=1;c<e.length;c++)if(r+=Fn(u,t,e[c]),n){var o=s;r+=o[c]}Mi.lastIndex=0;for(var d="",l;(l=Mi.exec(r))!==null;)d+="-"+l[1];var f=Pa(r)+d;return{name:f,styles:r,next:Pt}}function $i(e,t,u){var n="";return u.split(" ").forEach(function(r){e[r]!==void 0?t.push(e[r]+";"):r&&(n+=r+" ")}),n}var $a=function(t,u,n){var r=t.key+"-"+u.name;t.registered[r]===void 0&&(t.registered[r]=u.styles)},La=function(t,u,n){$a(t,u);var r=t.key+"-"+u.name;if(t.inserted[u.name]===void 0){var s=u;do t.insert(u===s?"."+r:"",s,t.sheet,!0),s=s.next;while(s!==void 0)}};function Li(e,t){if(e.inserted[t.name]===void 0)return e.insert("",t,e.sheet,!0)}function ji(e,t,u){var n=[],r=$i(e,n,u);return n.length<2?u:r+t(n)}var ja=function(t){var u=Oa(t);u.sheet.speedy=function(c){this.isSpeedy=c},u.compat=!0;var n=function(){for(var o=arguments.length,d=new Array(o),l=0;l<o;l++)d[l]=arguments[l];var f=Fr(d,u.registered,void 0);return La(u,f),u.key+"-"+f.name},r=function(){for(var o=arguments.length,d=new Array(o),l=0;l<o;l++)d[l]=arguments[l];var f=Fr(d,u.registered),p="animation-"+f.name;return Li(u,{name:f.name,styles:"@keyframes "+p+"{"+f.styles+"}"}),p},s=function(){for(var o=arguments.length,d=new Array(o),l=0;l<o;l++)d[l]=arguments[l];var f=Fr(d,u.registered);Li(u,f)},i=function(){for(var o=arguments.length,d=new Array(o),l=0;l<o;l++)d[l]=arguments[l];return ji(u.registered,n,ka(d))};return{css:n,cx:i,injectGlobal:s,keyframes:r,hydrate:function(o){o.forEach(function(d){u.inserted[d]=!0})},flush:function(){u.registered={},u.inserted={},u.sheet.flush()},sheet:u.sheet,cache:u,getRegisteredStyles:$i.bind(null,u.registered),merge:ji.bind(null,u.registered,n)}},ka=function e(t){for(var u="",n=0;n<t.length;n++){var r=t[n];if(r!=null){var s=void 0;switch(typeof r){case"boolean":break;case"object":{if(Array.isArray(r))s=e(r);else{s="";for(var i in r)r[i]&&i&&(s&&(s+=" "),s+=i)}break}default:s=r}s&&(u&&(u+=" "),u+=s)}}return u};ja({key:"css"});var Pu={exports:{}},Ha=Pu.exports,ki;function Va(){return ki||(ki=1,function(e,t){(function(u,n){n(t)})(Ha,function(u){var n=Object.defineProperty,r=Object.defineProperties,s=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,d=(S,M,j)=>M in S?n(S,M,{enumerable:!0,configurable:!0,writable:!0,value:j}):S[M]=j,l=(S,M)=>{for(var j in M||(M={}))c.call(M,j)&&d(S,j,M[j]);if(i)for(var j of i(M))o.call(M,j)&&d(S,j,M[j]);return S},f=(S,M)=>r(S,s(M)),p=(S,M,j)=>new Promise((ee,J)=>{var m=I=>{try{_(j.next(I))}catch(Y){J(Y)}},O=I=>{try{_(j.throw(I))}catch(Y){J(Y)}},_=I=>I.done?ee(I.value):Promise.resolve(I.value).then(m,O);_((j=j.apply(S,M)).next())}),h=(S=>(S[S.NONE=0]="NONE",S[S.LOADING=1]="LOADING",S[S.LOADED=2]="LOADED",S[S.ERROR=3]="ERROR",S))(h||{});class A{constructor(M){this.items={},this.factory=M}getOrCreateItemByURL(M){let j=this.items[M];return j||(j=this.items[M]=this.factory(M)),j}tryGetItemByURL(M){var j;return(j=this.items[M])!=null?j:null}removeItemByURL(M){const j=this.items[M];return j&&(this.items[M]=null,j)}}const C="__RUNTIME_IMPORT__";function v(S,M){var j,ee;const J=globalThis,m=(j=J[C])!=null?j:J[C]={};return(ee=m[S])!=null?ee:m[S]=M()}const E=v("cssCache",()=>new A(S=>({url:S,status:h.NONE,el:null,error:null,reject:null})));function B(S,M,j){const ee={handleLoad(){S.removeEventListener("load",ee.handleLoad),S.removeEventListener("error",ee.handleError),M()},handleError(J){S.removeEventListener("load",ee.handleLoad),S.removeEventListener("error",ee.handleError),j(J)}};S.addEventListener("load",ee.handleLoad),S.addEventListener("error",ee.handleError)}function P(S){const M=E.getOrCreateItemByURL(S),{status:j,error:ee}=M;return j===h.LOADED?Promise.resolve():j===h.ERROR?Promise.reject(ee):j===h.LOADING?new Promise((J,m)=>{const{el:O}=M;B(O,()=>J(),_=>m(_.error))}):(M.status=h.LOADING,new Promise((J,m)=>{const O=document.createElement("link");O.rel="stylesheet",O.href=S,B(O,()=>{M.status=h.LOADED,J()},_=>{const I=_.error||new Error(`Load css failed. href=${S}`);M.status=h.ERROR,M.error=I,m(I)}),M.el=O,O.setAttribute("data-runtime-import-type","css"),document.head.appendChild(O)}))}function x(S){return Promise.all(S.map(M=>P(M))).then(()=>Promise.resolve()).catch(M=>Promise.reject(M))}const k=v("jsCache",()=>new A(S=>({url:S,status:h.NONE,el:null,error:null,reject:null,exportThing:void 0}))),q=globalThis,{define:H}=q,{keys:Oe}=Object;let et=!1;typeof H<"u"&&!H.runtime_import&&(console.warn("runtime-import should NOT coexist with requiesjs or seajs or any other AMD/CMD loader."),et=!0);const Pe=v("pendingItemMap",()=>({})),We=function(...S){const M=S.pop(),{currentScript:j}=document;if(!j)throw new Error("currentScript is null.");const{src:ee}=j,J=Pe[ee];if(!J)throw new Error(`Can NOT find item, src=${ee}`);Pe[ee]=null;try{let m=S[0]||[],O=null;typeof m=="string"&&(O=m,m=S[1]||[]);const _=J.exportThing=(()=>{let I=!1;const Y={};let a=M(...m.map(D=>D==="exports"?(I=!0,Y):q[D]));return!a&&I&&(a=Y),a})();O&&(q[O]=_),_&&Oe(_).length===1&&_.default&&(J.exportThing=_.default,J.exportThing.default=_.default)}catch(m){J.status=h.ERROR,m instanceof Error&&(J.error=m),J.reject(m)}},vt=()=>{const{currentScript:S}=document;if(S){const{src:M}=S;if(Pe[M])return!0}return!1};["amd","cmd"].forEach(S=>{Object.defineProperty(We,S,{get:vt})}),We.runtime_import=!0;function Ke(S,M){if(et)throw new Error("runtime-import UMD mode uses window.define, you should NOT have your own window.define.");q.define||(q.define=We),Pe[S]=M}function zt(S){const M=/legao-comp\/(.*)\/[\d.]+\/web.js$/.exec(S),j=window;if(M&&M.length>0){const ee=M[1];j.g_config=j.g_config||{},j.g_config.appKey=ee}}function Mt(S,M,j){S.status=h.LOADING,zt(M);const{umd:ee,crossOrigin:J}=j;return new Promise((m,O)=>{const _=document.createElement("script");if(_.src=M,_.async=!1,_.crossOrigin=J,ee){_.setAttribute("data-runtime-import-type","javascript-umd"),S.reject=O;const I=_.src;Ke(I,S)}else _.setAttribute("data-runtime-import-type","javascript");B(_,()=>{S.status=h.LOADED,S.el=null,m(S.exportThing)},I=>{const Y=I.error||new Error(`Load javascript failed. src=${M}`);S.status=h.ERROR,S.error=Y,S.el=null,O(Y)}),S.el=_,document.body.appendChild(_)})}function me(S,M){const j=k.getOrCreateItemByURL(S),{status:ee,exportThing:J,error:m}=j;if(ee===h.LOADED)return Promise.resolve(J);if(ee===h.ERROR)return Promise.reject(m);if(ee===h.LOADING){const{el:O}=j;return new Promise((_,I)=>{B(O,()=>_(j.exportThing),Y=>I(Y.error))})}return Mt(j,S,M)}function ce(S,M){let j=Promise.resolve();const ee=S.length-1,{umd:J}=M;return S.forEach((m,O)=>{const _=J&&O===ee;j=j.then(()=>me(m,f(l({},M),{umd:_})))}),j}function X(S){return p(this,null,function*(){const{scripts:M,styles:j}=S;if(j){const{urls:_}=j;yield x(_)}const{dependencies:ee=[],entry:J,umd:m=!0,crossOrigin:O="anonymous"}=M;if((J?ee.concat([J]):ee).length)return yield ce(ee.concat([J]),{umd:m,crossOrigin:O})})}function tt(S,M){return p(this,null,function*(){const j=M??{};return yield X({scripts:{dependencies:[],entry:S,umd:j.umd,crossOrigin:j.crossOrigin}})})}function at(S){return p(this,null,function*(){return yield X({scripts:{dependencies:[],entry:""},styles:{urls:[S]}})})}const qe=X;u.importComponent=X,u.importModule=qe,u.importScript=tt,u.importStyle=at,Object.defineProperty(u,Symbol.toStringTag,{value:"Module"})})}(Pu,Pu.exports)),Pu.exports}Va();var Ua=/[\u1680\u2000-\u200A\u202F\u205F\u3000]/,Wa=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,Ka=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/,yr={Space_Separator:Ua,ID_Start:Wa,ID_Continue:Ka},pe={isSpaceSeparator(e){return typeof e=="string"&&yr.Space_Separator.test(e)},isIdStartChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="$"||e==="_"||yr.ID_Start.test(e))},isIdContinueChar(e){return typeof e=="string"&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="$"||e==="_"||e==="‌"||e==="‍"||yr.ID_Continue.test(e))},isDigit(e){return typeof e=="string"&&/[0-9]/.test(e)},isHexDigit(e){return typeof e=="string"&&/[0-9A-Fa-f]/.test(e)}};let br,Re,At,yn,Tt,Ze,Ae,vr,Tu;var qa=function(t,u){br=String(t),Re="start",At=[],yn=0,Tt=1,Ze=0,Ae=void 0,vr=void 0,Tu=void 0;do Ae=za(),Qa[Re]();while(Ae.type!=="eof");return typeof u=="function"?Br({"":Tu},"",u):Tu};function Br(e,t,u){const n=e[t];if(n!=null&&typeof n=="object")if(Array.isArray(n))for(let r=0;r<n.length;r++){const s=String(r),i=Br(n,s,u);i===void 0?delete n[s]:Object.defineProperty(n,s,{value:i,writable:!0,enumerable:!0,configurable:!0})}else for(const r in n){const s=Br(n,r,u);s===void 0?delete n[r]:Object.defineProperty(n,r,{value:s,writable:!0,enumerable:!0,configurable:!0})}return u.call(e,t,n)}let Q,G,Nu,Ft,Z;function za(){for(Q="default",G="",Nu=!1,Ft=1;;){Z=yt();const e=Hi[Q]();if(e)return e}}function yt(){if(br[yn])return String.fromCodePoint(br.codePointAt(yn))}function y(){const e=yt();return e===`
`?(Tt++,Ze=0):e?Ze+=e.length:Ze++,e&&(yn+=e.length),e}const Hi={default(){switch(Z){case"	":case"\v":case"\f":case" ":case" ":case"\uFEFF":case`
`:case"\r":case"\u2028":case"\u2029":y();return;case"/":y(),Q="comment";return;case void 0:return y(),de("eof")}if(pe.isSpaceSeparator(Z)){y();return}return Hi[Re]()},comment(){switch(Z){case"*":y(),Q="multiLineComment";return;case"/":y(),Q="singleLineComment";return}throw De(y())},multiLineComment(){switch(Z){case"*":y(),Q="multiLineCommentAsterisk";return;case void 0:throw De(y())}y()},multiLineCommentAsterisk(){switch(Z){case"*":y();return;case"/":y(),Q="default";return;case void 0:throw De(y())}y(),Q="multiLineComment"},singleLineComment(){switch(Z){case`
`:case"\r":case"\u2028":case"\u2029":y(),Q="default";return;case void 0:return y(),de("eof")}y()},value(){switch(Z){case"{":case"[":return de("punctuator",y());case"n":return y(),Kt("ull"),de("null",null);case"t":return y(),Kt("rue"),de("boolean",!0);case"f":return y(),Kt("alse"),de("boolean",!1);case"-":case"+":y()==="-"&&(Ft=-1),Q="sign";return;case".":G=y(),Q="decimalPointLeading";return;case"0":G=y(),Q="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":G=y(),Q="decimalInteger";return;case"I":return y(),Kt("nfinity"),de("numeric",1/0);case"N":return y(),Kt("aN"),de("numeric",NaN);case'"':case"'":Nu=y()==='"',G="",Q="string";return}throw De(y())},identifierNameStartEscape(){if(Z!=="u")throw De(y());y();const e=wr();switch(e){case"$":case"_":break;default:if(!pe.isIdStartChar(e))throw Vi();break}G+=e,Q="identifierName"},identifierName(){switch(Z){case"$":case"_":case"‌":case"‍":G+=y();return;case"\\":y(),Q="identifierNameEscape";return}if(pe.isIdContinueChar(Z)){G+=y();return}return de("identifier",G)},identifierNameEscape(){if(Z!=="u")throw De(y());y();const e=wr();switch(e){case"$":case"_":case"‌":case"‍":break;default:if(!pe.isIdContinueChar(e))throw Vi();break}G+=e,Q="identifierName"},sign(){switch(Z){case".":G=y(),Q="decimalPointLeading";return;case"0":G=y(),Q="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":G=y(),Q="decimalInteger";return;case"I":return y(),Kt("nfinity"),de("numeric",Ft*(1/0));case"N":return y(),Kt("aN"),de("numeric",NaN)}throw De(y())},zero(){switch(Z){case".":G+=y(),Q="decimalPoint";return;case"e":case"E":G+=y(),Q="decimalExponent";return;case"x":case"X":G+=y(),Q="hexadecimal";return}return de("numeric",Ft*0)},decimalInteger(){switch(Z){case".":G+=y(),Q="decimalPoint";return;case"e":case"E":G+=y(),Q="decimalExponent";return}if(pe.isDigit(Z)){G+=y();return}return de("numeric",Ft*Number(G))},decimalPointLeading(){if(pe.isDigit(Z)){G+=y(),Q="decimalFraction";return}throw De(y())},decimalPoint(){switch(Z){case"e":case"E":G+=y(),Q="decimalExponent";return}if(pe.isDigit(Z)){G+=y(),Q="decimalFraction";return}return de("numeric",Ft*Number(G))},decimalFraction(){switch(Z){case"e":case"E":G+=y(),Q="decimalExponent";return}if(pe.isDigit(Z)){G+=y();return}return de("numeric",Ft*Number(G))},decimalExponent(){switch(Z){case"+":case"-":G+=y(),Q="decimalExponentSign";return}if(pe.isDigit(Z)){G+=y(),Q="decimalExponentInteger";return}throw De(y())},decimalExponentSign(){if(pe.isDigit(Z)){G+=y(),Q="decimalExponentInteger";return}throw De(y())},decimalExponentInteger(){if(pe.isDigit(Z)){G+=y();return}return de("numeric",Ft*Number(G))},hexadecimal(){if(pe.isHexDigit(Z)){G+=y(),Q="hexadecimalInteger";return}throw De(y())},hexadecimalInteger(){if(pe.isHexDigit(Z)){G+=y();return}return de("numeric",Ft*Number(G))},string(){switch(Z){case"\\":y(),G+=Ga();return;case'"':if(Nu)return y(),de("string",G);G+=y();return;case"'":if(!Nu)return y(),de("string",G);G+=y();return;case`
`:case"\r":throw De(y());case"\u2028":case"\u2029":Ya(Z);break;case void 0:throw De(y())}G+=y()},start(){switch(Z){case"{":case"[":return de("punctuator",y())}Q="value"},beforePropertyName(){switch(Z){case"$":case"_":G=y(),Q="identifierName";return;case"\\":y(),Q="identifierNameStartEscape";return;case"}":return de("punctuator",y());case'"':case"'":Nu=y()==='"',Q="string";return}if(pe.isIdStartChar(Z)){G+=y(),Q="identifierName";return}throw De(y())},afterPropertyName(){if(Z===":")return de("punctuator",y());throw De(y())},beforePropertyValue(){Q="value"},afterPropertyValue(){switch(Z){case",":case"}":return de("punctuator",y())}throw De(y())},beforeArrayValue(){if(Z==="]")return de("punctuator",y());Q="value"},afterArrayValue(){switch(Z){case",":case"]":return de("punctuator",y())}throw De(y())},end(){throw De(y())}};function de(e,t){return{type:e,value:t,line:Tt,column:Ze}}function Kt(e){for(const t of e){if(yt()!==t)throw De(y());y()}}function Ga(){switch(yt()){case"b":return y(),"\b";case"f":return y(),"\f";case"n":return y(),`
`;case"r":return y(),"\r";case"t":return y(),"	";case"v":return y(),"\v";case"0":if(y(),pe.isDigit(yt()))throw De(y());return"\0";case"x":return y(),Ja();case"u":return y(),wr();case`
`:case"\u2028":case"\u2029":return y(),"";case"\r":return y(),yt()===`
`&&y(),"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":throw De(y());case void 0:throw De(y())}return y()}function Ja(){let e="",t=yt();if(!pe.isHexDigit(t)||(e+=y(),t=yt(),!pe.isHexDigit(t)))throw De(y());return e+=y(),String.fromCodePoint(parseInt(e,16))}function wr(){let e="",t=4;for(;t-- >0;){const u=yt();if(!pe.isHexDigit(u))throw De(y());e+=y()}return String.fromCodePoint(parseInt(e,16))}const Qa={start(){if(Ae.type==="eof")throw qt();_r()},beforePropertyName(){switch(Ae.type){case"identifier":case"string":vr=Ae.value,Re="afterPropertyName";return;case"punctuator":bn();return;case"eof":throw qt()}},afterPropertyName(){if(Ae.type==="eof")throw qt();Re="beforePropertyValue"},beforePropertyValue(){if(Ae.type==="eof")throw qt();_r()},beforeArrayValue(){if(Ae.type==="eof")throw qt();if(Ae.type==="punctuator"&&Ae.value==="]"){bn();return}_r()},afterPropertyValue(){if(Ae.type==="eof")throw qt();switch(Ae.value){case",":Re="beforePropertyName";return;case"}":bn()}},afterArrayValue(){if(Ae.type==="eof")throw qt();switch(Ae.value){case",":Re="beforeArrayValue";return;case"]":bn()}},end(){}};function _r(){let e;switch(Ae.type){case"punctuator":switch(Ae.value){case"{":e={};break;case"[":e=[];break}break;case"null":case"boolean":case"numeric":case"string":e=Ae.value;break}if(Tu===void 0)Tu=e;else{const t=At[At.length-1];Array.isArray(t)?t.push(e):Object.defineProperty(t,vr,{value:e,writable:!0,enumerable:!0,configurable:!0})}if(e!==null&&typeof e=="object")At.push(e),Array.isArray(e)?Re="beforeArrayValue":Re="beforePropertyName";else{const t=At[At.length-1];t==null?Re="end":Array.isArray(t)?Re="afterArrayValue":Re="afterPropertyValue"}}function bn(){At.pop();const e=At[At.length-1];e==null?Re="end":Array.isArray(e)?Re="afterArrayValue":Re="afterPropertyValue"}function De(e){return vn(e===void 0?`JSON5: invalid end of input at ${Tt}:${Ze}`:`JSON5: invalid character '${Ui(e)}' at ${Tt}:${Ze}`)}function qt(){return vn(`JSON5: invalid end of input at ${Tt}:${Ze}`)}function Vi(){return Ze-=5,vn(`JSON5: invalid identifier character at ${Tt}:${Ze}`)}function Ya(e){console.warn(`JSON5: '${Ui(e)}' in strings is not valid ECMAScript; consider escaping`)}function Ui(e){const t={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(t[e])return t[e];if(e<" "){const u=e.charCodeAt(0).toString(16);return"\\x"+("00"+u).substring(u.length)}return e}function vn(e){const t=new SyntaxError(e);return t.lineNumber=Tt,t.columnNumber=Ze,t}var Za=function(t,u,n){const r=[];let s="",i,c,o="",d;if(u!=null&&typeof u=="object"&&!Array.isArray(u)&&(n=u.space,d=u.quote,u=u.replacer),typeof u=="function")c=u;else if(Array.isArray(u)){i=[];for(const C of u){let v;typeof C=="string"?v=C:(typeof C=="number"||C instanceof String||C instanceof Number)&&(v=String(C)),v!==void 0&&i.indexOf(v)<0&&i.push(v)}}return n instanceof Number?n=Number(n):n instanceof String&&(n=String(n)),typeof n=="number"?n>0&&(n=Math.min(10,Math.floor(n)),o="          ".substr(0,n)):typeof n=="string"&&(o=n.substr(0,10)),l("",{"":t});function l(C,v){let E=v[C];switch(E!=null&&(typeof E.toJSON5=="function"?E=E.toJSON5(C):typeof E.toJSON=="function"&&(E=E.toJSON(C))),c&&(E=c.call(v,C,E)),E instanceof Number?E=Number(E):E instanceof String?E=String(E):E instanceof Boolean&&(E=E.valueOf()),E){case null:return"null";case!0:return"true";case!1:return"false"}if(typeof E=="string")return f(E);if(typeof E=="number")return String(E);if(typeof E=="object")return Array.isArray(E)?A(E):p(E)}function f(C){const v={"'":.1,'"':.2},E={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};let B="";for(let x=0;x<C.length;x++){const k=C[x];switch(k){case"'":case'"':v[k]++,B+=k;continue;case"\0":if(pe.isDigit(C[x+1])){B+="\\x00";continue}}if(E[k]){B+=E[k];continue}if(k<" "){let q=k.charCodeAt(0).toString(16);B+="\\x"+("00"+q).substring(q.length);continue}B+=k}const P=d||Object.keys(v).reduce((x,k)=>v[x]<v[k]?x:k);return B=B.replace(new RegExp(P,"g"),E[P]),P+B+P}function p(C){if(r.indexOf(C)>=0)throw TypeError("Converting circular structure to JSON5");r.push(C);let v=s;s=s+o;let E=i||Object.keys(C),B=[];for(const x of E){const k=l(x,C);if(k!==void 0){let q=h(x)+":";o!==""&&(q+=" "),q+=k,B.push(q)}}let P;if(B.length===0)P="{}";else{let x;if(o==="")x=B.join(","),P="{"+x+"}";else{let k=`,
`+s;x=B.join(k),P=`{
`+s+x+`,
`+v+"}"}}return r.pop(),s=v,P}function h(C){if(C.length===0)return f(C);const v=String.fromCodePoint(C.codePointAt(0));if(!pe.isIdStartChar(v))return f(C);for(let E=v.length;E<C.length;E++)if(!pe.isIdContinueChar(String.fromCodePoint(C.codePointAt(E))))return f(C);return C}function A(C){if(r.indexOf(C)>=0)throw TypeError("Converting circular structure to JSON5");r.push(C);let v=s;s=s+o;let E=[];for(let P=0;P<C.length;P++){const x=l(String(P),C);E.push(x!==void 0?x:"null")}let B;if(E.length===0)B="[]";else if(o==="")B="["+E.join(",")+"]";else{let P=`,
`+s,x=E.join(P);B=`[
`+s+x+`,
`+v+"]"}return r.pop(),s=v,B}},Xa={parse:qa,stringify:Za};const cu=typeof document<"u";function Wi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function ef(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Wi(e.default)}const oe=Object.assign;function xr(e,t){const u={};for(const n in t){const r=t[n];u[n]=Xe(r)?r.map(e):e(r)}return u}const Iu=()=>{},Xe=Array.isArray,Ki=/#/g,tf=/&/g,uf=/\//g,nf=/=/g,rf=/\?/g,qi=/\+/g,sf=/%5B/g,of=/%5D/g,zi=/%5E/g,cf=/%60/g,Gi=/%7B/g,lf=/%7C/g,Ji=/%7D/g,af=/%20/g;function Sr(e){return encodeURI(""+e).replace(lf,"|").replace(sf,"[").replace(of,"]")}function ff(e){return Sr(e).replace(Gi,"{").replace(Ji,"}").replace(zi,"^")}function Rr(e){return Sr(e).replace(qi,"%2B").replace(af,"+").replace(Ki,"%23").replace(tf,"%26").replace(cf,"`").replace(Gi,"{").replace(Ji,"}").replace(zi,"^")}function df(e){return Rr(e).replace(nf,"%3D")}function Df(e){return Sr(e).replace(Ki,"%23").replace(rf,"%3F")}function hf(e){return e==null?"":Df(e).replace(uf,"%2F")}function Mu(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const pf=/\/$/,gf=e=>e.replace(pf,"");function Or(e,t,u="/"){let n,r={},s="",i="";const c=t.indexOf("#");let o=t.indexOf("?");return c<o&&c>=0&&(o=-1),o>-1&&(n=t.slice(0,o),s=t.slice(o+1,c>-1?c:t.length),r=e(s)),c>-1&&(n=n||t.slice(0,c),i=t.slice(c,t.length)),n=Af(n??t,u),{fullPath:n+(s&&"?")+s+i,path:n,query:r,hash:Mu(i)}}function Cf(e,t){const u=t.query?e(t.query):"";return t.path+(u&&"?")+u+(t.hash||"")}function Qi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function mf(e,t,u){const n=t.matched.length-1,r=u.matched.length-1;return n>-1&&n===r&&lu(t.matched[n],u.matched[r])&&Yi(t.params,u.params)&&e(t.query)===e(u.query)&&t.hash===u.hash}function lu(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Yi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const u in e)if(!Ef(e[u],t[u]))return!1;return!0}function Ef(e,t){return Xe(e)?Zi(e,t):Xe(t)?Zi(t,e):e===t}function Zi(e,t){return Xe(t)?e.length===t.length&&e.every((u,n)=>u===t[n]):e.length===1&&e[0]===t}function Af(e,t){if(e.startsWith("/"))return e;if(!e)return t;const u=t.split("/"),n=e.split("/"),r=n[n.length-1];(r===".."||r===".")&&n.push("");let s=u.length-1,i,c;for(i=0;i<n.length;i++)if(c=n[i],c!==".")if(c==="..")s>1&&s--;else break;return u.slice(0,s).join("/")+"/"+n.slice(i).join("/")}const Nt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var $u;(function(e){e.pop="pop",e.push="push"})($u||($u={}));var Lu;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Lu||(Lu={}));function Ff(e){if(!e)if(cu){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),gf(e)}const yf=/^[^#]+#/;function bf(e,t){return e.replace(yf,"#")+t}function vf(e,t){const u=document.documentElement.getBoundingClientRect(),n=e.getBoundingClientRect();return{behavior:t.behavior,left:n.left-u.left-(t.left||0),top:n.top-u.top-(t.top||0)}}const Bn=()=>({left:window.scrollX,top:window.scrollY});function Bf(e){let t;if("el"in e){const u=e.el,n=typeof u=="string"&&u.startsWith("#"),r=typeof u=="string"?n?document.getElementById(u.slice(1)):document.querySelector(u):u;if(!r)return;t=vf(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Xi(e,t){return(history.state?history.state.position-t:-1)+e}const Pr=new Map;function wf(e,t){Pr.set(e,t)}function _f(e){const t=Pr.get(e);return Pr.delete(e),t}let xf=()=>location.protocol+"//"+location.host;function eo(e,t){const{pathname:u,search:n,hash:r}=t,s=e.indexOf("#");if(s>-1){let c=r.includes(e.slice(s))?e.slice(s).length:1,o=r.slice(c);return o[0]!=="/"&&(o="/"+o),Qi(o,"")}return Qi(u,e)+n+r}function Sf(e,t,u,n){let r=[],s=[],i=null;const c=({state:p})=>{const h=eo(e,location),A=u.value,C=t.value;let v=0;if(p){if(u.value=h,t.value=p,i&&i===A){i=null;return}v=C?p.position-C.position:0}else n(h);r.forEach(E=>{E(u.value,A,{delta:v,type:$u.pop,direction:v?v>0?Lu.forward:Lu.back:Lu.unknown})})};function o(){i=u.value}function d(p){r.push(p);const h=()=>{const A=r.indexOf(p);A>-1&&r.splice(A,1)};return s.push(h),h}function l(){const{history:p}=window;p.state&&p.replaceState(oe({},p.state,{scroll:Bn()}),"")}function f(){for(const p of s)p();s=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:o,listen:d,destroy:f}}function to(e,t,u,n=!1,r=!1){return{back:e,current:t,forward:u,replaced:n,position:window.history.length,scroll:r?Bn():null}}function Rf(e){const{history:t,location:u}=window,n={value:eo(e,u)},r={value:t.state};r.value||s(n.value,{back:null,current:n.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(o,d,l){const f=e.indexOf("#"),p=f>-1?(u.host&&document.querySelector("base")?e:e.slice(f))+o:xf()+e+o;try{t[l?"replaceState":"pushState"](d,"",p),r.value=d}catch(h){console.error(h),u[l?"replace":"assign"](p)}}function i(o,d){const l=oe({},t.state,to(r.value.back,o,r.value.forward,!0),d,{position:r.value.position});s(o,l,!0),n.value=o}function c(o,d){const l=oe({},r.value,t.state,{forward:o,scroll:Bn()});s(l.current,l,!0);const f=oe({},to(n.value,o,null),{position:l.position+1},d);s(o,f,!1),n.value=o}return{location:n,state:r,push:c,replace:i}}function Of(e){e=Ff(e);const t=Rf(e),u=Sf(e,t.state,t.location,t.replace);function n(s,i=!0){i||u.pauseListeners(),history.go(s)}const r=oe({location:"",base:e,go:n,createHref:bf.bind(null,e)},t,u);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Pf(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Of(e)}function Tf(e){return typeof e=="string"||e&&typeof e=="object"}function uo(e){return typeof e=="string"||typeof e=="symbol"}const no=Symbol("");var ro;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ro||(ro={}));function au(e,t){return oe(new Error,{type:e,[no]:!0},t)}function bt(e,t){return e instanceof Error&&no in e&&(t==null||!!(e.type&t))}const so="[^/]+?",Nf={sensitive:!1,strict:!1,start:!0,end:!0},If=/[.+*?^${}()[\]/\\]/g;function Mf(e,t){const u=oe({},Nf,t),n=[];let r=u.start?"^":"";const s=[];for(const d of e){const l=d.length?[]:[90];u.strict&&!d.length&&(r+="/");for(let f=0;f<d.length;f++){const p=d[f];let h=40+(u.sensitive?.25:0);if(p.type===0)f||(r+="/"),r+=p.value.replace(If,"\\$&"),h+=40;else if(p.type===1){const{value:A,repeatable:C,optional:v,regexp:E}=p;s.push({name:A,repeatable:C,optional:v});const B=E||so;if(B!==so){h+=10;try{new RegExp(`(${B})`)}catch(x){throw new Error(`Invalid custom RegExp for param "${A}" (${B}): `+x.message)}}let P=C?`((?:${B})(?:/(?:${B}))*)`:`(${B})`;f||(P=v&&d.length<2?`(?:/${P})`:"/"+P),v&&(P+="?"),r+=P,h+=20,v&&(h+=-8),C&&(h+=-20),B===".*"&&(h+=-50)}l.push(h)}n.push(l)}if(u.strict&&u.end){const d=n.length-1;n[d][n[d].length-1]+=.7000000000000001}u.strict||(r+="/?"),u.end?r+="$":u.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,u.sensitive?"":"i");function c(d){const l=d.match(i),f={};if(!l)return null;for(let p=1;p<l.length;p++){const h=l[p]||"",A=s[p-1];f[A.name]=h&&A.repeatable?h.split("/"):h}return f}function o(d){let l="",f=!1;for(const p of e){(!f||!l.endsWith("/"))&&(l+="/"),f=!1;for(const h of p)if(h.type===0)l+=h.value;else if(h.type===1){const{value:A,repeatable:C,optional:v}=h,E=A in d?d[A]:"";if(Xe(E)&&!C)throw new Error(`Provided param "${A}" is an array but it is not repeatable (* or + modifiers)`);const B=Xe(E)?E.join("/"):E;if(!B)if(v)p.length<2&&(l.endsWith("/")?l=l.slice(0,-1):f=!0);else throw new Error(`Missing required param "${A}"`);l+=B}}return l||"/"}return{re:i,score:n,keys:s,parse:c,stringify:o}}function $f(e,t){let u=0;for(;u<e.length&&u<t.length;){const n=t[u]-e[u];if(n)return n;u++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function io(e,t){let u=0;const n=e.score,r=t.score;for(;u<n.length&&u<r.length;){const s=$f(n[u],r[u]);if(s)return s;u++}if(Math.abs(r.length-n.length)===1){if(oo(n))return 1;if(oo(r))return-1}return r.length-n.length}function oo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Lf={type:0,value:""},jf=/[a-zA-Z0-9_]/;function kf(e){if(!e)return[[]];if(e==="/")return[[Lf]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${u})/"${d}": ${h}`)}let u=0,n=u;const r=[];let s;function i(){s&&r.push(s),s=[]}let c=0,o,d="",l="";function f(){d&&(u===0?s.push({type:0,value:d}):u===1||u===2||u===3?(s.length>1&&(o==="*"||o==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:d,regexp:l,repeatable:o==="*"||o==="+",optional:o==="*"||o==="?"})):t("Invalid state to consume buffer"),d="")}function p(){d+=o}for(;c<e.length;){if(o=e[c++],o==="\\"&&u!==2){n=u,u=4;continue}switch(u){case 0:o==="/"?(d&&f(),i()):o===":"?(f(),u=1):p();break;case 4:p(),u=n;break;case 1:o==="("?u=2:jf.test(o)?p():(f(),u=0,o!=="*"&&o!=="?"&&o!=="+"&&c--);break;case 2:o===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+o:u=3:l+=o;break;case 3:f(),u=0,o!=="*"&&o!=="?"&&o!=="+"&&c--,l="";break;default:t("Unknown state");break}}return u===2&&t(`Unfinished custom RegExp for param "${d}"`),f(),i(),r}function Hf(e,t,u){const n=Mf(kf(e.path),u),r=oe(n,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Vf(e,t){const u=[],n=new Map;t=fo({strict:!1,end:!0,sensitive:!1},t);function r(f){return n.get(f)}function s(f,p,h){const A=!h,C=lo(f);C.aliasOf=h&&h.record;const v=fo(t,f),E=[C];if("alias"in f){const x=typeof f.alias=="string"?[f.alias]:f.alias;for(const k of x)E.push(lo(oe({},C,{components:h?h.record.components:C.components,path:k,aliasOf:h?h.record:C})))}let B,P;for(const x of E){const{path:k}=x;if(p&&k[0]!=="/"){const q=p.record.path,H=q[q.length-1]==="/"?"":"/";x.path=p.record.path+(k&&H+k)}if(B=Hf(x,p,v),h?h.alias.push(B):(P=P||B,P!==B&&P.alias.push(B),A&&f.name&&!ao(B)&&i(f.name)),Do(B)&&o(B),C.children){const q=C.children;for(let H=0;H<q.length;H++)s(q[H],B,h&&h.children[H])}h=h||B}return P?()=>{i(P)}:Iu}function i(f){if(uo(f)){const p=n.get(f);p&&(n.delete(f),u.splice(u.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=u.indexOf(f);p>-1&&(u.splice(p,1),f.record.name&&n.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function c(){return u}function o(f){const p=Kf(f,u);u.splice(p,0,f),f.record.name&&!ao(f)&&n.set(f.record.name,f)}function d(f,p){let h,A={},C,v;if("name"in f&&f.name){if(h=n.get(f.name),!h)throw au(1,{location:f});v=h.record.name,A=oe(co(p.params,h.keys.filter(P=>!P.optional).concat(h.parent?h.parent.keys.filter(P=>P.optional):[]).map(P=>P.name)),f.params&&co(f.params,h.keys.map(P=>P.name))),C=h.stringify(A)}else if(f.path!=null)C=f.path,h=u.find(P=>P.re.test(C)),h&&(A=h.parse(C),v=h.record.name);else{if(h=p.name?n.get(p.name):u.find(P=>P.re.test(p.path)),!h)throw au(1,{location:f,currentLocation:p});v=h.record.name,A=oe({},p.params,f.params),C=h.stringify(A)}const E=[];let B=h;for(;B;)E.unshift(B.record),B=B.parent;return{name:v,path:C,params:A,matched:E,meta:Wf(E)}}e.forEach(f=>s(f));function l(){u.length=0,n.clear()}return{addRoute:s,resolve:d,removeRoute:i,clearRoutes:l,getRoutes:c,getRecordMatcher:r}}function co(e,t){const u={};for(const n of t)n in e&&(u[n]=e[n]);return u}function lo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Uf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Uf(e){const t={},u=e.props||!1;if("component"in e)t.default=u;else for(const n in e.components)t[n]=typeof u=="object"?u[n]:u;return t}function ao(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Wf(e){return e.reduce((t,u)=>oe(t,u.meta),{})}function fo(e,t){const u={};for(const n in e)u[n]=n in t?t[n]:e[n];return u}function Kf(e,t){let u=0,n=t.length;for(;u!==n;){const s=u+n>>1;io(e,t[s])<0?n=s:u=s+1}const r=qf(e);return r&&(n=t.lastIndexOf(r,n-1)),n}function qf(e){let t=e;for(;t=t.parent;)if(Do(t)&&io(e,t)===0)return t}function Do({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function zf(e){const t={};if(e===""||e==="?")return t;const n=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const s=n[r].replace(qi," "),i=s.indexOf("="),c=Mu(i<0?s:s.slice(0,i)),o=i<0?null:Mu(s.slice(i+1));if(c in t){let d=t[c];Xe(d)||(d=t[c]=[d]),d.push(o)}else t[c]=o}return t}function ho(e){let t="";for(let u in e){const n=e[u];if(u=df(u),n==null){n!==void 0&&(t+=(t.length?"&":"")+u);continue}(Xe(n)?n.map(s=>s&&Rr(s)):[n&&Rr(n)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+u,s!=null&&(t+="="+s))})}return t}function Gf(e){const t={};for(const u in e){const n=e[u];n!==void 0&&(t[u]=Xe(n)?n.map(r=>r==null?null:""+r):n==null?n:""+n)}return t}const Jf=Symbol(""),po=Symbol(""),wn=Symbol(""),go=Symbol(""),Tr=Symbol("");function ju(){let e=[];function t(n){return e.push(n),()=>{const r=e.indexOf(n);r>-1&&e.splice(r,1)}}function u(){e=[]}return{add:t,list:()=>e.slice(),reset:u}}function It(e,t,u,n,r,s=i=>i()){const i=n&&(n.enterCallbacks[r]=n.enterCallbacks[r]||[]);return()=>new Promise((c,o)=>{const d=p=>{p===!1?o(au(4,{from:u,to:t})):p instanceof Error?o(p):Tf(p)?o(au(2,{from:t,to:p})):(i&&n.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),c())},l=s(()=>e.call(n&&n.instances[r],t,u,d));let f=Promise.resolve(l);e.length<3&&(f=f.then(d)),f.catch(p=>o(p))})}function Nr(e,t,u,n,r=s=>s()){const s=[];for(const i of e)for(const c in i.components){let o=i.components[c];if(!(t!=="beforeRouteEnter"&&!i.instances[c]))if(Wi(o)){const l=(o.__vccOpts||o)[t];l&&s.push(It(l,u,n,i,c,r))}else{let d=o();s.push(()=>d.then(l=>{if(!l)throw new Error(`Couldn't resolve component "${c}" at "${i.path}"`);const f=ef(l)?l.default:l;i.mods[c]=l,i.components[c]=f;const h=(f.__vccOpts||f)[t];return h&&It(h,u,n,i,c,r)()}))}}return s}function Co(e){const t=Qe(wn),u=Qe(go),n=Ye(()=>{const o=Zt(e.to);return t.resolve(o)}),r=Ye(()=>{const{matched:o}=n.value,{length:d}=o,l=o[d-1],f=u.matched;if(!l||!f.length)return-1;const p=f.findIndex(lu.bind(null,l));if(p>-1)return p;const h=mo(o[d-2]);return d>1&&mo(l)===h&&f[f.length-1].path!==h?f.findIndex(lu.bind(null,o[d-2])):p}),s=Ye(()=>r.value>-1&&Xf(u.params,n.value.params)),i=Ye(()=>r.value>-1&&r.value===u.matched.length-1&&Yi(u.params,n.value.params));function c(o={}){if(Zf(o)){const d=t[Zt(e.replace)?"replace":"push"](Zt(e.to)).catch(Iu);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:n,href:Ye(()=>n.value.href),isActive:s,isExactActive:i,navigate:c}}function Qf(e){return e.length===1?e[0]:e}const Yf=ys({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Co,setup(e,{slots:t}){const u=Yt(Co(e)),{options:n}=Qe(wn),r=Ye(()=>({[Eo(e.activeClass,n.linkActiveClass,"router-link-active")]:u.isActive,[Eo(e.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:u.isExactActive}));return()=>{const s=t.default&&Qf(t.default(u));return e.custom?s:ar("a",{"aria-current":u.isExactActive?e.ariaCurrentValue:null,href:u.href,onClick:u.navigate,class:r.value},s)}}});function Zf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Xf(e,t){for(const u in t){const n=t[u],r=e[u];if(typeof n=="string"){if(n!==r)return!1}else if(!Xe(r)||r.length!==n.length||n.some((s,i)=>s!==r[i]))return!1}return!0}function mo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Eo=(e,t,u)=>e??t??u,e0=ys({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:u}){const n=Qe(Tr),r=Ye(()=>e.route||n.value),s=Qe(po,0),i=Ye(()=>{let d=Zt(s);const{matched:l}=r.value;let f;for(;(f=l[d])&&!f.components;)d++;return d}),c=Ye(()=>r.value.matched[i.value]);sn(po,Ye(()=>i.value+1)),sn(Jf,c),sn(Tr,r);const o=nc();return nu(()=>[o.value,c.value,e.name],([d,l,f],[p,h,A])=>{l&&(l.instances[f]=d,h&&h!==l&&d&&d===p&&(l.leaveGuards.size||(l.leaveGuards=h.leaveGuards),l.updateGuards.size||(l.updateGuards=h.updateGuards))),d&&l&&(!h||!lu(l,h)||!p)&&(l.enterCallbacks[f]||[]).forEach(C=>C(d))},{flush:"post"}),()=>{const d=r.value,l=e.name,f=c.value,p=f&&f.components[l];if(!p)return Ao(u.default,{Component:p,route:d});const h=f.props[l],A=h?h===!0?d.params:typeof h=="function"?h(d):h:null,v=ar(p,oe({},A,t,{onVnodeUnmounted:E=>{E.component.isUnmounted&&(f.instances[l]=null)},ref:o}));return Ao(u.default,{Component:v,route:d})||v}}});function Ao(e,t){if(!e)return null;const u=e(t);return u.length===1?u[0]:u}const t0=e0;function u0(e){const t=Vf(e.routes,e),u=e.parseQuery||zf,n=e.stringifyQuery||ho,r=e.history,s=ju(),i=ju(),c=ju(),o=rc(Nt);let d=Nt;cu&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=xr.bind(null,m=>""+m),f=xr.bind(null,hf),p=xr.bind(null,Mu);function h(m,O){let _,I;return uo(m)?(_=t.getRecordMatcher(m),I=O):I=m,t.addRoute(I,_)}function A(m){const O=t.getRecordMatcher(m);O&&t.removeRoute(O)}function C(){return t.getRoutes().map(m=>m.record)}function v(m){return!!t.getRecordMatcher(m)}function E(m,O){if(O=oe({},O||o.value),typeof m=="string"){const g=Or(u,m,O.path),F=t.resolve({path:g.path},O),w=r.createHref(g.fullPath);return oe(g,F,{params:p(F.params),hash:Mu(g.hash),redirectedFrom:void 0,href:w})}let _;if(m.path!=null)_=oe({},m,{path:Or(u,m.path,O.path).path});else{const g=oe({},m.params);for(const F in g)g[F]==null&&delete g[F];_=oe({},m,{params:f(g)}),O.params=f(O.params)}const I=t.resolve(_,O),Y=m.hash||"";I.params=l(p(I.params));const a=Cf(n,oe({},m,{hash:ff(Y),path:I.path})),D=r.createHref(a);return oe({fullPath:a,hash:Y,query:n===ho?Gf(m.query):m.query||{}},I,{redirectedFrom:void 0,href:D})}function B(m){return typeof m=="string"?Or(u,m,o.value.path):oe({},m)}function P(m,O){if(d!==m)return au(8,{from:O,to:m})}function x(m){return H(m)}function k(m){return x(oe(B(m),{replace:!0}))}function q(m){const O=m.matched[m.matched.length-1];if(O&&O.redirect){const{redirect:_}=O;let I=typeof _=="function"?_(m):_;return typeof I=="string"&&(I=I.includes("?")||I.includes("#")?I=B(I):{path:I},I.params={}),oe({query:m.query,hash:m.hash,params:I.path!=null?{}:m.params},I)}}function H(m,O){const _=d=E(m),I=o.value,Y=m.state,a=m.force,D=m.replace===!0,g=q(_);if(g)return H(oe(B(g),{state:typeof g=="object"?oe({},Y,g.state):Y,force:a,replace:D}),O||_);const F=_;F.redirectedFrom=O;let w;return!a&&mf(n,I,_)&&(w=au(16,{to:F,from:I}),qe(I,I,!0,!1)),(w?Promise.resolve(w):Pe(F,I)).catch(b=>bt(b)?bt(b,2)?b:at(b):X(b,F,I)).then(b=>{if(b){if(bt(b,2))return H(oe({replace:D},B(b.to),{state:typeof b.to=="object"?oe({},Y,b.to.state):Y,force:a}),O||F)}else b=vt(F,I,!0,D,Y);return We(F,I,b),b})}function Oe(m,O){const _=P(m,O);return _?Promise.reject(_):Promise.resolve()}function et(m){const O=j.values().next().value;return O&&typeof O.runWithContext=="function"?O.runWithContext(m):m()}function Pe(m,O){let _;const[I,Y,a]=n0(m,O);_=Nr(I.reverse(),"beforeRouteLeave",m,O);for(const g of I)g.leaveGuards.forEach(F=>{_.push(It(F,m,O))});const D=Oe.bind(null,m,O);return _.push(D),J(_).then(()=>{_=[];for(const g of s.list())_.push(It(g,m,O));return _.push(D),J(_)}).then(()=>{_=Nr(Y,"beforeRouteUpdate",m,O);for(const g of Y)g.updateGuards.forEach(F=>{_.push(It(F,m,O))});return _.push(D),J(_)}).then(()=>{_=[];for(const g of a)if(g.beforeEnter)if(Xe(g.beforeEnter))for(const F of g.beforeEnter)_.push(It(F,m,O));else _.push(It(g.beforeEnter,m,O));return _.push(D),J(_)}).then(()=>(m.matched.forEach(g=>g.enterCallbacks={}),_=Nr(a,"beforeRouteEnter",m,O,et),_.push(D),J(_))).then(()=>{_=[];for(const g of i.list())_.push(It(g,m,O));return _.push(D),J(_)}).catch(g=>bt(g,8)?g:Promise.reject(g))}function We(m,O,_){c.list().forEach(I=>et(()=>I(m,O,_)))}function vt(m,O,_,I,Y){const a=P(m,O);if(a)return a;const D=O===Nt,g=cu?history.state:{};_&&(I||D?r.replace(m.fullPath,oe({scroll:D&&g&&g.scroll},Y)):r.push(m.fullPath,Y)),o.value=m,qe(m,O,_,D),at()}let Ke;function zt(){Ke||(Ke=r.listen((m,O,_)=>{const I=E(m),Y=q(I);if(Y){H(oe(Y,{replace:!0,force:!0}),I).catch(Iu);return}d=I;const a=o.value;cu&&wf(Xi(a.fullPath,_.delta),Bn()),Pe(I,a).catch(D=>bt(D,12)?D:bt(D,2)?(H(oe(B(D.to),{force:!0}),I).then(g=>{bt(g,20)&&!_.delta&&_.type===$u.pop&&r.go(-1,!1)}).catch(Iu),Promise.reject()):(_.delta&&r.go(-_.delta,!1),X(D,I,a))).then(D=>{D=D||vt(I,a,!1),D&&(_.delta&&!bt(D,8)?r.go(-_.delta,!1):_.type===$u.pop&&bt(D,20)&&r.go(-1,!1)),We(I,a,D)}).catch(Iu)}))}let Mt=ju(),me=ju(),ce;function X(m,O,_){at(m);const I=me.list();return I.length?I.forEach(Y=>Y(m,O,_)):console.error(m),Promise.reject(m)}function tt(){return ce&&o.value!==Nt?Promise.resolve():new Promise((m,O)=>{Mt.add([m,O])})}function at(m){return ce||(ce=!m,zt(),Mt.list().forEach(([O,_])=>m?_(m):O()),Mt.reset()),m}function qe(m,O,_,I){const{scrollBehavior:Y}=e;if(!cu||!Y)return Promise.resolve();const a=!_&&_f(Xi(m.fullPath,0))||(I||!_)&&history.state&&history.state.scroll||null;return gs().then(()=>Y(m,O,a)).then(D=>D&&Bf(D)).catch(D=>X(D,m,O))}const S=m=>r.go(m);let M;const j=new Set,ee={currentRoute:o,listening:!0,addRoute:h,removeRoute:A,clearRoutes:t.clearRoutes,hasRoute:v,getRoutes:C,resolve:E,options:e,push:x,replace:k,go:S,back:()=>S(-1),forward:()=>S(1),beforeEach:s.add,beforeResolve:i.add,afterEach:c.add,onError:me.add,isReady:tt,install(m){const O=this;m.component("RouterLink",Yf),m.component("RouterView",t0),m.config.globalProperties.$router=O,Object.defineProperty(m.config.globalProperties,"$route",{enumerable:!0,get:()=>Zt(o)}),cu&&!M&&o.value===Nt&&(M=!0,x(r.location).catch(Y=>{}));const _={};for(const Y in Nt)Object.defineProperty(_,Y,{get:()=>o.value[Y],enumerable:!0});m.provide(wn,O),m.provide(go,as(_)),m.provide(Tr,o);const I=m.unmount;j.add(m),m.unmount=function(){j.delete(m),j.size<1&&(d=Nt,Ke&&Ke(),Ke=null,o.value=Nt,M=!1,ce=!1),I()}}};function J(m){return m.reduce((O,_)=>O.then(()=>et(_)),Promise.resolve())}return ee}function n0(e,t){const u=[],n=[],r=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const c=t.matched[i];c&&(e.matched.find(d=>lu(d,c))?n.push(c):u.push(c));const o=e.matched[i];o&&(t.matched.find(d=>lu(d,o))||r.push(o))}return[u,n,r]}function r0(){return Qe(wn)}const s0=(e,t)=>{const u=e.__vccOpts||e;for(const[n,r]of t)u[n]=r;return u},i0={name:"rtf",props:{vueState:{type:Object,required:!0},data:{type:Object,required:!0}},setup(e){window.addEventListener("popstate",A=>{var C;((C=A.state)==null?void 0:C.from)==="customCard"&&window.location.reload()}),window.addEventListener("message",A=>{A.data.type==="navigate"&&(window.history.pushState({from:"customCard"},"",A.data.url),window.location.reload())});const t=()=>(console.warn("创建应急路由实例"),u0({history:Pf(),routes:[]}));if(!(Qe("microRouter")||r0()||t()))throw console.error("Router实例未正确初始化"),new Error("路由服务不可用");const r=Yt({amcode:"",amname:"",amtype:"",faultdesc:"",applydatamap:{},bujianid:"",iscreateapply:!1,buttonname:"生成维修申请",billcode:"",repairapplyid:"",type:"",isLoading:!1}),s=()=>{try{const A=e.vueState.data.allText,C=atob(A),v=new Uint8Array(C.length);for(let k=0;k<C.length;k++)v[k]=C.charCodeAt(k);const E=new TextDecoder("utf-8").decode(v),B=Xa.parse(E);r.amcode=B.amcode,r.amname=B.amname,r.amtype=B.amtype,r.faultdesc=B.faultdesc,r.applydatamap=B,r.bujianid=B.vuebujianid;const P=window.location.href,x=new URLSearchParams(window.location.search);r.type=x.get("type")}catch(A){console.error("解析JSON数据时出错：",A)}},i=async()=>{const C=await(await fetch("/api/eam/eampm/v1.0/rep/repair/repairapply/checkcreatedata",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({vuebujianid:r.bujianid})})).json();if(C.resultType=="SUCCESS"){var v=C.appendData;v.iscreatedata&&(r.buttonname="已生成维修申请",r.iscreateapply=!0,r.billcode=v.billcode,r.repairapplyid=v.id)}C.appendData.iscreatedata&&(r.buttonname="已生成维修申请",r.iscreateapply=!0)};ws(()=>{s(),i()});const c=async(A,C)=>{if(r.iscreateapply||r.isLoading)return;r.isLoading=!0,r.buttonname="正在生成维修申请";const E=await(await fetch("/api/eam/eampm/v1.0/rep/repair/repairapply/createRepairApplyDuellm",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r.applydatamap)})).json();E.resultType=="SUCCESS"?(r.buttonname="已生成维修申请",r.iscreateapply=!0,r.billcode=E.appendData.billcode,r.repairapplyid=E.appendData.id):(r.buttonname="生成维修申请",r.iscreateapply=!1)},o=()=>{var A=r.repairapplyid;if(r.type=="mobile"){var C={styleId:"b8894174-be37-b305-7be5-83743a29babf",fdim:"",sdim:"",dataid:A,status:"view",ext:""};p(C,"")}else{var v="/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid="+A+"&status=view&runtime=true&styleid=b8894174-be37-b305-7be5-83743a29babf";f(A,"维修申请",v)}},d=()=>{try{return window.parent&&window.parent.gspframeworkAdapterService?window.parent:window.top&&window.top.gspframeworkAdapterService?window.top:window}catch{return window}},l=A=>{const C=new RegExp("(^|&)"+A+"=([^&]*)(&|$)","i");let v;return window.location.search&&(v=window.location.search.substr(1).match(C)),v!=null||window.location.hash&&(v=window.location.hash.substr(3).match(C),v!=null)?unescape(v[2]):""},f=(A,C,v)=>{const E=d();if(E){const B=E.allgspfuncs||E.gspframeworkAdapterService.funcSvc.getAllCachedFuncs(),P=l("pfuncid")||l("funcId")||l("funcid"),x="funcid="+A+"&pfuncid="+P;if(v.indexOf("?")===-1?v+="?"+x:v+="&"+x,E.gspframeworkAdapterService)return E.gspframeworkAdapterService.appSvc.getFeb().post("farrisapp-click",{FuncName:C,active:!1,appType:"menu",code:A,tabId:A,funcId:P||B[0].id,id:A,su:"views",isjquery:!0,reload:void 0,sessionid:localStorage.session,src:v,url:v}),A}},p=(A,C,v)=>{var E="b8894174-be37-b305-7be5-83743a29babf";h(A.dataid,A.status,E,C||{},"&fdim="+A.fdim+"&sdim="+A.sdim)},h=(A,C,v,E,B)=>{const P=d(),x=new URLSearchParams({dataId:A,status:C,...Object.fromEntries(new URLSearchParams(B||""))}),k=v||"b8894174-be37-b305-7be5-83743a29babf",q="/apps/fastdweb/views/mobile/index.html",H=new URL(P.location.origin+q);H.hash=`#/${k}/card?${x.toString()}&_t=${Date.now()}`;try{P===window?window.open(H.toString(),"_blank"):P.postMessage({type:"openNewWindow",url:H.toString(),action:"push"},"*")}catch(Oe){console.error("路由跳转失败:",Oe),P.location.href=H.toString()}};return{...oc(r),apply:c,view:o,openCustomCardNew:h,openCustomCard:p}}},o0={class:"repairinfomation",style:{display:"inline-block","min-width":"350px",width:"auto",border:"1px solid rgb(224, 224, 224)","border-radius":"5px",margin:"10px 0px"}},c0={class:"repairapplycontent",style:{display:"flex","flex-direction":"column",gap:"8px",padding:"5px"}},l0={class:"repairapplybutton"},a0=["disabled"],f0={key:0,class:"repairapplyjump",style:{"font-size":"16px",padding:"10px"}};function d0(e,t,u,n,r,s){return ir(),ui(st,null,[je("div",o0,[t[2]||(t[2]=je("div",{class:"repairapplytitle",style:{"font-size":"20px","background-image":"linear-gradient(135deg, #fdf2ff 0%, #fbefff 100%)","margin-bottom":"10px",padding:"5px"}},"维修申请单",-1)),je("div",c0,[je("span",null,"资产编号："+Lt(e.amcode),1),je("span",null,"资产名称："+Lt(e.amname),1),je("span",null,"资产类别："+Lt(e.amtype),1),je("span",null,"故障描述："+Lt(e.faultdesc),1)])]),je("div",l0,[je("button",{disabled:e.isLoading,style:{height:"40px","font-size":"16px",width:"350px",border:"none","background-color":"rgba(227, 242, 253, 1)",color:"blue","border-radius":"10px",cursor:"pointer"},onClick:t[0]||(t[0]=i=>n.apply(u.data,i))},Lt(e.buttonname),9,a0)]),e.iscreateapply?(ir(),ui("div",f0,[t[3]||(t[3]=je("span",null,"查看维修申请单",-1)),je("span",{onClick:t[1]||(t[1]=i=>n.view()),style:{cursor:"pointer",color:"blue"}},Lt(e.billcode),1)])):gl("",!0)],64)}const D0=s0(i0,[["render",d0],["__scopeId","data-v-3dc0083c"]]);function h0(e){return JSON.parse(JSON.stringify(e))}class p0{async initialize(){console.log("ExampleWidgetAPI initialized.")}async cleanup(){console.log("ExampleWidgetAPI cleanup.")}createWidget(){return new g0}}class g0{constructor(){_n(this,"vueState_",Yt({options:{mode:"full",implOptions:{data:{}}},data:{}}));_n(this,"vueApp_");_n(this,"isMounted_",!1);const t=this.vueState_;this.vueApp_=Zl({setup(){const u=t;return console.log("constuct"),nu(()=>u.options,n=>{console.log("ExampleWidget options updated.",n)}),nu(()=>u.data,n=>{console.log("ExampleWidget data updated.",n)}),{state:u}},render(){return console.log("render data",this.state.data),ar(D0,{vueState:t})}})}namespace(){return"sys"}name(){return"rtf"}options(){return this.vueState_.options}updateOptions(t){this.vueState_.options={...this.options(),...h0(t)}}data(){return console.log(this.options().implOptions.data),this.vueState_.data}updateData(t){console.log("ExampleWidget data updating.",t),this.vueState_.data=t,console.log("ExampleWidget data updated.",this.vueState_)}async mount(t){if(this.isMounted_)throw new Error("ExampleWidget already mounted.");this.vueApp_.mount(t),this.isMounted_=!0}async unmount(){if(!this.isMounted_)throw new Error("ExampleWidget is NOT mounted.");this.vueApp_.unmount(),this.isMounted_=!1}async rerender(){}async dispose(){this.isMounted_&&this.unmount()}addEventListener(t){return()=>{}}}const Ir=new p0;function C0(){return Ir.initialize()}function m0(){return Ir.cleanup()}function E0(){return Ir.createWidget()}Te.cleanup=m0,Te.createWidget=E0,Te.initialize=C0,Object.defineProperty(Te,Symbol.toStringTag,{value:"Module"})});
