/**
 * 费用查询-多渠道
 * @date:2022-5-26
 */
idp.event.bind("domReady", function () {
    idp.event.register("grid_main", "beforeGridFilter", function (e, filter) {
        const mode = parent.filterOptions.mode;
        const projectId = parent.filterOptions.projectId;
        const cbsId = parent.filterOptions.cbsId;
        filter.push({
            "Left": " ",
            "Field": "TPSCOSTBILL.PROJECTID",
            "Operate": " = ",
            "IsExpress": false,
            "Value": projectId,
            "Right": "",
            "Logic": " and "
        });
        if (mode === "cbs") {
            filter.push({
                "Left": " ",
                "Field": "CBSID",
                "Operate": " in ",
                "IsExpress": false,
                "Value": cbsId,
                "Right": "",
                "Logic": " "
            });
        }
        return filter;
    });
    idp.event.register("grid_acc", "beforeGridFilter", function (e, filter) {
        const mode = parent.filterOptions.mode;
        const projectId = parent.filterOptions.projectId;
        const cbsId = parent.filterOptions.cbsId;
        filter.push({
            "Left": " ",
            "Field": "PROJECTID",
            "Operate": " = ",
            "IsExpress": false,
            "Value": projectId,
            "Right": "",
            "Logic": " and "
        });
        if (mode === "cbs") {
            filter.push({
                "Left": " ",
                "Field": "PSCOSTID",
                "Operate": " in ",
                "IsExpress": false,
                "Value": cbsId,
                "Right": "",
                "Logic": " "
            });
        }
        return filter;
    });
});


idp.event.bind("viewReady", function (e, context) {
    idp.func.refresh("grid_main");
    idp.func.refresh("grid_acc");  
});
