/**
 * 项目预算
 * 宁凡栋
 * 2023.05.31 
 */
idp.event.bind("domReady", function () {

    //管理组织选择后
    idp.event.register(tpsAccValueCard.element.adminOrg, "selected", function (e, value, name, obj) {
		if(idp.control.get(tpsAccValueCard.element.project).getValue()){
			idp.control.get(tpsAccValueCard.element.project).clear();
			idp.control.get(tpsAccValueCard.element.psCost).clear();
		}
    });

    // region 项目帮助事件
    idp.event.register(tpsAccValueCard.element.project, "beforeHelpFilter", function (e, g) {
        let filters = [];
        filters = tps_pub.getProjectCommonFilter(filters);
        const mainData = PSPub.getIdpCardMainData();
        const adminValue = mainData['ADMINORG'];
        if (adminValue) {
            idp.store.commit('adminFilterRow', adminValue);
        }
        return filters;
    });
	idp.event.register(tpsAccValueCard.element.project, "selected", function (e, value, name, obj) {
		idp.control.get(tpsAccValueCard.element.psCost).clear();
	});
    // endregion

    //region 科目列表帮助
    idp.event.register(tpsAccValueCard.element.psCost, "beforeOpen", function (e, table, data, col) { 
        if (!idp.control.get(tpsAccValueCard.element.project).getValue()) {
            idp.warn(idp.lang.get("TPS_ACCVALUE_05"));
            return false;
        }
		var main = PSPub.getIdpCardMainData();
		var cbsId=main.PROJECTID_CBSID;
		var proId=main.PROJECTID;
		if(!cbsId){
			var para={
				proId:proId
			}
			var apiUrl = tpsApiUrlService.getTpsSfUrl("tpsAccValue", "getCbsId");
			idp.service.fetch(apiUrl, para,false).done(function (res) {
				if(res.success){
					cbsId=res.data;
				}
			});
		}
		if(!cbsId){
			idp.warn(idp.lang.get("TPS_ACCVALUE_04"));
			return false;
		}
		idp.uiview.setCtrlValue("PROJECTID_CBSID", cbsId);
    });
	idp.event.register(tpsAccValueCard.element.psCost, "beforeHelpFilter", function (e, table, data, col) { 
		var main = PSPub.getIdpCardMainData();
		var cbsId=main.PROJECTID_CBSID;
	    var filters = [];
	    filters.push({
	        "Left": "",
	        "Field": "ELEMENTTABLEID",
	        "Operate": "=",
	        "IsExpress": false,
	        "Value": cbsId,
	        "Right": "",
	        "Logic": " AND "
	    });
	    filters.push({
	        "Left": "",
	        "Field": "ELEMENTTABLEVALIDED",
	        "Operate": "=",
	        "IsExpress": false,
	        "Value": '1',
	        "Right": "",
	        "Logic": ""
	    });
	    return filters;
	});
})

idp.event.bind("afterAddData", function (e, data) {
    // 单据状态
	idp.uiview.setCtrlValue("BUSINESSDATE", new Date().Format("yyyy-MM-dd"));
	idp.uiview.setCtrlValue("PERIODDATE", new Date().Format("yyyy-MM-dd"));
})