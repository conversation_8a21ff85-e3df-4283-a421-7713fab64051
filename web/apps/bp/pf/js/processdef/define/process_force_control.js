/**
 * 流程属性：强制必须使用窗口对应脚本
 */
let $forceUse;
let $startTime;
let $allowEdit;
let pageEditable=true;
var parentIdp=window.parent.idp;

idp.event.bind("domReady",function(e){
    idp.event.register('radio_force_use','selected',function(g,value){
        if (value==1) {
            $startTime.setEnabled()
            $allowEdit.setEnabled()
        }else{
            $startTime.setDisabled()
            $allowEdit.setDisabled()
        }
    });
})

idp.event.bind("viewReady",function(e){
    $forceUse = idp.control.get('radio_force_use');
    $startTime = idp.control.get('dropdown_starttime');
    $allowEdit = idp.control.get('radio_allow_edit');

    loadData();

    if (parentIdp.utils.getQuery('editForceUseProcess')||parentIdp.store.get('privateProcessControlEditProcess') || parentIdp.store.get('pageState') == 'view' ||(parentIdp.utils.getQuery('processstate')=='his')) {
        pageEditable = false;
    }

    if (pageEditable){
        $forceUse.setEnabled();
        if($forceUse.getValue()=='1'){
            $startTime.setEnabled()
            $allowEdit.setEnabled()
        }else {
            $startTime.setDisabled()
            $allowEdit.setDisabled()
        }
    }else {
        $forceUse.setDisabled()
        $startTime.setDisabled()
        $allowEdit.setDisabled()
    }


    if(window.parent.generalvo.accesslevel=='private2'){
        $('#input_695203').parents('.table-item').show();
        let text = idp.control.get('input_695203').getValue();
        let split = text.split('*');
        let rootOrgNameEncode = window.parent.idp.utils.getQuery('rootOrgName');
        let rootOrgName = decodeURI(decodeURI(rootOrgNameEncode));
        idp.control.get('input_695203').setValue(split.join(rootOrgName));


    }


})

function loadData(){
    if (window.parent.generalvo['ifForceUse']){
        $forceUse.setValue(window.parent.generalvo['ifForceUse']);
        $startTime.setValue(window.parent.processPropertyMap['JTGK_effectiveDate']);
        $allowEdit.setValue(window.parent.processPropertyMap['JTGK_allowCompanyEdit']);
    }
}


function clickConfirm(){
    if (!pageEditable){
        window.parent.forceControlWindow.close();
        return;
    }
    let ifForceUse = $forceUse.getValue();
    let startTime = $startTime.getValue();
    let ifAllowEdit = $allowEdit.getValue();

    if (ifForceUse=='1' && !startTime){
        // 请设置生效日期
        idp.info(window.parent.idp.lang.get('FSPFProcess0040'));
        return false;
    }

    //校验设置的变化
    if (ifForceUse==='1' && ifAllowEdit==='0' &&  window.parent.processPropertyMap['JTGK_allowCompanyEdit']==='1'){
        //修改“允许下级单位修改”为否后,相关的单位私有流程将不再生效，请确认是否继续
        idp.confirm(window.parent.idp.lang.get('FSPFProcess0041'),()=>{
            saveDataAndCloseWindow();
        })
    }else{
        saveDataAndCloseWindow();
    }



    function saveDataAndCloseWindow(){
        if (window.parent.generalvo['ifForceUse']==='0' && ifForceUse==='1'){
            //标记：强制使用由否改为是，流程启用时提示
            window.parent.idp.store.commit('ifForceUseNewSetConfirm', true, true);
        }
        window.parent.generalvo['ifForceUse']=ifForceUse;
        window.parent.processPropertyMap['JTGK_allowCompanyEdit']=ifAllowEdit;
        window.parent.processPropertyMap['JTGK_effectiveDate']=startTime;

        window.parent.forceControlWindow.close();
    }

}


