//临时脚本文件
function getFilterMethod(orgId){
    let filterMethod=function(){
        let filters = [];
        filters.push({
            "Left": "",
            "Field": "ID",
            "Operate": "=",
            "IsExpress": false,
            "Value":orgId ,
            "Right": "",
            "Logic": "or"
        });
        if (idp.utils.getQuery('accesslevel')!='domain'){
            filters.push({
                "Left": "",
                "Field": "ownerid",
                "Operate": "=",
                "IsExpress": false,
                "Value":orgId ,
                "Right": "",
                "Logic": ""
            });
        }

        return filters;
    };
    return filterMethod;
}
