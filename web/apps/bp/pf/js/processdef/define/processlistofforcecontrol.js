
idp.event.bind("domReady",function(e){
    idp.event.register('grid_main', "beforeGridFilter", function(e, filter) {
        let rootId = idp.utils.getQuery('parentprocdefine');
        let a={
            "Left": "",
            "Field": "rootparentprocdefid",
            "Operate": "=",
            "IsExpress": false,
            "Value": rootId,
            "Right": "",
            "Logic": ""
        }
        filter.push(a);
        return filter;
    });
})



function clickConfirm(){
    let row = idp.control.get('grid_main').getSelected();
    if (!row){
        //请选中一条单据
        idp.info(idp.lang.get('FSPFFront0060'));
        return;
    }
    let formType=row['FORMTYPECODE'];
    let formTypeName=parent.idp.control.get("grid_817477").selected[0].NAME;
    let orgName=row['ORGNAME'];
    let rootOrgName=parent.idp.control.get("grid_766106").selected[0].ORGNAME;
    let onlyView=parent.idp.store.get('menuCode') == 'public';

    idp.utils.openurl(row.ID,idp.lang.get('FSPFFront0568')+'-'+row.NAME$LANGUAGE$,
        `/apps/fastdweb/views/runtime/page/card/cardpreview.html?assignId=${row.PROCASSIGNID}&styleid=d772e70b-c21c-4c5f-836e-2ed79af837c4&formtype=${formType}&pageState=view&parentid=${row.PARENTPROCDEFID}&formtypeName=${encodeURI(encodeURI(formTypeName))}&orgname=${encodeURI(encodeURI(orgName))}&publicinprivate=${false}&ifallowEdit=${true}&editForceUseProcess=true&rootorgname=${encodeURI(encodeURI(rootOrgName))}&onlyview=${onlyView}&procdefid=${row.ID}`,true);
}
