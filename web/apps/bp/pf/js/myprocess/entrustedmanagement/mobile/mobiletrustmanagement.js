var fsxzryid;
var dataScopeMap;
//单据类型总数
var allformtypecount = 0;
idp.define("Entrust/list", [], function() {
    var ctx = idp.getApp();

    function AddBtnClick() {
        ctx.$router.push({
            name: "898d3c79-24fb-f288-e35d-6de8478ac24acard",
            path: "/898d3c79-24fb-f288-e35d-6de8478ac24a/card",
            params: {
                type:"refresh",
                fsxzryid:fsxzryid,
                operate:"add"
            }});
    };

    function refershList() {
        ctx.control.get('grid_main').refresh();
    };
    //查询行政人员
    function getXzryId() {
        //获取当前行政人员
        var def = $.Deferred();
        idp.getApp().service.fetch(
            "/api/bp/pf/v1.0/myprocess/pfprocess/getfsxzrybyuserid",
            "",
            false
        ).then(function(data) {
            //成功回调方法
            if (data.result === true) {
                fsxzryid = data.value.id;
                def.resolve(true);
            } else {
                ctx.info(data.message);
                return false;
                def.resolve(false);
            }
        }).catch(function(data) {
            //失败回调方法
            // idp.error("调用失败");
            ctx.error(idp.lang.get('FSPFFront0055'));
            def.resolve(false);
        });
        return def.promise();
    }
    //删除单据
    async function deleteEvent(row) {
        var defer = $.Deferred();
        var ctx=idp.getApp();
        var id = row.ID;
        var state = row.STATE;
        ctx.confirm(idp.lang.get('FSPFMBFront0003'), deleteBill, function () {   //('确定要删除该单据吗？(删除成功后，本页面将自动关闭。)', deleteBill, function () {
            return false;
        });
        function deleteBill() {
            var param = {};
            param["ID"] = id;
            param["STATE"] = state;
            idp.getApp().service.fetch("/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/deletetrustproc", param, false).then(data=>{
                if (!data.result)
                {
                    ctx.info(data.message);
                    defer.resolve(false);
                }
                else {
                    idp.getApp().control.get('grid_main').refresh();
                    ctx.tips(idp.lang.get('FSPFMBFront0005'));//删除成功
                    defer.resolve(true);
                }
            }).catch(result=>{
                // ctx.error("删除失败");
                defer.resolve(false);
            });
            return defer.promise();
        }
    }
    var eventConfig = {
        'viewReady': function() {
            getXzryId();
        },
        'pageActive': function(cardorList, pageID) {
            refershList();
        },
        'controls': {
            'grid_main': {
                'rowclick': function (props, row, index) {
                    var query = '&STATE=' +  row.STATE +'&ID='+ row.ID;
                    if(row.STATE =="1" || row.STATE =="2" || row.STATE =="3")//历史单据,生效,未接受 卡片不能编辑
                    {
                        var params = {operate: "view"};//查看态
                        var operation="wfview";
                        ctx.openCard(row.ID, operation, '', params, query);
                    }else
                    {
                        var params = {operate: "edit"};//编辑态
                        var operation="edit";
                        ctx.openCard(row.ID, operation, '', params, query);
                    }

                },
                'beforeGridFilter': function (express) {
                    var filter = [];
                    var tabindex = ctx.control.get('tab_496267').getValue();
                    if (!!fsxzryid) {
                        filter.push({
                            "Left": "",
                            "Field": "PFREPROCTRUST.CLIENTID",
                            "Operate": "=",
                            "IsExpress": false,
                            "Value": fsxzryid,
                            "Right": "",
                            "Logic": "and",
                            "IsDate": false
                        });
                        if (tabindex == 0) {//委托单据
                            filter.push({
                                "Left": "",
                                "Field": "PFREPROCTRUST.STATE",
                                "Operate": "<>",
                                "IsExpress": false,
                                "Value": "3",
                                "Right": "",
                                "Logic": "and",
                                "IsDate": false
                            });
                        }
                        if (tabindex == 1) {//历史单据
                            filter.push({
                                "Left": "",
                                "Field": "PFREPROCTRUST.STATE",
                                "Operate": "=",
                                "IsExpress": false,
                                "Value": "3",
                                "Right": "",
                                "Logic": "and"
                            });

                        }
                        filter = filter.concat(express);
                        console.log(filter);
                        return filter;
                    }
                },
                'swipeclick': function (view, row, index) {
                    deleteEvent(row);
                },
            },
            'tab_496267':{
                'click':function (view,row,index) {
                    ctx.control.get('grid_main').refresh();
                    if (index == 1) {
                        ctx.controls.grid_main.swipe = 0;
                    }else{
                        ctx.controls.grid_main.swipe = 1;
                    }
                }
            }
        },
        //浮动按钮
        'floatBtnClick': function (btn) {
            AddBtnClick();
        },
    }

    return {
        init: function(ctx) {
            ctx.event.map(eventConfig);
        }
    }

});

idp.define("Entrust/card", [], function() {
    var ctx = idp.getApp();
    function getNowDate() {
        return new Date();
    }
    //暂存
    function save() {
        var ctx = idp.getApp();
        var requestParam = {};
        var param = {};
        if (!!idp.getApp().formItem.ID) {
            param.ID = idp.getApp().formItem.ID;
        }
        param.CLIENTID = idp.getApp().formItem.CLIENTID;
        param.TRUSTID = ctx.control.get("TRUSTEEID_NAME").currentValue;
        param.STARTTIME = ctx.control.get("input_wtksrq").currentValue;
        param.ENDTIME = ctx.control.get("input_wtjsrq").currentValue;
        param.NOTE = ctx.control.get("textarea_note").currentValue;
        param.IFACCEPTNOTIFY = ctx.control.get("checkbox_ifacceptnotify").currentValue;//委托人是否接受任务通知
        param.APPLYTOALL = "0";
        param.TASKTYPE = ctx.formItem.TASKTYPE;
        param.TRUSTTYPE = ctx.formItem.TRUSTTYPE;
        //param.APPLYTOALL = ctx.control.get("checkbox_allprocandform").currentValue[0] || null;
        param.IFEFFECTIVEATONCE = ctx.formItem.IFEFFECTIVEATONCE;
        requestParam["PFREPROCTRUST"] = param;
        if (!param.TRUSTID) {
            ctx.info(idp.lang.get('FSPFMBFront0006'));
            return false;
        }
        if (!param.NOTE) {
            ctx.info(idp.lang.get('FSPFMBFront0007'));
            return false;
        }
        if (param.NOTE && param.NOTE.length > 256) {
            ctx.info("委托说明最大长度为256,请修改");
            return false;
        }
        if (!param.STARTTIME) {
            ctx.info(idp.lang.get('FSPFMBFront0008'));
            return false;
        }
        if (!param.ENDTIME) {
            ctx.info(idp.lang.get('FSPFMBFront0009'));
            return false;
        }
        if(param.STARTTIME.length < 18){
            param.STARTTIME = param.STARTTIME + " 00:00:00";
        }
        if(param.ENDTIME.length < 18){
            param.ENDTIME = param.ENDTIME + " 23:59:59";
        }
        var startDate = Date.parse(param.STARTTIME);
        var endDate = Date.parse(param.ENDTIME);
        if (endDate < startDate) {
            ctx.info(idp.lang.get('FSPFMBFront00010'));
            return false;
        }
        if (endDate < getNowDate() - 1 * 24 * 60 * 60 * 1000 || startDate < getNowDate() - 1 * 24 * 60 * 60 * 1000) {
            ctx.info(idp.lang.get('FSPFMBFront00011'));
            return false;
        }
        var res = ctx.control.get("vue_formtype")._data.selectedformtype;
        if(allformtypecount.length!=0&&res.length==allformtypecount.length){
            param.APPLYTOALL="1";
        }
        if (param.APPLYTOALL != "1" && res.length == 0) {
            ctx.info(idp.lang.get('FSPFMBFront00012'));
            return false;
        }

        var proctrustscope = [];
        if(param.APPLYTOALL!="1"){
            for (var i = 0; i < res.length; i++) {
                var tmpParam = {};
                tmpParam.SCOPE = res[i].ID;
                tmpParam.TYPE = "2";
                proctrustscope.push(tmpParam);
            }
        }
        requestParam["PFREPROCTRUSTSCOPE"] = proctrustscope;

        return ctx.service.fetch('/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/savetrustproc', requestParam, false).then(function(data) {
            if (data.result) {
                ctx.tips(idp.lang.get('FSPFMBFront00013'));
                //refreshCard();
                idp.getApp().backClick();
            } else {
                ctx.info(data.message);
                return false;
            }
        }).catch(function(data) {
            //失败回调方法
            ctx.error(data.message);
            return false;
        });

    }
    // 终止
    function terminateTrust() {
        var id = idp.utils.getQuery('ID');
        var state = idp.utils.getQuery('STATE');
        var param = {};
        param["ID"] = id;
        param["STATE"] = state;
        idp.getApp().service.fetch("/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/terminaltrustproc", param, false).then(function(data) {
            //成功回调方法
            if (data.result === true) {
                idp.tips(idp.lang.get('FSPFMBFront00013'));
                idp.getApp().backClick();
            } else {
                idp.info(data.message);
                return false;
            }
        }).catch(function(data) {
            //失败回调方法
            idp.error(data.message);
            return false;
        });
        //idp.uiview.refreshGrid(idp.store.get("nowgrid"));
    }
    //提交
    function submit() {
        var ctx = idp.getApp();
        var requestParam = {};
        var param = {};
        param.ID=ctx.formItem.ID;
        param.CLIENTID = ctx.formItem.CLIENTID;
        param.TRUSTID = ctx.formItem.TRUSTEEID;
        param.STARTTIME = ctx.formItem.STARTTIME;
        param.ENDTIME = ctx.formItem.ENDTIME;
        param.IFACCEPTNOTIFY = ctx.formItem.IFACCEPTNOTIFY;
        param.NOTE = ctx.formItem.NOTE;
        param.APPLYTOALL = "0";
        param.TASKTYPE = ctx.formItem.TASKTYPE;
        param.TRUSTTYPE = ctx.formItem.TRUSTTYPE;
        param.IFEFFECTIVEATONCE = ctx.formItem.IFEFFECTIVEATONCE;
        requestParam["PFREPROCTRUST"] = param;
        if (!param.TRUSTID) {
            ctx.info(idp.lang.get('FSPFMBFront0006'));
            return false;
        }
        if (!param.NOTE) {
            ctx.info(idp.lang.get('FSPFMBFront0007'));
            return false;
        }
        if (param.NOTE && param.NOTE.length > 256) {
            ctx.info("委托说明最大长度为256,请修改");
            return false;
        }
        if (!param.STARTTIME) {
            ctx.info(idp.lang.get('FSPFMBFront0008'));
            return false;
        }
        if (!param.ENDTIME) {
            ctx.info(idp.lang.get('FSPFMBFront0009'));
            return false;
        }
        if(param.STARTTIME.length < 18){
            param.STARTTIME = param.STARTTIME + " 00:00:00";
        }
        if(param.ENDTIME.length < 18){
            param.ENDTIME = param.ENDTIME + " 23:59:59";
        }
        var startDate = Date.parse(param.STARTTIME);
        var endDate = Date.parse(param.ENDTIME);
        if (endDate < startDate) {
            ctx.info(idp.lang.get('FSPFMBFront00010'));
            return false;
        }
        if (endDate < getNowDate() - 1 * 24 * 60 * 60 * 1000 || startDate < getNowDate() - 1 * 24 * 60 * 60 * 1000) {
            ctx.info(idp.lang.get('FSPFMBFront00011'));
            return false;
        }

        var res = ctx.control.get("vue_formtype")._data.selectedformtype;
        if(allformtypecount!=0){
            param.APPLYTOALL = res.length == allformtypecount.length? "1":"0";
        }
        if (param.APPLYTOALL != "1" && res.length == 0) {
            ctx.info(idp.lang.get('FSPFMBFront00012'));
            return false;
        }

        var proctrustscope = [];
        if(param.APPLYTOALL != "1"){
            for (var i = 0; i < res.length; i++) {
                var tmpParam = {};
                tmpParam.SCOPE = res[i].ID;
                tmpParam.TYPE = "2";
                proctrustscope.push(tmpParam);
            }
        }
        requestParam["PFREPROCTRUSTSCOPE"] = proctrustscope;

        return ctx.service.fetch("/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/submittrustproc", requestParam, false).then(function(data) {
            if (data.result) {
                ctx.tips(idp.lang.get('FSPFMBFront00013'));
                idp.getApp().backClick();
            } else {
                ctx.info(data.message);
                return false;
            }
        }).catch(function(data) {
            //失败回调方法
            ctx.error(data.message);
            return false;
        });
    }
    //获取单据类型
    function gettrustprocscope(ctx) {
        var param = {};
        param.TRUSTID = ctx.formItem.ID;
        var dataScopeMap;
        ctx.service.fetchSync("/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/gettrustprocscope", param, false).done(function(data) {
            //成功回调方法
            if (data.result === true) {
                dataScopeMap = data.value;
            } else {
                ctx.info(data.message);
                return false;
            }
        }).fail(function(data) {
            //失败回调方法
            ctx.error(data.message);
            return false;;
        });
        return dataScopeMap;
    }

    async function getpfgeformtypedetail(dataScopeMap) {
        var res=[];
        var formList = dataScopeMap["FORMLIST"];
        var filter =[];
        if (!!formList) {
            // var lxCollect = "'" + formList.join('\',\'') + "'";
            filter.push({
                "Left": "",
                "Field": "pfgeformtype.ID",
                "Operate": "in",
                "IsExpress": false,
                "Value": formList,
                "Right": "",
                "Logic": "and",
                "IsDate": false
            });
        } else {
            filter.push({
                "Left": "",
                "Field": "1",
                "Operate": "=",
                "IsExpress": false,
                "Value": 2,
                "Right": "",
                "Logic": "and",
                "IsDate": false
            });
        }
        res = await idp.service.getQueryData('48945d29-c212-65b1-158e-65cb9c875777', filter, [], {});
        return res;
    }
    var eventConfig = {
        'viewReady': function() {
        },
        'pageActive': function (cardorList, pageID) {
            ctx.control.get("vue_formtype").viewinit();
            if(ctx.control.get("toolbar")) {
                idp.getApp().control.get("toolbar").setVisible(true);
                idp.getApp().control.get("toolbar").setDisabled(false);
                // idp.getApp().control.get("toolbar").data[0].hide=true;//暂存
                // idp.getApp().control.get("toolbar").data[1].hide=true;//提交
                // idp.getApp().control.get("toolbar").data[2].hide=true;//终止
            }
        },
        'loadData': function(ctx) {
            //ctx.control.get("vue_691273").getUserImage();
            //ctx.control.get("vue_formtype").viewinit();
            var state = ctx.formItem.STATE;
            ctx.control.get("TRUSTEEID_NAME").currentValue =ctx.formItem.TRUSTEEID;
            ctx.control.get("checkbox_ifacceptnotify").currentValue =ctx.formItem.IFACCEPTNOTIFY;
            if(ctx.control.get("toolbar") && state=="3"){
                idp.getApp().control.get("textarea_note").readonly=false;
                idp.getApp().control.get("toolbar").setVisible(false);
            }
            if(state=="0"||state=="4") {
                idp.getApp().control.get("textarea_note").setDisabled(false);
            }
            if(ctx.control.get("toolbar") && state!="3") {
                idp.getApp().control.get("toolbar").setVisible(true);
                idp.getApp().control.get("toolbar").setDisabled(false);
                if(state==="0") {//暂存 显示暂存和提交
                    idp.getApp().control.get("toolbar").data[0].hide=false;
                    idp.getApp().control.get("toolbar").data[1].hide=false;
                    idp.getApp().control.get("toolbar").data[2].hide=true;
                }else if (state==="1"|| state==="2"){//未接受和生效 显示终止
                    idp.getApp().control.get("toolbar").data[0].hide=true;
                    idp.getApp().control.get("toolbar").data[1].hide=true;
                    idp.getApp().control.get("toolbar").data[2].hide=false;
                }
                else if(state==="4"){//已拒绝
                    idp.getApp().control.get("toolbar").data[0].hide=false;
                    idp.getApp().control.get("toolbar").data[1].hide=false;
                    idp.getApp().control.get("toolbar").data[2].hide=false;
                }
            }
            //查询下所有单据类型数量
            ctx.service.getQueryData('31507cb8-afbf-b0f5-4843-373e30d28bb0', [], [], {}).then(function (data) {
                if (data.Data && data.Data.Rows && data.Data.Rows.length > 0) {
                    allformtypecount = data.Data.Rows;
                }
            });
            //获取备注

            // var dataScopeMap = gettrustprocscope(ctx);
            // var data = await getpfgeformtypedetail(dataScopeMap);
            // var namelist = [];
            // var namestr ="查看已选择单据";
            // if(data.Data && data.Data.Rows && data.Data.Rows.length > 0){
            //     namelist = data.Data.Rows;
            //     namestr="";
            //     for(var i = 0;i < namelist.length;i++){
            //         namestr= namestr + namelist[i].NAME$LANGUAGE$;
            //         if(i < namelist.length-1){
            //             namestr = namestr +",";
            //         }
            //     }
            // }
            // idp.getApp().control.get("vue_306158")._data.formnamevalue = namestr;
        },
        'controls': {
            //绑定按钮事件
            'toolbar': {
                'click': function(item, index) {
                    switch (item.id) {
                        case 'baritem_zz':
                            terminateTrust();
                            break;
                        case 'baritem_submit':
                            submit();
                            break;
                        case 'baritem_zc':
                            save();
                            break;
                        default:
                            break;
                    }
                }
            },
            'TRUSTEEID_NAME': {
                'selected': function(g, data) {
                    //重新获取用户头像
                    idp.getApp().control.get("input_trustdept").currentValue = data[0].ORGANIZATIONNAME;
                    idp.getApp().control.get("input_trustunit").currentValue = data[0].UNITNAME;
                    var state = ctx.formItem.STATE;
                    if(ctx.control.get("toolbar") && state=="3"){
                        idp.getApp().control.get("toolbar").setVisible(fasle);
                    }
                    if(ctx.control.get("toolbar") && state!="3") {
                        idp.getApp().control.get("toolbar").setVisible(true);
                        idp.getApp().control.get("toolbar").setDisabled(false);
                        if(state==="0") {//暂存 显示暂存和提交
                            idp.getApp().control.get("toolbar").data[0].hide=false;
                            idp.getApp().control.get("toolbar").data[1].hide=false;
                            idp.getApp().control.get("toolbar").data[2].hide=true;
                        }else if (state==="1"|| state==="2"){//未接受和生效 显示终止
                            idp.getApp().control.get("toolbar").data[0].hide=true;
                            idp.getApp().control.get("toolbar").data[1].hide=true;
                            idp.getApp().control.get("toolbar").data[2].hide=false;
                        }
                        else if(state==="4"){//已拒绝
                            idp.getApp().control.get("toolbar").data[0].hide=false;
                            idp.getApp().control.get("toolbar").data[1].hide=false;
                            idp.getApp().control.get("toolbar").data[2].hide=false;
                        }
                    }
                },
                'clearValue': function(g, data) {
                    idp.getApp().control.get("input_trustdept").currentValue = "";
                    idp.getApp().control.get("input_trustunit").currentValue = "";
                },
                'beforeHelpFilter': function () {
                    var ctx = idp.getApp();
                    var arr = [];
                    arr.push({   // 对公
                        "Left": "(",
                        "Field": "id",
                        "Operate": "<>",
                        "IsExpress": false,
                        "Value": fsxzryid,
                        "Right": ")",
                        "Logic": "and"
                    });

                    return arr;
                }
            },
        },
        'pageBack':function () {
            idp.getApp().control.get("vue_formtype").init();
        }
    }
    return {
        init: function(ctx) {
            ctx.event.map(eventConfig);
        }
    }
});

/**
 * @Description 移动window下暴露的方法
 * <AUTHOR>
 * @Date   2020-8-10 18:29:50
 * @Param
 * @Return
 */
window.rs = window.rs || {};
(function (rs, win, $) {

    function dateFormatRS(value, fmt = 'yyyy-MM-dd') {
        value = value || '';
        var returnValue = idp.utils.dateFormat(value, fmt);
        return returnValue == '1900-01-01' || returnValue == '1900.01.01' ? '' : returnValue;
    }

    function currency(value) {
        value = value || 0;
        return idp.utils.currency(value, 2);
    }
    function nameFormat(value){
        if(value && value.length>=2){
            return value.substring(value.length-2);
        }else{
            return value;
        }
    }
    //获取单据类型
    function gettrustprocscope(ctx) {
        var param = {};
        param.TRUSTID = ctx.formItem.ID;
        var dataScopeMap;
        ctx.service.fetchSync("/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/gettrustprocscope", param, false).done(function(data) {
            //成功回调方法
            if (data.result === true) {
                dataScopeMap = data.value;
            } else {
                ctx.info(data.message);
                return false;
            }
        }).fail(function(data) {
            //失败回调方法
            ctx.error(data.message);
            return false;;
        });
        return dataScopeMap;
    }
    function safeXSS(str) {
        if (!str) return str;
        if (typeof (str) != "string") return str;
        var temp = "";
        if (str.length == 0) return "";
        temp = str.replace(/<S/g, "&lt;s");
        temp = temp.replace(/<s/g, "&lt;s");
        temp = temp.replace(/<i/g, "&lt;i");
        temp = temp.replace(/<I/g, "&lt;I");
        //temp = temp.replace(/\s/g, "&nbsp;");
        temp = temp.replace(/\'/g, "&#39;");
        temp = temp.replace(/\"/g, "&quot;");
        return temp;
    }

    async function getpfgeformtypedetail(dataScopeMap) {
        var res=[];
        var formList = dataScopeMap["FORMLIST"];
        var filter =[];
        if (!!formList) {
            // var lxCollect = "'" + formList.join('\',\'') + "'";
            filter.push({
                "Left": "",
                "Field": "pfgeformtype.ID",
                "Operate": "in",
                "IsExpress": false,
                "Value": formList,
                "Right": "",
                "Logic": "and",
                "IsDate": false
            });
        } else {
            filter.push({
                "Left": "",
                "Field": "1",
                "Operate": "=",
                "IsExpress": false,
                "Value": 2,
                "Right": "",
                "Logic": "and",
                "IsDate": false
            });
        }
        res = await idp.service.getQueryData('48945d29-c212-65b1-158e-65cb9c875777', filter, [], {});
        return res;
    }
    //移动端子表样式mixin
    rs.form = {
        //格式化日期
        dateFormatRS: dateFormatRS,
        nameFormat : nameFormat,
        //千分位浮点小数
        currency: currency,
        gettrustprocscope:gettrustprocscope,
        getpfgeformtypedetail:getpfgeformtypedetail,
        //单据列表样式
        formRender: function(id, state, starttime,trusteeId,TRUSTEE_NAME, TRUSTEE_DEPTNAME,note,trusteeImage) {
            if(TRUSTEE_DEPTNAME == null){
                TRUSTEE_DEPTNAME = "";
            }
            var markRS = {
                "4": `<span style="float:right;font-family:PingFangSC-Regular;width: 75px;color:rgba(242,70,69,1);background:rgba(242,70,69,0.10);border-radius:3px;"> ${idp.lang.get("FSPFMBFront00014")} </span>`,
                "0": `<span style="float:right;font-family:PingFangSC-Regular;width: 75px;color:rgba(58,144,255,1);background:rgba(58,144,255,0.10);border-radius:3px;">${idp.lang.get("FSPFMBFront00015")}</span>`,
                "1": `<span style="float:right;font-family:PingFangSC-Regular;width: 75px;color:rgba(255,152,0,1);background:rgba(255,152,0,0.10);border-radius:3px;">${idp.lang.get("FSPFMBFront00016")}</span>`,
                "2": `<span style="float:right;font-family:PingFangSC-Regular;width: 75px;color:rgba(81,189,120,1);background:rgba(81,189,120,0.10);border-radius:3px;">${idp.lang.get("FSPFMBFront00017")}</span>`
            }
            var imageHtml = ` <div style="float: left;background: rgb(58 144 255);width: 45px;height: 45px;text-align: center;display: table;margin-left: 5px;border-radius: 50%;"> <div style="display: table-cell;vertical-align: middle;font-size: 16px;color: white;">${nameFormat(TRUSTEE_NAME)}</div></div>`;
            if(!!trusteeImage){
                imageHtml =  ` <div style="float: left;text-align: center;display: table;margin-left: 5px;border-radius: 50%;"><div style="height:45px"><img style="border-radius: 50%" width="45px" height="45px" src='`+trusteeImage +`' title="头像" alt="头像"/></div></div>`;
            }
            var stratInfo = `<div style="display: flex;margin:0px 5px;">`

            var itemInfo = `<div style="width:85%;padding-left:10px;">
                    <div style="display: flex;align-items: center;margin-right:0px;">
                        <div style="">
                            <span style="font-family: PingFangSC-Regular;font-size: 15px;color: #333333;font-weight: 400;">${safeXSS(TRUSTEE_NAME)}</span> 
                        </div>
                        <div style="font-size: 13px;text-align: center;margin-left:auto;">
                         ${markRS[state + ''] || ''}
                        </div>
                        
                    </div>
                    <div style="display: flex;align-items: center;margin-right:0px;">
                        <div style="font-size:13px;color: #888888;">
                             <span>${dateFormatRS(starttime, 'MM-dd')}</span>
                             <span style="padding-left: 5px">${safeXSS(TRUSTEE_DEPTNAME)}</span>
                        </div>   
                    </div>
                    <div style="display: flex;align-items: center;margin-right:0px;">
                        <div style="font-size:13px;color: #999999;">
                            <span style="width: 250px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block">${safeXSS(note)}</span>
                        </div>
                    </div>
                    `;

            var endInfo = ` </div>    </div>`;

            return stratInfo + imageHtml + itemInfo + endInfo;

        }

    };
    return rs;
})(window.rs || {}, window, jQuery);

//弹窗初始化
idp.component("rs-popup",{
    props:{
        ifshowcheckallvalue:{
            type: Boolean,
            default: true
        },
        poptitle:{}
    },
    template:
        `<div style="min-height:40vh;max-height:90vh">                   
                    <div @click="change" id='rs_popup'></div> 
                    <!--标题-->
                    <div>
                        <div class="pop-title">{{title}}</div>
                        <van-icon name="cross" color="#333333" size="25" @click="cancle" class="pop-title-btn" style="position: fixed;"/>
                    </div>
                    <!--列表组件-->
                    <div style = "padding:50px 5px;">
                        <van-cell v-if="rowDatas.length>0" v-for="(row,index) in rowDatas" @click="rowcellclick(row,index)">
                           <!--表单列表-->
                           <rs-formtypelist v-if="type=='FORMTYPE'" :row="row" :checkvalue="getSelectedResult(index)" :index = "index" 
                           @selected="selected"></rs-formtypelist>
                        </van-cell>                      
                        <van-cell v-if="rowDatas.length==0" center="true" >暂无数据</van-cell>
                    </div>
                    <!--确认-->
                    <div  v-show="ifshowcheckallvalue=='true'" class="f-toolbar" style="position: fixed;bottom: 0;width: 100%;height: 48px;letter-spacing: 2px;">
                        <van-checkbox v-model="checkAllValue"  @click="checkAllClick" style="margin-left: 21px;margin-top: 1px;font-size: 14px;letter-spacing: 2px;">${idp.lang.get("FSPFMBFront00033")}</van-checkbox>
                        <span style="font-size: 14px;letter-spacing: 2px;padding: 10px">${idp.lang.get("FSPFMBFront00034")}<span style="padding:0px 2px;color:#FA6400;">{{selectedDataIDS.length}}</span>${idp.lang.get("FSPFMBFront00035")}</span>
                        <van-button type="info" style="right: 20px;width: 84px;height: 34px;font-size: 16px;letter-spacing: 2px;position: fixed;" @click="confim">${idp.lang.get("FSPFMBFront00036")}</van-button>   
                    </div>
                </div>`,
    data:function(){
        return {
            title:"标题",
            type:"",//类型：表单列表
            show:false,//
            layout_id:"layout_ropop",//弹窗layout_id
            mulchoice:true,//是否多选
            rowDatas:[],//数据源
            addFunc:null,//选中后添加数据方法
            selectedData:[],//选中后的缓存值
            selectedDataIDS:[],//选中数据下标
            param:{},//
            checkAllValue: false,
        }
    },
    methods: {
        change: function () {
            this.show = !this.show;
            if(idp.getApp()["roPopUp"] && !idp.getApp()["roPopUp"]["MULCHOICE"] && idp.getApp()["roPopUp"]["MULCHOICE"]==false)
            {
                this.mulchoice=false;
            }
            this.setTitle(idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["TITLE"]);
            this.type = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["TYPE"]||"";
            this.rowDatas = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["DATA"]||[];
            //this.layout_id = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["LAYOUTID"]||layout_id;
            this.selectedData = [];
            this.selectedDataIDS = [];
            this.addFunc = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["addFunc"];
            this.param = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["param"]||{};
        },
        setTitle:function(title){
            if(!title){
                return;
            }
            var poptitle = this.poptitle;
            var returnTitle = title;
            if(poptitle.hasOwnProperty(returnTitle)){
                returnTitle = poptitle[returnTitle];
            }
            this.title = returnTitle;
        },
        cancle: function () {
            idp.getApp().togglePopUp('layout_ropop', false);
        },
        confim: function () {
            if (this.selectedDataIDS.length == 0) {
                idp.info("请选择数据");
            } else {
                this.addRowData();
            }
        },
        rowcellclick: function (row, index) {
            if(idp.getApp()["roPopUp"] && !idp.getApp()["roPopUp"]["MULCHOICE"] && idp.getApp()["roPopUp"]["MULCHOICE"]==false)
            {
                this.mulchoice=false;
            }
            this.addFunc = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["addFunc"];
            this.param = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["param"];
            //多选缓存数据
            var selectedDataIDS = this.selectedDataIDS;
            var rowDatas = this.rowDatas;
            if (this.mulchoice) {
                //确认是选择还是取消勾选
                var selectedIndex = selectedDataIDS.indexOf(index);
                if (row.checkvalue) {
                    row.checkvalue = false;
                    rowDatas[index] = row;
                    if(selectedIndex>-1){
                        selectedDataIDS.splice(selectedIndex, 1);
                    }
                } else {
                    row.checkvalue = true;
                    rowDatas[index] = row;
                    if(selectedIndex<0){
                        selectedDataIDS.push(index);
                    }
                }
                this.selectedDataIDS = selectedDataIDS;
                this.rowDatas = rowDatas;
            } else {
                //直接添加数据
                selectedDataIDS.push(index);
                this.selectedDataIDS = selectedDataIDS;
                this.addRowData();
            }
        },
        addRowData: function () {
            var selectedDataIDS = this.selectedDataIDS;
            var rowDatas = this.rowDatas;
            var selected = [];
            $.each(rowDatas,function(i,item){
                if(selectedDataIDS.indexOf(i)>-1){
                    selected.push(item);
                }
            })
            if (typeof this.addFunc == 'function' && selected.length > 0) {
                this.addFunc(selected,this.param);
            }
            this.cancle();
        },
        getSelectedResult: function (index) {
            if(this.selectedDataIDS.indexOf(index)<0){
                return false;
            }else{
                return true;
            }
        },
        selected: function (checkvalue, index) {
            var rowDatas = this.rowDatas;
            var selectedDataIDS = this.selectedDataIDS;
            if (this.mulchoice) {
                //确认是选择还是取消勾选
                var row = rowDatas[index];
                row.checkvalue = checkvalue;
                rowDatas[index] = row;
                var selectedIndex = selectedDataIDS.indexOf(index);
                if (checkvalue) {
                    if(selectedIndex<0){
                        selectedDataIDS.push(index);
                    }
                } else {
                    if(selectedIndex>-1){
                        selectedDataIDS.splice(selectedIndex, 1);
                    }
                }
                this.selectedDataIDS = selectedDataIDS;
                this.rowDatas = rowDatas;
                if (selectedDataIDS.length == this.rowDatas.length && selectedDataIDS.length!=0) {
                    this.checkAllValue = true;
                } else {
                    this.checkAllValue = false;
                }
            } else {
                //直接添加数据
                selectedDataIDS.push(index);
                thid.selectedDataIDS = selectedDataIDS;
                this.addRowData();
            }
        },
        checkAllClick: function () {
            var selfw = this;
            if (this.checkAllValue) {//全选
                $.each(this.rowDatas, function (i, item) {
                    selfw.selected(true, i);
                })
            } else {
                $.each(this.rowDatas, function (i, item) {
                    selfw.selected(false, i);
                })
            }
        },
    },
    created:function(){
        this.setTitle(idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["TITLE"]);
        this.type = idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["TYPE"]||"";
        this.rowDatas = idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["DATA"]||[];
        // this.layout_id = idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["LAYOUTID"]||layout_id;
        this.addFunc = idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["addFunc"];
        $('#vue_commonpop').parent().parent().parent().css('border-radius', '30px 30px 0px 0px');
    },
});
//表单类型列表
idp.component("rs-formtypelist", {
    props: {
        checkvalue: false,
        row: {},
        index: 0,
    },
    template: `
             <div>
                <div style="line-height: 24px;height: 24px;font-size: 16px;">
                  <div style="display:inline-block;vertical-align: middle; ">
                    <van-checkbox @change="rowcellclick" v-model="checkvalue"></van-checkbox>
                  </div>
                  <div style="width: 60%;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;display:inline-block;vertical-align: middle; "> 
                       <span>{{row.NAME$LANGUAGE$}}</span>    
                  </div>
                 </div> 
                <div style="display:block;margin:5px 0px;">
                    <div style="margin-left:30px;color:#999999;font-size:13px; letter-spacing: 1px;">
                        <span v-if="row.BIZAREA_NAME$LANGUAGE$">业务领域:{{row.BIZAREA_NAME$LANGUAGE$}}</span>
                        <span v-else>业务领域:暂无业务领域</span>
                    </div>
                </div>
             </div>`,
    methods: {
        rowcellclick: function () {
            this.row.checkvalue = this.checkvalue;
            this.$emit("selected", this.checkvalue, this.index);
        },
        currency(value) {
            value = value || 0;
            return idp.utils.currency(value, 2);
        },
        dateFormatRS(value, fmt = 'yyyy-MM-dd') {
            value = value || '';
            var returnValue = idp.utils.dateFormat(value, fmt);
            return returnValue == '1900-01-01' || returnValue == '1900.01.01' ? '' : returnValue;
        }
    }
});

