var clientid;
var dataStr;
//操作
var operate;
//委托主键
var dataId = "";
var dataScopeMap;
var viewFlag = false;
var state = '';
//表单类型选择数据
var forMdata = [];
//查看历史打开
var hisflag = false;
//受托人选择标识
var selectFlag = false;

//表单类型返回标识
var returnflag = "0";

//单据类型总数
var allformtypecount=0;

idp.define("Entrust_Add/card", [], function() {
    var ctx = idp.getApp();
    const commonAPI = {
        savetrustproc: '/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/savetrustproc',
        submittrustproc: '/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/submittrustproc',
        gettrustprocscope: '/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/gettrustprocscope'
    };

    //暂存
    function save() {
        var requestParam = {};
        var param = {};
        if(fsxzryid){
            clientid=fsxzryid;
        }
        param.CLIENTID = clientid;
        param.TRUSTID = ctx.control.get("input_trustid").currentValue;
        param.STARTTIME = ctx.control.get("input_wtksrq").currentValue;
        param.ENDTIME = ctx.control.get("input_wtjsrq").currentValue;
        param.NOTE = ctx.control.get("textarea_note").currentValue;
        param.IFACCEPTNOTIFY = ctx.control.get("checkbox_ifacceptnotify").currentValue;//委托人是否接受任务通知
        param.APPLYTOALL = "0";
        param.TASKTYPE = ctx.formItem.TASKTYPE;
        param.TRUSTTYPE = ctx.formItem.TRUSTTYPE;
        //param.APPLYTOALL = ctx.control.get("checkbox_allprocandform").currentValue[0] || null;
        requestParam["PFREPROCTRUST"] = param;
        if (!param.TRUSTID) {
            // ctx.info("请选择受托人");
            ctx.info(idp.lang.get('FSPFMBFront0006'));
            return false;
        }
        if (!param.NOTE) {
            // ctx.info("请填入委托说明");
            ctx.info(idp.lang.get('FSPFMBFront0007'));
            return false;
        }
        if (param.NOTE && param.NOTE.length > 256) {
            ctx.info("委托说明最大长度为256,请修改");
            return false;
        }
        if (!param.STARTTIME) {
            // ctx.info("请填写开始日期");
            ctx.info(idp.lang.get('FSPFMBFront0008'));
            return false;
        }
        if (!param.ENDTIME) {
            // ctx.info("请填写结束日期");
            ctx.info(idp.lang.get('FSPFMBFront0009'));
            return false;
        }
        if(param.STARTTIME){
            param.STARTTIME = param.STARTTIME + " 00:00:00";
        }
        if(param.ENDTIME){
            param.ENDTIME = param.ENDTIME + " 23:59:59";
        }
        var startDate = Date.parse(param.STARTTIME);
        var endDate = Date.parse(param.ENDTIME);
        if (endDate < startDate) {
            // ctx.info("结束时间不能早于开始时间");
            ctx.info(idp.lang.get('FSPFMBFront00010'));
            return false;
        }
        if (endDate < getNowDate() - 1 * 24 * 60 * 60 * 1000 || startDate < getNowDate() - 1 * 24 * 60 * 60 * 1000) {
            // ctx.info("开始时间与结束时间均不能早当前日期");
            ctx.info(idp.lang.get('FSPFMBFront00011'));
            return false;
        }

        // var res = ctx.control.get("vue_formtype")._data.selectedformtype;
        // if (param.APPLYTOALL != "1" && res.length == 0) {
        //     ctx.info("请选择表单");
        //     return false;
        // }

        // var proctrustscope = [];
        // for (var i = 0; i < res.length; i++) {
        //     var tmpParam = {};
        //     tmpParam.SCOPE = res[i].ID;
        //     tmpParam.TYPE = "2";
        //     proctrustscope.push(tmpParam);
        // }
        var res = ctx.control.get("vue_formtype")._data.selectedformtype;
        if(allformtypecount.length!=0 && res.length==allformtypecount.length){
            param.APPLYTOALL="1";
        }
        if (param.APPLYTOALL != "1" && res.length == 0) {
            // ctx.info("请选择表单");
            ctx.info(idp.lang.get('FSPFMBFront00012'));
            return false;
        }

        var proctrustscope = [];
        if(param.APPLYTOALL!="1"){
            for (var i = 0; i < res.length; i++) {
                var tmpParam = {};
                tmpParam.SCOPE = res[i].ID;
                tmpParam.TYPE = "2";
                proctrustscope.push(tmpParam);
            }
        }
        requestParam["PFREPROCTRUSTSCOPE"] = proctrustscope;

        return ctx.service.fetch(commonAPI.savetrustproc, requestParam, false).then(function(data) {
            if (data.result) {
                // ctx.tips("操作成功");
                ctx.info(idp.lang.get('FSPFMBFront00013'));
                refreshCard();
                idp.getApp().backClick();
            } else {
                ctx.info(data.message);
                return false;
            }
        }).catch(function(data) {
            //失败回调方法
            ctx.error(data.message);
            return false;
        });

    }

    //提交
    function submit() {
        var requestParam = {};
        var param = {};
        // if (!!dataId) {
        //     param.ID = dataId;
        // }
        if(fsxzryid){
            clientid=fsxzryid;
        }
        param.CLIENTID = clientid;
        param.TRUSTID = ctx.control.get("input_trustid").currentValue;
        param.STARTTIME = ctx.control.get("input_wtksrq").currentValue;
        param.ENDTIME = ctx.control.get("input_wtjsrq").currentValue;
        param.NOTE = ctx.control.get("textarea_note").currentValue;
        param.IFACCEPTNOTIFY = ctx.control.get("checkbox_ifacceptnotify").currentValue;//委托人是否接受任务通知
        //param.APPLYTOALL = ctx.control.get("checkbox_allprocandform").currentValue[0] || null;
        param.APPLYTOALL = "0";
        param.TASKTYPE = ctx.formItem.TASKTYPE;
        param.TRUSTTYPE = ctx.formItem.TRUSTTYPE;
        requestParam["PFREPROCTRUST"] = param;
        if (!param.TRUSTID) {
            // ctx.info("请选择受托人");
            ctx.info(idp.lang.get('FSPFMBFront0006'));
            return false;
        }
        if (!param.NOTE) {
            // ctx.info("请填入委托说明");
            ctx.info(idp.lang.get('FSPFMBFront0007'));
            return false;
        }
        if (param.NOTE && param.NOTE.length > 256) {
            ctx.info("委托说明最大长度为256,请修改");
            return false;
        }
        if (!param.STARTTIME) {
            // ctx.info("请填写开始日期");
            ctx.info(idp.lang.get('FSPFMBFront0008'));
            return false;
        }
        if (!param.ENDTIME) {
            // ctx.info("请填写结束日期");
            ctx.info(idp.lang.get('FSPFMBFront0009'));
            return false;
        }
        if(param.STARTTIME){
            param.STARTTIME = param.STARTTIME + " 00:00:00";
        }
        if(param.ENDTIME){
            param.ENDTIME = param.ENDTIME + " 23:59:59";
        }

        var startDate = Date.parse(param.STARTTIME);
        var endDate = Date.parse(param.ENDTIME);
        if (endDate < startDate) {
            // ctx.info("结束时间不能早于开始时间");
            ctx.info(idp.lang.get('FSPFMBFront00010'));
            return false;
        }
        if (endDate < getNowDate() - 1 * 24 * 60 * 60 * 1000 || startDate < getNowDate() - 1 * 24 * 60 * 60 * 1000) {
            // ctx.info("开始时间与结束时间均不能早当前日期");
            ctx.info(idp.lang.get('FSPFMBFront00011'));
            return false;
        }

        var res = ctx.control.get("vue_formtype")._data.selectedformtype;
        if(allformtypecount.length!=0 && res.length==allformtypecount.length){
            param.APPLYTOALL="1";
        }
        if (param.APPLYTOALL != "1" && res.length == 0) {
            // ctx.info("请选择表单");
            ctx.info(idp.lang.get('FSPFMBFront00012'));
            return false;
        }

        var proctrustscope = [];
        if(param.APPLYTOALL!="1"){
            for (var i = 0; i < res.length; i++) {
                var tmpParam = {};
                tmpParam.SCOPE = res[i].ID;
                tmpParam.TYPE = "2";
                proctrustscope.push(tmpParam);
            }
        }
        requestParam["PFREPROCTRUSTSCOPE"] = proctrustscope;

        return ctx.service.fetch(commonAPI.submittrustproc, requestParam, false).then(function(data) {
            if (data.result) {
                // ctx.tips("操作成功");
                ctx.info(idp.lang.get('FSPFMBFront00013'));
                refreshCard();
                idp.getApp().backClick();
                // ctx.$router.push({
                //     name: "9d268a3f-9fcf-0671-035a-bacac525e1a6list",
                //     path: "/9d268a3f-9fcf-0671-035a-bacac525e1a6/list",
                //     query: {
                //         id: "Entrust"
                //     }
                // });
            } else {
                ctx.info(data.message);
                return false;
            }
        }).catch(function(data) {
            //失败回调方法
            ctx.error(data.message);
            return false;
        });
    }

    function getNowDate() {
        return new Date();
    }

    function fun_date(num) {
        var date1 = getNowDate();
        //今天时间
        var time1 = date1.getFullYear() + "-" + (date1.getMonth() + 1) + "-" + date1.getDate()
        console.log(time1);
        var date2 = new Date(date1);
        date2.setDate(date1.getDate() + num);
        //num是正数表示之后的时间，num负数表示之前的时间，0表示今天
        var y = date2.getFullYear();
        console.log(y);
        var m = date2.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        var d = date2.getDate();
        d = d < 10 ? ('0' + d) : d;
        var time2 = y + '-' + m + '-' + d;
        console.log(time2);
        return time2;
    }

    function refreshCard() {
        //组件初始化 还原默认值
        idp.getApp().control.get("input_trustid").currentText = null;
        idp.getApp().control.get("input_trustid").currentValue = null;
        idp.getApp().control.get("input_trustdept").currentValue = null;
        idp.getApp().control.get("input_trustunit").currentValue = null;
        // idp.getApp().control.get("input_wtksrq").currentText = null;
        // idp.getApp().control.get("input_wtksrq").currentValue = null;
        // idp.getApp().control.get("input_wtjsrq").currentText = null;
        // idp.getApp().control.get("input_wtjsrq").currentValue = null;
        idp.getApp().control.get("textarea_note").currentValue = null;
        idp.getApp().control.get("checkbox_ifacceptnotify").currentValue = null;
        idp.getApp().control.get("vue_formtype").init();
    }

    var eventConfig = {
        'viewReady': function() {
            if (operate == "add") {
                var currentDate = idp.utils.dateFormat(new Date());
                idp.getApp().control.get("input_wtksrq").currentText = currentDate;
                idp.getApp().control.get("input_wtksrq").currentValue = currentDate;
                idp.getApp().control.get("input_wtjsrq").currentText = fun_date(7);
                idp.getApp().control.get("input_wtjsrq").currentValue = fun_date(7);
            }
        },
        'pageActive': function(cardorList, pageID) {
            // if (idp.getApp() && idp.getApp().$route.params && idp.getApp().$route.params.operate) {
            //     operate = idp.getApp().$route.params.operate;
            // }
            // var ctx = idp.getApp();
            // ctx.service.getQueryData('31507cb8-afbf-b0f5-4843-373e30d28bb0', [], [], {}).then(function (data) {
            //     if (data.Data && data.Data.Rows && data.Data.Rows.length > 0) {
            //         allformtypecount = data.Data.Rows;
            //     }
            // });
            // if (operate == "add") {
            //     var currentDate = idp.utils.dateFormat(new Date());
            //     ctx.control.get("input_wtksrq").currentText = currentDate;
            //     ctx.control.get("input_wtksrq").currentValue = currentDate;
            //     ctx.control.get("input_wtjsrq").currentText = fun_date(7);
            //     ctx.control.get("input_wtjsrq").currentValue = fun_date(7);
            // }
        },
        'loadData': function(ctx) {
            //如果是新增
            // var ctx = idp.getApp();
            // ctx.service.getQueryData('31507cb8-afbf-b0f5-4843-373e30d28bb0', filter, [], {}).then(function (data) {
            //     if (data.Data && data.Data.Rows && data.Data.Rows.length > 0) {
            //         allformtypecount = data.Data.Rows;
            //     }
            // });
            if (operate == "add") {
                var currentDate = idp.utils.dateFormat(new Date());
                // idp.getApp().control.get("input_wtksrq").currentText = currentDate;
                // idp.getApp().control.get("input_wtksrq").currentValue = currentDate;
                // idp.getApp().control.get("input_wtjsrq").currentText = fun_date(7);
                // idp.getApp().control.get("input_wtjsrq").currentValue = fun_date(7);
            }
        },
        'afterAddData': function(row) {
            // if (operate == "add") {
            var currentDate = idp.utils.dateFormat(new Date());
            row.STARTTIME = currentDate;
            row.ENDTIME = fun_date(7);
            // }
        },

        'initView': function() {
            if (operate == "add") {
                var currentDate = idp.utils.dateFormat(new Date());
                idp.getApp().control.get("input_wtksrq").currentText = currentDate;
                idp.getApp().control.get("input_wtksrq").currentValue = currentDate;
                idp.getApp().control.get("input_wtjsrq").currentText = fun_date(7);
                idp.getApp().control.get("input_wtjsrq").currentValue = fun_date(7);
            }
        },
        'pageBack': function() {
            refreshCard();
        },
        'controls': {
            //绑定按钮事件
            'toolbar_880803': {
                'click': function(item, index) {
                    switch (item.id) {
                        case 'baritem_save':
                            save();
                            break;
                        case 'baritem_submit':
                            submit();
                            break;
                        default:
                            break;
                    }
                }
            },
            'input_trustid': {
                'selected': function(g, data) {
                    selectFlag = true;
                    idp.getApp().control.get("input_trustdept").currentValue = data[0].ORGANIZATIONNAME;
                    idp.getApp().control.get("input_trustunit").currentValue = data[0].UNITNAME;
                },
                'clearValue': function(g, data) {
                    idp.getApp().control.get("input_trustdept").currentValue = "";
                    idp.getApp().control.get("input_trustunit").currentValue = "";
                },
                'beforeHelpFilter': function () {
                    var ctx = idp.getApp();
                    var arr = [];
                    arr.push({   // 对公
                        "Left": "(",
                        "Field": "emp.id",
                        "Operate": "<>",
                        "IsExpress": false,
                        "Value": fsxzryid,
                        "Right": ")",
                        "Logic": "and"
                    });

                    return arr;
                }

            },
        },
    }

    return {
        init: function(ctx) {
            ctx.event.map(eventConfig);
        }
    }

});

//弹窗初始化
idp.component("rs-popup",{
    props:{
        ifshowcheckallvalue:{
            type: Boolean,
            default: true
        },
        poptitle:{}
    },
    template:
        `<div style="min-height:40vh;max-height:90vh">                   
                    <div @click="change" id='rs_popup'></div> 
                    <!--标题-->
                    <div>
                        <div class="pop-title">{{title}}</div>
                        <van-icon name="cross" color="#333333" size="25" @click="cancle" class="pop-title-btn" style="position: fixed;"/>
                    </div>
                    <!--列表组件-->
                    <div style = "padding:50px 5px;">
                        <van-cell v-if="rowDatas.length>0" v-for="(row,index) in rowDatas" @click="rowcellclick(row,index)">
                           <!--表单列表-->
                           <rs-formtypelist v-if="type=='FORMTYPE'" :row="row" :checkvalue="getSelectedResult(index)" :index = "index" 
                           @selected="selected"></rs-formtypelist>
                        </van-cell>                      
                        <van-cell v-if="rowDatas.length==0" center="true" >暂无数据</van-cell>
                    </div>
                    <!--确认-->
                    <div  v-show="ifshowcheckallvalue=='true'" class="f-toolbar" style="position: fixed;bottom: 0;width: 100%;height: 48px;letter-spacing: 2px;">
                        <van-checkbox v-model="checkAllValue"  @click="checkAllClick" style="margin-left: 21px;margin-top: 1px;font-size: 14px;letter-spacing: 2px;">${idp.lang.get("FSPFMBFront00033")}</van-checkbox>
                        <span style="font-size: 14px;letter-spacing: 2px;padding: 10px">${idp.lang.get("FSPFMBFront00034")}<span style="padding:0px 2px;color:#FA6400;">{{selectedDataIDS.length}}</span>${idp.lang.get("FSPFMBFront00035")}</span>
                        <van-button type="info" style="right: 20px;width: 84px;height: 34px;font-size: 16px;letter-spacing: 2px;position: fixed;" @click="confim">${idp.lang.get("FSPFMBFront00036")}</van-button>   
                    </div>
                </div>`,
    data:function(){
        return {
            title:"标题",
            type:"",//类型：表单列表
            show:false,//
            layout_id:"layout_ropop",//弹窗layout_id
            mulchoice:true,//是否多选
            rowDatas:[],//数据源
            addFunc:null,//选中后添加数据方法
            selectedData:[],//选中后的缓存值
            selectedDataIDS:[],//选中数据下标
            param:{},//
            checkAllValue: false,
        }
    },
    methods: {
        change: function () {
            this.show = !this.show;
            if(idp.getApp()["roPopUp"] && !idp.getApp()["roPopUp"]["MULCHOICE"] && idp.getApp()["roPopUp"]["MULCHOICE"]==false)
            {
                this.mulchoice=false;
            }
            this.setTitle(idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["TITLE"]);
            this.type = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["TYPE"]||"";
            this.rowDatas = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["DATA"]||[];
            //this.layout_id = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["LAYOUTID"]||layout_id;
            this.selectedData = [];
            this.selectedDataIDS = [];
            this.addFunc = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["addFunc"];
            this.param = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["param"]||{};
        },
        setTitle:function(title){
            if(!title){
                return;
            }
            var poptitle = this.poptitle;
            var returnTitle = title;
            if(poptitle.hasOwnProperty(returnTitle)){
                returnTitle = poptitle[returnTitle];
            }
            this.title = returnTitle;
        },
        cancle: function () {
            idp.getApp().togglePopUp('layout_ropop', false);
        },
        confim: function () {
            if (this.selectedDataIDS.length == 0) {
                // idp.info("请选择数据");
                idp.info(idp.lang.get('FSPFMBFront00031'));
            } else {
                this.addRowData();
            }
        },
        rowcellclick: function (row, index) {
            if(idp.getApp()["roPopUp"] && !idp.getApp()["roPopUp"]["MULCHOICE"] && idp.getApp()["roPopUp"]["MULCHOICE"]==false)
            {
                this.mulchoice=false;
            }
            this.addFunc = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["addFunc"];
            this.param = idp.getApp()["roPopUp"] && idp.getApp()["roPopUp"]["param"];
            //多选缓存数据
            var selectedDataIDS = this.selectedDataIDS;
            var rowDatas = this.rowDatas;
            if (this.mulchoice) {
                //确认是选择还是取消勾选
                var selectedIndex = selectedDataIDS.indexOf(index);
                if (row.checkvalue) {
                    row.checkvalue = false;
                    rowDatas[index] = row;
                    if(selectedIndex>-1){
                        selectedDataIDS.splice(selectedIndex, 1);
                    }
                } else {
                    row.checkvalue = true;
                    rowDatas[index] = row;
                    if(selectedIndex<0){
                        selectedDataIDS.push(index);
                    }
                }
                this.selectedDataIDS = selectedDataIDS;
                this.rowDatas = rowDatas;
            } else {
                //直接添加数据
                selectedDataIDS.push(index);
                this.selectedDataIDS = selectedDataIDS;
                this.addRowData();
            }
        },
        addRowData: function () {
            var selectedDataIDS = this.selectedDataIDS;
            var rowDatas = this.rowDatas;
            var selected = [];
            $.each(rowDatas,function(i,item){
                if(selectedDataIDS.indexOf(i)>-1){
                    selected.push(item);
                }
            })
            if (typeof this.addFunc == 'function' && selected.length > 0) {
                this.addFunc(selected,this.param);
            }
            this.cancle();
        },
        getSelectedResult: function (index) {
            if(this.selectedDataIDS.indexOf(index)<0){
                return false;
            }else{
                return true;
            }
        },
        selected: function (checkvalue, index) {
            var rowDatas = this.rowDatas;
            var selectedDataIDS = this.selectedDataIDS;
            if (this.mulchoice) {
                //确认是选择还是取消勾选
                var row = rowDatas[index];
                row.checkvalue = checkvalue;
                rowDatas[index] = row;
                var selectedIndex = selectedDataIDS.indexOf(index);
                if (checkvalue) {
                    if(selectedIndex<0){
                        selectedDataIDS.push(index);
                    }
                } else {
                    if(selectedIndex>-1){
                        selectedDataIDS.splice(selectedIndex, 1);
                    }
                }
                this.selectedDataIDS = selectedDataIDS;
                this.rowDatas = rowDatas;
                if (selectedDataIDS.length == this.rowDatas.length && selectedDataIDS.length!=0) {
                    this.checkAllValue = true;
                } else {
                    this.checkAllValue = false;
                }
            } else {
                //直接添加数据
                selectedDataIDS.push(index);
                thid.selectedDataIDS = selectedDataIDS;
                this.addRowData();
            }
        },
        checkAllClick: function () {
            var selfw = this;
            if (this.checkAllValue) {//全选
                $.each(this.rowDatas, function (i, item) {
                    selfw.selected(true, i);
                })
            } else {
                $.each(this.rowDatas, function (i, item) {
                    selfw.selected(false, i);
                })
            }
        },
    },
    created:function(){
        this.setTitle(idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["TITLE"]);
        this.type = idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["TYPE"]||"";
        this.rowDatas = idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["DATA"]||[];
        // this.layout_id = idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["LAYOUTID"]||layout_id;
        this.addFunc = idp.getApp()["roPopUp"]&&idp.getApp()["roPopUp"]["addFunc"];
        $('#vue_commonpop').parent().parent().parent().css('border-radius', '30px 30px 0px 0px');
    },
});
//表单类型列表
idp.component("rs-formtypelist", {
    props: {
        checkvalue: false,
        row: {},
        index: 0,
    },
    template: `
             <div>
                <div style="line-height: 24px;height: 24px;font-size: 16px;">
                  <div style="display:inline-block;vertical-align: middle; ">
                    <van-checkbox @change="rowcellclick" v-model="checkvalue"></van-checkbox>
                  </div>
                  <div style="width: 60%;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;display:inline-block;vertical-align: middle; "> 
                       <span>{{row.NAME$LANGUAGE$}}</span>    
                  </div>
                 </div> 
                <div style="display:block;margin:5px 0px;">
                    <div style="margin-left:30px;color:#999999;font-size:13px; letter-spacing: 1px;">
                        <span v-if="row.BIZAREA_NAME$LANGUAGE$">业务领域:{{row.BIZAREA_NAME$LANGUAGE$}}</span>
                        <span v-else>业务领域:暂无业务领域</span>
                    </div>
                </div>
             </div>`,
    methods: {
        rowcellclick: function () {
            this.row.checkvalue = this.checkvalue;
            this.$emit("selected", this.checkvalue, this.index);
        },
        currency(value) {
            value = value || 0;
            return idp.utils.currency(value, 2);
        },
        dateFormatRS(value, fmt = 'yyyy-MM-dd') {
            value = value || '';
            var returnValue = idp.utils.dateFormat(value, fmt);
            return returnValue == '1900-01-01' || returnValue == '1900.01.01' ? '' : returnValue;
        }
    }
});

