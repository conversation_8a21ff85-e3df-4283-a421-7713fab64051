var fsxzryid;
idp.define("History/list", [], function() {
    var ctx = idp.getApp();
    var first = true;
    var eventConfig = {
        'viewReady': function() {},
        'pageActive': function(cardorList, pageID) {
            if (idp.getApp() && idp.getApp().$route.params && idp.getApp().$route.params.fsxzryid) {
                fsxzryid = idp.getApp().$route.params.fsxzryid;
            }
            if (first) {
                first = false;
            } else {
                idp.control.get("grid_876783").refresh();
            }
        },
        'loadData': function(ctx) {},
        'controls': {
            'grid_876783': {
                'beforeGridFilter': function(filter) {
                    if (!!fsxzryid) {
                        filter.push({
                            "Left": "",
                            "Field": "PFREPROCTRUST.CLIENTID",
                            "Operate": "=",
                            "IsExpress": false,
                            "Value": fsxzryid,
                            "Right": "",
                            "Logic": "and",
                            "IsDate": false
                        });
                        filter.push({
                            "Left": "",
                            "Field": "PFREPROCTRUST.STATE",
                            "Operate": "=",
                            "IsExpress": false,
                            "Value": '3',
                            "Right": "",
                            "Logic": "and",
                            "IsDate": false
                        });
                    }
                }
            }
        },
    }

    return {
        init: function(ctx) {
            ctx.event.map(eventConfig);
        }
    }
});


idp.define("History/card", [], function() {
    var ctx = idp.getApp();

    function ViewBtnClick(){
        var param = {};
        var hisflag = true;
        param.hisflag = hisflag;
        param.item = idp.uiview.formItem;
        return ctx.openPage('6ddf98a2-d252-ecf9-65b9-ff99a37b7a6a', 'list', param, {});
        // 898d3c79-24fb-f288-e35d-6de8478ac24a
    }

    var eventConfig = {
        'viewReady': function() {

        },
        'loadData': function(ctx) {
            var data = idp.uiview.formItem;
            // idp.getApp().control.get("CLIENTID_NAME").currentValue = data.CLIENTID_NAME_CHS;
            // idp.getApp().control.get("CLIENTID_ORGANIZATION_NAME").currentValue = data.CLIENTID_ORGANIZATION_NAME;
        },
        'floatBtnClick': function(btn) {
            ViewBtnClick();
        },
    }

    return {
        init: function(ctx) {
            ctx.event.map(eventConfig);
        }
    }
});