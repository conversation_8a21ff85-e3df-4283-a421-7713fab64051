/**
 * @Description
 * <AUTHOR>
 * @Date   2020/7/21 9:12
 * @Param
 * @Return
 */
var reloadData = true;
idp.define("STCL/list", [], function () {

    var ctx = idp.getApp();
    var fsxzryid;
    ctx.service.fetchSync('/api/bp/pf/v1.0/myprocess/pfprocess/getfsxzrybyuserid', "").then(data=>{
        if (data.result) {
            fsxzryid = data.value.id;
        }
        else {
            ctx.info(data.message);
            return false;
        }
    });

    var stclEventConfig = {
        'viewReady': function () {

        },
        //切面切换
        'pageActive': function (cardorList, pageID) {
            idp.getApp().control.get('grid_main').refresh()
        },
        'pageBack': function () {

        },
        //浮动按钮
        'floatBtnClick': function (btn) {
            if(btn.id=='baritem_addbill'){

            }
        },

        'saveData': function () {
        },
        'beforeAddGridRow': function (table, data) {},
        'afterAddGridRow': function (table, data) {
        },
        'beforeAddData': function (row) {},
        'afterAddData': function (row) {},
        'loadData': function (ctx) {
        },
        'controls': {
            'grid_main': {
                'rowclick': function (props, row, index) {
                },
                'beforeGridFilter': function (express) {
                    var filter = [];
                    var tabindex=ctx.control.get('tab_spst').getValue();
                    filter.push({
                        "Left": "",
                        "Field": "PFREPROCTRUST.TRUSTEEID",
                        "Operate": "=",
                        "IsExpress": false,
                        "Value": fsxzryid,
                        "Right": "",
                        "Logic": "and",
                        "IsDate": false
                    });
                    filter.push({
                        "Left": "",
                        "Field": "PFREPROCTRUST.STATE",
                        "Operate": "<>",
                        "IsExpress": false,
                        "Value": '0',
                        "Right": "",
                        "Logic": "and"
                    });
                    if(tabindex==1)
                    {
                        var states = ["2","3","4"];

                        filter.push({
                            "Left": "",
                            "Field": "PFREPROCTRUST.STATE",
                            "Operate": "in",
                            "IsExpress": false,
                            "Value":  states,
                            "Right": "",
                            "Logic": "and"
                        });

                    };
                    if(tabindex==0)
                    {
                        filter.push({
                            "Left": "",
                            "Field": "PFREPROCTRUST.STATE",
                            "Operate": "=",
                            "IsExpress": false,
                            "Value": '1',
                            "Right": "",
                            "Logic": "and"
                        });
                    };
                    filter = filter.concat(express);
                    return filter;
                },

                'swipeButtonClick':function(g,e,row,ctx2,index){
                    var ctx=idp.getApp();
                    if(g=='baritem_accpet'){
                        var param = {};
                        param.ID = row.ID;
                        ctx.service.fetchSync(
                            "/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/accepttrustedtask",
                            param
                        ).then(function(data) {
                            if (data.result === true) {
                                ctx.tips(idp.lang.get('FSPFMBFront00024'));
                                ctx.control.get('grid_main').refresh();
                            } else {
                                ctx.info(data.message);
                            }
                        })
                    }
                    else if(g=='baritem_refuse')
                    {
                        var param = {};
                        param.ID = row.ID;
                        ctx.service.fetchSync(
                            "/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/rejecttrustedtask",
                            param
                        ).then(function(data) {
                            if (data.result === true) {
                                ctx.tips(idp.lang.get('FSPFMBFront00025'));
                                ctx.control.get('grid_main').refresh();
                            } else {
                                ctx.info(data.message);
                            }
                        })
                    }
                }
            },
            'tab_spst':{
                'click':function (view,row,index) {
                    if(index==0){
                        idp.getApp().control.get("grid_main").vprops.swipe=true;
                    }else{
                        idp.getApp().control.get("grid_main").vprops.swipe=false;
                    }
                    ctx.control.get('grid_main').refresh();
                }
            }

        }
    };

    //入口注册差旅config
    return {
        init: function (ctx) {
            ctx.event.map(stclEventConfig);
        }
    }
});
idp.define("STCL/card", [], function () {
    var ctx = idp.getApp();
    var stclcardEventConfig = {
        'viewReady': function () {
        },
        //切面切换
        'pageActive': function (cardorList, pageID) {
            if(ctx.formItem.STATE=='1'){
                idp.getApp().control.get("toolbar").setVisible(true);
            }else{
                idp.getApp().control.get("toolbar").setVisible(false);
                ctx.setUIReadonly(true);
            }
            idp.getApp().control.get("toolbar").setDisabled(false);
        },
        'pageBack': function () {

        },
        //浮动按钮
        'floatBtnClick': function (btn) {
        },

        'saveData': function () {
        },
        'beforeAddGridRow': function (table, data) {},
        'afterAddGridRow': function (table, data) {
        },
        'beforeAddData': function (row) {},
        'afterAddData': function (row) {},
        'loadData': function (ctx) {
            if(ctx.formItem.STATE=='1'){
                idp.getApp().control.get("toolbar").setVisible(true);
            }else{
                idp.getApp().control.get("toolbar").setVisible(false);
                ctx.setUIReadonly(true);
            }
            idp.getApp().control.get("toolbar").setDisabled(false);
        },
        'controls': {
            'toolbar': {//绑定卡片按钮事件
                'click': function (item, index) {
                    switch (item.id) {
                        case 'baritem_accept':
                            var ctx=idp.getApp();
                            var param = {};
                            param.ID = ctx.formItem.ID;;
                            ctx.loading(idp.lang.get('FSPFMBFront00025'))
                            ctx.service.fetchSync(
                                "/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/accepttrustedtask",
                                param
                            ).then(function(data) {
                                if (data.result === true) {
                                    idp.success(idp.lang.get('FSPFMBFront00026'));
                                    setTimeout(()=>{
                                        ctx.loaded();
                                        idp.getApp().backClick();
                                    },2000);
                                } else {
                                    ctx.info(data.message);
                                }
                            })
                            break;
                        case 'baritem_reject':
                            var ctx=idp.getApp();
                            var param = {};
                            param.ID = ctx.formItem.ID;
                            ctx.loading(idp.lang.get('FSPFMBFront00027'))
                            ctx.service.fetchSync(
                                "/api/bp/pf/v1.0/myprocess/pfentrustmanagerapi/rejecttrustedtask",
                                param
                            ).then(function(data) {
                                if (data.result === true) {
                                    idp.success(idp.lang.get('FSPFMBFront00028'));
                                    setTimeout(()=>{
                                        ctx.loaded();
                                        idp.getApp().backClick();
                                    },2000);
                                } else {
                                    ctx.info(data.message);
                                    idp.getApp().backClick();
                                }
                            })

                            break;
                        default:
                            break;
                    }
                }
            },

        }
    };

    //入口注册差旅config
    return {
        init: function (ctx) {
            ctx.event.map(stclcardEventConfig);
        }
    }
});
