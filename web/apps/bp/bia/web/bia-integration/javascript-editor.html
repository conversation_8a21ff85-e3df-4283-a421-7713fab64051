<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>JS Editor</title>

    <!-- CodeMirror 核心样式 -->
    <link rel="stylesheet" href="codemirror.css">

    <!-- 深色主题样式 -->
    <link rel="stylesheet" href="dracula.css">

    <style>
        /* 重置默认样式 */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            box-sizing: border-box;
        }

        /* 状态栏样式保持不变 */
        .status-bar {
            height: 24px;
            background: #292d3e;
            color: #9da5b4;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            box-shadow: 0 -1px 0 #1e222d inset;
            z-index: 100;
            position: relative;
        }

        /* 主容器改为水平布局 */
        .editor-container {
            display: flex;
            flex-direction: row; /* 改为水平布局 */
            height: 100vh;
        }

        /* 左侧容器样式 */
        .left-panel {
            display: flex;
            flex-direction: column;
            flex: 1 1 50%;
            height: 100vh;
            overflow: hidden;
        }

        /* 状态栏样式保持不变 */
        .status-bar {
            height: 24px;
            background: #292d3e;
            color: #9da5b4;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            box-shadow: 0 -1px 0 #1e222d inset;
            z-index: 100;
            position: relative;
        }

        /* CodeMirror 容器样式 */
        .CodeMirror {
            flex: 1;
            border: none;
            height: calc(100vh - 24px);
            overflow: hidden;
        }

        /* 新增右侧面板容器样式 */
        .right-panel {
            flex: 1 1 50%;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
        }

        /* 标题栏样式 */
        .json-title-bar {
            height: 24px;
            background: #f5f5f5;
            color: #333;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 0 10px;
            box-shadow: 0 1px 0 #e0e0e0 inset;
            z-index: 99;
            position: relative;
            border-bottom: 1px solid #e0e0e0;
        }

        /* JSON展示区域样式 */
        .json-preview {
            flex: 1;
            padding: 10px;
            background-color: white;
            color: #000000;
            overflow-y: auto;
            box-sizing: border-box;
            font-size: 14px;
        }

        /* 语法高亮容器 */
        .json-highlight {
            background-color: #f8f8f8 !important;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
        }

        /* 语法高亮样式覆盖 */
        code .token.property {
            color: #111111;
            font-weight: bold;
        }

        code .token.string {
            color: #22863a;
        }

        code .token.number {
            color: #005cc5;
        }

        code .token.boolean {
            color: #d73a49;
        }

        code .token.punctuation {
            color: #555555;
        }
    </style>
</head>
<body>
<div class="editor-container">
    <!-- 左侧面板（包含状态栏和编辑器） -->
    <div class="left-panel">
        <!-- 状态栏 -->
        <div class="status-bar">
        </div>

        <!-- CodeMirror 编辑器容器 -->
        <div id="code-editor"></div>
    </div>

    <!-- 右侧面板（包含标题栏和JSON展示） -->
    <div class="right-panel">
        <div class="json-title-bar">
            JSON
        </div>
        <div id="json-preview" class="json-preview">
            <pre class="json-highlight"><code class="language-json"></code></pre>
        </div>
    </div>
</div>

<!-- CodeMirror 核心库 -->
<script src="codemirror.js"></script>

<!-- JavaScript 语法模式 -->
<script src="javascript-style.js"></script>

<!-- 代码折叠插件 -->
<script src="foldcode.js"></script>
<script src="foldgutter.js"></script>
<script src="bracefold.js"></script>

<!-- 添加语法高亮依赖 -->
<link rel="stylesheet" href="prismjs.css">
<script src="prismjs.js"></script>
<script src="prismjs_prism-json.js"></script>

<script>
    // 从URL参数获取脚本内容
    function getURLParameter(name) {
        const params = new URLSearchParams(window.location.search);
        return params.has(name) ? params.get(name) : null;
    }

    // 获取并解码URL参数中的脚本内容
    const encodedScript = getURLParameter("scriptContent");
    const decodedScript = encodedScript !== null && encodedScript.trim() !== ""
        ? decodeURIComponent(encodedScript)
        : "function transform(dataItem) {\n    // Example: Convert input data item field from 'oldField' to 'newField'\n    // var transformedItem = {\n    //     newField: dataItem.oldField,\n    //     //......\n    // };\n    // Add conversion code here\n}";
    // 初始化 CodeMirror 编辑器
    const editor = CodeMirror(document.getElementById("code-editor"), {
        value: decodedScript,
        mode: "javascript",
        theme: "dracula",
        lineNumbers: true,
        styleActiveLineGutter: true,
        foldGutter: true,
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
        autorefresh: true,
        extraKeys: {
            "Ctrl-Enter": function (cm) {
                cm.execCommand("foldAll");
            },
            "Shift-Ctrl-Enter": function (cm) {
                cm.execCommand("unfoldAll");
            }
        }
    });

    // 提供给父窗口调用的获取内容方法
    window.getEditorContent = function () {
        return editor.getValue();
    };

    // 添加自动刷新功能
    window.addEventListener("resize", function () {
        if (editor) {
            editor.refresh();
        }
    });

    // 异步获取并处理JSON数据
    async function fetchAndRenderJSON() {
        try {
            const modelId = getURLParameter("dataModelId");
            if (!modelId) {
                throw new Error("Missing modelID");
            }

            // 第一步：获取模型定义
            const getModelResponse = await fetch(`/api/runtime/csb/v1.0/InterfaceServiceModel/get/${modelId}`);
            if (!getModelResponse.ok) {
                throw new Error(`Failed to obtain model: ${getModelResponse.statusText}`);
            }
            const modelData = await getModelResponse.json();

            // 第二步：生成JSON数据
            const generateDataResponse = await fetch(
                "/api/runtime/csb/v1.0/InterfaceServiceModel/generateJsonDataBySchema",
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: JSON.stringify(modelData)
                }
            );

            if (!generateDataResponse.ok) {
                throw new Error(`Failed to generate JSON: ${generateDataResponse.statusText}`);
            }

            // 返回生成的数据
            return await generateDataResponse.json();

        } catch (error) {
            console.error("Data loading exception:", error);
            return {
                error: error.message,
                detail: "Data loading failed"
            };
        }
    }

    // 页面加载时执行
    window.addEventListener('DOMContentLoaded', async function () {
        const jsonPreview = document.querySelector('#json-preview code');

        // 显示加载状态
        jsonPreview.textContent = "Loading...";

        // 获取并渲染数据
        const result = await fetchAndRenderJSON();

        // 格式化并渲染JSON
        jsonPreview.textContent = JSON.stringify(result, null, 2);
        Prism.highlightElement(jsonPreview);

        // 强制初始化CodeMirror尺寸
        setTimeout(() => editor.refresh(), 100);
    });
</script>
</body>
</html>