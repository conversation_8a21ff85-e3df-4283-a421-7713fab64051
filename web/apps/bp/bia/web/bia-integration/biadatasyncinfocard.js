/**
 * @description: 数据同步明细表单JS脚本
 * <AUTHOR>
 * @date 2025/7/15 10:06
 * @version 1.0
 */
const system = idp.utils.getQuery(`system`);
var isEdit = false;
idp.event.bind("viewReady", function () {
    //增量标识集触发
    document
        .getElementById("INCREMENTALCODE")
        .addEventListener("click", function () {
            if (!isEdit) return;
            return openBPBIADATAINCREMENTAL();
        });
    //步骤前拓展触发数据抽取
    document
        .getElementById("STEPAHEADEXPANSION")
        .addEventListener("click", function () {
            if (!isEdit) return;
            return openBPBIADATASTEPEXPANSION("0", "0");
        });
    //步骤后拓展触发数据抽取
    document
        .getElementById("DESTEPBYSTEPEXPANSION")
        .addEventListener("click", function () {
            if (!isEdit) return;
            return openBPBIADATASTEPEXPANSION("0", "1");
        });
    //步骤前拓展触发数据转换
    document
        .getElementById("DTSTEPAHEADEXPANSION")
        .addEventListener("click", function () {
            if (!isEdit) return;
            return openBPBIADATASTEPEXPANSION("1", "0");
        });
    //步骤后拓展触发数据转换
    document
        .getElementById("DTSTEPBYSTEPEXPANSION")
        .addEventListener("click", function () {
            if (!isEdit) return;
            return openBPBIADATASTEPEXPANSION("1", "1");
        });
    //步骤前拓展触发数据转换
    document
        .getElementById("DPSTEPAHEADEXPANSION")
        .addEventListener("click", function () {
            if (!isEdit) return;
            return openBPBIADATASTEPEXPANSION("2", "0");
        });
    //步骤后拓展触发数据转换
    document
        .getElementById("DPSTEPBYSTEPEXPANSION")
        .addEventListener("click", function () {
            if (!isEdit) return;
            return openBPBIADATASTEPEXPANSION("2", "1");
        });
    //JS脚本触发
    document.getElementById("JS").addEventListener("click", function () {
        if (!isEdit) return;
        return openJSEditor();
    });
});

idp.event.bind("domReady", function () {
    idp.event.register("DATATYPE", "selected", function (e, filters) {
        var Data = idp.uiview.modelController.deafaultData[0].data[0];
        var lx = idp.control.get("DATATYPE").getValue();
        if (lx === "0") {
            $("#EXTRACTIONTYPE").parents(".table-item").show();
            $("#DATAFORMAT").parents(".table-item").show();
            $("#DATAMODELNAME").parents(".table-item").show();
            $("#UNIQUEIDENTIFIER").parents(".table-item").show();
            $("#TIMESTAMPFIELD").parents(".table-item").show();
            $("#INCREMENTALCODE").parents(".table-item").show();
            var EXTRACTIONTYPE = Data.EXTRACTIONTYPE;
            if (EXTRACTIONTYPE === "0") {
                $("#TIMESTAMPFIELD").parents(".table-item").hide();
                $("#INCREMENTALCODE").parents(".table-item").hide();
            } else if (EXTRACTIONTYPE === "1") {
                $("#TIMESTAMPFIELD").parents(".table-item").show();
                $("#INCREMENTALCODE").parents(".table-item").hide();
            } else if (EXTRACTIONTYPE === "2") {
                $("#TIMESTAMPFIELD").parents(".table-item").hide();
                $("#INCREMENTALCODE").parents(".table-item").show();
            }
        } else {
            $("#EXTRACTIONTYPE").parents(".table-item").hide();
            $("#DATAFORMAT").parents(".table-item").hide();
            $("#DATAMODELNAME").parents(".table-item").hide();
            $("#UNIQUEIDENTIFIER").parents(".table-item").hide();
            $("#TIMESTAMPFIELD").parents(".table-item").hide();
            $("#INCREMENTALCODE").parents(".table-item").hide();
        }
    });
    idp.event.register("EXTRACTIONTYPE", "selected", function (e, filters) {
        var lx = idp.control.get("EXTRACTIONTYPE").getValue();
        if (lx === "0") {
            $("#TIMESTAMPFIELD").parents(".table-item").hide();
            $("#INCREMENTALCODE").parents(".table-item").hide();
        } else if (lx === "1") {
            $("#TIMESTAMPFIELD").parents(".table-item").show();
            $("#INCREMENTALCODE").parents(".table-item").hide();
        } else if (lx === "2") {
            $("#TIMESTAMPFIELD").parents(".table-item").hide();
            $("#INCREMENTALCODE").parents(".table-item").show();
        }
    });
    idp.event.register("DATAFORMAT", "selected", function (e, filters) {
        var lx = idp.control.get("DATAFORMAT").getValue();
        if (lx === "0") {
            $("#TRANSFERMETHOD").parents(".table-item").show();
            $("#MIDTABLE").parents(".table-item").show();
        } else if (lx === "1") {
            $("#TRANSFERMETHOD").parents(".table-item").hide();
            $("#MIDTABLE").parents(".table-item").hide();
        }
    });
    idp.event.register("TRANSFERMETHOD", "selected", function (e, filters) {
        var lx = idp.control.get("TRANSFERMETHOD").getValue();
        if (lx === "0") {
            $("#MIDTABLE").parents(".table-item").hide();
        } else if (lx === "1") {
            $("#MIDTABLE").parents(".table-item").show();
        } else if (lx === "2") {
            $("#MIDTABLE").parents(".table-item").hide();
        }
    });
    idp.event.register("TRANSFORMATION", "selected", function (e, filters) {
        var lx = idp.control.get("TRANSFORMATION").getValue();
        if (lx === "0") {
            $("#CONVERSIONRULESNAME").parents(".table-item").show();
            $("#TRANSCODING").parents(".table-item").hide();
            $("#JS").parents(".table-item").hide();
        } else if (lx === "1") {
            $("#CONVERSIONRULESNAME").parents(".table-item").hide();
            $("#TRANSCODING").parents(".table-item").hide();
            $("#JS").parents(".table-item").show();
        } else if (lx === "2") {
            $("#CONVERSIONRULESNAME").parents(".table-item").hide();
            $("#TRANSCODING").parents(".table-item").show();
            $("#JS").parents(".table-item").hide();
        } else if (lx === "3") {
            $("#CONVERSIONRULESNAME").parents(".table-item").show();
            $("#TRANSCODING").parents(".table-item").show();
            $("#JS").parents(".table-item").show();
        }
    });
    idp.event.register("PUSHMETHOD", "selected", function (e, filters) {
        var lx = idp.control.get("PUSHMETHOD").getValue();
        var isBatchPush = idp.control.get("ISBATCHPUSH").getValue();
        var returnParseType = idp.control.get("RETURNPARSETYPE").getValue();
        if (lx === "0") {
            $("#EXTERNALSERVICES").parents(".table-item").show();
            $("#ENCODINGCLASS").parents(".table-item").hide();
            $("#RETURNPARSETYPE").parents(".table-item").show();
            if (returnParseType === "0") {
                $("#RETURNPARSE").parents(".table-item").hide();
                $("#RETURNSTATUS").parents(".table-item").show();
                $("#RETURNMESSAGE").parents(".table-item").show();
                if (isBatchPush === "0") {
                    $("#RETURNUNIQUE").parents(".table-item").hide();
                } else if (isBatchPush === "1") {
                    $("#RETURNUNIQUE").parents(".table-item").show();
                }
            } else if (returnParseType === "1") {
                $("#RETURNPARSE").parents(".table-item").show();
                $("#RETURNSTATUS").parents(".table-item").hide();
                $("#RETURNMESSAGE").parents(".table-item").hide();
                $("#RETURNUNIQUE").parents(".table-item").hide();
            }
        } else if (lx === "1") {
            $("#EXTERNALSERVICES").parents(".table-item").hide();
            $("#ENCODINGCLASS").parents(".table-item").show();
            $("#RETURNPARSETYPE").parents(".table-item").hide();
            $("#RETURNPARSE").parents(".table-item").hide();
            $("#RETURNSTATUS").parents(".table-item").hide();
            $("#RETURNMESSAGE").parents(".table-item").hide();
            $("#RETURNUNIQUE").parents(".table-item").hide();
        }
    });
    idp.event.register("ISBATCHPUSH", "selected", function (e, filters) {
        var isBatchPush = idp.control.get("ISBATCHPUSH").getValue();
        var pushMethod = idp.control.get("PUSHMETHOD").getValue();
        var returnParseType = idp.control.get("RETURNPARSETYPE").getValue();
        if (isBatchPush === "0") {
            $("#RETURNUNIQUE").parents(".table-item").hide();
        } else if (
            isBatchPush === "1" &&
            pushMethod === "0" &&
            returnParseType === "0"
        ) {
            $("#RETURNUNIQUE").parents(".table-item").show();
        }
    });
    idp.event.register("RETURNPARSETYPE", "selected", function (e, filters) {
        var returnParseType = idp.control.get("RETURNPARSETYPE").getValue();
        var isBatchPush = idp.control.get("ISBATCHPUSH").getValue();
        if (returnParseType === "0") {
            $("#RETURNPARSE").parents(".table-item").hide();
            $("#RETURNSTATUS").parents(".table-item").show();
            $("#RETURNMESSAGE").parents(".table-item").show();
            if (isBatchPush === "0") {
                $("#RETURNUNIQUE").parents(".table-item").hide();
            } else if (isBatchPush === "1") {
                $("#RETURNUNIQUE").parents(".table-item").show();
            }
        } else if (returnParseType === "1") {
            $("#RETURNPARSE").parents(".table-item").show();
            $("#RETURNSTATUS").parents(".table-item").hide();
            $("#RETURNMESSAGE").parents(".table-item").hide();
            $("#RETURNUNIQUE").parents(".table-item").hide();
        }
    });
    idp.event.register("VERIFYTYPE", "selected", function (e, filters) {
        var lx = idp.control.get("VERIFYTYPE").getValue();
        if (lx === "0") {
            $("#VERIFYCONFIGURATION").parents(".table-item").hide();
        } else {
            $("#VERIFYCONFIGURATION").parents(".table-item").show();
        }
    });
});
idp.event.bind("loadData", function () {
    var data = idp.uiview.modelController.deafaultData[0].data[0];
    if (data.EXTRACTIONTYPE === "0") {
        $("#TIMESTAMPFIELD").parents(".table-item").hide();
        $("#INCREMENTALCODE").parents(".table-item").hide();
    } else if (data.EXTRACTIONTYPE === "1") {
        $("#TIMESTAMPFIELD").parents(".table-item").show();
        $("#INCREMENTALCODE").parents(".table-item").hide();
    } else if (data.EXTRACTIONTYPE === "2") {
        $("#TIMESTAMPFIELD").parents(".table-item").hide();
        $("#INCREMENTALCODE").parents(".table-item").show();
    }
    if (data.TRANSFORMATION === "0") {
        $("#CONVERSIONRULESNAME").parents(".table-item").show();
        $("#TRANSCODING").parents(".table-item").hide();
        $("#JS").parents(".table-item").hide();
    } else if (data.TRANSFORMATION === "1") {
        $("#CONVERSIONRULESNAME").parents(".table-item").hide();
        $("#TRANSCODING").parents(".table-item").hide();
        $("#JS").parents(".table-item").show();
    } else if (data.TRANSFORMATION === "2") {
        $("#CONVERSIONRULESNAME").parents(".table-item").hide();
        $("#TRANSCODING").parents(".table-item").show();
        $("#JS").parents(".table-item").hide();
    } else if (data.TRANSFORMATION === "3") {
        $("#CONVERSIONRULESNAME").parents(".table-item").show();
        $("#TRANSCODING").parents(".table-item").show();
        $("#JS").parents(".table-item").show();
    }
    if (data.PUSHMETHOD === "0") {
        $("#EXTERNALSERVICES").parents(".table-item").show();
        $("#ENCODINGCLASS").parents(".table-item").hide();
        $("#RETURNPARSETYPE").parents(".table-item").show();
        if (data.RETURNPARSETYPE === "0") {
            $("#RETURNPARSE").parents(".table-item").hide();
            $("#RETURNSTATUS").parents(".table-item").show();
            $("#RETURNMESSAGE").parents(".table-item").show();
            if (data.ISBATCHPUSH === "0") {
                $("#RETURNUNIQUE").parents(".table-item").hide();
            } else if (data.ISBATCHPUSH === "1") {
                $("#RETURNUNIQUE").parents(".table-item").show();
            }
        } else if (data.RETURNPARSETYPE === "1") {
            $("#RETURNPARSE").parents(".table-item").show();
            $("#RETURNSTATUS").parents(".table-item").hide();
            $("#RETURNMESSAGE").parents(".table-item").hide();
            $("#RETURNUNIQUE").parents(".table-item").hide();
        }
    } else if (data.PUSHMETHOD === "1") {
        $("#EXTERNALSERVICES").parents(".table-item").hide();
        $("#ENCODINGCLASS").parents(".table-item").show();
        $("#RETURNPARSETYPE").parents(".table-item").hide();
        $("#RETURNPARSE").parents(".table-item").hide();
        $("#RETURNSTATUS").parents(".table-item").hide();
        $("#RETURNMESSAGE").parents(".table-item").hide();
        $("#RETURNUNIQUE").parents(".table-item").hide();
    }
});
//编辑后
idp.event.bind("afterEdit", function () {
    isEdit = true;
});
//取消后
idp.event.bind("afterCancel", function () {
    isEdit = false;
});
// 保存前
idp.event.bind("beforeSave", function (e, Data) {
    if (system) {
        idp.uiview.modelController.setValue("PARENTID", system);
    }
});
//保存后
idp.event.bind("afterSave", function (e, data) {
    createRecordTable(data);
});
//保存前校验
idp.event.bind("beforeCheck", function (e, Data) {
    var DATAACQUISITIONMETHOD = Data[0].data[0].DATAACQUISITIONMETHOD; //数据获取方式TRANSFORMATION
    var INTEGRATIONSERVICEID = Data[0].data[0].INTEGRATIONSERVICEID; //集成服务id
    var DATATYPE = Data[0].data[0].DATATYPE; //数据类型
    var EXTRACTIONTYPE = Data[0].data[0].EXTRACTIONTYPE; //取数类型
    var DATAFORMAT = Data[0].data[0].DATAFORMAT; //数据形式
    var DATAMODELNAME = Data[0].data[0].DATAMODELNAME; //数据模型
    var UNIQUEIDENTIFIER = Data[0].data[0].UNIQUEIDENTIFIER; //唯一标识字段
    var TIMESTAMPFIELD = Data[0].data[0].TIMESTAMPFIELD; //时间戳字段
    var INCREMENTALCODE = Data[0].data[0].INCREMENTALCODE; //增量标识集
    var TRANSFERMETHOD = Data[0].data[0].TRANSFERMETHOD; //中转方式
    var MIDTABLE = Data[0].data[0].MIDTABLE; //中间表
    var RECORDTABLE = Data[0].data[0].RECORDTABLE; //记录表
    var TRANSFORMATION = Data[0].data[0].TRANSFORMATION; //转换方式
    var CONVERSIONRULESNAME = Data[0].data[0].CONVERSIONRULESNAME; //转换规则
    var TRANSCODING = Data[0].data[0].TRANSCODING; //编码转换
    var JS = Data[0].data[0].JS; //脚本
    var PUSHMETHOD = Data[0].data[0].PUSHMETHOD; //推送方式
    var ISBATCHPUSH = Data[0].data[0].ISBATCHPUSH; //是否批量推送
    var EXTERNALSERVICES = Data[0].data[0].EXTERNALSERVICES; //外部服务
    var RETURNPARSETYPE = Data[0].data[0].RETURNPARSETYPE; //是否批量推送
    var RETURNPARSE = Data[0].data[0].RETURNPARSE; //返回值解析
    var RETURNSTATUS = Data[0].data[0].RETURNSTATUS; //返回状态
    var RETURNMESSAGE = Data[0].data[0].RETURNMESSAGE; //返回信息
    var RETURNUNIQUE = Data[0].data[0].RETURNUNIQUE; //返回唯一标识
    var ENCODINGCLASS = Data[0].data[0].ENCODINGCLASS; //编码实现类
    if (DATATYPE === "0" && DATAMODELNAME === "") {
        //业务数据
        idp.warn(idp.lang.get("DataSyncCard_01"));
        return false;
    }
    if (DATATYPE === "0" && UNIQUEIDENTIFIER === "") {
        //业务数据
        idp.warn(idp.lang.get("DataSyncCard_02"));
        return false;
    }
    if (EXTRACTIONTYPE === "1" && TIMESTAMPFIELD === "") {
        //取数类型
        idp.warn(idp.lang.get("DataSyncCard_03"));
        return false;
    }
    if (EXTRACTIONTYPE === "2" && INCREMENTALCODE === "") {
        //取数类型
        idp.warn(idp.lang.get("DataSyncCard_04"));
        return false;
    }
    if (TRANSFERMETHOD === "2" && MIDTABLE === "") {
        //中转方式
        idp.warn(idp.lang.get("DataSyncCard_05"));
        return false;
    }
    if (TRANSFORMATION === "0" && CONVERSIONRULESNAME === "") {
        //转换方式
        idp.warn(idp.lang.get("DataSyncCard_06"));
        return false;
    }
    if (TRANSFORMATION === "2" && TRANSCODING === "") {
        //转换方式
        idp.warn(idp.lang.get("DataSyncCard_07"));
        return false;
    }
    if (TRANSFORMATION === "1" && JS === "") {
        //转换方式
        idp.warn(idp.lang.get("DataSyncCard_08"));
        return false;
    }
    if (TRANSFORMATION === "3") {
        // 统计非空字段的数量
        var nonEmptyCount = 0;
        if (CONVERSIONRULESNAME !== "") nonEmptyCount++;
        if (TRANSCODING !== "") nonEmptyCount++;
        if (JS !== "") nonEmptyCount++;
        // 检查是否至少有两个非空字段
        if (nonEmptyCount < 2) {
            idp.warn(idp.lang.get('DataSyncCard_09'));
            return false;
        }
    }
    if (PUSHMETHOD === "0" && EXTERNALSERVICES === "") {
        //推送方式
        idp.warn(idp.lang.get('DataSyncCard_10'));
        return false;
    }
    if (PUSHMETHOD === "0" && RETURNPARSETYPE === "") {
        idp.warn(idp.lang.get('DataSyncCard_11'));
        return false;
    }
    if (PUSHMETHOD === "0" && RETURNPARSETYPE === "0" && RETURNSTATUS === "") {
        idp.warn(idp.lang.get('DataSyncCard_12'));
        return false;
    }
    if (PUSHMETHOD === "0" && RETURNPARSETYPE === "0" && RETURNMESSAGE === "") {
        idp.warn(idp.lang.get('DataSyncCard_13'));
        return false;
    }
    if (
        PUSHMETHOD === "0" &&
        RETURNPARSETYPE === "0" &&
        ISBATCHPUSH === "1" &&
        RETURNUNIQUE === ""
    ) {
        idp.warn(idp.lang.get('DataSyncCard_14'));
        return false;
    }
    if (PUSHMETHOD === "0" && RETURNPARSETYPE === "1" && RETURNPARSE === "") {
        idp.warn(idp.lang.get('DataSyncCard_15'));
        return false;
    }
    if (PUSHMETHOD === "1" && ENCODINGCLASS === "") {
        //推送方式
        idp.warn(idp.lang.get('DataSyncCard_16'));
        return false;
    }
});
//保存后
idp.event.bind("afterSave", function (e, data) {
    isEdit = false;
});

//打开增量标识集
function openBPBIADATAINCREMENTAL() {
    var zbid = idp.uiview.modelController.deafaultData[0].data[0].ID;
    var opts = {
        title: idp.lang.get('DataSyncCard_19'),
        name: `zlbsj`,
        isHidden: false,
        showMax: false,
        width: 600,
        slide: false,
        height: 400,
        url:
            "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=e5e4c810-8da9-60b0-3be6-35f6e68b76b4&dataid=&status=add&j=true&zbid=" +
            zbid +
            "",
        // buttons: [
        //   {
        //     text: "关闭",
        //     cls: "lee-btn-primary lee-dialog-btn-no",
        //     onclick: function (item, dialog) {
        //       var data = document
        //         .getElementById("zlbsj")
        //         .contentWindow.idp.control.get("grid_main").rows;
        //       idp.control.get("INCREMENTALCODE").setValue("");
        //       if (data.length > 0) {
        //         if (data[0].ISENABLED == "1") {
        //           const codeString = data.map((item) => item.CODE).join(";");
        //           idp.control.get("INCREMENTALCODE").setValue(codeString);
        //         }
        //       }
        //       dialog.close();
        //     },
        //   },
        // ],
        onClose: function () {
            var data = document
                .getElementById("zlbsj")
                .contentWindow.idp.control.get("grid_main").rows;
            idp.control.get("INCREMENTALCODE").setValue("");
            if (data.length > 0) {
                if (data[0].ISENABLED === "1") {
                    const codeString = data.map((item) => item.CODE).join(";");
                    idp.control.get("INCREMENTALCODE").setValue(codeString);
                }
            }
        },
    };
    var dg = $.leeDialog.open(opts).max();
}

function openBPBIADATASTEPEXPANSION(datatype, isstep) {
    var zbid = idp.uiview.modelController.deafaultData[0].data[0].ID;
    var title = "";
    if (isstep === "0") {
        title = idp.lang.get('DataSyncCard_20');
    } else {
        title = idp.lang.get('DataSyncCard_21');
    }
    var opts = {
        title: title,
        name: `bztz`,
        isHidden: false,
        showMax: false,
        width: 600,
        slide: false,
        height: 400,
        url:
            "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=eee29910-2d78-e45c-16b9-d09f2c73d808&dataid=&status=add&j=true&zbid=" +
            zbid +
            "&datatype=" +
            datatype +
            "&isstep=" +
            isstep +
            "",
        // buttons: [
        //   {
        //     text: "关闭",
        //     cls: "lee-btn-primary lee-dialog-btn-no",
        //     onclick: function (item, dialog) {
        //       //清空步骤文本框
        //       cleanStep(datatype, isstep);
        //       var data = document
        //         .getElementById("bztz")
        //         .contentWindow.idp.control.get("grid_main").rows;
        //       if (data.length > 0) {
        //         if (data[0].ISENABLED == "1") {
        //           const codeString = data
        //             .map((item) => item.STEPEXPANSION)
        //             .join(";");
        //           if (datatype == "0" && isstep == "0") {
        //             idp.control.get("STEPAHEADEXPANSION").setValue(codeString);
        //           } else if (datatype == "0" && isstep == "1") {
        //             idp.control.get("DESTEPBYSTEPEXPANSION").setValue(codeString);
        //           } else if (datatype == "1" && isstep == "0") {
        //             idp.control.get("DTSTEPAHEADEXPANSION").setValue(codeString);
        //           } else if (datatype == "1" && isstep == "1") {
        //             idp.control.get("DTSTEPBYSTEPEXPANSION").setValue(codeString);
        //           } else if (datatype == "2" && isstep == "0") {
        //             idp.control.get("DPSTEPAHEADEXPANSION").setValue(codeString);
        //           } else if (datatype == "2" && isstep == "1") {
        //             idp.control.get("DPSTEPBYSTEPEXPANSION").setValue(codeString);
        //           }
        //         }
        //       }
        //       dialog.close();
        //     },
        //   },
        // ],
        onClose: function () {
            //清空步骤文本框
            cleanStep(datatype, isstep);
            var data = document
                .getElementById("bztz")
                .contentWindow.idp.control.get("grid_main").rows;
            if (data.length > 0) {
                if (data[0].ISENABLED === "1") {
                    const codeString = data.map((item) => item.STEPEXPANSION).join(";");
                    if (datatype === "0" && isstep === "0") {
                        idp.control.get("STEPAHEADEXPANSION").setValue(codeString);
                    } else if (datatype === "0" && isstep === "1") {
                        idp.control.get("DESTEPBYSTEPEXPANSION").setValue(codeString);
                    } else if (datatype === "1" && isstep === "0") {
                        idp.control.get("DTSTEPAHEADEXPANSION").setValue(codeString);
                    } else if (datatype === "1" && isstep === "1") {
                        idp.control.get("DTSTEPBYSTEPEXPANSION").setValue(codeString);
                    } else if (datatype === "2" && isstep === "0") {
                        idp.control.get("DPSTEPAHEADEXPANSION").setValue(codeString);
                    } else if (datatype === "2" && isstep === "1") {
                        idp.control.get("DPSTEPBYSTEPEXPANSION").setValue(codeString);
                    }
                }
            }
        },
    };
    var dg = $.leeDialog.open(opts).max();
}

function cleanStep(datatype, isstep) {
    if (datatype === "0" && isstep === "0") {
        idp.control.get("STEPAHEADEXPANSION").setValue("");
    } else if (datatype === "0" && isstep === "1") {
        idp.control.get("DESTEPBYSTEPEXPANSION").setValue("");
    } else if (datatype === "1" && isstep === "0") {
        idp.control.get("DTSTEPAHEADEXPANSION").setValue("");
    } else if (datatype === "1" && isstep === "1") {
        idp.control.get("DTSTEPBYSTEPEXPANSION").setValue("");
    } else if (datatype === "2" && isstep === "0") {
        idp.control.get("DPSTEPAHEADEXPANSION").setValue("");
    } else if (datatype === "2" && isstep === "1") {
        idp.control.get("DPSTEPBYSTEPEXPANSION").setValue("");
    }
}

function openJSEditor() {
    // 获取当前JS字段值
    var currentScript = idp.control.get("JS").getValue();
    // 获取当前数据模型ID
    var dataModelId = idp.uiview.modelController.getMainRowObj().DATAMODEL;

    // 创建临时iframe容器
    var opts = {
        title: idp.lang.get('DataSyncCard_22'),
        name: "jsEditor",
        isHidden: false,
        showMax: true,
        width: 600,
        height: 400,
        url:
            "/apps/bp/bia/web/bia-integration/javascript-editor.html?scriptContent=" +
            (currentScript ? encodeURIComponent(currentScript) : "") +
            "&dataModelId=" +
            (dataModelId ? encodeURIComponent(dataModelId) : ""),
        buttons: [
            {
                text: idp.lang.get('DataSyncCard_23'),
                cls: "lee-btn-primary lee-dialog-btn-ok",
                onclick: function (item, dialog) {
                    // 从弹窗iframe获取编辑后的内容
                    var newScript = document
                        .getElementById("jsEditor")
                        .contentWindow.getEditorContent();

                    // 保存回JS字段
                    if (newScript !== undefined) {
                        idp.control.get("JS").setValue(newScript);
                    }

                    dialog.close();
                },
            },
            {
                text: idp.lang.get('DataSyncCard_24'),
                cls: "lee-btn-primary lee-dialog-btn-no",
                onclick: function (item, dialog) {
                    dialog.close();
                },
            },
        ],
    };

    $.leeDialog.open(opts).max();
}

function createRecordTable(data) {
    var recorcTableName = data[0].data[0].RECORDTABLE;
    var url = "/api/bp/bia/v1.0/dataIntegration/createRecordTable";
    // 构造符合要求的 param 参数
    var param = {
        recordTableName: recorcTableName, // 使用 记录表名 作为入参
    };
    idp.service
        .fetch(url, param, true, "POST")
        .then((res) => {
            if (res) {
                if (!res.success) {
                    // 失败逻辑：显示后端返回的错误信息
                    idp.warn(res.message || idp.lang.get('DataSyncCard_17'));
                }
            } else {
                idp.warn(idp.lang.get('DataSyncCard_18'));
            }
        })
        .fail((err) => {
            idp.warn(`ERROR_STATUS${err.status}，ERROR_STATUSTEXT${err.statusText}`);
        });
}
