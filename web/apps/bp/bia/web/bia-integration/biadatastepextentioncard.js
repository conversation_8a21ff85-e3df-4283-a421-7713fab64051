/**
 * @description: 步骤前后扩展表单JS脚本
 * <AUTHOR>
 * @date 2025/7/15 10:16
 * @version 1.0
 */
const zbid = idp.utils.getQuery(`zbid`);
const datatype = idp.utils.getQuery(`datatype`);
const isstep = idp.utils.getQuery(`isstep`);
idp.event.bind("domReady", function() {

    idp.event.register('grid_main', 'beforeGridFilter', function(e, filters) {
        if (filters.length > 0) {
            filters[filters.length - 1].Logic = " and "; //如果有其他默认条件 需要处理拼接逻辑
        }
        if (zbid !== "") {
            filters.push({
                "Left": "",
                "Field": "PARENTID",
                "Operate": "=",
                "Value": zbid,
                "Right": "",
                "Logic": "and"
            });
            filters.push({
                "Left": "",
                "Field": "DATATYPE",
                "Operate": "=",
                "Value": datatype,
                "Right": "",
                "Logic": "and"
            });
            filters.push({
                "Left": "",
                "Field": "ISSTEPBYSTEP",
                "Operate": "=",
                "Value": isstep,
                "Right": "",
                "Logic": ""
            });

        }
        return filters;
    });

    idp.event.bind("beforeSave", function(e, Data) {
        if (zbid !== "") {
            var data = idp.control.get("grid_main").rows;
            if (data.length > 0) {
                for (var i = 0; i < data.length; i++) {
                    idp.control.get("grid_main").updateCell('PARENTID', zbid, i);
                    idp.control.get("grid_main").updateCell('ISSTEPBYSTEP', isstep, i);
                    idp.control.get("grid_main").updateCell('DATATYPE', datatype, i);
                    idp.control.get("grid_main").updateCell('ISENABLED', '1', i);
                }

            }
        }
    });
});

function CloseDialog() {
    var diaLogClose = window.parent.document.getElementsByClassName('lee-icon lee-dialog-winbtn lee-dialog-close');
    if (diaLogClose && diaLogClose.length > 0) {
        diaLogClose[0].click();
        return true;
    }
    return idp.uiview.close();
}