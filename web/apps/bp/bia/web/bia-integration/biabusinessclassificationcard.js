/**
 * @description: 业务分类表单JS脚本
 * <AUTHOR>
 * @date 2025/7/15 10:13
 * @version 1.0
 */
function AddBSTYPE() {
    $("#BUSINESSMODULECODE").parents(".table-item").show();
    $("#BUSINESSMODULENAME").parents(".table-item").show();
    $("#BUSINEESSCODE").parents(".table-item").hide();
    $("#BUSINEESSNAME").parents(".table-item").hide();
    $("#SYSTEMCODE").parents(".table-item").hide();
    $("#SYSTEMNAME").parents(".table-item").hide();
    return idp.uiview.addSame();
}

function addDown() {
    var data = idp.control.get("grid_main").getSelected();
    if (!data) {
        idp.warn(idp.lang.get('BusinessClassification_01'));
        return false;
    }
    if (data.ISDETAIL !== "0") {
        idp.warn(idp.lang.get('BusinessClassification_02'));
        return false;
    }
    $("#BUSINESSMODULECODE").parents(".table-item").hide();
    $("#BUSINESSMODULENAME").parents(".table-item").hide();
    $("#BUSINEESSCODE").parents(".table-item").show();
    $("#BUSINEESSNAME").parents(".table-item").show();
    $("#SYSTEMCODE").parents(".table-item").hide();
    $("#SYSTEMNAME").parents(".table-item").hide();
    return idp.uiview.addDown();
}

function addSystem() {
    var data = idp.control.get("grid_main").getSelected();
    if (!data) {
        idp.warn(idp.lang.get('BusinessClassification_03'));
        return false;
    }
    if (data.ISDETAIL !== "1") {
        idp.warn(idp.lang.get('BusinessClassification_04'));
        return false;
    }
    $("#BUSINESSMODULECODE").parents(".table-item").hide();
    $("#BUSINESSMODULENAME").parents(".table-item").hide();
    $("#BUSINEESSCODE").parents(".table-item").hide();
    $("#BUSINEESSNAME").parents(".table-item").hide();
    $("#SYSTEMCODE").parents(".table-item").show();
    $("#SYSTEMNAME").parents(".table-item").show();
    return idp.uiview.addDown();
}

function Edit() {
    var data = idp.control.get("grid_main").getSelected();
    if (!data) {
        idp.warn(idp.lang.get('BusinessClassification_05'));
        return false;
    }
    if (data.ISDETAIL === "0") {
        $("#BUSINESSMODULECODE").parents(".table-item").show();
        $("#BUSINESSMODULENAME").parents(".table-item").show();
        $("#BUSINEESSCODE").parents(".table-item").hide();
        $("#BUSINEESSNAME").parents(".table-item").hide();
        $("#SYSTEMCODE").parents(".table-item").hide();
        $("#SYSTEMNAME").parents(".table-item").hide();
    } else if (data.ISDETAIL === "1") {
        $("#BUSINESSMODULECODE").parents(".table-item").hide();
        $("#BUSINESSMODULENAME").parents(".table-item").hide();
        $("#BUSINEESSCODE").parents(".table-item").show();
        $("#BUSINEESSNAME").parents(".table-item").show();
        $("#SYSTEMCODE").parents(".table-item").hide();
        $("#SYSTEMNAME").parents(".table-item").hide();
    } else if (data.ISDETAIL === "2") {
        $("#BUSINESSMODULECODE").parents(".table-item").hide();
        $("#BUSINESSMODULENAME").parents(".table-item").hide();
        $("#BUSINEESSCODE").parents(".table-item").hide();
        $("#BUSINEESSNAME").parents(".table-item").hide();
        $("#SYSTEMCODE").parents(".table-item").show();
        $("#SYSTEMNAME").parents(".table-item").show();
    }
    return idp.uiview.edit();
}
idp.event.bind("beforeSave", function(e, data) {
    var BUSINESSMODULENAME = idp.control.get("BUSINESSMODULENAME").getValue().BUSINESSMODULENAME;
    var BUSINEESSNAME = idp.control.get("BUSINEESSNAME").getValue().BUSINEESSNAME;
    var SYSTEM = idp.control.get("SYSTEMNAME").getValue().SYSTEMNAME;
    if (BUSINESSMODULENAME !== "") {
        idp.uiview.modelController.setValue("ISDETAIL", "0");
    }
    if (BUSINEESSNAME !== "") {
        var data = idp.control.get("grid_main").getSelected();
        idp.uiview.modelController.setValue("PARENTID", data.ID);
        idp.uiview.modelController.setValue("ISDETAIL", "1");
        //idp.uiview.modelController.setValue("BUSINESSMODULE", data.BUSINESSMODULE);
    }
    if (SYSTEM !== "") {
        var data = idp.control.get("grid_main").getSelected();
        idp.uiview.modelController.setValue("PARENTID", data.ID);
        idp.uiview.modelController.setValue("ISDETAIL", "2");
        //idp.uiview.modelController.setValue("BUSINESSMODULE", data.BUSINESSMODULE);
    }
});
idp.event.bind("domReady", (e, data) => {
    idp.event.register("grid_main", "selectRow", function(e, data, row) {
        if (data.ISDETAIL === "0") {
            $("#BUSINESSMODULECODE").parents(".table-item").show();
            $("#BUSINESSMODULENAME").parents(".table-item").show();
            $("#BUSINEESSCODE").parents(".table-item").hide();
            $("#BUSINEESSNAME").parents(".table-item").hide();
            $("#SYSTEMCODE").parents(".table-item").hide();
            $("#SYSTEMNAME").parents(".table-item").hide();
        } else if (data.ISDETAIL === "1") {
            $("#BUSINESSMODULECODE").parents(".table-item").hide();
            $("#BUSINESSMODULENAME").parents(".table-item").hide();
            $("#BUSINEESSCODE").parents(".table-item").show();
            $("#BUSINEESSNAME").parents(".table-item").show();
            $("#SYSTEMCODE").parents(".table-item").hide();
            $("#SYSTEMNAME").parents(".table-item").hide();
        } else if (data.ISDETAIL === "2") {
            $("#BUSINESSMODULECODE").parents(".table-item").hide();
            $("#BUSINESSMODULENAME").parents(".table-item").hide();
            $("#BUSINEESSCODE").parents(".table-item").hide();
            $("#BUSINEESSNAME").parents(".table-item").hide();
            $("#SYSTEMCODE").parents(".table-item").show();
            $("#SYSTEMNAME").parents(".table-item").show();
        }
    });
});

idp.event.bind("afterCancel", function() {
    $("#BUSINESSMODULECODE").parents(".table-item").show();
    $("#BUSINESSMODULENAME").parents(".table-item").show();
    $("#BUSINEESSCODE").parents(".table-item").show();
    $("#BUSINEESSNAME").parents(".table-item").show();
    $("#SYSTEMCODE").parents(".table-item").show();
    $("#SYSTEMNAME").parents(".table-item").show();
});
idp.event.bind("afterSave", function(e, data) {
    $("#BUSINESSMODULECODE").parents(".table-item").show();
    $("#BUSINESSMODULENAME").parents(".table-item").show();
    $("#BUSINEESSCODE").parents(".table-item").show();
    $("#BUSINEESSNAME").parents(".table-item").show();
    $("#SYSTEMCODE").parents(".table-item").show();
    $("#SYSTEMNAME").parents(".table-item").show();
});