/**
 * @description: 数据同步明细索引JS脚本
 * <AUTHOR>
 * @date 2025/7/15 10:04
 * @version 1.0
 */
function AddData() {
    var data = idp.control.get("grid_243055").getSelected();
    if (!data) {
        idp.warn(idp.lang.get('DataSyncIndex_01'));
        return false;
    }
    idp.func.addCard("system=" + data.ID + "");
    return true;
}

function EditData() {
    var data = idp.control.get('grid_main').getSelected();
    if (!data) {
        idp.warn(idp.lang.get('DataSyncIndex_02'));
        return false;
    }
    var isPrefabricated = data.ISPREFABRICATED;
    if (isPrefabricated === '0') {
        idp.warn(idp.lang.get('DataSyncIndex_03'))
        return false;
    }
    idp.func.editCard();
    return true;
}

function CreateComPonenTask() {
    var data = idp.control.get("grid_main").getSelected();
    if (!data) {
        idp.warn(idp.lang.get('DataSyncIndex_04'));
        return false;
    }
    idp.confirm(idp.lang.get('DataSyncIndex_05'), function () {
        var code = data.CODE;
        var url = "/api/bp/bia/v1.0/dataIntegration/createSchedule";
        // 构造符合要求的 param 参数
        var param = {
            code: code, // 使用 data.CODE 作为入参
        };
        idp.service
            .fetch(url, param, true, "POST")
            .then((res) => {
                if (res) {
                    if (!res.success) {
                        // 失败逻辑：显示后端返回的错误信息
                        idp.warn(res.message || idp.lang.get('DataSyncIndex_06'));
                        return false;
                    } else {
                        // 成功逻辑：提示成功信息
                        idp.tips(idp.lang.get('DataSyncIndex_07'));
                    }
                } else {
                    idp.warn(idp.lang.get('DataSyncIndex_08'));
                    return false;
                }
            })
            .fail((err) => {
                idp.warn(`ERROR_STATUS${err.status}，ERROR_STATUSTEXT${err.statusText}`);
                return false;
            });
        idp.tips(idp.lang.get('DataSyncIndex_09'));
    });
}

function DeleteComPonenTask() {
    var data = idp.control.get("grid_main").getSelected();
    if (!data) {
        idp.warn(idp.lang.get('DataSyncIndex_10'));
        return false;
    }
    idp.confirm(idp.lang.get('DataSyncIndex_11'), function () {
        var code = data.CODE;
        var url = "/api/bp/bia/v1.0/dataIntegration/deleteSchedule";
        // 构造符合要求的 param 参数
        var param = {
            code: code, // 使用 data.CODE 作为入参
        };
        idp.service
            .fetch(url, param, true, "POST")
            .then((res) => {
                if (res) {
                    if (!res.success) {
                        // 失败逻辑：显示后端返回的错误信息
                        idp.warn(res.message || idp.lang.get('DataSyncIndex_12'));
                        return false;
                    } else {
                        // 成功逻辑：提示成功信息
                        idp.tips(idp.lang.get('DataSyncIndex_13'));
                    }
                } else {
                    idp.warn(idp.lang.get('DataSyncIndex_08'));
                    return false;
                }
            })
            .fail((err) => {
                idp.warn(`ERROR_STATUS${err.status}，ERROR_STATUSTEXT${err.statusText}`);
                return false;
            });
        idp.tips(idp.lang.get('DataSyncIndex_14'));
    });
}

function ManualPush() {
    var data = idp.control.get("grid_main").getSelected();
    if (!data) {
        idp.warn(idp.lang.get('DataSyncIndex_15'));
        return false;
    }
    idp.confirm(idp.lang.get('DataSyncIndex_16'), function () {
        var code = data.CODE;
        var url = "/api/bp/bia/v1.0/dataIntegration/manualPush";
        // 构造符合要求的 param 参数
        var param = {
            code: code, // 使用 data.CODE 作为入参
        };
        idp.service
            .fetch(url, param, true, "POST")
            .then((res) => {
                if (res) {
                    if (!res.success) {
                        // 失败逻辑：显示后端返回的错误信息
                        idp.warn(res.message || idp.lang.get('DataSyncIndex_17'));
                        return false;
                    } else {
                        // 成功逻辑：提示成功信息
                        console.log(idp.lang.get('DataSyncIndex_18'));
                    }
                } else {
                    idp.warn(idp.lang.get('DataSyncIndex_08'));
                    return false;
                }
            })
            .fail((err) => {
                idp.warn(`ERROR_STATUS${err.status}，ERROR_STATUSTEXT${err.statusText}`);
                return false;
            });
        idp.tips(idp.lang.get('DataSyncIndex_19'));
    });
}