//存款管理下的利息计提联查表单：存款利息计提联查表单（报账用）763917ad-bc8d-cd67-6b74-1782e751d04b

var taskType = '';
var operationGlobal = "";
var ifAutoVoucher = '0';//是否自动生成凭证
var formtype = 'TMCKLXJTPZ';

//region domReady
idp.event.bind("domReady", function () {
    taskType = idp.utils.getQuery('TASKTYPE');
    operationGlobal = idp.utils.getQuery('operation') || idp.utils.getQuery('status');

    //财务审核节点支持生成财务凭证 ilink581265
    var param = idp.utils.getQuery("otherParam");
    if (param && param != "") {
        var otherParam = JSON.parse(param);
        // 审核后直接生成，对应单据类型设置中的凭证生成方式
        if (otherParam && otherParam.ifautovoucher && otherParam.ifautovoucher == "1") {
            ifAutoVoucher = "1";
        }
    }
});
//endregion

//region viewReady
idp.event.bind("viewReady", function (e) {
    //已办打开、联查进入时，通过按钮控制不可用
    if (operationGlobal == "ybbl" || operationGlobal == "view") {
        _setButtonViable('toolbar1', [], ["baritem_pass","baritem_unpass"]);
    }
});
//endregion

idp.event.bind("loadData", function () {
    //获取流程配置信息，设置共享流程的按钮控制
    setFSPFBtnControl();
});

//region通过
function passLXJT() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    if (!row) {
        idp.warn(idp.lang.get("YHCKLXJTCZ33"));//请选择审批的申请单据
        return false;
    }

    var id = row.ID;
    var pftaskid = idp.utils.getQuery('pftaskid');

    var params = {};
    params.BILLID = id;
    params.PFPROCINTERACTINFO = null;
    params.PFTASKID = pftaskid;
    params.FORMTYPE = formtype;

    var url = "/api/tm/bd/provisioninterest/v1.0/provisioninterest/getPassWindowUrl";
    idp.service.fetch(url, params, false, "PUT")
        .then(function (resInfo) {
            if (resInfo.result) {
                var urlvalue = resInfo.value;

                openTJTGdilog = $.leeDialog.open({
                    title: "",
                    name: "",
                    isHidden: false,
                    showMax: true,
                    width: 700,
                    slide: false,
                    height: 500,
                    url: urlvalue,
                    urlParms: {},
                    onclose: function () {
                        //获取通过窗口变量
                        var option = idp.store.get("pfoption"); //用于判断操作类型，”cancel”：取消或者关闭按钮；
                        if (!option) {
                            option = "cancel";
                        }
                        if (option === "cancel") {
                            return;
                        }
                        //流程交互信息 需要在调用通过接口时传入
                        params.PFPROCINTERACTINFO = idp.store.get("pfprocinteractinfo");

                        var url = "/api/tm/bd/provisioninterest/v1.0/provisioninterest/passLXJT";
                        idp.service.fetch(url, params, false, "PUT")
                            .then(function (resInfo) {
                                if (resInfo.result) {
                                    //通过成功，根据参数判断是否自动生成凭证 ilink581265
                                    var operateNow = idp.utils.getQuery("operation");
                                    var param = idp.utils.getQuery("otherParam");
                                    if (param && param != "") {
                                        var otherParam = JSON.parse(param);
                                        // 审核后直接生成
                                        if (otherParam && otherParam.ifautovoucher && otherParam.ifautovoucher == "1") {
                                            ifAutoVoucher = "1";
                                        }
                                    }
                                    if (taskType == 'FSJH' && operateNow == "dbbl" && ifAutoVoucher == '1') {
                                        idp.tips(idp.lang.get("YHCKLXJTCZ34"));//通过处理成功
                                        //修改按钮显示隐藏

                                        //显示财务凭证的相关按钮
                                        _setButtonViable('toolbar1', ["baritem_generateVourcher", "baritem_viewVoucher", "baritem_cancelVoucher", "baritem_linkVoucher"], ["baritem_pass"]);

                                        //生成财务凭证
                                        onGenerateVourcher();
                                    } else {
                                        idp.tips(idp.lang.get("YHCKLXJTCZ34"));//通过处理成功
                                        idp.uiview.close();
                                    }
                                } else {
                                    idp.error(idp.lang.get("YHCKLXJTCZ35"));//通过处理失败
                                }

                            }).fail(function (resInfo) {
                            idp.loaded();
                        });
                    },
                });
            } else {
                idp.error(idp.lang.get("YHCKLXJTCZ35"));//通过处理失败
            }
        });
}

//endregion

//region退回
function unpassLXJT() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    if (!row) {
        idp.warn(idp.lang.get("YHCKLXJTCZ38"));//请选择退回的申请单据
        return false;
    }

    var id = row.ID;
    var pftaskid = idp.utils.getQuery('pftaskid');

    var params = {};
    params.BILLID = id;
    params.PFPROCINTERACTINFO = null;
    params.PFTASKID = pftaskid;
    params.FORMTYPE = formtype;

    var url = "/api/tm/bd/provisioninterest/v1.0/provisioninterest/getUnPassWindowUrl";
    idp.service.fetch(url, params, false, "POST")
        .then(function (resInfo) {
            if (resInfo.result) {
                var urlvalue = resInfo.value;

                openWDDBTHdilog = $.leeDialog.open({
                    title: "",
                    name: 'ProcessScopeHelp',
                    isHidden: false,
                    showMax: true,
                    width: 700,
                    slide: false,
                    height: 500,
                    url: urlvalue,
                    onclose: function () {
                        var returnParam = openWDDBTHdilog.frame.returnParam;
                        //用于判断操作类型，”cancel”：取消或者关闭按钮；
                        var option = returnParam.operate;
                        if (!option) {
                            option = "cancel";
                        }
                        if (option === "cancel") {
                            return;
                        }
                        //流程交互信息 需要在调用通过接口时传入
                        params.PFPROCINTERACTINFO = returnParam.PFPROCINTERACTINFO

                        var url = "/api/tm/bd/provisioninterest/v1.0/provisioninterest/unPassLXJT";
                        idp.service.fetch(url, params, false, "POST")
                            .then(function (resInfo) {
                                if (resInfo.result) {
                                    idp.tips(idp.lang.get("YHCKLXJTCZ39"));//退回处理成功
                                    idp.uiview.close();
                                } else {
                                    idp.error(idp.lang.get("YHCKLXJTCZ40"));//退回处理失败
                                }

                            }).fail(function (resInfo) {
                            idp.loaded();
                        });
                    },
                });
            } else {
                idp.error(idp.lang.get("YHCKLXJTCZ40"));//退回处理失败
            }
        });
}

//endregion

//region生成凭证
function onGenerateVourcher() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    var varID = row.ID;
    var isGenerVouch = row.ISPRODUCTVOUCHER;//是否生成财务凭证
    var pfuncid = idp.utils.getQuery('pfuncid');

    function generVouch() {
        openVoucherGenerateDilog = $.leeDialog.open({
            name: 'VoucherGenerateDialog',
            title: idp.lang.get("YHCKLXJTCZ16"),//请选择模板
            showMax: true,
            isResize: true,
            width: (window.innerWidth * 0.6) >= 800 ? window.innerWidth * 0.6 : 800,
            height: (window.innerHeight * 0.6) >= 450 ? window.innerHeight * 0.6 : 450,
            url: `/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=4d7a27e7-93ce-4a77-aa94-9fe7fabcf554&dataid=&status=add&j=true&pfuncid=` + pfuncid + `&FORMTYPE=` + formtype + `&DJID=` + varID
        })
    }

    gspframeworkService.rtf.frmEvent.eventListener('funcSwitchEvent', function (obj) {
        var funcid = idp.utils.getQuery("funcId");
        var tabId = obj.tabId;
        var state = idp.uiview.getStatus();
        if (funcid == tabId && state == 'view') {
            idp.uiview.loadData(varID);
        }
    }, idp.utils.getQuery("funcId"));

    if (isGenerVouch == '1') {
        //已生成凭证，是否继续生成
        idp.confirm(idp.lang.get("YHCKLXJTCZ17"), function () {
            generVouch();
        });
    } else {
        generVouch();
    }
}

//endregion

//region预览凭证
function onViewVoucher() {
    const DJID = idp.uiview.modelController.deafaultData[0].data[0].ID;
    var pfuncid = idp.utils.getQuery('pfuncid');

    voucherGenerateDialog = $.leeDialog.open({
        name: 'VoucherGenerateDialog',
        title: idp.lang.get("YHCKLXJTCZ16"),//请选择模板
        showMax: true,
        isResize: true,
        width: (window.innerWidth * 0.6) >= 800 ? window.innerWidth * 0.6 : 800,
        height: (window.innerHeight * 0.6) >= 450 ? window.innerHeight * 0.6 : 450,
        url: `/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=4d7a27e7-93ce-4a77-aa94-9fe7fabcf554&dataid=&status=add&j=true&voucheraction=preview&pfuncid=` + pfuncid + `&FORMTYPE=` + formtype + `&DJID=` + DJID
    })
}

//endregion

//region取消凭证
function onCancelVoucher() {
    //确认要取消凭证？
    idp.confirm(idp.lang.get("YHCKLXJTCZ36"), function () {
        idp.loading();
        const DJID = idp.uiview.modelController.deafaultData[0].data[0].ID;
        const param = {
            DJID: DJID,
            FORMTYPE: formtype
        }

        var url = "/api/tm/bd/provisioninterest/v1.0/provisioninterest/cancelGenerateVoucher";
        idp.service.fetch(url, param, true, "POST").done(function (data) {
            idp.loaded();
            if (data.result) {
                var jsonValue = $.parseJSON(data.value);
                if (jsonValue) {
                    if (jsonValue.SuccessFlag) {
                        idp.tips(jsonValue.ResultDescription);
                        idp.uiview.loadData(DJID);
                    } else {
                        idp.alert(jsonValue.ResultDescription);
                    }
                }
            } else {
                idp.alert(data.message);
            }
        }).fail(function (data) {
            idp.loaded();
            idp.error(data.message);
            return false;
        });
    });
}

//endregion

//region联查凭证
function onLinkVoucher() {
    idp.loading();
    const DJID = idp.uiview.modelController.deafaultData[0].data[0].ID;
    const param = {
        DJID: DJID,
        FORMTYPE: formtype
    }

    var url = "/api/tm/bd/provisioninterest/v1.0/provisioninterest/linkVoucher";
    idp.service.fetch(url, param, true, "POST").done(function (data) {
        idp.loaded();
        if (data.result) {
            var jsonValue = $.parseJSON(data.value);

            var parameterMap = new Map();
            parameterMap.set("ZWPZK_SavedIDList", jsonValue.ZWPZK_SavedIDList.replace(":", "="));
            parameterMap.set('ZWPZK_IDList', jsonValue.ZWPZK_SavedIDList.replace(":", "="));
            parameterMap.set("CurYear", jsonValue.CurYear);
            parameterMap.set('OnlyView', '1');
            gspframeworkService.rtf.func.openMenu({
                funcId: 'AdAccountingDocument_Link',
                appId: '',
                tabId: jsonValue.ZWPZK_SavedIDList,
                appEntrace: '',
                appType: 'menu',
                queryStringParams: parameterMap
            })
        } else {
            idp.alert(data.message);
        }
    }).fail(function (data) {
        idp.loaded();
        idp.error(data.message);
        return false;
    });
}

//endregion

//region控制按钮是否可见-基础方法
function _setButtonViable(toolbar, enableBtns, disableBtns) {
    if (enableBtns.length > 0) {
        idp.control.get(toolbar).toggleBtns(enableBtns, true);
    }
    if (disableBtns.length > 0) {
        idp.control.get(toolbar).toggleBtns(disableBtns, false);
    }
}

//endregion

//region 获取流程配置信息，设置共享流程的按钮控制
function setFSPFBtnControl() {
    const DJID = idp.uiview.modelController.deafaultData[0].data[0].ID;
    const param = {
        DJID: DJID,
        FORMTYPE: formtype
    }

    //如果单据类型设置中凭证生成方式为不生成，则不再展示财务凭证的相关按钮 tfs:1190569
    var url = "/api/tm/bd/provisioninterest/v1.0/provisioninterest/getCWPZSCFS";
    idp.service.fetch(url, param, true, "POST").done(function (data) {
        idp.loaded();
        if (data.result) {
            const operateStr = idp.utils.getQuery('operates');
            if (operateStr && operateStr != 'null') {
                let operateJSON = JSON.parse(operateStr);
                if (operateJSON && operateJSON.length > 0) {
                    operateJSON.forEach(operate => {
                        if (operate.IFENABLED && operate.IFENABLED == '1') {
                            _setButtonViable('toolbar1', [operate.OPERATE], []);
                        } else {
                            _setButtonViable('toolbar1', [], [operate.OPERATE]);
                        }
                    })
                }
            }
        }
    }).fail(function (data) {
        idp.loaded();
        return false;
    });
}

//endregion