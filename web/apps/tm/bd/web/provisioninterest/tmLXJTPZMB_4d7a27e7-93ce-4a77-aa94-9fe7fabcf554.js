//存款利息计提功能中生成凭证、预览凭证时选择凭证模板的卡片
// 凭证模板_存款利息计提 4d7a27e7-93ce-4a77-aa94-9fe7fabcf554

const API = '/api/tm/bd/provisioninterest/v1.0/provisioninterest';// API地址
var DJID = idp.utils.getQuery("DJID");
var formType = idp.utils.getQuery("FORMTYPE");
var voucherAction = idp.utils.getQuery("voucheraction");

idp.event.bind("viewReady", function (e, data) {
    $("#grid_temp").leeGrid({
        columns: [
            {
                display: 'datasourceid',
                name: 'datasourceid',
                align: 'left',
                width: 0,
                minWidth: 0,
                hide: true
            },
            {
                display: 'id',
                name: 'id',
                align: 'left',
                width: 0,
                minWidth: 0,
                hide: true
            },
            {
                display: idp.lang.get("YHCKLXJTCZ24"), //编号
                name: 'code',
                align: 'left',
                width: '40%',
                minWidth: 60
            },
            {
                display: idp.lang.get("YHCKLXJTCZ25"), //名称
                name: 'name',
                align: 'left',
                width: '40%',
                minWidth: 60
            },
            {
                display: idp.lang.get("YHCKLXJTCZ26"), //是否必须生成
                name: 'must',
                align: 'left',
                width: '20%',
                minWidth: 60
            }
        ],
        enabledEdit: true,
        usePager: false,
        height: "auto",
        rownumbers: true,
        rowHeight: 30
    });
    loadData();
    bindButtonEvent();
})

function loadData() {
    const param = {
        operationtype: "getTemplateList",
        DJID: DJID,
        FORMTYPE: formType
    }
    idp.service.fetch(`${API}/generateVoucher`, param, true, "POST")
        .then(res => {
            if (res.result) {
                var rows = changetext($.parseJSON(res.value));
                idp.control.get("grid_temp").loadData({Rows: rows});
            }
        })
        .always(() => {
            idp.loaded();
            var tempgird = idp.control.get('grid_temp');
            if (tempgird && tempgird.rows && tempgird.rows.length && tempgird.rows.length == 1) {
                //如果只有一个模板，则直接打开
                idp.control.get('grid_temp').select(0);
                if ($("[toolbarid=baritem_generate]"))
                    $("[toolbarid=baritem_generate]").click();
            }
        })
}

// 绑定顶部工具栏按钮点击事件
function bindButtonEvent() {
    function setButtonClick(barId1, buttonId, callback) {
        let barId = $('[toolbarid=' + buttonId + ']').parent().attr('id');
        if (!barId) return;
        idp.control.get(barId).options.items.forEach(btn => {
            if (btn.id === buttonId) btn.click = callback
            else if (btn.childs) btn.childs.find(item => item.id === buttonId) && (btn.childs.find(item => item.id === buttonId).click = callback)
        });
    }

    if (voucherAction == 'preview') {
        //凭证预览
        setButtonClick('toolbar1', 'baritem_generate', preViewVoucher);
        $('[toolbarid = baritem_generate]')[0].innerText = idp.lang.get("YHCKLXJTCZ37");//预览
    } else {
        //凭证生成
        setButtonClick('toolbar1', 'baritem_generate', onGenerate);
    }
    setButtonClick('toolbar1', 'baritem_close', onClose);

}

/**
 * 关闭
 */
function onClose() {
    window.parent.$.leeDialog.hide();
}

/**
 * 生成凭证
 * @returns
 */
function onGenerate() {
    if (!idp.control.get('grid_temp').selected || idp.control.get('grid_temp').selected.length < 1) {
        idp.warn(idp.lang.get("YHCKLXJTCZ27")); //请先选择模版
        return false;

    }
    idp.loading(idp.lang.get("YHCKLXJTCZ28")); //处理中...
    var templateID = idp.control.get('grid_temp').selected[0].id;
    const param = {
        operationtype: "generate",
        templateID: templateID,
        DJID: DJID,
        FORMTYPE: formType
    }
    idp.service.fetch(`${API}/generateVoucher`, param, true, "POST").done(function (data) {
        idp.loaded();

        if (data.result) {
            if (data.value) {
                var jsonValue = $.parseJSON(data.value);
                if (jsonValue) {
                    if (jsonValue.SuccessFlag) {
                        window.parent.$.leeDialog.hide()
                        var myMap = new Map();
                        //相当于java的map.put();
                        myMap.set('PARAMTYPE', "1");
                        myMap.set('RELATEQRY', "1");
                        myMap.set('DipInf', jsonValue.PreviewInfo.DipInf);
                        myMap.set('ZWPZK_IDList', jsonValue.PreviewInfo.ZWPZK_IDList);
                        gspframeworkService.rtf.func.openMenu({
                            funcId: 'AdAccountingDocument_Link',
                            appId: '',
                            tabId: DJID + 'pz',
                            appEntrace: '',
                            appType: 'menu',
                            queryStringParams: myMap
                        })
                    } else {
                        idp.alert(jsonValue.ResultDescription);
                    }

                }
            }
        } else {
            idp.alert(data.message);
        }
    }).fail(function (data) {
        idp.loaded();
        idp.error(data.message);
        return false;
    })
}

/**
 * 预览凭证（只预览）
 */
function preViewVoucher() {
    if (!idp.control.get('grid_temp').selected || idp.control.get('grid_temp').selected.length < 1) {
        idp.warn(idp.lang.get("YHCKLXJTCZ27")); //请先选择模版
        return false;

    }
    idp.loading(idp.lang.get("YHCKLXJTCZ28")); //处理中...
    var templateID = idp.control.get('grid_temp').selected[0].id;
    const param = {
        operationtype: "OnlyPreview",
        templateID: templateID,
        DJID: DJID,
        FORMTYPE: formType
    }
    idp.service.fetch(`${API}/generateVoucher`, param, true, "POST").done(function (data) {
        idp.loaded();

        if (data.result) {
            if (data.value) {
                var jsonValue = $.parseJSON(data.value);
                if (jsonValue) {
                    if (jsonValue.SuccessFlag) {
                        window.parent.$.leeDialog.hide()
                        var myMap = new Map();
                        //相当于java的map.put();
                        myMap.set('PARAMTYPE', "1");
                        myMap.set('RELATEQRY', "1");
                        myMap.set('DipInf', jsonValue.PreviewInfo.DipInf);
                        myMap.set('ZWPZK_IDList', jsonValue.PreviewInfo.ZWPZK_IDList);
                        myMap.set('OnlyPreView', "1");
                        gspframeworkService.rtf.func.openMenu({
                            // funcId: 'AdAccountingDocument',
                            funcId: 'AdAccountingDocument_Link',
                            appId: '',
                            tabId: DJID + 'pz',
                            appEntrace: '',
                            appType: 'menu',
                            queryStringParams: myMap
                        })
                    } else {
                        idp.alert(jsonValue.ResultDescription);
                    }

                }
            }
        } else {
            idp.alert(data.message);
            window.setTimeout(function () {
                parent.openVoucherGenerateDilog.close();
            }, 1000);
        }
    }).fail(function (data) {
        idp.error(data.message);
        return false;
    })

}

/**
 * 替换文字
 * @param {*} rows
 * @returns
 */
function changetext(rows) {
    if (rows.length > 0) {
        for (var i = 0; i < rows.length; i++) {
            if (rows[i].must) {
                rows[i].must = idp.lang.get("YHCKLXJTCZ29");  //是
            } else {
                rows[i].must = idp.lang.get("YHCKLXJTCZ30");  //否
            }
        }
    }
    return rows;
}