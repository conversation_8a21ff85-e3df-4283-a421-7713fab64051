var action = '';
var beforetitle_innerHTML = "";
var operation = '';
var netaction = '';
idp.event.bind("domReady", function() {
    operation = idp.utils.getQuery('operation');
    netaction = idp.utils.getQuery('netaction');
    //填写合同编号前，校验使用单位和币种
    idp.event.register("input_389939", "beforeOpen", function() {
        if (idp.control.get("SYDW").getValue() == "") {
            idp.alert(idp.lang.get("alertinfo1"));
            return false;
        }
        if (idp.control.get("BZBH").getValue() == "") {
            idp.alert(idp.lang.get("alertinfo2"));
            return false;
        }
    });
    //更改生效日时，自动计算到期日
    idp.event.register("SXR", "modify", function() {
        var dateStr = idp.control.get("SXR").getValue();
        var str = dateStr.split('-');
        var year = Number(str[0]);
        var month = Number(str[1]);
        var day = Number(str[2]);
        var month_limit = idp.control.get("YXQX").getValue() == "" ? 0 : Number(idp.control.get("YXQX").getValue());
        var month_temp = (month + month_limit - 1) % 12 + 1;
        var month_end = month_temp < 10 ? "0" + month_temp : month_temp;
        var year_end = year + Math.floor((month + month_limit - 1) / 12);
        var date_end = year_end + "-" + month_end + "-" + day;
        idp.control.get("DQRQ").setValue(date_end);
    })
    //更改有效期时，自动计算到期日
    idp.event.register("YXQX", "modify", function() {
        if (!idp.control.get("SXR").getValue() == "") {
            var dateStr = idp.control.get("SXR").getValue();
            var str = dateStr.split('-');
            var year = Number(str[0]);
            var month = Number(str[1]);
            var day = Number(str[2]);
            var month_limit = Number(idp.control.get("YXQX").getValue());
            var month_temp = (month + month_limit - 1) % 12 + 1;
            var month_end = month_temp < 10 ? "0" + month_temp : month_temp;
            var year_end = year + Math.floor((month + month_limit - 1) / 12);
            var date_end = year_end + "-" + month_end + "-" + day;
            idp.control.get("DQRQ").setValue(date_end);
        }
    })
    try{
        gspframeworkService.rtf.frmEvent.eventListener('funcSwitchEvent', function () {
            var row = idp.uiview.modelController.deafaultData[0].data[0];
            var djid=row.NM;
            var barname='baritem_image';
            getEisCountSDM( djid, barname);
        }, "");
    }catch (e) {
    }
})
// 加载数据后
idp.event.bind('loadData', () => {
    var data = idp.uiview.modelController.deafaultData[0].data[0];
    btnControlAfterLoadData();

    var docno = idp.control.get("ZMDH").getValue();
    var docstatus = idp.control.get("ZT").getValue();
    var docstatusName = idp.control.get("ZT").getText();
    docno=idp.utils.safeXSS(docno);

    //设置单据标题
    if (beforetitle_innerHTML && beforetitle_innerHTML.length > 0) {
        title_innerHTML = beforetitle_innerHTML;
    } else {
        title_innerHTML = $(".app-title")[0].innerHTML;
        beforetitle_innerHTML = title_innerHTML;
    }
    $(".app-title")[0].innerHTML = title_innerHTML + "  " + "<span class='title-text' style=' font-size: 14px;color:rgba(0,0,0,0.65); '> " + idp.lang.get("alertinfo_docno") + ":" + docno + "  " + " </span>   <span class='title-text' id='title_docstatus' style=' font-size: 13px;color:#5cc171;'>    " + docstatusName + "</span>";
    if (docstatus === -2 || docstatus === -3) {
        $("#title_docstatus").css("color", "#FF0000");
    }
    var c = idp.control.get('toolbar1');
    c.setFlex();
    var djid=data.NM;
    var barname='baritem_image';
    getEisCountSDM( djid, barname);
});
//保存前校验
idp.event.bind("beforeCheck", function() {
    var dateStr = idp.control.get("SXR").getValue();
    var str = dateStr.split('-');
    var year = Number(str[0]);
    var month = Number(str[1]);
    var month_limit = Number(idp.control.get("YXQX").getValue());
    var month_end = (month + month_limit - 1) % 12 + 1;
    var year_end = year + Math.floor((month + month_limit - 1) / 12);
    var dateStr1 = idp.control.get("DQRQ").getValue();
    var str1 = dateStr1.split('-');
    var year1 = Number(str1[0]);
    var month1 = Number(str1[1]);
    // if(!(year1==year_end&&month1==month_end)){
    //      idp.alert(idp.lang.get("alertinfo15"));
    //      return false;
    // }
    var sxrq = idp.control.get("SXR").getValue();
    var dqrq = idp.control.get("DQRQ").getValue();
    var kcrq = idp.control.get("KCRQ").getValue();
    var yxqx = idp.control.get("YXQX").getValue();
    if (yxqx <= 0) {
        idp.alert(idp.lang.get("alertinfo16"));
        return false;
    }
    if (sxrq < kcrq) {
        idp.alert(idp.lang.get("alertinfo17"));
        return false;
    }
    if (sxrq > dqrq) {
        idp.alert(idp.lang.get("alertinfo18"));
        return false;
    }
    if (kcrq > dqrq) {
        idp.alert(idp.lang.get("alertinfo19"));
        return false;
    }
    var zbf = idp.control.get("ZBF").getValue();
    var n = 0;
    for (var i = 0; i < zbf.length; i++) {
        var ch = zbf.charCodeAt(i);
        if (ch > 255) { // 中文字符集
            n += 2;
        } else {
            n++;
        }
    }
    if (n > 60) {
        idp.alert(idp.lang.get("info239"));//投标（合作）方不能大于60个英文字符或30个汉字！
        return false;
    }
});
/**编辑*/
function editTable() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    var djzt = row.ZT;
    if (djzt != "1" && djzt != "5") {
        idp.alert(idp.lang.get("alertinfo3"));
        return false;
    } else {
        return idp.uiview.edit();
    }
}
/**删除*/
function deleteTable() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    var djzt = row.ZT;
    if (djzt != "1" && djzt != "5") {
        idp.alert(idp.lang.get("alertinfo4"));
        return false;
    } else {
        return idp.uiview.deleteData();
    }
}
/**修改*/
function reviseTable() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    var djzt = row.ZT;
    //idp.control.get("ZJXDZM_LXBH").setDisabled();
    if (djzt == '6') {
        return idp.uiview.edit();
    }
    else
    {
        idp.warn(idp.lang.get("alertinfo3"))
        return false;
    }
}

/**注销*/
function logout() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    var id = row.NM;
    var params = {};
    params.BILLID = id;
    var url = "/api/tm/sdm/zxzmgl/v1.0/logout";
    idp.service.fetch(url, params, false, "PUT")
        .then(function(resInfo) {
            if (resInfo.result) {
                idp.alert(idp.lang.get("alertinfo21"));
                idp.uiview.reloadData();
            }
        }).fail(function(resInfo) {
        idp.error(idp.lang.get("alertinfo22"))
    });
}

/**取消注销*/
function nologout() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    var id = row.NM;
    var params = {};
    params.BILLID = id;
    var url = "/api/tm/sdm/zxzmgl/v1.0/nologout";
    idp.service.fetch(url, params, false, "PUT")
        .then(function(resInfo) {
            if (resInfo.result) {
                idp.alert(idp.lang.get("alertinfo23"));
                idp.uiview.reloadData();
            }
        }).fail(function(resInfo) {
        idp.error(idp.lang.get("alertinfo24"))
    });
}
/** 提交按钮 */
function submitFssp() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];

    var id = row.NM;
    var params = {};
    params.mark = '2';
    params.BILLID = id;
    params.PFPROCINTERACTINFO = null;
    var url = "/api/tm/sdm/zxzmgl/v1.0/submitFssp";
    idp.service.fetch(url, params, false, "PUT")
        .then(function(resInfo) {
            if (resInfo.result) {
                if (resInfo.code == 0) {
                    // 弹窗提醒
                    idp.alert(idp.lang.get("alertinfo5"));
                    operation = "view";
                    idp.uiview.reloadData();
                } else if (resInfo.code == 2) { //弹出选择审批人的交互窗口
                    var url = resInfo.value.url;
                    openTJTGdilog = $.leeDialog.open({
                        title: "",
                        name: "",
                        isHidden: false,
                        showMax: true,
                        width: 700,
                        slide: false,
                        height: 500,
                        url: url,
                        urlParms: {},
                        onclose: function() { // 获取提交窗口变量

                            var params = {};
                            params.BILLID = id;
                            params.PFPROCINTERACTINFO = resInfo.value;
                            idp.service.fetch(url, params, false, "PUT")
                                .then(function(resInfo) {
                                    if (resInfo.result) {
                                        if (resInfo.code == 0) {
                                            // 弹窗提醒
                                            idp.alert(idp.lang.get("alertinfo5"));
                                            operation = "view";
                                            idp.uiview.reloadData();
                                        } else {
                                            // 弹窗提醒
                                            idp.warn(idp.lang.get("alertinfo6") + resInfo.message);
                                        }

                                    } else {
                                        // 弹窗提醒
                                        idp.alert(idp.lang.get("alertinfo6") + resInfo.message);
                                    }
                                }).fail(function(resInfo) {
                                idp.error(idp.lang.get("alertinfo6"))
                            });
                        },
                    });

                } else {
                    // 弹窗提醒
                    idp.warn(idp.lang.get("alertinfo6") + resInfo.message);
                }

            } else {
                // 弹窗提醒
                idp.alert(idp.lang.get("alertinfo6") + resInfo.message);
            }
        }).fail(function(resInfo) {
        idp.error(idp.lang.get("alertinfo6"))
    });


}

/** 撤回 */
function withdrawFssp() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];

    var id = row.NM;
    var params = {};
    params.mark = '2';
    params.BILLID = id;
    params.PFTASKID = '';
    params.DQHJBH = 'STARTNODE';
    //var taskType=idp.util.getQuery("TASKTYPE");
    var url = "/api/tm/sdm/zxzmgl/v1.0/fsWithdraw";
    idp.service.fetch(url, params, false, "PUT")
        .then(function(resInfo) {
            if (resInfo.result) {
                idp.alert(idp.lang.get("alertinfo7"));
                operation = "view";
                idp.uiview.reloadData();
            }
        }).fail(function(resInfo) {
        idp.error(idp.lang.get("alertinfo8"))
    });
}

/** 通过 */
function SetPassFssp() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];

    var id = row.NM;
    var pftaskid = idp.utils.getQuery('pftaskid');

    var params = {};
    params.mark = '2';
    params.BILLID = id;
    params.PFPROCINTERACTINFO = null;
    params.PFTASKID = pftaskid;

    var url = "/api/tm/sdm/zxzmgl/v1.0/getPassUrl";
    idp.service.fetch(url, params, false, "PUT")
        .then(function(resInfo) {
            if (resInfo.result) {

                var urlvalue = resInfo.value;

                openTJTGdilog = $.leeDialog.open({
                    title: "",
                    name: "",
                    isHidden: false,
                    showMax: true,
                    width: 700,
                    slide: false,
                    height: 500,
                    url: urlvalue,
                    urlParms: {},
                    onclose: function() {
                        //获取通过窗口变量
                        var option = idp.store.get("pfoption"); //用于判断操作类型，”cancel”：取消或者关闭按钮；
                        if (!option) {
                            option = "cancel";
                        }
                        if (option === "cancel") {
                            return;
                        }
                        //流程交互信息 需要在调用通过接口时传入
                        params.PFPROCINTERACTINFO = idp.store.get("pfprocinteractinfo ");

                        url = "/api/tm/sdm/zxzmgl/v1.0/fsPass";
                        idp.service.fetch(url, params, false, "PUT")
                            .then(function(resInfo) {
                                if (resInfo.result) {
                                    idp.alert(idp.lang.get("alertinfo9"));
                                    operation = "view";
                                    idp.uiview.reloadData();
                                } else {
                                    idp.error(idp.lang.get("alertinfo10"))
                                }

                            }).fail(function(resInfo) {
                            idp.error(idp.lang.get("alertinfo10"))
                        });
                    },
                });
            } else {
                idp.error(idp.lang.get("alertinfo10"))
            }
        });
}

/** 退回 */
function SetNoPassFssp() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];

    var id = row.NM;
    var pftaskid = idp.utils.getQuery('pftaskid');

    var params = {};
    params.mark = '2';
    params.BILLID = id;
    params.PFPROCINTERACTINFO = null;
    params.PFTASKID = pftaskid;

    var url = "/api/tm/sdm/zxzmgl/v1.0/getUnPassUrl";
    idp.service.fetch(url, params, false, "PUT")
        .then(function(resInfo) {
            if (resInfo.result) {
                var urlvalue = resInfo.value;

                openWDDBTHdilog = $.leeDialog.open({
                    title: "",
                    name: 'ProcessScopeHelp',
                    isHidden: false,
                    showMax: true,
                    width: 700,
                    slide: false,
                    height: 460,
                    url: urlvalue,
                    onclose: function() {
                        var returnParam = openWDDBTHdilog.frame.returnParam;
                        //用于判断操作类型，”cancel”：取消或者关闭按钮；
                        var operate = returnParam.operate;
                        if (!operate) {
                            operate = "cancel";
                        }
                        if (operate === "cancel") {
                            return;
                        }
                        //流程交互信息 需要在调用退回接口时传入
                        params.PFPROCINTERACTINFO = returnParam.PFPROCINTERACTINFO

                        url = "/api/tm/sdm/zxzmgl/v1.0/fsReturn";
                        idp.service.fetch(url, params, false, "PUT")
                            .then(function(resInfo) {
                                if (resInfo.result) {
                                    idp.alert(idp.lang.get("alertinfo11"));
                                    operation = "view";
                                    idp.uiview.reloadData();
                                } else {
                                    idp.error(idp.lang.get("alertinfo12"))
                                }

                            }).fail(function(resInfo) {
                            idp.error(idp.lang.get("alertinfo12"))
                        });
                    },
                });

            } else {
                idp.error(idp.lang.get("alertinfo12"))
            }
        });
}
/**复核通过*/
function SetReviewFssp() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];

    var id = row.NM;
    var pftaskid = idp.utils.getQuery('pftaskid');

    var params = {};
    params.mark = '2';
    params.BILLID = id;
    params.PFPROCINTERACTINFO = null;
    params.PFTASKID = pftaskid;

    var url = "/api/tm/sdm/zxzmgl/v1.0/getPassUrl";
    idp.service.fetch(url, params, false, "PUT")
        .then(function(resInfo) {
            if (resInfo.result) {

                var urlvalue = resInfo.value;

                openTJTGdilog = $.leeDialog.open({
                    title: "",
                    name: "",
                    isHidden: false,
                    showMax: true,
                    width: 700,
                    slide: false,
                    height: 500,
                    url: urlvalue,
                    urlParms: {},
                    onclose: function() {
                        //获取通过窗口变量
                        var option = idp.store.get("pfoption"); //用于判断操作类型，”cancel”：取消或者关闭按钮；
                        if (!option) {
                            option = "cancel";
                        }
                        if (option === "cancel") {
                            return;
                        }
                        //流程交互信息 需要在调用通过接口时传入
                        params.PFPROCINTERACTINFO = idp.store.get("pfprocinteractinfo ");

                        url = "/api/tm/sdm/zxzmgl/v1.0/fsReview";
                        idp.service.fetch(url, params, false, "PUT")
                            .then(function(resInfo) {
                                if (resInfo.result) {
                                    idp.tips(idp.lang.get("alertinfo13"));
                                    idp.uiview.reloadData();
                                } else {
                                    idp.error(idp.lang.get("alertinfo14"))
                                }

                            }).fail(function(resInfo) {
                            idp.error(idp.lang.get("alertinfo14"))
                        });
                    },
                });


            } else {
                idp.error(idp.lang.get("alertinfo14"))
            }

        });
}
/**打印*/
function print() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    if (row.length === 0) {
        return idp.warn(idp.lang.get("alertinfo1")); //请选择数据
    }
    var nm = row.NM;
    idp.print.startCloud("38642bbb-7939-6e0a-df26-270da2539711", nm);
    return true;
}

function btnControlAfterLoadData() {
    //action=idp.utils.getQuery('action');
    var data = idp.uiview.modelController.deafaultData[0].data[0];
    if (operation.toLowerCase() == "view" && data.ZT != "1" && data.ZT != "5") {
        idp.control.get("toolbar1").toggleBtns(['baritem_add', 'baritem_cancel', 'baritem_modify', 'baritem_save', 'baritem_delete', 'baritem_submit', 'baritem_withdraw', 'baritem_review', 'baritem_pass', 'baritem_noPass', 'baritem_revise', 'baritem_nologout', 'baritem_logout'], false);
    } else if (operation.toLowerCase() == "view" && (data.ZT == "1" || data.ZT == "5") && netaction == "private") {
        idp.control.get("toolbar1").toggleBtns(['baritem_add', 'baritem_cancel', 'baritem_modify', 'baritem_save', 'baritem_delete', 'baritem_submit', 'baritem_withdraw', 'baritem_review', 'baritem_pass', 'baritem_noPass', 'baritem_revise', 'baritem_nologout', 'baritem_logout'], false);
    } else if (data.ZT == "3") { //状态为审批中时，隐藏全部
        idp.control.get("toolbar1").toggleBtns(['baritem_add', 'baritem_cancel', 'baritem_modify', 'baritem_save', 'baritem_delete', 'baritem_submit', 'baritem_withdraw', 'baritem_revise', 'baritem_review', 'baritem_nologout', 'baritem_logout'], false); // 隐藏全部按钮
    } else if (data.ZT == "4") { //状态为审批通过时，在平台的待办事项处查看卡片时只显示复核、退回、关闭、电子影像
        idp.control.get("toolbar1").toggleBtns(['baritem_add', 'baritem_cancel', 'baritem_modify', 'baritem_save', 'baritem_delete', 'baritem_submit', 'baritem_withdraw', 'baritem_pass', 'baritem_revise', 'baritem_nologout', 'baritem_logout'], false);
    } else if (data.ZT == "6") { //状态为完成时,只显示关闭、修改、保存、注销、打印、电子影像
        idp.control.get("toolbar1").toggleBtns(['baritem_add', 'baritem_cancel', 'baritem_modify', 'baritem_delete', 'baritem_submit', 'baritem_withdraw', 'baritem_pass', 'baritem_noPass', 'baritem_review', 'baritem_nologout'], false);
    } else if (data.ZT == "2") { //状态为注销时，只显示关闭、取消注销、打印、电子影像
        idp.control.get("toolbar1").toggleBtns(['baritem_add', 'baritem_cancel', 'baritem_modify', 'baritem_save', 'baritem_delete', 'baritem_submit', 'baritem_withdraw', 'baritem_pass', 'baritem_revise', 'baritem_noPass', 'baritem_review', 'baritem_logout'], false);
    } else if (data.ZT == "1" || data.ZT == "5") { //状态为制单和退回时，隐藏修改、注销和取消注销、电子影像
        idp.control.get("toolbar1").toggleBtns(['baritem_revise', 'baritem_logout', 'baritem_nologout', 'baritem_pass', 'baritem_noPass', 'baritem_review'], false);
    }
}

//电子影像
function viewImageFssp() {
    var row = idp.uiview.modelController.deafaultData[0].data[0];
    if (!row) {
        return false;
    }
    var billType = 'SDZXZMDJ';
    var djzt = row.ZT;
    var operation = (djzt == "1" || djzt == "5" )  ? 'edit' : 'view';
    if (netaction == "private")
    {
        operation='add';
    }

    //影像信息
    const param = {
        BillNM: row.NM,
        BillCODE: row.ZMDH,
        BillType: billType,
        BillTypeID: billType,
        OPERATION: operation,
        USERCODE: idp.context.get("UserCode"),
        SourceSys: "SDZXZMGL",
        MKID: 'SDM',
        DWBH: row.SYDW,
        BILLSTATE: 'SAVE',
        TabID:row.ZMDH,
        TabName:idp.lang.get("bzjskbzdcard_34")
    };
    const dftmImageService = new dftmImageCommonService();
    dftmImageService.openImage(param);
}