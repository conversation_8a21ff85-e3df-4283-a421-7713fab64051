/*
 * @Author: wyt
 * @Date: 2021-06-21 17:45:52
 * @FilePath: ../../../../../../../idpjavascript/收到的保函登记列表.js
 */
var fsspUtils = new fsspUtilsFunc();
var CannotOperateOthersDoc = "0";//不允许非制单人操作
//返回列表中单据状态的显示值
function getGridStatus(DOCSTATUS) {
    var docstatus = idp.lang.get("docStatus" + DOCSTATUS);
    // 审批退回，办理退回，复核退回
    if (DOCSTATUS === "04") {
        return "<p style=\"color:red;\">" + docstatus + "</p>";
    } else {
        return "<p>" + docstatus + "</p>";
    }
}
/**
 * 单据编号联查
 */
function docNoLinkView(type, rowdata, column) {
    console.log(idp.lang.get('info211'));//单据编号联查
    let id = rowdata.ID;
    var docSrc = rowdata.DOCSRC;
    var cardBeforeUrl = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid=";
    var status = "view";
    var url = cardBeforeUrl + id + "&status=view&runtime=true&fdim=zd&styleid=5cdad095-8a72-42c8-a900-0023fed57ad8&&formState=";
    idp.utils.openurl("", idp.lang.get('info232'), url);//收到保函登记详情
}

function customAdd() {
    console.log(idp.lang.get('info233'));//新增
    var cardBeforeUrl = `/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid=`;
    var status = "add";
    // url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid="+id+"&status="+status+"&styleid=045c47d7-b2d2-4d83-9df4-f622d9173e3e&runtime=true&&formState="
    var url = `${cardBeforeUrl}&status=${status}&styleid=5cdad095-8a72-42c8-a900-0023fed57ad8&&formState=`;
    idp.utils.openurl("", idp.lang.get('info232'), url);//收到保函登记详情
}
/**
 * 自定义校验
 * @param {*} actionInt 执行动作 1:新增,2:编辑,3:保存,4:取消,5:删除,6:提交,7:撤回,8:打印
 * */
function CustomValid(actionInt) {
    console.log(idp.lang.get('info234'));//自定义校验
    var current = idp.control.get("grid_main").getSelected();
    let docstatus = current.BHZT;
    if (actionInt == 2) {
        if (!current) {
            idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
            return false;
        }

        if(CannotOperateOthersDoc=="1")
        {
            var creator =current.CREATOR;
            if (idp.context.get("UserId") != creator) {
                idp.warn(idp.lang.get('info223'));//不能编辑他人填制的单据
                return false;
            }
        }

        if (docstatus != '01' && docstatus != '04') {
            idp.warn(idp.lang.get('info224'));//非制单，退回单据不能编辑
            return false;
        }
        idp.func.editCard('runtime=true&fdim=zd')
        return true;
    }

};
//编辑
function customEdit() {
    console.log(idp.lang.get('info236'));//编辑
    // 获取当前行
    var current = idp.control.get("grid_main").getSelected();
    if (!current) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if (current.DOCSTATUS != 1 && current.DOCSTATUS != -3) {
        idp.warn(idp.lang.get('info227')); //"只有制单、办理退回的单据才能编辑!"
        return false;
    }
    let id = current.ID;

    var cardBeforeUrl = `/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid=${id}`;
    var status = "edit";
    // url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid="+id+"&status="+status+"&styleid=045c47d7-b2d2-4d83-9df4-f622d9173e3e&runtime=true&&formState="

    var url = `${cardBeforeUrl}&status=${status}&styleid=5cdad095-8a72-42c8-a900-0023fed57ad8&&formState=`;
    idp.utils.openurl("", idp.lang.get('info232'), url);//收到保函登记详情
    //return true;
}

function CustomDelete() {
    // 获取当前行
    var current = idp.control.get("grid_main").getSelected();
    var docStatus = current.BHZT;
    // 勾选数据
    var gxEntity = idp.control.get('grid_main').selected;
    if (!current) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if (gxEntity.length > 1) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if(CannotOperateOthersDoc=="1")
    {
        var creator =current.CREATOR;
        if (idp.context.get("UserId") != creator) {
            idp.warn(idp.lang.get('info226'));//不能删除他人填制的单据
            return false;
        }
    }
    if (docStatus == "01" || docStatus == "04") {
        idp.func.delete();
        return true;
    } else {
        idp.warn(idp.lang.get('info238'));//非制单，审批退回，办理退回单据不能删除
        return false;
    }

}


/**
 * 共享提交
 * @param id 单据ID
 */
function CustomFsspSubmit1() {
    console.log(idp.lang.get('info214')); //共享提交
    // 获取当前行
    var entity = idp.control.get("grid_main").getSelected();
    // 勾选数据
    var gxEntity = idp.control.get('grid_main').selected;
    if (!entity) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if (gxEntity.length > 1) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if(CannotOperateOthersDoc=="1")
    {
        var creator =entity.CREATOR;
        if (idp.context.get("UserId") != creator) {
            idp.warn(idp.lang.get('info215'));//不能提交他人填制的单据
            return false;
        }
    }
    var formType = "SDM_SDBHDJ";
    var commonParams = {
        action: "Submit",
        billId: entity.ID,
        docNo: entity.BHBH,
        unitId: entity.SYDW,
        formType: formType
    }
    var param = fsspUtils.fsspParamsToValue(commonParams);
    return fsspUtils.fsspHttp(customutils.langIsZh() ? idp.lang.get('info216') : "Submit", param); //提交
}

function CustomFsspSubmit() {
    var row = idp.control.get("grid_main").getSelected();
    var gxEntity = idp.control.get('grid_main').selected;
    if (!row) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if (gxEntity.length > 1) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if(CannotOperateOthersDoc=="1")
    {
        var creator =row.CREATOR;
        if (idp.context.get("UserId") != creator) {
            idp.warn(idp.lang.get('info215'));//不能提交他人填制的单据
            return false;
        }
    }
    idp.loading(idp.lang.get('sddbh_9')); //加载中。。。
    var formType = "SDM_SDBHDJ";
    var commonParams = {
        action: "Submit",
        billId: row.ID,
        docNo: row.BHBH,
        unitId: row.SYDW,
        formType: formType
    }
    var param = fsspUtils.fsspParamsToValue(commonParams);
    param.pfProcInteractInfo = {};
    var url = "/api/tm/sdm/creditguarantee/v1.0/tmcreditguarantee/TMCreditGuaranteeOperation";
    idp.service.fetch(url, param, false, "PUT")
        .then(function(result) {
            idp.loaded();
            if (result) {
                if (result.result) {
                    if (result.code == "2") {
                        openTJTGdilog = $.leeDialog.open({
                            title: "",
                            name: "",
                            isHidden: false,
                            showMax: true,
                            width: 700,
                            slide: false,
                            height: 500,
                            url: result.value.url,
                            urlParms: {},
                            onclose: function() {
                                //获取提交窗口变量
                                var option = idp.store.get("pfoption"); //用于判断操作类型，”cancel”：取消或者关闭按钮；
                                if (!option) {
                                    option = "cancel";
                                }
                                if (option === "cancel") {
                                    return;
                                }
                                //流程交互信息 需要在调用退回接口时传入
                                var PFPROCINTERACTINFO = idp.store.get("pfprocinteractinfo");
                                //再次发起流程
                                idp.loading(idp.lang.get('sddbh_9')); //加载中。。。
                                param.pfProcInteractInfo = PFPROCINTERACTINFO;
                                var url = "/api/tm/sdm/creditguarantee/v1.0/tmcreditguarantee/TMCreditGuaranteeOperation";
                                idp.service.fetch(url, param, false, "PUT")
                                    .then(function(result) {
                                        idp.loaded();
                                        if (result) {
                                            if (result.result) {
                                                idp.tips(idp.lang.get('info219')); //提交成功
                                                idp.func.refresh();
                                            } else {
                                                idp.error(result.message);
                                            }
                                        }
                                    });
                            },
                        });
                    } else {
                        //idp.tips(idp.lang.get("lsitOPSuss"));
                        idp.tips(customutils.langIsZh() ? idp.lang.get('info216') : "Submit" + ' ' + customutils.langIsZh() ? idp.lang.get('info218') : "success"); //提交  成功
                        customutils.cardLoad(); // 卡片刷新
                    }
                } else {
                    idp.error(result.message);
                }
            }
        }).always(function() {
        idp.loaded();
    });
    return true;
}

/**
 * 共享撤回
 */
function CustomFsspWithdraw(id) {
    console.log(idp.lang.get('info220')); //共享撤回
    // 获取当前行
    var entity = idp.control.get("grid_main").getSelected();
    // 勾选数据
    var gxEntity = idp.control.get('grid_main').selected;
    if (!entity) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if (gxEntity.length > 1) {
        idp.warn(idp.lang.get('kcdbh_24')); //"请选择一条数据"
        return false;
    }
    if(CannotOperateOthersDoc=="1")
    {
        var creator =entity.CREATOR;
        if (idp.context.get("UserId") != creator) {
            idp.warn(idp.lang.get('info221'));//不能撤回他人填制的单据
            return false;
        }
    }
    var formType = "SDM_SDBHDJ";
    var commonParams = {
        action: "Withdraw",
        billId: entity.ID,
        docNo: entity.BHBH,
        unitId: entity.SYDW,
        formType: formType
    }
    var param = fsspUtils.fsspParamsToValue(commonParams);
    param.currentProcessNode = 'STARTNODE';
    return fsspUtils.fsspHttp(customutils.langIsZh() ?  idp.lang.get('info222') : "Withdraw", param); //撤回
}

function fsspUtilsFunc() {
    /**
     * 参数赋值函数
     * @param {*} commonParams 执行动作,单据ID,单据编号,单位ID,表单类型
     * @param {String} pfTaskId 待办任务ID
     * @param {String} approveOpinion 流程通过、退回等的原因
     */
    this.fsspParamsToValue = function(commonParams, pfTaskId, approveOpinion) {
        var fsspParams = fsspUtils.fsspParamsObj;
        fsspParams.action = commonParams.action;
        fsspParams.billId = commonParams.billId;
        fsspParams.docNo = commonParams.docNo;
        fsspParams.unitId = commonParams.unitId;
        fsspParams.formType = commonParams.formType;
        fsspParams.pfTaskId = pfTaskId;
        fsspParams.approveOpinion = approveOpinion;
        return fsspParams;
    }
    /**
     * 共享流程相关API调用
     * @param chsActionName 中文操作名称
     * @param fsspParams 共享流程所需参数
     */
    this.fsspHttp = function(chsActionName, fsspParams) {
        // 显示遮罩
        idp.loading(idp.lang.get('sddbh_9')); //加载中。。。
        // api,data,async,method
        idp.service.fetch(`/api/tm/sdm/creditguarantee/v1.0/tmcreditguarantee/TMCreditGuaranteeOperation`, fsspParams, true, "PUT")
            .then(
                (esInfo) => {
                    idp.loaded();
                    idp.tips(chsActionName + ' ' + customutils.langIsZh() ?  idp.lang.get('info218') : "success"); //操作成功
                    customutils.listLoad();
                },
                error => {
                    // idp.error(error.responseJSON);
                    idp.loaded();
                    return false;
                }
            );
    }
    /**
     * 共享流程
     */
    this.fsspParamsObj = ({
        /**
         * 执行动作
         */
        action: "",
        /**
         * 单据ID
         */
        billId: "",

        /**
         * 单据编号
         */
        docNo: "",
        /**
         * 单位ID
         */
        unitId: "",
        /**
         * 表单类型
         */
        formType: "",
        /**
         * 待办任务ID
         */
        pfTaskId: "",
        /**
         * 流程通过、退回等的原因
         */
        approveOpinion: "",
        /**
         * 其他信息 提交
         */
        otherInfo: "",
        /**
         * 要退回到的节点
         */
        targetProcessNode: "",
        /**
         * 当前节点编号
         */
        currentProcessNode: "",
    });
}

/**电子影像*/
function viewImageList() {
    var row = idp.control.get("grid_main").getSelected();
    if (!row) {
        idp.warn(idp.lang.get("alertinfo1"))
    }

    var billType = 'SDM_SDBHDJ';
    var djzt = row.BHZT;
    var operation = (djzt == "01" || djzt == "04" )  ? 'edit' : 'view';

    const param = {
        BillNM: row.ID,
        BillCODE: row.BHBH,
        BillType: billType,
        BillTypeID: billType,
        OPERATION: operation,
        USERCODE: idp.context.get("UserCode"),
        SourceSys: "KCBHGL",
        MKID: 'SDM',
        DWBH: row.SYDW,
        BILLSTATE: 'SAVE',
        TabID:row.DJBH,
        TabName:idp.lang.get("bzjskbzdcard_34")
    };
    const dftmImageService = new dftmImageCommonService();
    dftmImageService.openImage(param);

}

/**超链接查看卡片*/
function viewCard(rowData, column, el) {
    idp.control.get('grid_main').select(rowData.__index)
    let url = `&operation=view&docsrc=listview&runtime=true&fdim=zd`;
    idp.func.viewCard(url)
}

/**查看流程*/
function viewProFssp() {
    var row = idp.control.get("grid_main").getSelected();
    if (!row) {
        idp.warn(idp.lang.get("alertinfo1"))
    }

    var id = row.ID;
    var params = {};
    params.mark = '1';
    params.BILLID = id;
    params.FORMTYPE='SDM_SDBHDJ';

    var url = "/api/tm/sdm/bhdj/v1.0/bhviewProFssp";
    idp.service.fetch(url, params, false, "PUT")
        .then(function(resInfo) {

            if (resInfo.result) {
                if (resInfo.code == 0) {
                    // 弹窗提醒

                    var url = resInfo.value;
                    idp.utils.openurl( row.BHDH, idp.lang.get('info43'), resInfo.value, true);//查看流程

                } else {
                    // 弹窗提醒
                    idp.warn(idp.lang.get("alertinfo6") + resInfo.message);
                }

            } else {
                // 弹窗提醒
                idp.alert(idp.lang.get("alertinfo6") + resInfo.message);
            }
        }).fail(function(resInfo) {

        idp.error(idp.lang.get("alertinfo6"))
    });
}

idp.event.bind("domReady", function() { //需要在界面加载后绑定
    getConfig();
    idp.event.register("grid_main", "selectRow", function (e, data) {
        var djid=data.ID;
        var barname='baritem_image';
        getEisCountSDM( djid, barname);
    });
});

function getConfig() {
    var url = "/api/tm/tmfnd/v1.0/tmpub/bfparams?su=TM&keys=TM_CannotOperateOthersDoc";
    idp.service.fetch(url, null, false, "GET")
        .then(function (resInfo) {
            if (resInfo.TM_CannotOperateOthersDoc === "1") {
                CannotOperateOthersDoc = resInfo.TM_CannotOperateOthersDoc;
            }

        });
}