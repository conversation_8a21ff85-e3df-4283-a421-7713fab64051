idp.event.bind('domReady', function (e, context) {
    idp.event.register("grid_main", "selectRow", function (e, data) {
        var djid=data.ID;
        var barname='baritem_image';
        getEisCountSDM( djid, barname);
    });
})

/**查看流程*/
function viewProFssp() {
    var row = idp.control.get("grid_main").getSelected();
    if (!row) {
        idp.warn(idp.lang.get("alertinfo1"))
    }

    var id = row.ID;
    var params = {};
    params.mark = '1';
    params.BILLID = id;

    var url = "/api/tm/sdm/bhsq/v1.0/bhviewProFssp";
    idp.service.fetch(url, params, false, "PUT")
        .then(function (resInfo) {

            if (resInfo.result) {
                if (resInfo.code == 0) {
                    //idp.utils.openurl('BHSQ' + row.DJBH, "查看流程", resInfo.value, true);
                    // 弹窗提醒

                    var url = resInfo.value;
                    //查看流程
                    idp.utils.openurl( row.DJBH, idp.lang.get("kcbhsqlist_0"), resInfo.value, true);
                    // openTJTGdilog = $.leeDialog.open({
                    //     title: "",
                    //     name: "",
                    //     isHidden: false,
                    //     showMax: true,
                    //     width: 700,
                    //     slide: false,
                    //     height: 500,
                    //     url: url,
                    //     urlParms: {},
                    //     onclose: function () { // 获取提交窗口变量

                    //     },
                    // });
                } else {
                    // 弹窗提醒
                    idp.warn(idp.lang.get("alertinfo6") + resInfo.message);
                }
            } else {
                // 弹窗提醒
                idp.alert(idp.lang.get("alertinfo6") + resInfo.message);
            }
        }).fail(function (resInfo) {

        idp.error(idp.lang.get("alertinfo6"))
    });
}

/**超链接查看卡片*/
function viewCard(rowData, column, el) {
    idp.control.get('grid_main').select(rowData.__index)
    var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=7ee32a61-af48-0eb3-d7af-4bfd3bdb4e3a&dataid=" + rowData.ID + "&status=view&operation=view&j=true&runtime=true&&fdim=zd&action=LCVIEW";
    idp.utils.openurl(rowData.ID, idp.lang.get("kcdbh_40"), url, true);
}
/**打印*/
function print() {
    var row = idp.control.get("grid_main").selected;

    if (row.length === 0) {
        return idp.warn(idp.lang.get("alertinfo1")); //请选择数据
    }
    const ids = row.map(({
                             ID: id
                         }) => {
        return id;
    }).join(',')
    idp.print.startCloud("7ee32a61-af48-0eb3-d7af-4bfd3bdb4e3a", ids);
    return true;
}

//电子影像
function viewImageSQLIST() {
    var row = idp.control.get("grid_main").getSelected();
    if (!row) {
        idp.warn(idp.lang.get("alertinfo1"))
    }
    var dzyxZT = "view";
    var billcode = idp.layout.getTitle() || idp.lang.get("kcbhzq_1");//影像信息
    const param = {
        BillNM: row.ID,
        BillCODE: billcode,
        BillType: "KCBHSQ",
        BillTypeID: "KCBHSQ",
        OPERATION: dzyxZT,
        USERCODE: idp.context.get("UserCode"),
        "SourceSys": "IDP",
        MKID: 'SDM',
        DWBH: row.SQDW,
        BILLSTATE: 'SAVE',
        TabID:row.DJBH,
        TabName:idp.lang.get("kcbhzq_2")
    };
    const dftmImageService = new dftmImageCommonService();
    dftmImageService.openImage(param);

}
