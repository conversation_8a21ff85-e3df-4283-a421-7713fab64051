var fxtznm = idp.utils.getQuery('fxtznm');
idp.event.bind("viewReady", function(e, context) {
    idp.control.toolbar.toggleBtns("toolbar1", ["baritem_add", "baritem_modify", "baritem_save", "baritem_cancel", "baritem_delete", "baritem_close"], false);

});

idp.event.bind("domReady", function() { //需要在界面加载后绑定

    idp.event.register("grid_main", "beforeGridFilter", function (e, filters) {

        if (filters.length > 0) {
            filters[filters.length - 1].Logic = " and "; //如果有其他默认条件 需要处理拼接逻辑
        }
        filters.push({
            "Left": "",
            "Field": "TMZQFXHKTZ.JJNM",
            "Operate": "=",
            "Value": fxtznm,
            "Right": "",
            "Logic": "and"
        });
        filters.push({
            "Left": "",
            "Field": "TMZQFXHKTZ.YWFX",
            "Operate": "=",
            "Value": 2,
            "Right": "",
            "Logic": "and"
        });
        filters.push({ //此处过滤条件排除作废的还款台账
            "Left": "((",
            "Field": "TMZQFXHKTZ.ZXZT",
            "Operate": "<>",
            "IsExpress": false,
            "Value": parseInt(-5),
            "Right": ")",
            "Logic": "or"
        });
        filters.push({
            "Left": "(",
            "Field": "TMZQFXHKTZ.ZXZT",
            "Operate": "isnull",
            "IsExpress": false,
            "Right": "))",
            "Logic": "and"
        });
        return filters;
    });
});

