let globalVariables = {
    STYLEID: "3df6eb33-26d7-739e-7831-8b7d64710752",
};

idp.event.bind("viewReady", function () {
    //方案注册事件
    idp.event.register("col_filter", "solutionSet", function (reg, filterInfo, fields) {
        eao.common
            .getDefault()
            .done(function (res) {
                if (res && res.appendData) {
                    var defaultInfo = res.appendData;
                    var orgCols = fields.filter((e) => e.isShow && e.id === "EAOORG");
                    if (filterInfo.Id === "solutionorigin" && orgCols.length > 0 && defaultInfo.org) {
                        idp.control.get("EAOORG").setValue(defaultInfo.org.id, defaultInfo.org.name);
                        eaopolicypricelistcontroller.orgName = defaultInfo.org.name;
                        eaopolicypricelistcontroller.orgCode = defaultInfo.org.code;
                    }
                    idp.func.refresh();
                }
            })
            .fail(function (error) {
                console.log(error);
            });
    });
});

var eaopolicypricelistcontroller = {
    orgName: "",
    orgCode: "",
    /**
     * 新增
     * @returns 新增结果
     */
    addEx: function () {
        let org = idp.control.get("EAOORG").getValue();
        if (!org) {
            idp.warn(idp.lang.get("EAO_POLICYPRICE_NEEDORG") || "请先选择经营组织");
            return false;
        }
        if (idp.control.get("EAOORG").getText()) {
            eaopolicypricelistcontroller.orgName = idp.control.get("EAOORG").getText();
        }
        if (idp.control.get("EAOORG").curData) {
            eaopolicypricelistcontroller.orgCode = idp.control.get("EAOORG").curData[0].CODE;
        }
        return eao.common.listAddCard(null, null, {
            orgId: org,
            orgCode: eaopolicypricelistcontroller.orgCode,
            orgName: escape(eaopolicypricelistcontroller.orgName),
        });
    },
    /**
     * 编辑
     * @returns 编辑结果
     */
    editEx: function () {
        var row = idp.list.utils.getCheckedRows();
        if (row.length === 1) {
            var approveFlag = row[0].BILLSTATUS;
            if (approveFlag === "" || (approveFlag !== "0" && approveFlag !== "3")) {
                //非制单、审批退回状态不能编辑！
                idp.warn(idp.lang.get("EAO_SELECT_NOT_EDIT"));
            } else {
                var formState = {};
                eao.common.listEditCard(globalVariables.STYLEID, null, formState, "grid_main");
                return true;
            }
        } else {
            idp.warn(idp.lang.get("EAO_PUB_TIP_002"));
        }
    },
    /**
     * 超链接打开转租申请卡片
     * @param {*} rowdata 超链接
     * @returns true
     */
    linkCard: function (rowdata) {
        let formstate = {};
        eao.common.listViewCard(globalVariables.STYLEID, "", formstate, rowdata);
        return true;
    },
    /**
     * 提交按钮
     * @returns 提交结果
     */
    submitEx: function () {
        var options = {
            checkFile: true,
            checkFileOptions: {
                billcode: "CODE",
                filelimit: "EAOPOLICYPRICE",
            },
        };
        return eao.workflow.listSubmit(globalVariables.STYLEID, "grid_main", options);
    },
    /**
     * 撤回提交
     * @returns 撤回结果
     */
    cancelSubmitEx: function () {
        return eao.workflow.listCancelSubmit(globalVariables.STYLEID, "grid_main");
    },
    /**
     * 列表联查审批流程
     * @returns 查看结果
     */
    viewWorkProcess: function () {
        return eao.workflow.listView("", "PROCESSINSTANCEID");
    },
    /**
     * 附件
     */
    appendix: function () {
        var options = {
            isAmAppendix: true,
            activeTypeId: "EAOPOLICYPRICE",
        };
        eao.appendix.listOpenEis(options);
    },
    /**
     * 打印
     */
    print: function () {
        eao.print.listRuntime(globalVariables.STYLEID);
    },
    /**
     * 导入
     */
    importExcel: function () {
        var styleid = idp.list.getStyleID();
        idp.importExcel(styleid, "", function () {
            idp.func.refresh("grid_main");
        });
    },
    /**
     * 下载模板
     */
    downloadExcel: function () {
        var styleid = idp.list.getStyleID();
        idp.downLoadExcel(styleid, idp.lang.get("EAO_POLICYPRICE_EXCELMODEL") || "政策单价导入模板");
    },
    /**
     * 启用
     */
    enable: function (operation) {
        var row = idp.control.get("grid_main").getSelected();
        if (!row || !row.ID) {
            idp.warn(idp.lang.get("EAO_PUB_SELECTONE_OPERATE") || "请选择一条数据进行操作");
            return;
        }
        if (row.ENABLED !== "0" && operation === "1") {
            idp.warn(idp.lang.get("EAO_POLICYPRICE_DISENABLEONLY") || "停用状态才可以启用");
            return false;
        }
        if (row.ENABLED !== "1" && operation === "0") {
            idp.warn(idp.lang.get("EAO_POLICYPRICE_ENABLEONLY") || "启用状态才可以停用");
            return false;
        }
        var id = row.ID;
        var apiUrl = eao.http.getApiUrl("property", "/policyprice/enable");
        var param = {
            id: id,
            enabled: operation,
        }
        idp.loading(idp.lang.get("EAO_POLICYPRICE_OPERATING") || "操作中....");
        eao.http.post(apiUrl, param).then(function (result) {
            
            if (operation === "1") {
                idp.tips(idp.lang.get("EEAO_POLICYPRICE_ENABLESUCCESS") || "启用成功");
            }
            if (operation === "0") {
                idp.tips(idp.lang.get("EEAO_POLICYPRICE_DISABLESUCCESS") || "停用成功");
            }
            idp.loaded();
            idp.func.refresh("grid_main");
        }, function(result) {
            idp.loaded();
        });
    },
    /**
     * 调价
     */
    changePrice: function () {
        var row = idp.list.utils.getCheckedRows();
        if (row.length === 1) {
            var billStatus = row[0].BILLSTATUS;
            if ("2" !== billStatus) {
                idp.warn(idp.lang.get("EAO_POLICYPRICE_PASSONLY") || "审批通过状态才可以操作");
                return false
            }
            let formState = {
                addtype: "changeprice",
                originid: row[0].ID,
            }
            let formStateUrl = eao.common.formStateEncode(formState);
            var params = "&runtime=true&fdim=" + row[0].ACTIVETYPE + "&sdim=" + formStateUrl;
            eaopolicypricelistcontroller.addCardOverride(params);
        } else {
            idp.warn(idp.lang.get("EAO_PUB_SELECTONE_OPERATE"));
            return false;
        }
    },
    /**
     * idp列表新增重写，支持列表运行时仍取基础表单styleid
     * @param {String} params url参数
     */
    addCardOverride: function (params) {
        params = params || "";
        if (params.indexOf("&") !== 0) params = "&" + params;
        idp.utils.openurl(
            globalVariables.STYLEID,
            idp.lang.get("NEW") + idp.list.getTitle(),
            idp.func.buildURL(
                "/page/card/cardpreview.html?styleid=" +
                    globalVariables.STYLEID +
                    "&dataid=&status=add" +
                    params
            )
        );
    },
};