let globalVariables = {
    STYLEID: "3df6eb33-26d7-739e-7831-8b7d64710752",
};
idp.event.bind("loadData", function () {
    idp.vueDefine["vue_187203"].changeOriginId(idp.uiview.modelController.getSaveData()[0].data[0].ORIGINID);
});
idp.event.bind("domReady", function () {
    eao.usePolicyPriceTimelineComponent();
    eao.initVueState();
});

idp.event.bind("viewReady", function () {
    var viewMode = idp.utils.getQuery("viewMode");    
    let enableEdit = idp.utils.getQuery("workflowEdit") || "";
    if (viewMode === "wf") {
        // 审批流联查
        if (enableEdit === "true") {
            idp.control.toolbar.toggleBtns(
                "toolbar1",[
                    "baritem_add", 
                    "baritem_delete", 
                    "baritem_submit", 
                    "baritem_close",
                    "baritem_enable",
                    "baritem_disable",
                ],
                false
            );
        } else {
            //设置按钮状态
            idp.control.toolbar.toggleBtns("toolbar1", [
                "baritem_add",
                "baritem_modify",
                "baritem_save",
                "baritem_cancel",
                "baritem_delete",
                "baritem_submit",
                "baritem_close",
                "baritem_enable",
                "baritem_disable",
            ], false);
        }
    }
});

idp.event.bind("beforeCheck", function (e, data) {
    idp.uiview.endEdit();
    var rows = idp.control.get("grid_EAOPOLICYPRICEDETAIL").rows;
    if (rows.length < 1) {
        idp.warn(idp.lang.get("EAO_POLICYPRICE_DETIAILEMPTY") || "分录政策单价设置不得为空");
        return false;
    }
    rows.sort((a, b) => a.MINLEVEL - b.MINLEVEL);
    // 校验区间连续
    var minLevel;
    var maxLevel;
    var checkPoint;
    for (let i = 0; i < rows.length; i ++) {
        minLevel = rows[i].MINLEVEL;
        maxLevel = rows[i].MAXLEVEL;
        if (i === (rows.length - 1) && rows[i].MAXLEVEL) {
            idp.warn(idp.lang.get("EAO_POLICYPRICE_MAXLEVELEMPTY") || "最高用量上限需要为空，确保区间覆盖正无穷");
            return false;
        }
        if (i === 0) {
            if (minLevel !== 0) {
                idp.warn(idp.lang.get("EAO_POLICYPRICE_MINLEVELZERO") || "最低用量下限需要为0");
                return false;
            }
        } else {
            if (checkPoint !== minLevel) {
                idp.warn(idp.lang.get("EAO_POLICYPRICE_RANGENOTCONTINUE") || "检测到政策单价设置用量区间不连续，请确认");
                return false;
            }
        }
        checkPoint = maxLevel;
    }
});

idp.event.bind("afterAddData", function (e, data) {
    //经营组织
    var orgId = idp.store.get("orgId");
    var orgName = idp.store.get("orgName");
    if (orgId) {
        idp.uiview.setCtrlValue("EAOORG", orgId);
        idp.uiview.setCtrlValue("EAOORG_NAME", unescape(orgName));
    }

    // 调价跳转处理
    let addtype = idp.store.get("addtype") || "";
    if (addtype === "changeprice") {
        //租赁申请复制
        eaopolicypricecardcontroller.changePriceCopy();
        delete idp.store.getAll().addtype;
    }
});

idp.event.bind("beforeSave", function (e, data) {
    //驳回的单据保存后状态更新为制单
    if (data[0].data[0].BILLSTATUS == "3") {
        data[0].data[0].BILLSTATUS = "0";
        data[0].data[0].BILLSTATUS_NAME = idp.lang.get("制单");
    }
    return data;
});

var eaopolicypricecardcontroller = {
    addEx: function () {
        return eao.common.add();
    },
    editEx: function () {
        return eao.common.edit();
    },
    deleteEx: function () {
        return eao.common.delete();
    },
    submitEx: function () {
        var options = {
            checkFile: true,
            checkFileOptions: {
                billcode: "CODE",
                filelimit: "EAOPOLICYPRICE",
            },
        };
        return eao.workflow.cardSubmit(globalVariables.STYLEID, options);
    },
    cancelSubmitEx: function () {
        return eao.workflow.cardCancelSubmit(globalVariables.STYLEID);
    },
    viewWorkProcess: function () {
        return eao.workflow.cardView("PROCESSINSTANCEID");
    },
    //附件
    appendix: function () {
        var options = {
            isAmAppendix: true,
            activeTypeId: "EAOPOLICYPRICE",
        };
        eao.appendix.cardOpenEis(options);
    },
    //打印
    print: function () {
        eao.print.cardRuntime();
    },
    /**
     * 启用
     */
    enable: function (operation) {
        var mainData = idp.uiview.modelController.getSaveData()[0].data[0];
        if (mainData.ENABLED !== "0" && operation === "1") {
            idp.warn(idp.lang.get("EAO_POLICYPRICE_DISENABLEONLY") || "停用状态才可以启用");
            return false;
        }
        if (mainData.ENABLED !== "1" && operation === "0") {
            idp.warn(idp.lang.get("EAO_POLICYPRICE_ENABLEONLY") || "启用状态才可以停用");
            return false;
        }
        var id = mainData.ID;
        var apiUrl = eao.http.getApiUrl("property", "/policyprice/enable");
        var param = {
            id: id,
            enabled: operation,
        }
        idp.loading(idp.lang.get("EAO_POLICYPRICE_OPERATING") || "操作中....");
        eao.http.post(apiUrl, param).then(function (result) {
            if (operation === "1") {
                idp.tips(idp.lang.get("EEAO_POLICYPRICE_ENABLESUCCESS") || "启用成功");
            }
            if (operation === "0") {
                idp.tips(idp.lang.get("EEAO_POLICYPRICE_DISABLESUCCESS") || "停用成功");
            }
            idp.loaded();
            idp.uiview.reloadData();
        }, function(result) {
            idp.loaded();
        });
    },
    /**
     * 调价复制
     */
    changePriceCopy: function () {
        let originId = idp.store.get("originid") || "";
        idp.service.fetch(
                (IDPENV.API_RUNTIME || "/api/fastdweb/runtime4j/v1.0") + "/Card/getCardData",
                {
                    styleId: globalVariables.STYLEID,
                    id: originId,
                }
            )
            .done(function (result) {
                if (result.Data == null || typeof result.Data === "boolean") return;
                let datainfo = result.Data[0].data;
                var newdata = idp.uiview.modelController.deafaultData;
                let mainData = newdata[0].data[0];
                //更新主表ID
                datainfo.ID = mainData.ID;
                datainfo.BILLSTATUS = mainData.BILLSTATUS;
                datainfo.PROCESSINSTANCEID = mainData.PROCESSINSTANCEID;
                datainfo.CODE = "";
                datainfo.ENABLED = "0";
                //更新时间戳等字段
                datainfo.CREATEDBYID = mainData.CREATEDBYID;
                datainfo.CREATEDBY = mainData.CREATEDBY;
                datainfo.CREATEDON = mainData.CREATEDON;
                datainfo.LASTCHANGEDBY = mainData.LASTCHANGEDBY;
                datainfo.LASTCHANGEDON = mainData.LASTCHANGEDON;
                //申请明细 更新主键及关联即可
                let unitData = result.Data[1].data;
                for (let i = 0; i < unitData.length; i++) {
                    unitData[i].ID = eao.common.generateUUID();
                    unitData[i].PARENTID = datainfo.ID;
                }
                newdata[0].data = datainfo;
                newdata[1].data = unitData;

                idp.uiview.modelController.setModel(newdata); //界面赋值
                idp.uiview.setValue(); //界面赋值
                idp.express.triggerALL(); //刷新计算公式
            })
            .fail(function (error) {
                console.log("error:" + error);
                idp.error(idp.lang.get("EAO_POLICYPRICE_COPY_ERROR") || "获取调价单据失败");
            });
    },
};