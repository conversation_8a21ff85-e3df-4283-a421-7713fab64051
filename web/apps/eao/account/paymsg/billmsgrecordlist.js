var globalVariables = {
    STYLEID: "bb71be86-1b61-9293-12ed-89538da93c0f", //缴费通知记录表单id
};
idp.event.bind("viewReady", function () {
    //双击查看卡片
    idp.control.get("grid_main").bind("dblClickRow", function (rowData) {
        msgRecordList.listViewCard(rowData);
    });

    //方案注册事件
    idp.event.register("col_filter", "solutionSet", function (reg, filterInfo, data) {
        eao.common.getDefault().done(function (res) {
            var defaultInfo = res.appendData;
            var dueDateFieldName = "BUSINESSDATE";
            var orgNameFieldName = "EAOORG";
            //获取筛选框
            var dueDate;
            var orgName;
            data.forEach(function (value) {
                if (value.id === dueDateFieldName) {
                    dueDate = value;
                }
                if (value.id === orgNameFieldName) {
                    orgName = value;
                }
            });
            //系统预置方案
            if (filterInfo.IsSys == "1") {
                //方案有日期默认近1月
                if (dueDate.isShow) {
                    idp.control.get(dueDateFieldName).setValue(eao.date.getPreiod());
                }
                if (!!defaultInfo.org.id && orgName.isShow) {
                    idp.control
                        .get(orgNameFieldName)
                        .setValue(defaultInfo.org.id, defaultInfo.org.name);
                }
            }
            idp.func.refresh();
        });
    });
});
var msgRecordList = {
    print: function () {
        var rows = idp.list.utils.getCheckedRows();
        eao.print.listRuntime("", rows);
    },
    close: function () {
        return idp.func.close();
    },
    listViewCard: function (rowData) {
        var params = {
            runtime: true,
            fdim: "",
            sdim: "",
        };
        var formState = {
            orgName: rowData.EAOORG_NAME,
            orgId: rowData.EAOORG,
        };
        eao.common.listViewCard(globalVariables.STYLEID, params, formState, rowData);
    },
};
