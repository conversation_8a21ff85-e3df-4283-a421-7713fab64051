var globalVariables = {
    SENDTEXT_STYLEID: "eff7d5da-9683-616f-89f0-89a902f9fd3b", //发送内容表单id
    CONTRACT_STYLEID:"3a4d038a-3423-4545-9c78-cf76c85dd03d",//经营合同表单id
    EAOBILL_STYLEID:"ec19b73a-d685-1596-a809-063b30e56ea0",//账单表单id
};

idp.event.bind("viewReady", function () {
    //关联合同联查
    $("#CONTRACT_CODE").css("color", "#2A87FF");
    $("#CONTRACT_CODE").css("cursor", "pointer");
    $("#CONTRACT_CODE").on("click", function () {
        let mainData = idp.uiview.modelController.getMainRowObj();
        msgList.linkContract(mainData);
    });
});

var msgRecordController = {
    /**
     * 发送
     */
    send: function () {
        let id = idp.uiview.config.dataid;
        let url = eao.http.getApiUrl("account", "/billMsg/sendMessage");
        idp.loading(idp.lang.get("发送中") || "发送中");
        eao.http.post(url, id).then(
            function (result) {
                idp.uiview.reloadData();
                idp.uiview.refreshGrid("grid_EAOBILLMSG");
                idp.tips(idp.lang.get("发送完成") || "发送完成");
                idp.loaded();
            },
            function () {
                idp.loaded();
            }
        );
    },
    /**
     * 打印
     */
    print: function () {
        eao.print.cardRuntime();
    },
    close: function () {
        return idp.uiview.close();
    },
};
var msgList = {
    /**
     * 查看发送内容
     */
    viewText: function (index) {
        var row = idp.control.get("grid_EAOBILLMSG").getRow(index);
        var dialogUrl = idp.utils.buildURL(
            "/apps/fastdweb/views/runtime/page/card/cardpreview.html?status=view" +
                "&styleid=" +
                globalVariables.SENDTEXT_STYLEID +
                "&dataid=" +
                row.ID +
                "&formState=isDialog:1" +
                "&runtime=true"
        );
        idp.dialogUrl(
            {
                id: "dialog_ref_card",
                url: dialogUrl,
                width: 960,
                title: idp.lang.get("Eao_BillMsgWeb_SendMsg") || "发送内容",
                buttons: [],
            },
            function () {}
        );
    },
    linkContract: function (rowData) {
        var title = idp.lang.get("合同");
        var params = {
            runtime: true,
            fdim: "",
            sdim: "",
            status: "view",
            title: title,
            viewMode: "",
        };
        var formState = {};
        //子表超链接跟卡片上的超链接取数不一样，分别取数
        var contractId = rowData.BILLID_CONTRACT || rowData.CONTRACT;
        eao.common.linkCard(globalVariables.CONTRACT_STYLEID, params, formState, contractId);
    },
    linkEaoBill: function (rowData) {
        var title = idp.lang.get("账单管理");
        var params = {
            runtime: true,
            fdim: rowData.BILLID_ACTION,
            sdim: "",
            title: title,
        };
        var formState = {
            billaction: rowData.BILLID_ACTION,
            orgId: rowData.BILLID_EAOORG,
            signOrgId: rowData.BILLID_SIGNORG,
            contractSysType: rowData.BILLID_CONTRACTSYSTYPE,
        };
        eao.common.linkCard(globalVariables.EAOBILL_STYLEID, params, formState, rowData.BILLID);
    },
};
