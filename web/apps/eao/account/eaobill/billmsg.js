var globalVariables = {
    useNewPayMessage: "0",//是否启用新缴费通知菜单
};
idp.event.bind("viewReady", function () {
    let url = eao.http.getApiUrl("pub", "/common/getGlobalParamByConfigKey");
    eao.http.post(url, "EAO_UseNewBillPayMsg").then(function (result) {
        globalVariables.useNewPayMessage = result.appendData.configValue;
        if(globalVariables.useNewPayMessage === "0"){
            $("#BUSINESSDATE").parents(".table-item").hide();
            $("#SENDMSG").parents(".table-item").hide();
        }
    });
});

idp.event.bind("afterAddData", function (e, data) {
    //默认值赋值
    idp.control.get("SENDMSG").setValue("1");
});
