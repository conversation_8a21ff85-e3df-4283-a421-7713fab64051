
var currEmp = "";
var currEmpId = "";
var currEmpName = "";
var auth = "";

idp.event.bind("domReady", function(e) {
    getAuth();
    getEmploeeIdAndOrg();

    idp.event.register("grid_main", "beforeAddGridRow", function (e, table, row, id) {
        let currenttime = formatCurrentTime();
        row.CREATOR = currEmpId;
        row.CREATOR_NAME$LANGUAGE$ = currEmpName;
        row.CREATEDTIME = currenttime;
        row.LASTMODIFIER = currEmpId;
        row.LASTMODIFIEDTIME = currenttime;
        row.STATE = '1';
    });

    idp.event.register('grid_main', "beforeGridFilter", function (e, filter) {
        if(!filter){
            filter=[];
        }
        let fssc = idp.control.get("lookup_fssc").getValue();
        //本功能有共享中心权限，如果没有选择共享中心，则默认根据权限过滤
        if(!fssc) {
            if (!!auth) {
                filter.push({
                    "Left": "",
                    "Field": "FSUSER_FSSC",
                    "Operate": "in",
                    "IsExpress": false,
                    "Value": auth,
                    "Right": "",
                    "Logic": "and"
                });
            }
        } else {
            let fsscList = fssc.split(";");
            filter.push({
                "Left": "",
                "Field": "FSUSER_FSSC",
                "Operate": "in",
                "IsExpress": false,
                "Value": fsscList,
                "Right": "",
                "Logic": "and"
            });
        }
        let ywz = idp.control.get("lookup_ywz").getValue();
        if (!!ywz) {
            let ywzList = ywz.split(";");
            filter.push({
                "Left": "",
                "Field": "FSUSER_YWZ",
                "Operate": "in",
                "IsExpress": false,
                "Value": ywzList,
                "Right": "",
                "Logic": "and"
            });
        }
        let zyry = idp.control.get("lookup_zyry").getValue();
        if (!!zyry) {
            let zyryList = zyry.split(";");
            filter.push({
                "Left": "",
                "Field": "FSUSERID",
                "Operate": "in",
                "IsExpress": false,
                "Value": zyryList,
                "Right": "",
                "Logic": "and"
            });
        }
        let date = idp.control.get("input_date").getValue();
        if (!!date) {
            let dateList = idp.control.get("input_date").getValue().split(" - ");
            filter.push({
                "Left": "((",
                "Field": "STARTTIME",
                "Operate": ">=",
                "IsExpress": false,
                "Value": dateList[0] + " 00:00:00",
                "Right": "",
                "Logic": "and",
                "IsDate": true
            });
            filter.push({
                "Left": "",
                "Field": "STARTTIME",
                "Operate": "<=",
                "IsExpress": false,
                "Value": dateList[1] + " 23:59:59",
                "Right": ")",
                "Logic": "or",
                "IsDate": true
            });
            filter.push({
                "Left": "(",
                "Field": "STARTTIME",
                "Operate": "<=",
                "IsExpress": false,
                "Value": dateList[0] + " 00:00:00",
                "Right": "",
                "Logic": "and",
                "IsDate": true
            });
            filter.push({
                "Left": "",
                "Field": "ENDTIME",
                "Operate": ">=",
                "IsExpress": false,
                "Value": dateList[1] + " 23:59:59",
                "Right": ")",
                "Logic": "or",
                "IsDate": true
            })
            filter.push({
                "Left": "(",
                "Field": "ENDTIME",
                "Operate": ">=",
                "IsExpress": false,
                "Value": dateList[0] + " 00:00:00",
                "Right": "",
                "Logic": "and",
                "IsDate": true
            });
            filter.push({
                "Left": "",
                "Field": "ENDTIME",
                "Operate": "<=",
                "IsExpress": false,
                "Value": dateList[1] + " 23:59:59",
                "Right": "))",
                "Logic": "and",
                "IsDate": true
            });
        }
        return filter;
    });

    idp.event.register("grid_main", "beforeEdit", function (e, rowindex, rowdata, celldata) {

        var state = idp.uiview.fsmController.getState();

        if (state == "modify" || (state == "add" && rowdata.__status != 'add')) {
            if (celldata.id == 'FSUSERID_FSUSER_NAME$LANGUAGE$') {
                // ("不允许修改作业人员");
                idp.info(idp.lang.get('FSSPSetFront0296'));
                return false;
            }
            if (celldata.id == 'STARTTIME') {
                let currenttime = new Date();
                let rowStartTime = new Date(rowdata.STARTTIME);
                if (!!rowdata.STARTTIME && currenttime > rowStartTime) {
                    // ("早于当前时间的不允许修改");
                    idp.info(idp.lang.get('FSSPSetFront0297'));
                    return false;
                }
            }
            if (celldata.id == 'ENDTIME') {
                let currenttime = new Date();
                let rowEndTime = new Date(rowdata.ENDTIME);
                if (!!rowdata.ENDTIME && currenttime > rowEndTime) {
                    // ("早于当前时间的不允许修改");
                    idp.info(idp.lang.get('FSSPSetFront0297'));
                    return false;
                }
            }
        }
        return true;
    });

    idp.event.register("grid_main", "afterEndEdit", function (e,opts) {
        //每次修改休假开始时间或者休假结束时间的时候，都标记一下，每行的key为数据的id，里面的key分别为IFCHANGESTARTTIME 和 IFCHANGEENDTIME，1代表修改
        //此标记用于保存时候的校验：修改原有数据的情况下，判断"允许编辑的"休假开始时间或结束时间是否有小于当前时间的，这里记录一下“允许编辑的”数据是否被编辑过
        var row=opts.record;//行数据
        var column=opts.column;//列数据
        if (column.columnname == "STARTTIME") {
            let recordMap = idp.store.get(row.ID);
            if (!!recordMap) {
                recordMap.set("IFCHANGESTARTTIME", "1");
                idp.store.commit(row.ID, recordMap, true);
            } else {
                recordMap = new Map();
                recordMap.set("IFCHANGESTARTTIME", "1");
                idp.store.commit(row.ID, recordMap, true);
            }
        }
        if (column.columnname == "ENDTIME") {
            let recordMap = idp.store.get(row.ID);
            if (!!recordMap) {
                recordMap.set("IFCHANGEENDTIME", "1");
                idp.store.commit(row.ID, recordMap, true);
            } else {
                recordMap = new Map();
                recordMap.set("IFCHANGEENDTIME", "1");
                idp.store.commit(row.ID, recordMap, true);
            }
        }
    });

    idp.event.register("lookup_fssc", "beforeHelpFilter", function (e, p) {
        let filters = [];
        if (!!auth) {
            filters.push({
                "Left": "",
                "Field": "LSFSSC_ID",
                "Operate": "in",
                "IsExpress": false,
                "Value": auth,
                "Right": "",
                "Logic": "and"
            });
        }
        return filters;
    });

    idp.event.register("lookup_ywz", "beforeHelpFilter", function (e, p) {
        let filters = [];
        let fssc = idp.control.get("lookup_fssc").getValue();
        if (!!fssc) {
            filters.push({
                "Left": "",
                "Field": "FSYWZ_FSSC",
                "Operate": "in",
                "IsExpress": false,
                "Value": fssc.split(";"),
                "Right": "",
                "Logic": "and"
            });
        } else {
            if (!!auth) {
                filters.push({
                    "Left": "",
                    "Field": "FSYWZ_FSSC",
                    "Operate": "in",
                    "IsExpress": false,
                    "Value": auth,
                    "Right": "",
                    "Logic": "and"
                });
            }
        }
        return filters;
    });

    idp.event.register("lookup_zyry", "beforeHelpFilter", function (e, p) {
        let filters = [];
        let fssc = idp.control.get("lookup_fssc").getValue();
        if (!!fssc) {
            filters.push({
                "Left": "",
                "Field": "FSUSER_FSSC",
                "Operate": "in",
                "IsExpress": false,
                "Value": fssc.split(";"),
                "Right": "",
                "Logic": "and"
            });
        } else {
            if (!!auth) {
                filters.push({
                    "Left": "",
                    "Field": "FSUSER_FSSC",
                    "Operate": "in",
                    "IsExpress": false,
                    "Value": auth,
                    "Right": "",
                    "Logic": "and"
                });
            }
        }
        let ywz = idp.control.get("lookup_ywz").getValue();
        if (!!ywz) {
            filters.push({
                "Left": "",
                "Field": "FSUSER_YWZ",
                "Operate": "in",
                "IsExpress": false,
                "Value": ywz.split(";"),
                "Right": "",
                "Logic": "and"
            });
        }
        return filters;
    });

})

idp.event.bind("viewReady", function(e) {})


function getEmploeeIdAndOrg() {
    // idp.service.fetch("/api/fssp/ssp/v1.0/datacommon/employeeinfoapi/getemployeelistinfobygspuserid?userid=" + idp.context.get("UserId"), null, false, "get").done(function(data) {
    idp.service.fetch("/api/fssp/ssp/v1.0/datacommon/employeeinfoapi/getemployeeinfobygspuserid", idp.context.get("UserId"), false).done(function(data) {
        //成功回调方法
        if (data.result) {
            // if (data.value.userList.length > 0) {
                // currEmpId = data.value.userList[0];
            currEmp = data.value.user;
            currEmpId = currEmp.id;
            currEmpName = currEmp.name;
                return true;
            // }
        }
        // ("未获取到当前登录用户的行政人员信息");
        // alertNOX(idp.lang.get('FSSPSetFront0218'), 'close');
        //当前登陆用户没有共享中心权限，即将退出
        // idp.info(idp.lang.get('FSSPSetFront0218'));
        // return false;
    }).fail(function(data) {
        //失败回调方法
    });

}

//获取权限
function getAuth() {
    var params = {};
    params.authorizationId = "FSXJGL"; //权限对象中的标识
    params.operationId = "FSXJGLAuth"; //权限对象中数据操作标识
    params.authFields = ["DA_FSSC_FSSC"]; //权限对象中权限字段标识
    idp.service.fetch("/api/bf/df/v1.0/fssp/getfsspauth/getauthresult", params, false)
        .done(function(data) {
            if (!data || !data.result || !data.value) {
                // 当前用户无共享中心权限，无法操作该功能
                alertNOX(idp.lang.get('FSSPSetFront0140'), 'close');
                return;
            }
            if (!!data.value.DA_FSSC_FSSC) {
                if(!data.value.DA_FSSC_FSSC.hasAuth){
                    // 当前用户无共享中心权限，无法操作该功能
                    alertNOX(idp.lang.get('FSSPSetFront0140'), 'close');
                    return;
                }
                //只处理部分权限。无权限已经在上面处理；全部权限不需要过滤
                if (data.value.DA_FSSC_FSSC.authMsg == 'partAuth') {
                    auth = data.value.DA_FSSC_FSSC.filterSql;
                }
            }
        }).fail(function(data) {
        if (data) {
            if (data.message) {
                idp.error(data.message);
            } else if (data.responseText) {
                idp.error(data.responseText);
            }
        }
    });
}

function alertNOX(msg, close) {
    $.leeUIDefaults.Dialog.allowClose = false;
    idp.alert(msg, null, function () {
        if (close) {
            idp.uiview.close();
        }
    });
    $.leeUIDefaults.Dialog.allowClose = true;
}

function cancel() {
    //把所有存在idp.store里面的所有记录开始时间结束时间是否修改的标记，都清除掉
    let rows = idp.control.get("grid_main").rows;
    for (let i = 0; i < rows.length; i++) {
        let recordMap = idp.store.get(rows[i].ID);
        if (!!recordMap) {
            idp.store.commit(rows[i].ID, null, true);
        }
    }
    return idp.uiview.cancel();
}

function save() {
    //模拟鼠标点击其他区域，使光标焦点移出输入框，防止点击确定时获取不到输入框数据校验不通过
    $("#toolbar1").click();
    if (!checkBeforeSave()) {
        return;
    }
    //把所有存在idp.store里面的所有记录开始时间结束时间是否修改的标记，都清除掉
    let rows = idp.control.get("grid_main").rows;
    for (let i = 0; i < rows.length; i++) {
        let recordMap = idp.store.get(rows[i].ID);
        if (!!recordMap) {
            idp.store.commit(rows[i].ID, null, true);
        }
    }
    return idp.uiview.saveData();
}

function checkBeforeSave() {
    let rows = idp.control.get("grid_main").rows;
    for (let i = 0; i < rows.length; i++) {
        //只要不是未修改，不管是更新的还是新增的数据都要校验一下
        if (rows[i].__status != 'nochanged') {
            if (!rows[i].FSUSERID) {
                idp.info(idp.lang.get('FSSPSetFront0298'));
                // ("作业人员不允许为空");
                return false;
            }
            if (!rows[i].REASON) {
                idp.info(idp.lang.get('FSSPSetFront0299'));
                // ("休假事由不允许为空");
                return false;
            }
            if (!rows[i].STARTTIME) {
                idp.info(idp.lang.get('FSSPSetFront0300'));
                // ("休假开始时间不允许为空");
                return false;
            }
            if (!rows[i].ENDTIME) {
                idp.info(idp.lang.get('FSSPSetFront0301'));
                // ("休假结束时间不允许为空");
                return false;
            }
            if (rows[i].__status == 'update') {
                //修改原有数据的情况下，判断"允许编辑的"(这个需要每次修改的时候判断一下是否修改了开始时间结束时间，然后保存在idp.store里面)休假开始时间或结束时间是否有小于当前时间的
                var ctlmap = idp.store.get(rows[i].ID);
                if (!!ctlmap && ctlmap.get("IFCHANGESTARTTIME") == "1") {
                    let currenttime = new Date();
                    let rowStartTime = new Date(rows[i].STARTTIME);
                    if (!!rows[i].STARTTIME && currenttime > rowStartTime) {
                        // ("休假开始时间不允许小于当前时间");
                        idp.info(idp.lang.get('FSSPSetFront0302'));
                        return false;
                   }
                }
                if (!!ctlmap && ctlmap.get("IFCHANGEENDTIME") == "1") {
                    let currenttime = new Date();
                    let rowEndTime = new Date(rows[i].ENDTIME);
                    if (!!rows[i].ENDTIME && currenttime > rowEndTime) {
                        // ("休假结束时间不允许小于当前时间");
                        idp.info(idp.lang.get('FSSPSetFront0303'));
                        return false;
                    }
                }
            }
            if (rows[i].__status == 'add') {
                //如果是新增数据，检查休假开始时间或休假结束时间是否有小于当前时间的
                let currenttime = new Date();
                let rowStartTime = new Date(rows[i].STARTTIME);
                let rowEndTime = new Date(rows[i].ENDTIME);
                if (!!rows[i].STARTTIME && currenttime > rowStartTime) {
                    // ("休假开始时间不允许小于当前时间");
                    idp.info(idp.lang.get('FSSPSetFront0302'));
                    return false;
                }
                if (!!rows[i].ENDTIME && currenttime > rowEndTime) {
                    // ("休假结束时间不允许小于当前时间");
                    idp.info(idp.lang.get('FSSPSetFront0303'));
                    return false;
                }
            }
            //不管是新增数据还是编辑的数据，都要检查休假开始时间是否大于休假结束时间
            if (!!rows[i].STARTTIME && !!rows[i].ENDTIME) {
                let rowStartTime = new Date(rows[i].STARTTIME);
                let rowEndTime = new Date(rows[i].ENDTIME);
                if (rowStartTime > rowEndTime) {
                    // ("休假开始时间不允许大于休假结束时间");
                    idp.info(idp.lang.get('FSSPSetFront0304'));
                    return false;
                }
            }
            //检查当前作业人员其他“启用状态”的休假记录中是否与当前休假记录中的休假时间中存在交叉时间的
            let fsuserid = rows[i].FSUSERID;
            let filteredData = rows.filter(function(row) {
                return row.FSUSERID === fsuserid && row.STATE === '1';
            });
            let currowStartTime = new Date(rows[i].STARTTIME);
            let currowEndTime = new Date(rows[i].ENDTIME);
            for (let j = 0; j < filteredData.length; j++) {
                if (filteredData[j].ID === rows[i].ID) {
                    continue;
                }
                let rowStartTime = new Date(filteredData[j].STARTTIME);
                let rowEndTime = new Date(filteredData[j].ENDTIME);
                if ((rowStartTime <= currowStartTime && rowEndTime >= currowStartTime) || (rowStartTime <= currowEndTime && rowEndTime >= currowEndTime)) {
                    // ("当前作业人员在其他启用状态的休假记录中存在交叉时间，请检查");
                    idp.info(idp.lang.get('FSSPSetFront0305'));
                    return false;
                }
            }
        }
    }
    return true;
}

function deleteData() {
    let row = idp.control.get("grid_main").getSelected();
    if(!row){
        // 请选择一行数据!
        idp.info(idp.lang.get('FSSPSetFront0097'));
        return;
    }
    let currenttime = new Date();
    let rowStartTime = new Date(row.STARTTIME);
    let rowEndTime = new Date(row.ENDTIME);
    if ((!!row.STARTTIME && currenttime > rowStartTime) || (!!row.ENDTIME && currenttime > rowEndTime)) {
        // ("休假开始时间或休假结束时间小于当前时间时，不允许删除");
        idp.info(idp.lang.get('FSSPSetFront0306'));
        return;
    }
    return idp.uiview.deleteListTmp();
}

function enable(){
    let row = idp.control.get("grid_main").getSelected();
    if(!row){
        // 请选择一行数据!
        idp.info(idp.lang.get('FSSPSetFront0097'));
        return;
    }
    if(row.STATE == '1'){
        // 当前数据已是启用状态
        idp.info(idp.lang.get('FSSPSetFront0034'));
        return;
    }
    let rows = idp.control.get("grid_main").rows;
    let fsuserid = row.FSUSERID;
    let filteredData = rows.filter(function(row) {
        return row.FSUSERID === fsuserid && row.STATE === '1';
    });
    let currowStartTime = new Date(row.STARTTIME);
    let currowEndTime = new Date(row.ENDTIME);
    for (let j = 0; j < filteredData.length; j++) {
        if (filteredData[j].ID === row.ID) {
            continue;
        }
        let rowStartTime = new Date(filteredData[j].STARTTIME);
        let rowEndTime = new Date(filteredData[j].ENDTIME);
        if ((rowStartTime <= currowStartTime && rowEndTime >= currowStartTime) || (rowStartTime <= currowEndTime && rowEndTime >= currowEndTime)) {
            // (row.FSUSERID_FSUSER_NAME$LANGUAGE$ + "的相关休假记录启用后会导致多条记录中有休假时间交叉，不允许启用");
            idp.info(row.FSUSERID_FSUSER_NAME$LANGUAGE$ + idp.lang.get('FSSPSetFront0307'));
            return false;
        }
    }
    updateState(row.ID,"1");
}
function disable(){
    let row = idp.control.get("grid_main").getSelected();
    if(!row){
        // 请选择一行数据!
        idp.info(idp.lang.get('FSSPSetFront0097'));
        return;
    }
    if(row.STATE == '0'){
        // 当前数据已是停用状态
        idp.info(idp.lang.get('FSSPSetFront0033'));
        return;
    }
    updateState(row.ID,"0");
}

function updateState(mainid,state){
    let param = {};
    param.state = state;
    param.mainid = mainid;
    idp.service.fetch("/api/fssp/ssp/v1.0/setting/fsxjgl/updatestate", param, false).done(function(data) {
        //成功回调方法
        if (data.result) {
            // idp.tips("操作成功");
            if(state == '0'){
                idp.tips(idp.lang.get("FSSPSetFront0180"));
            }else if(state == "1"){
                idp.tips(idp.lang.get("FSSPSetFront0179"));
            }else {
                idp.tips(idp.lang.get("FSSPSetFront0166"));
            }
            return true;
        }
        idp.info(idp.lang.get("FSSPSetFront0328"));
        return false;
    }).fail(function(data) {
        //失败回调方法
        idp.error(idp.lang.get("FSSPSetFront0328"));
        return false;
    });
    idp.uiview.refreshMainDS();
}

function formatCurrentTime() {
    const date = new Date();

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
    const day = String(date.getDate()).padStart(2, '0');

    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

