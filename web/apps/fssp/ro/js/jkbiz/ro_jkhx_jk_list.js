idp.event.bind("domReady", function(e) {
    var urlParms = window.parent.idp.store.get("urlParms")||window.parent.openJKHXdilog.options.urlParms;
    var ygnm = urlParms.ygnm;
    var ygxm =urlParms.ygxm;
    var employees =urlParms.employees;
    //报账单位，根据报账单位过滤
    var dwid=urlParms.dwid;
    var jkdnmList = urlParms.jkdnmList;
    if(jkdnmList){
        idp.store.commit("jkdnmList", jkdnmList, true);
        parent.$('.lee-dialog-tc-inner').css('height','auto');
        parent.$('.lee-dialog-close').hide();
    }

    idp.store.commit("ygnm",ygnm);
    idp.store.commit("ygxm",ygxm);
    idp.store.commit("employees",employees);


    idp.event.register("grid_main", "beforeGridFilter", function (e, filter) {
        var ygnm = idp.store.get("ygnm");
        //var employees = idp.utils.jse(idp.store.get("employees"));
        var empl = "'" + idp.store.get("employees").replace(RegExp(";", "g"), "','") + "'";
        var employees = empl.split(',').map(function(item) {
            //return item.replace(/'/g, '');
            return item.replace(/[' ]/g, '');
        });

        if (ygnm) {
            filter.push({
                "Left": "",
                "Field": "RODJSQ_YGNM",
                "Operate": " in ",
                "IsExpress": false,
                "Value": employees,
                "Right": "",
                "Logic": "and"
            });
        }
        if (dwid) {
            filter.push({
                "Left": "",
                "Field": "ROJKFT_DWID",
                "Operate": " = ",
                "IsExpress": false,
                "Value": dwid,
                "Right": "",
                "Logic": "and"
            });
        }

        if(jkdnmList){
            filter.push({
                "Left": "",
                "Field": "RODJSQ_NM",
                "Operate": " in  ",
                "IsExpress": false,
                //"Value":idp.utils.jse(billcode),
                "Value": jkdnmList,
                "Right": "",
                "Logic": "and"
            });
        }
        if (filter.length > 0 && filter[filter.length - 1]) {
            var lastFilter = filter[filter.length - 1];
            if (lastFilter["Logic"] === "and") {
                lastFilter["Logic"] = "";
            }
            filter[filter.length - 1] = lastFilter;
        }
        return filter;
    });
    //加载数据前过滤数据
    idp.event.register("grid_main", "beforeGridInit", function(e, p) {
        if(jkdnmList){
            p.checkbox = false;
        }
        var urlParms = window.parent.idp.store.get("urlParms")||window.parent.openJKHXdilog.options.urlParms;
        var readOnly=urlParms.readOnly;
        if(readOnly){
            idp.control.get("toolbar1").toggleBtns(["baritem_add"], false);
        }
        p.onBeforeShowData = berforeLoadData;
    });

    idp.event.register("grid_main", "afterLoadData", function (e, p){
        //保存提交的样式，需要判断是不是新增借款核销打开的。
        if(idp.store.get('jkdnmList') && $("#jkhxControl_dialog")[0]) {
            $('#col_876036').css("display", "none");
            document.getElementById('layout_976234').appendChild($("#jkhxControl_dialog")[0]);
            $('#jkhxControl_dialog').show();
        }
    })

});

//显示加载数据之前
function berforeLoadData(data) {
    if(data&&data.Rows.length<=0)return;
    //借款核销已经加载过的数据
    idp.loading(idp.lang.get("FSROFront0338"));//表单加载中
    var jkdnmList = idp.store.get('jkdnmList');
    //已经加载的分摊内码
    var urlParms = window.parent.idp.store.get("urlParms")||window.parent.openJKHXdilog.options.urlParms;
    var hxHasLoad = urlParms.ftnm;
    var notselectedRows = [];
    if (hxHasLoad && hxHasLoad.length > 0) {
        //借款核销分摊内码数组
        var rows = data && data.Rows;
        $.each(rows, function(i, item) {
            if (hxHasLoad.indexOf(item.ROJKFT_NM) < 0) {
                notselectedRows.push(item);
            }
        })
    } else{
        notselectedRows =data.Rows;
    }
    var ft_jk={};
    //遍历获取分摊ID
    var ftIds = notselectedRows.map(function(item) { 
        ft_jk[item.ROJKFT_NM]=item.ROJKFT_BXNM;
        return item.ROJKFT_NM;
    });
    //获取分摊在途金额
    var jkhxftids = [];
    $.each(ftIds,(i,e)=>{
        if(parent.window.hxztData[e.ROJKHX_FTID]===undefined){
            jkhxftids.push(e);
        }
    });
    var ftztjeArr ={}
    if(jkhxftids.length>0){
        ftztjeArr = getjkhxztje(jkhxftids);
    }
    var ftztjeObj ={};
    var ztjeArr ={};
    if(ftztjeArr){
        $.each(ftztjeArr,function(i,item){
            var hxje =item.ROJKHX_HXJE;
            var jkbh = item.ROJKHX_JKBH;
            ftztjeObj[item.ROJKHX_FTID] =hxje;
            if(ztjeArr[item.ROJKHX_JKBH]){
                ztjeArr[item.ROJKHX_JKBH] = toFloat(ztjeArr[item.ROJKHX_JKBH]+hxje);
            }else{
                ztjeArr[item.ROJKHX_JKBH] =hxje;
            }
            //增加借款单内码
            var newBH=item.ROJKHX_JKBH+'-'+ft_jk[item.ROJKHX_FTID];
            if(ztjeArr[newBH]){
                ztjeArr[newBH] = toFloat(ztjeArr[newBH]+hxje);
            }else{
                ztjeArr[newBH] =hxje;
            }
        })
    }
    parent.window.hxztData=$.extend(parent.window.hxztData,ftztjeObj);
    parent.window.ztjeData=$.extend(parent.window.ztjeData,ztjeArr);
    ftztjeObj=parent.window.hxztData;
    ztjeArr=parent.window.ztjeData;

    var dataRows= [];
    //分摊在途金额合并到相关数据中
    var deleterojkhx =parent.idp.store.get('delterojkhx')||{};

    $.each(notselectedRows,function(i,item){
        var delteHXJE = deleterojkhx[item.ROJKFT_NM] ||0;

        item["ROJKFT_FTZT"] = ftztjeObj[item.ROJKFT_NM]?ftztjeObj[item.ROJKFT_NM]:0;
        item["ROJKFT_FTZT"] = accSub(item["ROJKFT_FTZT"],delteHXJE);

        var newBH=item.RODJSQ_BH+'-'+ft_jk[item.ROJKFT_NM];
        item["RODJSQ_ZTJE"] = ztjeArr[newBH]?ztjeArr[newBH]:0;
        item["RODJSQ_ZTJE"] = accSub(item["RODJSQ_ZTJE"],delteHXJE);

        item["ROJKFT_SYQK"] = accSub(accSub(item["ROJKFT_JE"],item["ROJKFT_FTZT"]),item["ROJKFT_YHK"]);
        item["RODJSQ_SYQK"] = accSub(accSub(item["RODJSQ_JKJE"],item["RODJSQ_ZTJE"]),item["RODJSQ_YHK"]);

        
        //添加判断：借款分摊金额=已还款金额+在途金额,借款分摊列表不显示
        if(toFloat(item["ROJKFT_FTZT"]+item["ROJKFT_YHK"]) < item["ROJKFT_JE"]){
            dataRows.push(item);
        }
        // else if(billcode){
        //     dataRows.push(item);
        // }
    })
    data.Rows =dataRows;
    data.Total = dataRows.length;

    if(jkdnmList) {
        //保存提交的样式，需要判断是不是新增借款核销打开的。
        var jkhxControlHtml2 = `<div id="jkhxControl_dialog" style="display: none;">`;
        jkhxControlHtml2 += `<div class="jkhxControl1" style="display: flex;
        flex-direction: column;margin: 10px;">`;
        if (dataRows && dataRows.length > 0) {
            dataRows.sort(function (a,b){
                if(a.RODJSQ_LXMC == b.RODJSQ_LXMC){
                    return b.RODJSQ_BH.localeCompare(a.RODJSQ_BH);
                }
                return a.RODJSQ_LXMC.localeCompare(b.RODJSQ_LXMC);
            })

            var groupsJE = {};
            var groupsZTJE = {};
            var groupsYHKJE = {};
            dataRows.forEach(function (item) {
                var groupName = item.SYMBOL == null ? "" : item.SYMBOL;
                var valueJE = item.ROJKFT_JE;
                var valueZTJE = item.ROJKFT_FTZT;
                var valueYHKJE = item.ROJKFT_YHK;
                if (!groupsJE[groupName]) {
                    groupsJE[groupName] = 0;
                }
                if (!groupsZTJE[groupName]) {
                    groupsZTJE[groupName] = 0;
                }
                if (!groupsYHKJE[groupName]) {
                    groupsYHKJE[groupName] = 0;
                }
                groupsJE[groupName] += valueJE;
                groupsZTJE[groupName] += valueZTJE;
                groupsYHKJE[groupName] += valueYHKJE;
            });

            jkhxControlHtml2 += `<div class="jkhxControlTips" style="display: flex;align-items: center;">
<span class="l-icon l-icon-message_warning" style="color: #FE9439;"></span>
<p style="font-size: 16px;">您目前有${jkdnmList.length}张待还款单据，请您先全部核销后再继续操作。</p>
</div>`;

            jkhxControlHtml2 += `<div class="jkhxControlSUMJE" style="display: flex;margin: 5px;">`;
            //借款金额合计
            jkhxControlHtml2 += `<div class="jkhxControlSUMjkje" style="display: flex;width: 300px;margin: 10px;flex-direction: column;">`;
            jkhxControlHtml2 += `<div class="SUMjkje" style="margin: 5px;">
                        <p style="">借款金额</p></div>`
            let jkjeTitle = '';
            for (var groupName in groupsJE) {
                if (groupsJE.hasOwnProperty(groupName)) {
                    jkjeTitle += groupName + idp.utils.currency(groupsJE[groupName], 2,true) + ";";
                }
            }
            jkhxControlHtml2 += `<div class="SUMjkje" title=${jkjeTitle} style="margin: 5px;display: flex;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">`
            for (var groupName in groupsJE) {
                if (groupsJE.hasOwnProperty(groupName)) {
                    jkhxControlHtml2 += `<p style="color: #FF6C29;font-size: 22px;">${groupName + idp.utils.currency(groupsJE[groupName], 2,true) + ";"}</p>`;
                }
            }
            jkhxControlHtml2 += `</div></div>`;
            //在途金额合计
            jkhxControlHtml2 += `<div class="jkhxControlSUMztje" style="display: flex;width: 300px;margin: 10px;flex-direction: column;">`;
            jkhxControlHtml2 += `<div class="SUMztje" style="margin: 5px;">
                        <p style="">在途金额</p></div>`
            let ztjeTitle = '';
            for (var groupName in groupsZTJE) {
                if (groupsZTJE.hasOwnProperty(groupName)) {
                    ztjeTitle += groupName + idp.utils.currency(groupsZTJE[groupName], 2,true) + ";";
                }
            }
            jkhxControlHtml2 += `<div class="SUMztje" title=${ztjeTitle} style="margin: 5px;display: flex;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">`
            for (var groupName in groupsZTJE) {
                if (groupsZTJE.hasOwnProperty(groupName)) {
                    jkhxControlHtml2 += `<p style="color: #51BD78;font-size: 22px;">${groupName + idp.utils.currency(groupsZTJE[groupName], 2,true) + ";"}</p>`;
                }
            }
            jkhxControlHtml2 += `</div></div>`;
            //已还金额合计
            jkhxControlHtml2 += `<div class="jkhxControlSUMykhje" style="display: flex;width: 300px;margin: 10px;flex-direction: column;">`;
            jkhxControlHtml2 += `<div class="SUMykhje" style="margin: 5px;">
                        <p style="">已还金额</p></div>`
            let yhkjeTitle = '';
            for (var groupName in groupsYHKJE) {
                if (groupsYHKJE.hasOwnProperty(groupName)) {
                    yhkjeTitle += groupName + idp.utils.currency(groupsYHKJE[groupName], 2,true) + ";";
                }
            }
            jkhxControlHtml2 += `<div class="SUMykhje" title=${yhkjeTitle} style="margin: 5px;display: flex;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">`
            for (var groupName in groupsYHKJE) {
                if (groupsYHKJE.hasOwnProperty(groupName)) {
                    jkhxControlHtml2 += `<p style="color: #2A87FF;font-size: 22px;">${groupName + idp.utils.currency(groupsYHKJE[groupName], 2,true) + ";"}</p>`;
                }
            }
            jkhxControlHtml2 += `</div></div>`;

            jkhxControlHtml2 += `</div>`;

            for (var i = 0; i < dataRows.length; i++) {
                var symbol = dataRows[i].SYMBOL == null ? "" : dataRows[i].SYMBOL;
                jkhxControlHtml2 += `<div class="jkhxControl2" style="background-image: linear-gradient(0deg, #F7FBFF 0%, #FFFFFF 100%);
border: 1px solid rgba(42,135,255,0.10);border-radius: 6px;display: flex;margin: 5px;">`;

                jkhxControlHtml2 += `<div class="djbh" style="width: 200px;margin: 10px;">
                        <p style="margin: 5px;">单据编号</p>`;
                if(dataRows[i].RODJSQ_SFCS == '1') {
                    jkhxControlHtml2 += `<a style="margin: 5px;color: rgba(0, 0, 0, 0.75);">${dataRows[i].RODJSQ_BH}</a>
                      </div>`;
                }else {
                    jkhxControlHtml2 += `<a href="javascript:void(0);" onclick="
                        linkDJ({
            BILLID:'${dataRows[i].RODJSQ_NM}',
            FORMTYPE:'${dataRows[i].RODJSQ_SQLX}',
            FORMTYPENAME:'${dataRows[i].RODJSQ_SQLXMC}',
            BILLBH:'${dataRows[i].RODJSQ_BH}',
            OPERATION:'view'
        })" style="margin: 5px;">${idp.utils.safeXSS(dataRows[i].RODJSQ_BH)}</a>
                      </div>`;
                }
                jkhxControlHtml2 += `<div class="jklx" style="width: 150px;margin: 10px;">
                        <p style="margin: 5px;">借款类型</p>
                        <p style="margin: 5px;">${dataRows[i].RODJSQ_SQLXMC}</p>
                      </div>`;
                jkhxControlHtml2 += `<div class="jkje" style="width: 150px;margin: 10px;">
                        <p style="margin: 5px;">借款金额</p>
                        <p style="margin: 5px;">${symbol + idp.utils.currency(dataRows[i].ROJKFT_JE, 2,true)}</p>
                      </div>`;
                jkhxControlHtml2 += `<div class="ztje" style="width: 150px;margin: 10px;">
                        <p style="margin: 5px;">在途金额</p>
                        <p style="margin: 5px;">${symbol + idp.utils.currency(dataRows[i].ROJKFT_FTZT, 2,true)}</p>
                      </div>`;
                jkhxControlHtml2 += `<div class="yhkje" style="width: 150px;margin: 10px;">
                        <p style="margin: 5px;">已还款</p>
                        <p style="margin: 5px;">${symbol + idp.utils.currency(dataRows[i].ROJKFT_YHK, 2,true)}</p>
                      </div>`;
                jkhxControlHtml2 += `<div class="jkrq" style="width: 150px;margin: 10px;">
                        <p style="margin: 5px;">借款日期</p>
                        <p style="margin: 5px;">${idp.utils.dateFormat(dataRows[i].RODJSQ_RQ, "yyyy-MM-dd")}</p>
                      </div>`;
                jkhxControlHtml2 += `<div class="jksm" style="flex:1;margin: 10px;overflow: hidden;white-space: nowrap;">
                        <p style="margin: 5px;">借款说明</p>
                        <p title=${dataRows[i].RODJSQ_ZY} style="margin: 5px;">${dataRows[i].RODJSQ_ZY}</p>
                      </div>`;
                jkhxControlHtml2 += `</div>`;
            }
        }

        jkhxControlHtml2 += `</div></div>`;
        if ($('#jkhxControl_dialog').length > 0) {
            $('#jkhxControl_dialog').remove();
        }
        $('body').append(jkhxControlHtml2);
    }

    idp.loaded();
    return data;
}
/**
 * 获取在途金额
 * addby yxy
 * @param ftIds
 */
function getjkhxztje(ftIds){
    if(ftIds&&ftIds.length<=0){return;}
    //获取在途金额
    var ztjeArr = [];
    var param ={
        ftnmStr:ftIds,
        // RODJNM:parent.idp.uiview.modelController.dataid
    };
    idp.service.fetch("/api/fssp/rs/v1.0/setting/jkhx/getjkhxztje",param,false).done(function(data) {
        if (data == null || !data.result) {
            idp.error(data.message);
            return false;
        }
        ztjeArr = data.value;
    }).fail(function(data) {
        return false;
    });
    return ztjeArr;
}

/**
 * 联查借款单（借款单差旅申请单）
 * @param rowdata
 */
function linkJKD(rowdata){
    linkDJ({
        BILLID:rowdata.RODJSQ_NM,
        FORMTYPE:rowdata.RODJSQ_SQLX,
        FORMTYPENAME:rowdata.RODJSQ_SQLXMC,
        BILLBH:rowdata.RODJSQ_BH,
        OPERATION:'view'
    })
}
