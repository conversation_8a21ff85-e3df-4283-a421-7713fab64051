
var schemenm = '';
var ifEditable = false;
isAuthEnable();
idp.event.bind("domReady", function(e) {
    lxnm = idp.utils.getQuery("lxnm");
    idp.store.commit("lxnm",idp.utils.getQuery("lxnm"));

    // 停用方案红色显示
    idp.event.register("grid_main", "beforeGridInit", function (e, p) {
        p.rowClsRender = function (rowdata) {
            if (rowdata.STATES == '1' || rowdata.STATES == 1) {
                return "red";
            }
        }
    });
    // 禁用按钮
    idp.event.register("IFUSEDALLUNIT", "selected", function (e, value) {
        if (idp.control.get("IFUSEDALLUNIT").getValue() == 1) {
            //勾选 “应用全部单位” 后，子表的两个按钮不能使用  ，并清空已应用的部分单位
            idp.control.toolbar.setDisabled("layout_detail_toolbar_0", ["baritem_applyUnit", "baritem_removeRow"]);
            var old = JSON.parse(JSON.stringify(idp.control.get('grid_FSROSQUSEDCOMPANY').rows));
            idp.uiview.setTableDataWithTrigger('FSROSQUSEDCOMPANY', {Rows: []});
            idp.control.get('grid_FSROSQUSEDCOMPANY').deletedRows = old;
        }else{
            idp.control.toolbar.setEnabled("layout_detail_toolbar_0", ["baritem_applyUnit", "baritem_removeRow"]);
        }
    });

    let dwAuths = idp.store.get("dwAuths");
    // 列表过滤
    idp.event.register("grid_main", "beforeGridFilter", function(e, filter) {
        filter.push({
            "Left": "",
            "Field": "SQTYPE",
            "Operate": " = ",
            "IsExpress": false,
            "Value": lxnm,
            "Right": "",
            "Logic": "and"
        });

        let ifShow = idp.control.get("checkbox_IFSHOW").getValue();// 显示停用
        if (ifShow == '0') {
            filter.push({
                "Left": "",
                "Field": "STATES",
                "Operate": " = ",
                "IsExpress": false,
                "Value": 0,
                "Right": "",
                "Logic": "and"
            });
        }
        if (idp.store.get("authEnable") && dwAuths != 'allAuth') {
            filter.push({
                "Left": "(",
                "Field": "MAINTENUNIT",
                "Operate": "in",
                "IsExpress": false,
                "Value": dwAuths,
                "Right": "",
                "Logic": "or"
            });
            filter.push({
                "Left": "",
                "Field": "MAINTENUNIT",
                "Operate": "isnull",
                "IsExpress": false,
                "Value": "",
                "Right": ")",
                "Logic": "and"
            });
        }
        // 未启用 直接显示全部方案
        // if (!idp.store.get("authEnable")) { //未启用单位权限，默认加载维护单位为空的方案
        //     filter.push({
        //         "Left": "",
        //         "Field": "MAINTENUNIT",
        //         "Operate": "isnull",
        //         "IsExpress": false,
        //         "Value": "",
        //         "Right": "",
        //         "Logic": "and"
        //     });
        // }
        return filter;
    });

    // 维护单位帮助过滤
    idp.event.register("MAINTENUNIT_NAME", "beforeHelpFilter", function (e, value, text, obj){
        var filter = [];
        if (dwAuths != 'allAuth') {
            filter.push({
                "Left": "",
                "Field": "id",
                "Operate": "in",
                "IsExpress": false,
                "Value": dwAuths,
                "Right": "",
                "Logic": "and",
                "IsDate": false
            });
        }
        return filter;
    });
    idp.event.register("lookup_MAINTAINUNIT", "beforeHelpFilter", function (e, value, text, obj){
        var filter = [];
        if (dwAuths != 'allAuth') {
            filter.push({
                "Left": "",
                "Field": "id",
                "Operate": "in",
                "IsExpress": false,
                "Value": dwAuths,
                "Right": "",
                "Logic": "and",
                "IsDate": false
            });
        }
        return filter;
    });
});

idp.event.bind("viewReady", function(e, index, id, tabid){
    // 隐藏维护单位控件
    let control = idp.control.get("lookup_MAINTAINUNIT");
    let control2 = idp.control.get("MAINTENUNIT_NAME");

    if (!idp.store.get("authEnable") && control && control.element) {
        $(control.element).parents(".table-item").hide();
    }
    if (control2 && control2.element) {
        if (!idp.store.get("authEnable")) {
            $(control2.element).parents(".table-item").hide();
        }else{
            $('#lbl_MAINTENUNIT_NAME').before('<span class="red" style="color:red; vertical-align: middle; line-height: 16px;">*  </span>');
        }

    }
});

// 新增后
idp.event.bind("afterAddData", function(e, data){
    idp.uiview.setCtrlValue("STATES", "0"); // 默认启用
    idp.uiview.setCtrlValue("SQTYPE", lxnm);
    idp.uiview.setCtrlValue("IFPREPARED", "0"); // 非预制方案
});

// 编辑后
idp.event.bind("afterEdit", function (e, data) {
    // 禁用应用单位
    let ifUsedAllUnit = idp.control.get("IFUSEDALLUNIT").getValue();
    if (ifUsedAllUnit == '1') {
        idp.control.toolbar.setDisabled("layout_detail_toolbar_0", ["baritem_applyUnit", "baritem_removeRow"]);
    } else {
        idp.control.toolbar.setEnabled("layout_detail_toolbar_0", ["baritem_applyUnit", "baritem_removeRow"]);
    }
    // 预制方案禁止修改编号和名称
    let maindata = idp.uiview.modelController.getMainRowObj();
    if (maindata && maindata.IFPREPARED == '1') {
        idp.control.get("SCHEMECODE").setDisabled();
        idp.control.get("SCHEMENAME").setDisabled();
    } else {
        idp.control.get("SCHEMECODE").setEnabled();
        idp.control.get("SCHEMENAME").setEnabled();
    }
});

// 保存前校验
idp.event.bind("beforeCheck", function (e, data) {
    let MAINTENUNIT = idp.uiview.modelController.getMainRowObj().MAINTENUNIT || '';
    let MAINTENUNIT_NAME = idp.uiview.modelController.getMainRowObj().MAINTENUNIT_NAME || '';
    if (idp.store.get("authEnable") && !(MAINTENUNIT && MAINTENUNIT_NAME)) {
        idp.warn(idp.lang.get("FSROSetFront0747"));   // 维护单位必填！
        return false;
    }

    let flag = false;
    let schemeCode = idp.uiview.modelController.getMainRowObj().SCHEMECODE;
    let schemeName = idp.uiview.modelController.getMainRowObj().SCHEMENAME;
    let applyUnits = idp.control.get("grid_FSROSQUSEDCOMPANY").rows;
    let applyUnitIDs = applyUnits.length > 0 ? applyUnits.map(item => item.COMPANYID) : [];
    let params = {
        LXNM: lxnm,
        NM: schemenm,
        SCHEMECODE: schemeCode,
        SCHEMENAME: schemeName,
        APPLYUNIT: applyUnitIDs,
        IFUSEDALLUNIT: idp.control.get("IFUSEDALLUNIT").getValue() || '0',
    };
    idp.service.fetch("/api/fssp/rs/v1.0/setting/fsroapplyamoutsetapi/fsroapplyamoutsetsavecheck", params, false).done(function(data) {
        if (data.result) {
            flag = true;
        }else{
            idp.info(data.message);
        }
    });
    return flag;
});

idp.event.bind("loadData", function (e, data) {
    // 停用方案禁用编辑
    var mainData = idp.control.get("grid_main").getSelected();
    if (mainData) {
        if (mainData.STATES == '1') {
            idp.control.toolbar.setDisabled("toolbar1", ["baritem_modify"]);
        }else{
            idp.control.toolbar.setEnabled("toolbar1", ["baritem_modify"]);
        }
    }
    var schemeNM = '';
    if (!Array.isArray(data)) { //新增时data是对象类型 从data里拿schemenm
        schemeNM = data.ID;
    }else {
        var selectRow = idp.control.get("grid_main").getSelected();
        schemeNM = selectRow.ID;
    }
    schemenm = schemeNM;
});

// 取消后
idp.event.bind("afterCancel", function () {
    idp.control.toolbar.setEnabled("toolbar1", ["baritem_enable", "baritem_disable"]);
});

// 新增方案
function add() {
    var defer = $.Deferred();
    let whdw = idp.control.get("lookup_MAINTAINUNIT").getValue();
    if(!whdw && idp.store.get("authEnable") == true){
        let filter = [];
        let dwAuths = idp.store.get("dwAuths");
        if (dwAuths != 'allAuth') {
            filter.push({
                "Left": "",
                "Field": "id",
                "Operate": "in",
                "IsExpress": false,
                "Value": dwAuths,
                "Right": "",
                "Logic": "and",
                "IsDate": false
            });
        }
        idp.lookup(dwOptions(false, filter), function (res) {
            idp.control.get("lookup_MAINTAINUNIT").setValue(res[0].ID);
            idp.control.get("lookup_MAINTAINUNIT").setText(res[0].NAME$LANGUAGE$);
            idp.uiview.add();
            idp.uiview.setCtrlValue("MAINTENUNIT", res[0].ID);
            idp.uiview.setCtrlValue("MAINTENUNIT_CODE", res[0].CODE);
            idp.uiview.setCtrlValue("MAINTENUNIT_NAME", res[0].NAME$LANGUAGE$);
            defer.resolve(true);
            return defer.promise();
        });
    }else{
        idp.uiview.add();   // 新增后默认赋值维护单位
        if (whdw) {
            let filter = [
                {
                    "Left": "",
                    "Field": "ID",
                    "Operate": "=",
                    "IsExpress": false,
                    "Value": whdw,
                    "Right": "",
                    "Logic": ""
                }
            ];
            regetQueryData('c37d6a21-2f70-4dbd-aead-6c65abb734c6', filter).done(function (helpdata) {
                if (helpdata.Data && helpdata.Data.Rows) {
                    let res = helpdata.Data.Rows[0];
                    idp.uiview.setCtrlValue("MAINTENUNIT", whdw);
                    idp.uiview.setCtrlValue("MAINTENUNIT_CODE", res.CODE);
                    idp.uiview.setCtrlValue("MAINTENUNIT_NAME", res.NAME$LANGUAGE$);
                }
            });
        }
        defer.resolve(true);
    }
    return defer.promise();
}

// 启用停用
function ifEnable(enable) {
    var mainData = idp.control.get("grid_main").getSelected();
    if(!mainData){
        idp.info(idp.lang.get("FSROSetFront0067")); //请选中一条数据
        return false;
    }
    if (mainData.STATES == 0 && enable == 0) {
        idp.info(idp.lang.get("FSPubFront0045"));//已经启用，无需再次启用
        return false;
    }
    if (mainData.STATES == 1 && enable == 1) {
        idp.info(idp.lang.get("FSPubFront0046"));//已经停用，无需再次停用
        return false;
    }
    let applyUnits = idp.control.get("grid_FSROSQUSEDCOMPANY").rows;
    let applyUnitIDs = applyUnits.length > 0 ? applyUnits.map(item => item.COMPANYID) : [];
    let params = {
        SCHEMENM: schemenm,
        STATES: enable,
        LXNM: idp.utils.getQuery("lxnm"),
        APPLYUNIT: applyUnitIDs,
        IFUSEDALLUNIT: idp.control.get("IFUSEDALLUNIT").getValue() || '0',
    };
    idp.service.fetch("/api/fssp/rs/v1.0/setting/fsroapplyamoutsetapi/ifenbaleapplyamoutsetscheme", params, false).done(function(data) {
        if (data.result) {
            enable == 0 && idp.tips(idp.lang.get("FSROSetFront0144"));//启用成功
            enable == 1 && idp.tips(idp.lang.get("FSROSetFront0146"));//停用成功
        } else {
            if (data.message) {
                idp.info(data.message);
            }else{
                enable == 0 && idp.error(idp.lang.get("FSPubFront0044")); //启用失败
                enable == 1 && idp.error(idp.lang.get("FSPubFront0027"));//停用失败
            }
        }
    });
    idp.uiview.refreshGrid('grid_main');
}

// 新增应用单位
function addApplyUnit() {
    // 过滤已选项
    let rows = idp.control.get("grid_FSROSQUSEDCOMPANY").rows;
    let dwids = rows.length > 0 ? rows.map(item => item.COMPANYID) : [];
    let filter = [];
    if (dwids.length > 0) {
        let count = Math.ceil(dwids.length / 900);
        for (let i = 0; i < count; i++) {
            filter.push({
                "Left": "",
                "Field": "id",
                "Operate": "not in",
                "IsExpress": false,
                "Value": dwids.slice(900 * i, 900 * (i + 1)),
                "Right": "",
                "Logic": "and"
            });
        }
    }
    let dwList = rows;
    var old = JSON.parse(JSON.stringify(idp.control.get('grid_FSROSQUSEDCOMPANY').deletedRows));
    idp.lookup(dwOptions(true, filter), function (res) {
        for (var i = 0; i < res.length; i++) {
            dwList.push({
                ID: Guid.NewGuid().ToString(),
                COMPANYID: res[i].ID,
                COMPANYID_CODE: res[i].CODE,
                COMPANYID_NAME: res[i].NAME$LANGUAGE$,
                SQTYPE: lxnm,
                SCHEMENM: schemenm,
                __status:'add'
            });
            // 影响性能
            // idp.control.get("grid_FSROSCHUSEDCOMPANY").addRow({
            //     ID: Guid.NewGuid().ToString(),
            //     COMPANYID: res[i].ID,
            //     COMPANYID_CODE: res[i].CODE,
            //     COMPANYID_NAME: res[i].NAME$LANGUAGE$,
            //     DJLX: lxnm,
            //     SCHEMENM: schemenm,
            // });
        }
        idp.uiview.setTableDataWithTrigger('FSROSQUSEDCOMPANY', {Rows: dwList});
        idp.control.get('grid_FSROSQUSEDCOMPANY').deletedRows = old;
        idp.uiview.modelController.setGridChange('grid_FSROSQUSEDCOMPANY', dwList);
    });
}

// 批量删除应用单位
function deleteApplyUnit() {
    var data = idp.control.get("grid_FSROSQUSEDCOMPANY").selected;
    if (data.length <= 0) {
        idp.info(idp.lang.get("FSROSetFront0115"));//请选择需要删除的数据
        return false;
    }
    return idp.control.get("grid_FSROSQUSEDCOMPANY").deleteSelectedRow();
}

function dwOptions(isMul=false,filter=[]) {
    let p = {
        helpID: 'f51245b1-5bf9-881a-2604-df52dffc812e',
        height: '80%',
        width: '40%',
        isMul: isMul, //是否多选
        autoChild: true,
        childOnly: false,
        async: true, //异步加载
        isTotal: true, //构造完整树
        getFilter: function () {
            return filter;
        }
    };
    return p;
}

function regetQueryData(sqlId, fields) {
    var params = {
        sqlId: sqlId,
        orders: [],
        fields: fields,
        page: 0,
        pageSize: 0,
        bizId: '',
        bizOpId: ''
    }
    idp.service.setCurrentStyleId(sqlId);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
}

// 单位权限
function isAuthEnable() {
    idp.service.fetch("/api/bf/df/v1.0/fssp/getfsspauth/getauthenable", {
        authObject: "FSROFJGK",
        dwAuthFiled: "DA_RS_BZDW"
    }, false).done(function (data) {
        if (data.result && data.value) {    // 已启用
            idp.store.commit("authEnable", true, true);
            let params = {};
            params.authObject = "FSROFJGK";
            params.ywObject = "FSROFJGK";
            params.dwAuthFiled = "DA_RS_BZDW";
            idp.service.fetch("/api/bf/df/v1.0/fssp/getfsspauth/getallauthorityencry", params, false)
                .done(function (data) {
                    if (data.result && data.value) {
                        if (!data.value.dwAuths || data.value.dwAuths == "noAuth") {
                            //data.value.dwAuths = idp.utils.jse("''");
                            data.value.dwAuths = [''];
                        }
                        idp.store.commit("dwAuths", data.value.dwAuths, true);
                    }
                }).fail(function (data) {
                if (data) {
                    if (data.message) {
                        idp.error(data.message);
                    } else if (data.responseText) {
                        idp.error(data.responseText);
                    }
                }
            });
        } else {
            idp.store.commit("authEnable", false, true);
        }
    }).fail(function (data) {
        if (data) {
            if (data.message) {
                idp.error(data.message);
            } else if (data.responseText) {
                idp.error(data.responseText);
            }
        }
    })
}