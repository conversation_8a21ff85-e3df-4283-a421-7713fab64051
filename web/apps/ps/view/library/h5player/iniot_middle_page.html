<!DOCTYPE html>
<html>
<head>
    <title>这是个标题</title>
    <script type="text/javascript" src="/platform/runtime/common/web/gsprtf/gsp.rtf.core.js"></script>
    <script type="text/javascript">
        window.iotJumpService = window.iotJumpService || {};
        iotJumpService.iot = iotJumpService.iot || {};

        iotJumpService.iot = (function (iot, win) {

            /**
             * 从当前window url hash中获取query参数
             * hash 在路由场景中是通用方式
             * url: xxx/index.html#/x/x?query
             * @param {query 参数名} paraName
             */
            var hashQuery = function (paraName) {
                var url = win.location.hash;
                var arrObj = url && url.split('?');
                if (arrObj && arrObj.length > 1) {
                    var arrPara = arrObj[1].split('&');
                    var arr;
                    for (var i = 0; i < arrPara.length; i++) {
                        arr = arrPara[i].split('=');
                        if (arr != null && arr[0] === paraName) {
                            return arr[1];
                        }
                    }
                    return '';
                } else {
                    return '';
                }
            }

            var getQueryString = function (key) {
                var reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)", "i");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]);
                return null;
            }

            /**
             * 无路由信息时，url无hash值，直接取url中的query
             * url: xxx/index.html?query
             * @param {query 参数名} paraName
             */
            var urlQuery = function (paraName) {
                var result = location.search.match(new RegExp("[\?\&]" + paraName + "=([^\&]+)", "i"));
                if (result == null || result.length < 1) {
                    return "";
                }
                return result[1];
            }

            var objCopy = function (target) {
                var result;
                if (typeof target === 'object') {
                    if (Array.isArray(target)) {
                        result = [];
                        for (var i in target) {
                            result.push(objCopy(target[i]))
                        }
                    } else if (target === null) {
                        result = null;
                    } else if (target.constructor === RegExp) {
                        result = target;
                    } else {
                        result = {};
                        for (var i in target) {
                            result[i] = objCopy(target[i]);
                        }
                    }
                } else {
                    result = target;
                }
                return result;
            }

            /**
             * rest 公共服务封装
             */
            iot.rest = (function (rest) {

                var setHeaders = function (http, headers) {
                    if (!headers) {
                        return;
                    }
                    Object.entries(headers).forEach((entry) => {
                        const name = entry[0];
                        const value = entry[1];
                        http.setRequestHeader(name, value);
                    })
                }
                var request = function (method, url, headers, body) {
                    const http = new XMLHttpRequest();
                    const promise = new Promise((resolve, reject) => {
                        http.onreadystatechange = () => {
                            if (http.readyState !== 4) {
                                return;
                            }
                            if (http.status >= 200 && http.status < 300) {
                                if (http.responseText === undefined) {
                                    resolve(result);
                                } else {
                                    let result = http.responseText;
                                    if (result) {
                                        if (typeof (http.responseText) != 'string') {
                                            result = JSON.parse(http.responseText);
                                        }
                                        var open = result.substring(result.length - 4, result.length);
                                        if (open == 'true') {

                                            //setTimeout(5000);
                                            var closeOpts = window.iotJumpService.iot.getCommonVariable.queryCloseOpts();
                                            window.gspframeworkService.rtf.func.close(closeOpts);
                                            //window.iotJumpService.iot.commonVariable.queryName('')
                                            window.open(result);
                                        } else {
                                            //var a = result.split('&OpenInNewTab')[0]
                                            window.location.href = result;
                                        }

                                    }
                                    resolve(result);
                                }
                            } else {
                                reject();
                            }
                        }
                    });

                    http.open(method, url);
                    setHeaders(http, headers);
                    body = body ? JSON.stringify(body) : null;
                    http.send(body);

                    return promise;
                }

                rest.get = function (url, headers) {
                    return request('GET', url, headers, null);
                }

                rest.put = function (url, body, headers) {
                    return request('PUT', url, headers, body);
                }

                rest.post = function (url, body, headers) {
                    return request('POST', url, headers, body);
                }

                return rest;
            })(iot.rest || {});

            iot.commonVariable = (function (commonVariable) {

                commonVariable.queryParam = function (queryName) {
                    return hashQuery(queryName) || urlQuery(queryName);
                }

                commonVariable.formToken = function () {
                    return hashQuery('cvft') || urlQuery('cvft');
                }

                commonVariable.get = function () {
                    return getQueryString('vizId');
                }

                return commonVariable;
            })(iot.commonVariable || {});


            iot.getCommonVariable = (function (commonVariable) {

                commonVariable.queryCloseOpts = function () {
                    var tabId = hashQuery('tabId') || urlQuery('tabId');
                    var appType = hashQuery('appType') || urlQuery('appType');
                    var formToken = hashQuery('cvft') || urlQuery('cvft');
                    var appMode = hashQuery('appMode') || urlQuery('appMode');
                    var commonVariable = {
                        tabId: tabId,
                        appType: appType,
                        formToken: formToken,
                        appMode: appMode,
                    }
                    return commonVariable;
                }

                return commonVariable;
            })(iot.getCommonVariable || {});


            return iot;
        })(iotJumpService.iot || {}, window);
        //var cvft = window.iotJumpService.iot.commonVariable.formToken();
        //var json1 = JSON.stringify(cvft);
        //debugger;
        // var vizId = window.iotJumpService.iot.commonVariable.get();
        var url = '/api/runtime/iotfnd/v1.0/iot-jump-service/video/screen';
        var header = {'Content-Type': 'application/json'};
        var object = {};
        var params = [];
        let callUrl = iotJumpService.iot.commonVariable.queryParam('screenNumber');
        let encodedUrl = encodeURI(callUrl);
        let objects = {
            callUrl: '/standalone/screen/?screenNumber=' + (encodedUrl ? encodedUrl : 1),
            origin: 'http://*************:9527',
            projectMode: 'other'
        };

        window.iotJumpService.iot.rest.post(url, objects, header)
            .then(s => console.log(s))
            .catch(e => console.log(e));


    </script>
</head>
<body>

</body>
</html>
