const projectRoleFunc = {
    //新增同级
    addSame: function () {
        var row = idp.control.get("grid_main").getSelected();
        if (!row) {
            idp.uiview.addSame();
            return true;
        }
        var parentId = row.PARENTID;
        var parentRows = idp.control.get("grid_main").rows.filter(ele => ele.ID === parentId);
        if(parentRows.length > 0 && parentRows[0].ISENABLED !== "1"){
            idp.warn(idp.lang.get("PS_Pub_118"));
            return false;
        }
        idp.uiview.addSame();
        return true;
    },
    //新增下级
    addDown: function () {
        var row = idp.control.get("grid_main").getSelected();
        if (!row) {
            idp.warn(idp.lang.get("PS_Pub_NoSelectRow"));
            return false;
        }
        if (row.ISENABLED !== '1') {
            idp.warn(idp.lang.get("PS_Pub_118"));
            return false;
        }
        var layer = (row.LAYER || 0) + 1;
        if (layer === 9) {
            idp.warn(idp.lang.get("PS_Pub_119"));
            return false;
        }
        idp.uiview.addDown();
        return true;
    },
    //编辑
    edit: function () {
        var flRow = idp.control.get("grid_main").getSelected();
        if (!flRow) {
            idp.warn(idp.lang.get("PS_Pub_NoSelectRow"));
            return false;
        }
        return idp.uiview.edit();
    },
    //保存
    save: function () {
        return idp.uiview.saveData();
    },
    //取消
    cancel: function () {
        return idp.uiview.cancel();
    },
    //删除
    delete: function () {
        //删除前校验
        var flRow = idp.control.get("grid_main").getSelected();
        if (!flRow) {
            idp.warn(idp.lang.get("PS_Pub_NoSelectRow"));
            return false;
        }
        if (flRow.ISENABLED === '1') {
            idp.warn(idp.lang.get("PS_Pub_57"));
            return false;
        }
        if(flRow.ISSYS === "1" ){
            idp.warn(idp.lang.get('PS_Pub_01'));
            return;
        }
        var defer = $.Deferred();
        idp.uiview.deleteTreeData().then(function (ret) {
            defer.resolve(ret)
        });
        return defer.promise();

    },
    //启用
    enable: function () {
        var flRow = idp.control.get("grid_main").getSelected();
        if (!flRow) {
            idp.warn(idp.lang.get("PS_Pub_NoSelectRow"));
            return false;
        }
        if (flRow.ISENABLED === '1') {
            idp.warn(idp.lang.get("PS_Pub_53"));
            return false;
        }

        idp.service.fetch("/api/ps/pssu/v1.0/bd/projectRole/enableProjectRole",{id: flRow.ID}).done(function(data){
            idp.loaded();
            if(data.success){
                idp.tips(idp.lang.get('PS_Pub_EnabledSuccessfully'));
                idp.uiview.refreshGrid("grid_main");
            }else{
                idp.error(data.msg);
            }
        });

    },
    //停用
    disable: function () {
        var flRow = idp.control.get("grid_main").getSelected();
        if (!flRow) {
            idp.warn(idp.lang.get("PS_Pub_NoSelectRow"));
            return false;
        }
        if (flRow.ISENABLED !== '1') {
            idp.warn(idp.lang.get("PS_Pub_54"));
            return false;
        }

        var setDisable = function () {
            idp.service.fetch("/api/ps/pssu/v1.0/bd/projectRole/disableProjectRole",{id: flRow.ID}).done(function(data){
                idp.loaded();
                if(data.success){
                    idp.tips(idp.lang.get('PS_Pub_DeactivationSucceeded'));
                    idp.uiview.refreshGrid("grid_main");
                }else{
                    idp.error(data.msg);
                }

            });
        };
        if (flRow.ISDETAIL === "0") {
            idp.confirm(idp.lang.get("PS_Pub_120"), setDisable);
            return;
        }
        setDisable();
    },
    //关闭
    close: function () {
        return idp.uiview.close();
    }
};