/*! Last Update Time: 2025-06-26 16:36:00 */
/*! Last Update Time: 2025-06-26 16:36:00 */
var __defProp=Object.defineProperty,__defNormalProp=(obj,key,value)=>key in obj?__defProp(obj,key,{enumerable:!0,configurable:!0,writable:!0,value:value}):obj[key]=value,__publicField=(obj,key,value)=>(__defNormalProp(obj,"symbol"!=typeof key?key+"":key,value),value);System.register(["vue","@farris/devkit-vue","@farris/ui-vue","@farris/command-services-vue"],(function(exports,module){"use strict";var defineComponent,toRefs,inject,ref,createVNode,resolveComponent,withDirectives,vModelText,createTextVNode,h,HttpClient,ViewModel,Injector,FLoadingService,FMessageBoxService,NOTIFY_SERVICE_TOKEN,MODAL_SERVICE_TOKEN,RuntimeFrameworkService;return{setters:[module2=>{defineComponent=module2.defineComponent,toRefs=module2.toRefs,inject=module2.inject,ref=module2.ref,createVNode=module2.createVNode,resolveComponent=module2.resolveComponent,withDirectives=module2.withDirectives,vModelText=module2.vModelText,createTextVNode=module2.createTextVNode,h=module2.h},module2=>{HttpClient=module2.HttpClient,ViewModel=module2.ViewModel,Injector=module2.Injector},module2=>{FLoadingService=module2.FLoadingService,FMessageBoxService=module2.FMessageBoxService},module2=>{NOTIFY_SERVICE_TOKEN=module2.NOTIFY_SERVICE_TOKEN,MODAL_SERVICE_TOKEN=module2.MODAL_SERVICE_TOKEN,RuntimeFrameworkService=module2.RuntimeFrameworkService}],execute:function(){class ImportDialogArgs{constructor(){__publicField(this,"ruleId"),__publicField(this,"importOption"),__publicField(this,"recordRule"),__publicField(this,"validateScriptPath"),__publicField(this,"enableReimport"),__publicField(this,"methodType")}ResetBeforeLoadFrom(){}LoadFromJsonObject(obj){}}class CommonParams{constructor(){__publicField(this,"ruleId"),__publicField(this,"keys"),__publicField(this,"option"),__publicField(this,"fileName"),__publicField(this,"frameContext"),__publicField(this,"repository"),__publicField(this,"befRestService"),__publicField(this,"suffix"),__publicField(this,"loadingParam"),__publicField(this,"i18nResource"),__publicField(this,"importParam"),__publicField(this,"mainObjId"),__publicField(this,"su"),__publicField(this,"importResult"),__publicField(this,"enableSecurityLevel"),__publicField(this,"securityLevelPrefix"),__publicField(this,"securityLevelSuffix"),__publicField(this,"securityLevelName"),__publicField(this,"methodType"),__publicField(this,"validateScriptPath"),__publicField(this,"enableReimport"),__publicField(this,"fileBase64Str"),__publicField(this,"originFileName"),this.ruleId="",this.keys=[],this.option={},this.fileName="",this.frameContext=null,this.repository=null,this.befRestService=null,this.suffix="",this.loadingParam={},this.i18nResource={},this.importParam={},this.mainObjId="",this.su="",this.importResult={},this.securityLevelName="",this.validateScriptPath=""}}var MethodType=(MethodType2=>(MethodType2[MethodType2.CommonImport=0]="CommonImport",MethodType2[MethodType2.ImportSubTableDetail=1]="ImportSubTableDetail",MethodType2[MethodType2.CommonExport=2]="CommonExport",MethodType2[MethodType2.ExportCurrentTableData=3]="ExportCurrentTableData",MethodType2[MethodType2.ExportCurrentChildData=4]="ExportCurrentChildData",MethodType2[MethodType2.CardFormDataImport=5]="CardFormDataImport",MethodType2[MethodType2.DownloadImportTemplate=6]="DownloadImportTemplate",MethodType2[MethodType2.ImportSubSubTableDetail=7]="ImportSubSubTableDetail",MethodType2))(MethodType||{});class FileConst{}__publicField(FileConst,"xls","Office Excel97-2003(*.xls)"),__publicField(FileConst,"xlsx","Office Excel(*.xlsx)"),__publicField(FileConst,"xlsm","Office Excel Macro(*.xlsm)"),__publicField(FileConst,"et","WPS Table(*.et)"),__publicField(FileConst,"xml","XML File(*.xml)"),__publicField(FileConst,"pdf","PDF File(*.pdf)"),__publicField(FileConst,"csv","CSV File(*.csv)");class LayerElement{constructor(){__publicField(this,"ID"),__publicField(this,"Code"),__publicField(this,"Name"),__publicField(this,"Extend"),__publicField(this,"Layer"),__publicField(this,"ParentId"),__publicField(this,"ChildIds")}}function isNullOrUndefined(obj){return void 0===obj||null===obj}function encrypt(password){let result="";for(let i=0;i<password.length;i++)result=result.concat(String.fromCharCode(password.charCodeAt(i)+1));return result=function newGuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(c){const r=16*Math.random()|0;return("x"===c?r:3&r|8).toString(16)}))}().concat("-pp").concat(result),result}function isNullOrEmpty(obj){return void 0===obj||(null===obj||0==obj.length)}function getQueryString(paraName){const arrObj=window.location.hash.split("?");if(arrObj.length>1){const arrPara=arrObj[1].split("&");let arr;for(let i=0;i<arrPara.length;i++)if(arr=arrPara[i].split("="),null!=arr&&arr[0]===paraName)return arr[1];return""}return""}class FileTypeUtil{static getFileExtension(valueField){let suffix=null;switch(valueField){case FileConst.xls:suffix=".xls";break;case FileConst.xlsx:suffix=".xlsx";break;case FileConst.xlsm:suffix=".xlsm";break;case FileConst.et:suffix=".et";break;case FileConst.xml:suffix=".xml";break;case FileConst.pdf:suffix=".pdf";break;case FileConst.csv:suffix=".csv"}return suffix}static getFileTypeName(fileType){let fileTypeName="";switch(fileType){case"XLS":fileTypeName=FileConst.xls;break;case"XLSX":fileTypeName=FileConst.xlsx;break;case"XLSM":fileTypeName=FileConst.xlsm;break;case"ET":fileTypeName=FileConst.et;break;case"XML":fileTypeName=FileConst.xml;break;case"PDF":fileTypeName=FileConst.pdf;break;case"CSV":fileTypeName=FileConst.csv}return fileTypeName}}class DataIEUtils{static ChooseSu(repository,option){let su="";if(null!==repository){const baseUrl=DataIEUtils.getBaseUrl(repository);su=DataIEUtils.getSu(baseUrl)}return void 0!==option.su&&null!==option.su&&(su=option.su),su}static getSu(preurl){const index=preurl.indexOf("//");let su;index>=0&&(preurl=preurl.substring(index+2));preurl.startsWith("")&&(preurl=preurl.substring(0));return su=preurl.split("/")[3],su}static getApp(preurl){return preurl.substring(preurl.indexOf("//")+2).split("/")[2]}static getPort(preurl){const str=preurl.substring(preurl.indexOf("//")+2);return str.substr(str.indexOf(":")+1,2)}static getBaseUrl(repository){return repository.apiProxy.baseUrl}static GetPreUrl(baseUrl){const lastIndex=baseUrl.lastIndexOf("/");return baseUrl.substr(0,lastIndex)}static JudgeIllegalChar(str){return["?","、","\\","/","*","'",'"',"“","”","<",">","|",","," "].forEach((item=>{if(-1!==str.indexOf(item))return!0})),!1}static hasSameFileObjectName(model){const mainObject=model.MainObject,objects=mainObject.ChildrenDeo;objects.push(mainObject);for(let i=0;i<objects.length;i++)for(let j=i+1;j<objects.length;j++)if(objects[i].FileObjectName===objects[j].FileObjectName)return!0;return!1}static findBaseColumns(d,allColumns){const res=[];if(isNullOrEmpty(d.id))return res;const childColumns=allColumns.filter((e=>e.parentId===d.id));if(isNullOrEmpty(childColumns))res.push(d);else for(const childColumn of childColumns){const childRes=this.findBaseColumns(childColumn,allColumns);res.push(...childRes)}return res}static getLayerByFront(columns,allColumns){const layerElements=[],allColumnsByLayer=[];for(const c of columns[0]){const res=this.findAllColumns(c,allColumns);allColumnsByLayer.push(...res)}if(columns.length>1)for(const column of allColumnsByLayer){const layerElement=this.buildViewLayerElement(column,allColumnsByLayer);if(isNullOrEmpty(layerElement)||isNullOrEmpty(layerElement.Extend))return[];layerElements.push(layerElement)}return layerElements}static findAllColumns(d,allColumns){const res=[];if(!allColumns.includes(d))return res;const childColumns=allColumns.filter((e=>e.parentId===d.id));if(res.push(d),!isNullOrEmpty(childColumns))for(const childColumn of childColumns){const childRes=this.findAllColumns(childColumn,allColumns);res.push(...childRes)}return res}static buildViewLayerElement(column,allColumns){const layerElement=new LayerElement,childColumns=allColumns.filter((e=>e.parentId===column.id));layerElement.Extend=!isNullOrEmpty(childColumns),layerElement.Name=column.title,layerElement.ID=column.id,layerElement.ParentId=column.parentId,layerElement.Code=column.field;let layer=1,tempColumn=column;for(;!isNullOrEmpty(tempColumn.parentId);)layer++,tempColumn=allColumns.find((e=>e.id===tempColumn.parentId));layerElement.Layer=layer;const childIds=[];for(const childColumn of childColumns)childIds.push(childColumn.id);return layerElement.ChildIds=childIds,layerElement}static buildViewElements(dataGridColumns,allColumns){const res=[];for(const d of dataGridColumns[0])if(d.visible){const baseColumns=DataIEUtils.findBaseColumns(d,allColumns);for(const baseColumn of baseColumns){const field={code:baseColumn.field,name:baseColumn.title};res.push(field)}}return res}}class DataImportService{constructor(viewModel,injector,http,localePipe,runtimeFrameWorkService){__publicField(this,"origin",window.location.origin),__publicField(this,"DataIeProgressParams",{ImportProgressValue:13}),__publicField(this,"complete"),__publicField(this,"errorlevel"),__publicField(this,"notify"),__publicField(this,"master_table_noid"),__publicField(this,"sub_table_noid"),__publicField(this,"sub_table_error"),__publicField(this,"bind_form_error"),__publicField(this,"validate_result_show"),__publicField(this,"extract_result_show"),__publicField(this,"commonParams"),this.viewModel=viewModel,this.injector=injector,this.http=http,this.localePipe=localePipe,this.runtimeFrameWorkService=runtimeFrameWorkService,this.notify=this.injector.get(NOTIFY_SERVICE_TOKEN)}ImportSubSubTable(commonParams){const ruleId=commonParams.ruleId,option=commonParams.option,frameContext=commonParams.frameContext,methodType=commonParams.methodType,suffix=commonParams.suffix,fileName=commonParams.fileName,befRestService=this.getBefRestService(frameContext.repository);commonParams.befRestService=befRestService,commonParams.repository=frameContext.repository;let globalVar="";null!==option.GlobalParam&&void 0!==option.GlobalParam&&(globalVar="object"==typeof option.GlobalParam?JSON.stringify(option.GlobalParam):option.GlobalParam);let model="";void 0!==option.Models&&null!==option.Models&&option.Models.length>0&&(model=JSON.stringify(option.Models[0]));const mainObjId=frameContext.bindingData.list.currentId;let subObjIdWithImportSubSubData="";const bindingContext=frameContext.appContext.frameContextManager.getFrameContextById(option.importChildChildNodeCode);if(isNullOrEmpty(bindingContext))return this.notify.info(this.sub_table_error),!1;const bindingPath=bindingContext.viewModel.bindingPath,bindingList=frameContext.bindingData.getValue([bindingPath.slice(1)]);if(isNullOrEmpty(bindingList))return this.notify.info(this.sub_table_noid),!1;if(subObjIdWithImportSubSubData=bindingList.currentId,isNullOrEmpty(mainObjId))return this.notify.info(this.master_table_noid),!1;if(isNullOrEmpty(subObjIdWithImportSubSubData))return this.notify.info(this.sub_table_noid),!1;{const importParam={body:{dataImportContext:{RuleId:ruleId,FileName:fileName,MainObjId:mainObjId,SubObjIdWithImportSubSubData:subObjIdWithImportSubSubData,GlobalParam:globalVar,Suffix:suffix,Model:model,funcId:getQueryString("funcId"),securityLevelName:commonParams.securityLevelName,securityLevelPrefix:commonParams.securityLevelPrefix,securityLevelSuffix:commonParams.securityLevelSuffix,enableSecurityLevel:commonParams.enableSecurityLevel,methodType:methodType,fileBase64Str:commonParams.fileBase64Str},requestInfo:befRestService.buildRequestInfo()}};commonParams.importParam=importParam,commonParams.mainObjId=mainObjId,this.Import(commonParams)}}ImportSubTable(commonParams){const ruleId=commonParams.ruleId,option=commonParams.option,frameContext=commonParams.frameContext,methodType=commonParams.methodType,suffix=commonParams.suffix,fileName=commonParams.fileName,befRestService=this.getBefRestService(frameContext.repository);commonParams.befRestService=befRestService,commonParams.repository=frameContext.repository;let globalVar="";null!==option.GlobalParam&&void 0!==option.GlobalParam&&(globalVar="object"==typeof option.GlobalParam?JSON.stringify(option.GlobalParam):option.GlobalParam);let model="";void 0!==option.Models&&null!==option.Models&&option.Models.length>0&&(model=JSON.stringify(option.Models[0]));const mainObjId=frameContext.bindingData.list.currentId;if(isNullOrEmpty(mainObjId))return this.notify.info(this.master_table_noid),!1;{const importParam={body:{dataImportContext:{RuleId:ruleId,FileName:fileName,MainObjId:mainObjId,GlobalParam:globalVar,Suffix:suffix,Model:model,funcId:getQueryString("funcId"),securityLevelName:commonParams.securityLevelName,securityLevelPrefix:commonParams.securityLevelPrefix,securityLevelSuffix:commonParams.securityLevelSuffix,enableSecurityLevel:commonParams.enableSecurityLevel,methodType:methodType,fileBase64Str:commonParams.fileBase64Str},requestInfo:befRestService.buildRequestInfo()}};commonParams.importParam=importParam,commonParams.mainObjId=mainObjId,this.Import(commonParams)}}ImportSubTable4HandCraft(commonParams){const ruleId=commonParams.ruleId,option=commonParams.option,frameContext=commonParams.frameContext,suffix=commonParams.suffix,fileName=commonParams.fileName;let globalVar="";null!==option.GlobalParam&&void 0!==option.GlobalParam&&(globalVar="object"==typeof option.GlobalParam?JSON.stringify(option.GlobalParam):option.GlobalParam);let mainObjId="";if(null===(null===frameContext?null:frameContext.repository)){if(void 0===option.mainId||null===option.mainId||""===option.mainId)return this.notify.info(this.master_table_noid),!1;mainObjId=option.mainId}else{if(""===frameContext.bindingData.list.currentId||null===frameContext.bindingData.list.currentId)return this.notify.info(this.master_table_noid),!1;mainObjId=frameContext.bindingData.list.currentId}const importParam={ruleId:ruleId,fileName:fileName,mainObjId:mainObjId,globalParam:globalVar,suffix:suffix,funcId:getQueryString("funcId"),securityLevelName:commonParams.securityLevelName};commonParams.importParam=importParam,commonParams.mainObjId=mainObjId}ImportByRuleId(commonParams){const ruleId=commonParams.ruleId,option=commonParams.option;commonParams.repository;const frameContext=commonParams.frameContext,suffix=commonParams.suffix,fileName=commonParams.fileName,methodType=commonParams.methodType;let globalVar="";null!==option.GlobalParam&&void 0!==option.GlobalParam&&(globalVar="object"==typeof option.GlobalParam?JSON.stringify(option.GlobalParam):option.GlobalParam);let model="";void 0!==option.Models&&null!==option.Models&&option.Models.length>0&&(model=JSON.stringify(option.Models[0]));const funcId=getQueryString("funcId");let subObjIdWithImportSubSubData="";if(methodType===MethodType.CardFormDataImport){if(commonParams.mainObjId=option.mainObjId,!isNullOrEmpty(option.importChildChildNodeCode)){const bindingContext=frameContext.appContext.frameContextManager.getFrameContextById(option.importChildChildNodeCode);if(isNullOrEmpty(bindingContext))return this.notify.info(this.sub_table_noid),!1;const bindingPath=bindingContext.viewModel.bindingPath,bindingList=frameContext.bindingData.getValue([bindingPath.slice(1)]);if(isNullOrEmpty(bindingList))return this.notify.info(this.sub_table_error),!1;subObjIdWithImportSubSubData=bindingList.currentId}}else commonParams.mainObjId="";return commonParams.importParam={body:{dataImportContext:{RuleId:ruleId,FileName:fileName,GlobalParam:globalVar,Suffix:suffix,Model:model,funcId:funcId,securityLevelName:commonParams.securityLevelName,securityLevelPrefix:commonParams.securityLevelPrefix,securityLevelSuffix:commonParams.securityLevelSuffix,enableSecurityLevel:commonParams.enableSecurityLevel,mainObjId:commonParams.mainObjId,subObjIdWithImportSubSubData:subObjIdWithImportSubSubData,methodType:methodType,fileBase64Str:commonParams.fileBase64Str}}},this.Import(commonParams)}Import(commonParams){this.commonParams=commonParams,commonParams.repository;const importParam=commonParams.importParam;commonParams.methodType;const url=DataIEUtils.getBaseUrl(this.viewModel.repository)+"/service/dataimportvmaction",loading=FLoadingService.show({message:this.localePipe.transform("loading")});return this.http.request("PUT",url,{headers:{},body:importParam.body}).then((res=>{var _a;null==(_a=null==loading?void 0:loading.value)||_a.close();const response=res.returnValue;return response.succeed?(this.notify.success(this.localePipe.transform("import_success")),Promise.resolve()):(this.notify.error(response.message),Promise.reject())}),(error=>{var _a;return null==(_a=null==loading?void 0:loading.value)||_a.close(),this.notify.error(error.message),Promise.reject()}))}ShowValidatedResult(commonParams){const importResult=commonParams.importResult,loadingParam=commonParams.loadingParam;if(commonParams.befRestService,importResult.extractResults&&importResult.extractResults.length>0){importResult.extractResults[0].validateInfoAllWarning=!1,loadingParam.loadingService.clearAll();const cmpRef=this.CreateValidatedComponent();cmpRef.instance.validateResult=importResult.extractResults,cmpRef.instance.su=commonParams.su,cmpRef.instance.i18nResource=commonParams.i18nResource,cmpRef.instance.loadingParam=loadingParam,cmpRef.instance.isExtractResult=!0,cmpRef.instance.isCustomExtract=importResult.customExtract;const validateModalConfig={title:this.extract_result_show,width:950,height:580,buttons:cmpRef.instance.modalFooter,showHeader:!0,showMaxButton:!1,showButtons:!0},dialog=this.modalService.show(cmpRef,validateModalConfig);dialog.dialog.instance.closed.subscribe((()=>{this.complete.emit("validateBreak")})),cmpRef.instance.closeModal.subscribe((()=>{dialog.close()})),cmpRef.instance.okModal.subscribe((()=>{dialog.close()}))}else if(importResult.validatedResults&&importResult.validatedResults.length>0){loadingParam.loadingService.clearAll();const cmpRef=this.CreateValidatedComponent();cmpRef.instance.validateResult=importResult.validatedResults,cmpRef.instance.su=commonParams.su,cmpRef.instance.i18nResource=commonParams.i18nResource,cmpRef.instance.validateScriptPath=commonParams.validateScriptPath,cmpRef.instance.loadingParam=loadingParam,cmpRef.instance.allDxcCommonData=importResult.allDxcCommonData,cmpRef.instance.validateRows=importResult.validateRows,cmpRef.instance.isCustomExtract=importResult.customExtract,cmpRef.instance.enableReimport=commonParams.enableReimport;const validateModalConfig={title:this.validate_result_show,width:1100,height:580,buttons:cmpRef.instance.modalFooter,showHeader:!0,showMaxButton:!1,showButtons:!0},dialog=this.modalService.show(cmpRef,validateModalConfig);let closed=0;dialog.dialog.instance.closed.subscribe((()=>{0===closed&&this.complete.emit("validateBreak")})),cmpRef.instance.closeModal.subscribe((()=>{closed=0,dialog.close()})),cmpRef.instance.okModal.subscribe((validatedInfoList=>{closed=1,dialog.close(),loadingParam.loadingService.show(loadingParam.loadingConfig),commonParams.importParam.body.dataImportContext.validatedInfoList=validatedInfoList,this.Import(commonParams)}))}else this.complete.emit(importResult.message)}CreateValidatedComponent(){const comRef=this.resolver.resolveComponentFactory(ValidateFormComponent).create(this.injector);return comRef.instance.commonParams=this.commonParams,comRef}JudgeIllegalChar(str){return["?","、","\\","/","*","'",'"',"“","”","<",">","|",","].forEach((item=>{if(-1!==str.indexOf(item))return!0})),!1}DownloadTemplate(ruleId,repository,importOption,suffix){const befRestService=this.getBefRestService(repository),preUrl=DataIEUtils.GetPreUrl(befRestService.baseUri),tempUrl=`${preUrl}/import`;let su=DataIEUtils.getSu(tempUrl);void 0!==importOption.su&&null!==importOption.su&&(su=importOption.su);let securityLevelName="";isNullOrUndefined(importOption.securityLevelName)||(securityLevelName=importOption.securityLevelName);let model="";void 0!==importOption.Models&&null!==importOption.Models&&importOption.Models.length>0&&(model=JSON.stringify(importOption.Models[0]));let globalVar="";isNullOrUndefined(importOption.GlobalParam)||(globalVar="object"==typeof importOption.GlobalParam?JSON.stringify(importOption.GlobalParam):importOption.GlobalParam);const templateParam={body:{ruleId:ruleId,suffix:suffix,su:su,model:model,globalParam:globalVar,securityLevelName:securityLevelName}},fixedUrl=this.origin+"/api/runtime/dip/v1.0/rpcimport";befRestService.request(`${fixedUrl}/TemplateDownload`,"post",{su:su},templateParam).subscribe((data=>{if(!0===data.result){let filename="";if(""===importOption.templatefilename||void 0===importOption.templatefilename)filename=data.fileName;else{const suffixIndex=data.fileName.lastIndexOf("."),tempsuffix=data.fileName.substring(suffixIndex);let customName=importOption.templatefilename;filename=this.JudgeIllegalChar(customName)?data.fileName:-1!==customName.indexOf(".")?customName:customName+tempsuffix}this.operateService.DownloadFile(data.docRelativePath,filename,preUrl,importOption);const subscription=this.operateService.downloadComplete.subscribe((result=>{""===result?(this.complete.emit(""),subscription.unsubscribe()):(this.complete.emit(result),subscription.unsubscribe())}))}else this.complete.emit(data.message)}),(error=>{this.complete.emit("downloadTemplateFailed")}))}DownloadTemplate4HandCraft(ruleId,importOption,suffix,repository){const su=importOption.su;let globalVar="";null!==importOption.GlobalParam&&void 0!==importOption.GlobalParam&&(globalVar="object"==typeof importOption.GlobalParam?JSON.stringify(importOption.GlobalParam):importOption.GlobalParam);let securityLevelName="";isNullOrUndefined(importOption.securityLevelName)||(securityLevelName=importOption.securityLevelName);const fixedUrl=this.origin+"/api/runtime/dip/v1.0/AllSteps/TemplateDownload";return this.http.request("POST",fixedUrl,{headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"},body:{ruleId:ruleId,suffix:suffix,su:su,globalParam:globalVar,securityLevelName:securityLevelName},responseType:"arraybuffer",observe:"response"}).then((res=>{const data=res.body;if("application/json"==data.type){const reader=new FileReader;reader.onload=e=>{const responseParam=JSON.parse(reader.result);responseParam.result||this.complete.emit(responseParam.message)},reader.readAsText(data,"utf-8")}else{let fileName="",contentDisposition=res.headers.get("content-disposition");if(contentDisposition){let matches=/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);null!=matches&&matches[1]&&(fileName=matches[1].replace(/['"]/g,"")),fileName=decodeURI(fileName)}if(!isNullOrEmpty(importOption.templatefilename)){const suffixIndex=fileName.lastIndexOf("."),suffix2=fileName.substring(suffixIndex);fileName=importOption.templatefilename+suffix2}const blob=new Blob([data]);if(data&&0!=data.size)if(navigator.msSaveOrOpenBlob)navigator.msSaveOrOpenBlob(blob,fileName);else{const link=document.createElement("a");link.setAttribute("href",window.URL.createObjectURL(blob)),link.setAttribute("download",fileName),link.style.visibility="hidden",document.body.appendChild(link),link.click(),document.body.removeChild(link)}else this.complete.emit(this.i18nResource.return_data_empty)}}),(error=>{isNullOrEmpty(error.error.Message)&&!isNullOrEmpty(error.message)?this.notify.error(error.message):this.notify.error(error.error.Message)}))}}class DataExportService{constructor(viewModel,injector,http,localePipe,runtimeFrameWorkService){__publicField(this,"origin",window.location.origin),__publicField(this,"errorlevel"),__publicField(this,"notify"),this.viewModel=viewModel,this.injector=injector,this.http=http,this.localePipe=localePipe,this.runtimeFrameWorkService=runtimeFrameWorkService,this.notify=this.injector.get(NOTIFY_SERVICE_TOKEN)}Export(repository,exportParam,option){this.ExportByVMAction(exportParam,option,repository)}ExportByPackage(frameContext,optionJson){const packitems=optionJson.package.packitems;if(null==packitems||0===packitems.length)return void this.notify.error(this.i18nResource.pack_info_empty);const exportParam={body:{exportContext:{packageInfo:{packType:"zip",customFileName:optionJson.package.customfilename,packitems:optionJson.package.packitems}}}};this.ExportByVMAction(exportParam,exportParam,frameContext.repository)}ExecuteMutiRequest(packitems,befRestService,relativePaths,itemsFileName,optionJson){if(packitems.length<=0)return;const item=packitems.pop();let customfilename="";const ruleId=item.ruleid;if(void 0===ruleId||""===ruleId)return this.notify.error(this.i18nResource.pack_rule_id_empty),void this.CloseLoading();let globalVar="";null!==item.globalParam&&void 0!==item.globalParam&&(globalVar="object"==typeof item.globalParam?JSON.stringify(item.globalParam):item.globalParam);let itemfilename=item.itemfilename;null!=itemfilename&&itemsFileName.push(itemfilename);const funcId=getQueryString("funcId");let exportParam=null;const filter=item.filter;exportParam=null!=filter&&"{}"!==JSON.stringify(filter)?{body:{exportContext:{RuleId:ruleId,Filter:filter,GlobalParam:globalVar,funcId:funcId}}}:{body:{exportContext:{RuleId:ruleId,GlobalParam:globalVar,funcId:funcId}}};befRestService.request(`${befRestService.baseUri}/service/dataexportvmaction`,"put",null,exportParam).subscribe((data=>{let exportResult;if(exportResult=isNullOrUndefined(data.returnValue)?data:data.returnValue,!0===exportResult.succeed)if(relativePaths.push(exportResult.docRelativePath),customfilename=exportResult.fileName.split(".")[0],0===packitems.length){const preurl=DataIEUtils.GetPreUrl(befRestService.baseUri);void 0!==optionJson.package.customfilename&&""!==optionJson.package.customfilename&&(customfilename=optionJson.package.customfilename);let packtype="zip";void 0!==optionJson.package.packtype&&(packtype=optionJson.package.packtype),0===itemsFileName.length&&(itemsFileName=null),this.operateService.DownloadPackageFile(relativePaths,itemsFileName,customfilename,preurl,packtype);const subscriber=this.operateService.downloadComplete.subscribe((result=>{this.CloseLoading(),""===result?(this.export.emit(""),subscriber.unsubscribe()):(this.export.emit(result),subscriber.unsubscribe())}))}else this.ExecuteMutiRequest(packitems,befRestService,relativePaths,itemsFileName,optionJson);else this.CloseLoading(),this.errorlevel=exportResult.errorLevel,this.export.emit(exportResult.message)}),(error=>{this.CloseLoading(),this.notify.error(error.error.Message)}))}Export4HandCraft(exportParam,option){this.ExportByAllStepsVMAction(exportParam,option)}ExportCurrentData(ruleId,frameContext,pageIndex,pageSize,option,suffix){let exportParam=null,model="";void 0!==option.Models&&null!==option.Models&&option.Models.length>0&&(model=JSON.stringify(option.Models[0]));let globalVar="";null!==option.globalParam&&void 0!==option.globalParam&&(globalVar="object"==typeof option.globalParam?JSON.stringify(option.globalParam):option.globalParam),null==suffix&&(suffix="");const funcId=getQueryString("funcId");let filterParem=option.filter;const newFilters=[],selectFilterGridComponent=option.selectFilterGridComponent;if(!isNullOrUndefined(selectFilterGridComponent)&&!isNullOrEmpty(selectFilterGridComponent)){const ids=frameContext.appContext.frameContextManager.getFrameContextById(selectFilterGridComponent).uiState.ids;if(isNullOrUndefined(ids)||!(ids.length>0))return void this.notify.info(this.i18nResource.select_data_export);{let idsStr="";for(let i=0;i<ids.length;i++){const id=ids[i];i===ids.length-1?idsStr+=id:idsStr+=id+"\r\n"}const selectIdFilterCondition={FilterField:"ID",Compare:14,Value:idsStr,Relation:1,Expresstype:0};newFilters.push(selectIdFilterCondition)}}const filterConditionList=frameContext.uiState.filterConditionList;if(null!=filterConditionList&&""!==filterConditionList&&"[]"!==filterConditionList){let filterConditionListJson;filterConditionListJson="string"==typeof filterConditionList?JSON.parse(filterConditionList):filterConditionList;for(let index=0;index<filterConditionListJson.length;index++){const element=filterConditionListJson[index],value=decodeURIComponent(element.Value);element.Value=value,newFilters.push(element)}}newFilters.length>0&&(isNullOrUndefined(filterParem)||null===filterParem.FilterConditions?(isNullOrUndefined(filterParem)&&(filterParem={}),filterParem.FilterConditions=newFilters):filterParem.FilterConditions=newFilters.concat(filterParem.FilterConditions)),isNullOrUndefined(filterParem)?exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Suffix:suffix,PaginationInfo:{PageIndex:pageIndex,PageSize:pageSize},Model:model,GlobalParam:globalVar,funcId:funcId}}}:(!isNullOrUndefined(filterParem.FilterConditions)&&filterParem.FilterConditions.length>0&&(filterParem.FilterConditions[filterParem.FilterConditions.length-1].Relation=0),exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Filter:filterParem,Suffix:suffix,PaginationInfo:{PageIndex:pageIndex,PageSize:pageSize},Model:model,GlobalParam:globalVar,funcId:funcId}}}),this.Export(frameContext.repository,exportParam,option)}ExportById(ruleId,option,suffix){let exportParam=null,model="";void 0!==option.Models&&null!==option.Models&&option.Models.length>0&&(model=JSON.stringify(option.Models[0]));let globalVar="";null!==option.globalParam&&void 0!==option.globalParam&&(globalVar="object"==typeof option.globalParam?JSON.stringify(option.globalParam):option.globalParam),null==suffix&&(suffix="");const funcId=getQueryString("funcId");let filterParam=option.filter;return isNullOrUndefined(filterParam)?exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Suffix:suffix,Model:model,GlobalParam:globalVar,funcId:funcId,packageInfo:option.packageInfo}}}:(!isNullOrUndefined(filterParam.FilterConditions)&&filterParam.FilterConditions.length>0&&(filterParam.FilterConditions[filterParam.FilterConditions.length-1].Relation=0),exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Filter:filterParam,Suffix:suffix,Model:model,GlobalParam:globalVar,funcId:funcId,packageInfo:option.packageInfo}}}),this.Export(this.viewModel.repository,exportParam,option)}ExportById4HandCraft(ruleId,option,suffix,exportMode){let paginationInfo,packageInfo,globalVar="",idpFilter=option.filter;const newFilters=[];isNullOrUndefined(option.globalParam)||(globalVar="object"==typeof option.globalParam?JSON.stringify(option.globalParam):option.globalParam),isNullOrUndefined(suffix)&&(suffix="");let funcId="";if(funcId=isNullOrUndefined(option.funcId)?getQueryString("funcId"):option.funcId,"current"===exportMode)paginationInfo={PageIndex:option.idpPageIndex,PageSize:option.idpPageSize};else if("all"===exportMode){let packType="zip";isNullOrEmpty(option.packType)||(packType=option.packType),packageInfo={packType:packType,customFileName:"PACK"+getNowDate().split(" ")[0],packageSize:option.packageSize,totalData:option.totalData}}else if("checkedRow"===exportMode){const ids=option.checkedRowIds;if(isNullOrUndefined(ids)||!(ids.length>0))return this.notify.info(this.i18nResource.select_data_export),0;{let idsStr="";for(let i=0;i<ids.length;i++){const id=ids[i];i===ids.length-1?idsStr+=id:idsStr+=id+"\r\n"}const selectIdFilterCondition={FilterField:"ID",Compare:14,Value:idsStr,Relation:1,Expresstype:0};newFilters.push(selectIdFilterCondition)}newFilters.length>0&&(isNullOrUndefined(idpFilter)||null===idpFilter.FilterConditions?(isNullOrUndefined(idpFilter)&&(idpFilter={}),idpFilter.FilterConditions=newFilters):idpFilter.FilterConditions=newFilters.concat(idpFilter.FilterConditions))}if(!isNullOrEmpty(idpFilter)&&!isNullOrEmpty(idpFilter.FilterConditions)&&idpFilter.FilterConditions.length>0){const idpFilterCondition=idpFilter.FilterConditions;idpFilterCondition[idpFilterCondition.length-1].Relation=0}const exportParam={password:option.password,ruleId:ruleId,filter:idpFilter,suffix:suffix,globalParam:globalVar,funcId:funcId,PaginationInfo:paginationInfo,packageInfo:packageInfo};this.Export4HandCraft(exportParam,option)}ExportCurrentChildData(ruleId,frameContext,option,suffix){let exportParam=null,globalVar="";null!==option.globalParam&&void 0!==option.globalParam&&(globalVar="object"==typeof option.globalParam?JSON.stringify(option.globalParam):option.globalParam),null==suffix&&(suffix="");const funcId=getQueryString("funcId");exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Suffix:suffix,MainObjId:frameContext.bindingData.list.currentId,GlobalParam:globalVar,funcId:funcId}}},this.Export(frameContext.repository,exportParam,option)}NewExport(repository,exportParam,option){this.ExportByVMAction(exportParam,option,repository)}convertBase64ToBlob(imageEditorBase64){const base64Arr=imageEditorBase64.split(",");let imgtype="",base64String="";base64Arr.length>0&&(base64String=base64Arr[0],imgtype=base64Arr[0].substring(base64Arr[0].indexOf(":")+1,base64Arr[0].indexOf(";")));const bytes=atob(base64String),bytesCode=new ArrayBuffer(bytes.length),byteArray=new Uint8Array(bytesCode);for(let i=0;i<bytes.length;i++)byteArray[i]=bytes.charCodeAt(i);return new Blob([bytesCode],{type:imgtype})}ParseExportJson(option,frameContext){const voId=frameContext.appContext.formId;if(isNullOrUndefined(option)||isNullOrEmpty(option))return{filter:null,voId:voId,customfilename:"",applyui:!1,vsto:!1};let optionJson=null;try{optionJson=JSON.parse(option)}catch{return this.notify.warning(this.i18nResource.params_json_error),{filter:null,voId:voId,customfilename:"",applyui:!1,vsto:!1}}return"object"!=typeof optionJson&&(optionJson={}),optionJson.voId=voId,optionJson}NewExportCurrentData(ruleId,frameContext,pageIndex,pageSize,option,filterConditionListInfo,suffix){let exportParam=null,model="";void 0!==option.Models&&null!==option.Models&&option.Models.length>0&&(model=JSON.stringify(option.Models[0]));let globalVar="";null!==option.globalParam&&void 0!==option.globalParam&&(globalVar="object"==typeof option.globalParam?JSON.stringify(option.globalParam):option.globalParam),null==suffix&&(suffix="");const funcId=getQueryString("funcId");let filterParem=option.filter;const newFilters=[],selectFilterGridComponent=option.selectFilterGridComponent;if(!isNullOrUndefined(selectFilterGridComponent)&&!isNullOrEmpty(selectFilterGridComponent)){if(isNullOrEmpty(frameContext.appContext.frameContextManager.getFrameContextById(selectFilterGridComponent)))return this.notify.warning(this.i18nResource.export_current_data_component_error),0;const ids=frameContext.appContext.frameContextManager.getFrameContextById(selectFilterGridComponent).uiState.ids;if(isNullOrUndefined(ids)||!(ids.length>0))return this.notify.info(this.i18nResource.select_data_export),0;{let idsStr="";for(let i=0;i<ids.length;i++){const id=ids[i];i===ids.length-1?idsStr+=id:idsStr+=id+"\r\n"}const selectIdFilterCondition={FilterField:"ID",Compare:14,Value:idsStr,Relation:1,Expresstype:0};newFilters.push(selectIdFilterCondition)}}for(let index=0;index<filterConditionListInfo.length;index++){const element=filterConditionListInfo[index],value=decodeURIComponent(element.Value);element.Value=value,newFilters.push(element)}newFilters.length>0&&(isNullOrUndefined(filterParem)||null===filterParem.FilterConditions?(isNullOrUndefined(filterParem)&&(filterParem={}),filterParem.FilterConditions=newFilters):filterParem.FilterConditions=newFilters.concat(filterParem.FilterConditions)),isNullOrUndefined(filterParem)?exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Suffix:suffix,PaginationInfo:{PageIndex:pageIndex,PageSize:pageSize},Model:model,GlobalParam:globalVar,funcId:funcId}}}:(!isNullOrUndefined(filterParem.FilterConditions)&&filterParem.FilterConditions.length>0&&(filterParem.FilterConditions[filterParem.FilterConditions.length-1].Relation=0),exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Filter:filterParem,Suffix:suffix,PaginationInfo:{PageIndex:pageIndex,PageSize:pageSize},Model:model,GlobalParam:globalVar,funcId:funcId}}}),this.NewExport(frameContext.repository,exportParam,option)}NewExportById(ruleId,frameContext,option,filterConditionListInfo,suffix){let exportParam=null,model="";void 0!==option.Models&&null!==option.Models&&option.Models.length>0&&(model=JSON.stringify(option.Models[0]));let globalVar="";null!==option.globalParam&&void 0!==option.globalParam&&(globalVar="object"==typeof option.globalParam?JSON.stringify(option.globalParam):option.globalParam),null==suffix&&(suffix="");const funcId=getQueryString("funcId");let filterParam=option.filter;const newFilters=[];if(!0===option.enableScreeningScheme)for(let index=0;index<filterConditionListInfo.length;index++){const element=filterConditionListInfo[index],value=decodeURIComponent(element.Value);element.Value=value,newFilters.push(element)}newFilters.length>0&&(isNullOrUndefined(filterParam)||null===filterParam.FilterConditions?(isNullOrUndefined(filterParam)&&(filterParam={}),filterParam.FilterConditions=newFilters):filterParam.FilterConditions=newFilters.concat(filterParam.FilterConditions));let customfilename="PACK"+getNowDate().split(" ")[0],packType="zip";isNullOrEmpty(option.packType)||(packType=option.packType),isNullOrUndefined(filterParam)?exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Suffix:suffix,Model:model,GlobalParam:globalVar,funcId:funcId,customExportInfo:option.customExportInfo,packageInfo:{packType:packType,customFileName:customfilename,packageSize:option.packageSize,totalData:option.totalData}}}}:(!isNullOrUndefined(filterParam.FilterConditions)&&filterParam.FilterConditions.length>0&&(filterParam.FilterConditions[filterParam.FilterConditions.length-1].Relation=0),exportParam={body:{exportContext:{password:option.password,RuleId:ruleId,Filter:filterParam,Suffix:suffix,Model:model,GlobalParam:globalVar,funcId:funcId,customExportInfo:option.customExportInfo,packageInfo:{packType:packType,customFileName:customfilename,packageSize:option.packageSize,totalData:option.totalData}}}}),this.NewExport(frameContext.repository,exportParam,option)}ExportByVMAction(exportParam,option,repository){isNullOrEmpty(exportParam)||isNullOrEmpty(exportParam.body)||isNullOrEmpty(exportParam.body.exportContext)||isNullOrEmpty(exportParam.body.exportContext.password)||(exportParam.body.exportContext.password=encrypt(exportParam.body.exportContext.password));const url=DataIEUtils.getBaseUrl(this.viewModel.repository)+"/service/dataexportvmaction",loading=FLoadingService.show({message:this.localePipe.transform("loading")});return this.http.request("PUT",url,{headers:{},body:exportParam.body}).then((res=>{var _a,_b;null==(_a=null==loading?void 0:loading.value)||_a.close();const response=res.returnValue;if(!response.succeed)return this.notify.error(response.message),null==(_b=null==loading?void 0:loading.value)||_b.close(),Promise.reject();const data=response.fileBase64Str;let fileName=response.fileName;if(!response.enableCustomFileName&&!isNullOrEmpty(option.customfilename)){const suffixIndex=fileName.lastIndexOf("."),suffix=fileName.substring(suffixIndex);fileName=option.customfilename+suffix}if(data&&0!==data.length){const blob=this.convertBase64ToBlob(response.fileBase64Str);if(navigator.msSaveOrOpenBlob)navigator.msSaveOrOpenBlob(blob,fileName);else{const link=document.createElement("a");link.setAttribute("href",window.URL.createObjectURL(blob)),link.setAttribute("download",fileName),link.style.visibility="hidden",document.body.appendChild(link),link.click(),document.body.removeChild(link)}}else this.notify.warning(this.localePipe.transform("return_data_empty"));return this.notify.success(this.localePipe.transform("export_success")),Promise.resolve()}),(error=>(this.notify.error(error.message),Promise.reject())))}ExportByAllStepsVMAction(exportParam,option,repository){this.StartLoading();const su=option.su,fixedUrl=this.origin+"/api/runtime/dip/v1.0/AllSteps/dataexportvmaction";let http=this.http;isNullOrUndefined(http)&&(http=DataIEUtils.GetBefRestService(repository).proxy.httpClient),http.put(fixedUrl,exportParam,{headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"},responseType:"blob",observe:"response",params:{su:su}}).subscribe((res=>{this.CloseLoading();const data=res.body;if("application/json"===data.type){const reader=new FileReader;reader.onload=e=>{const dataIeResult=JSON.parse(reader.result);dataIeResult.succeed||(this.errorlevel=dataIeResult.errorLevel,this.export.emit(dataIeResult.message))},reader.readAsText(data,"utf-8")}else{let fileName="";const contentDisposition=res.headers.get("content-disposition");if(contentDisposition){const matches=/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);null!=matches&&matches[1]&&(fileName=matches[1].replace(/['"]/g,"")),fileName=decodeURI(fileName)}if(!isNullOrEmpty(option.customfilename)){const suffixIndex=fileName.lastIndexOf("."),suffix=fileName.substring(suffixIndex);fileName=option.customfilename+suffix}const blob=new Blob([data]);if(data&&0!==data.size){if(navigator.msSaveOrOpenBlob)navigator.msSaveOrOpenBlob(blob,fileName);else{const link=document.createElement("a");link.setAttribute("href",window.URL.createObjectURL(blob)),link.setAttribute("download",fileName),link.style.visibility="hidden",document.body.appendChild(link),link.click(),document.body.removeChild(link)}this.export.emit("")}else this.export.emit("返回的数据为空")}}),(error=>{this.CloseLoading(),this.notify.error(error.error.Message)}))}}const ImportDialogSelector=defineComponent({name:"ImportDialogSelector",props:{args:{type:ImportDialogArgs,require:!0}},setup(props,ctx){const{args:args}=toRefs(props),localePipe=inject("LocalePipe"),viewModel=inject("ViewModel");viewModel.getInjector().get(HttpClient);const dataImportService=viewModel.getInjector().get(DataImportService);let responseEntity=ref({});const uploadFileName=ref(localePipe.transform("import_placeholder")),base64Data=ref(null),recordRule=args.value.recordRule,importOption=args.value.importOption,ruleId=args.value.ruleId,fileType=ref(FileTypeUtil.getFileTypeName(recordRule.fileType)),currentRuleFileType=ref(fileType.value),fileTypeList=ref([]),fileInput=ref(null);fileTypeList.value.push({id:"Office Excel97-2003(*.xls)",name:"Office Excel97-2003(*.xls)"}),fileTypeList.value.push({id:"Office Excel(*.xlsx)",name:"Office Excel(*.xlsx)"}),fileTypeList.value.push({id:"Office Excel Macro(*.xlsm)",name:"Office Excel Macro(*.xlsm)"}),fileTypeList.value.push({id:"WPS Table(*.et)",name:"WPS Table(*.et)"});const downloadTemplate=()=>{var _a;const loading=FLoadingService.show({message:localePipe.transform("loading")});let suffix=null;if(canDownloadTemplate()&&fileType.value!==currentRuleFileType.value&&(suffix=FileTypeUtil.getFileExtension(fileType.value)),isNullOrEmpty(importOption.su)){const baseUrl=DataIEUtils.getBaseUrl(viewModel.repository);importOption.su=DataIEUtils.getSu(baseUrl)}dataImportService.DownloadTemplate4HandCraft(ruleId,importOption,suffix,viewModel.repository),null==(_a=null==loading?void 0:loading.value)||_a.close()},selectFile=()=>{var _a;null==(_a=fileInput.value)||_a.click()},handleFileChange=e=>{const file=e.target.files[0],fileExt=file.name.split(".").pop().toLowerCase();["xlsx","xls","xlsm","csv"].includes(fileExt)?(uploadFileName.value=file.name,convertToBase64(file)):alert("请选择Excel文件(.xlsx/.xls/.csv)")},convertToBase64=file=>{const reader=new FileReader;reader.onload=()=>{base64Data.value=reader.result,responseEntity.value={fileBase64Str:base64Data.value,fileName:uploadFileName.value,suffix:getSuffix(),importOption:importOption}},reader.readAsDataURL(file)},canDownloadTemplate=()=>{const suffix=FileTypeUtil.getFileExtension(fileType.value);return!([".xls",".xlsx",".xlsm",".et"].findIndex((t=>t===suffix))<0)},getSuffix=()=>{let suffix=null;return canDownloadTemplate()&&fileType.value!==currentRuleFileType.value&&(suffix=FileTypeUtil.getFileExtension(fileType.value)),suffix};return ctx.expose({response:()=>({fileBase64Str:base64Data.value,fileName:uploadFileName.value,suffix:getSuffix(),importOption:importOption})}),()=>createVNode("div",{class:"f-form-layout farris-form farris-form-controls-inline f-form-label-xl"},[createVNode("div",{class:"col-12",style:"padding-top:25px; justify-content: center; align-items: center"},[createVNode("div",{class:"farris-group-wrap form-inline farris-form-inline"},[createVNode("div",{class:"form-group farris-form-group"},[createVNode("label",{class:"col-form-label"},[createVNode("span",{class:"farris-label-text"},[localePipe.transform("ruleselect_filetype")])]),createVNode("div",{style:""},[createVNode(resolveComponent("f-combo-list"),{className:"ng-untouched ng-pristine ng-valid",enableClear:!1,enableTitle:!0,data:fileTypeList.value,modelValue:fileType.value,"onUpdate:modelValue":$event=>fileType.value=$event},null)]),createVNode("div",{style:"padding-left: 10px"},[createVNode("button",{className:"btn btn-secondary bill-tracker-toolbar-btn mr-1",onClick:downloadTemplate},[localePipe.transform("download_template")])])])]),createVNode("div",{class:"farris-group-wrap form-inline farris-form-inline"},[createVNode("div",{class:"form-group farris-form-group"},[createVNode("label",{class:"col-form-label"},[createVNode("span",{class:"farris-label-text"},[localePipe.transform("import_file")])]),createVNode("div",{class:""},[createVNode("input",{type:"file",onchange:handleFileChange,accept:".xlsx,.xls,.xlsm,.csv",ref:fileInput,hidden:!0},null),withDirectives(createVNode("input",{type:"text",style:"width: 190px","onUpdate:modelValue":$event=>uploadFileName.value=$event,disabled:!0},null),[[vModelText,uploadFileName.value]])]),createVNode("div",{style:"padding-left: 10px"},[createVNode("button",{class:"btn btn-secondary bill-tracker-toolbar-btn mr-1",onClick:selectFile},[createTextVNode(" "),localePipe.transform("choose_file")])])])])])])}}),WfTaskCommentCreationVueComponents=[(component=>{const c=component;return c.install=function(app){app.component(c.name,component)},component})(ImportDialogSelector)],GSP_WF_VIEWMODEL_INJECTION_TOKEN=Symbol("GSP_WF_VIEWMODEL");class DataImportExportService{constructor(viewModel,injector,http,localePipe,runtimeFrameWorkService,dataImportService,dataExportService){__publicField(this,"notify"),this.viewModel=viewModel,this.injector=injector,this.http=http,this.localePipe=localePipe,this.runtimeFrameWorkService=runtimeFrameWorkService,this.dataImportService=dataImportService,this.dataExportService=dataExportService,this.notify=this.injector.get(NOTIFY_SERVICE_TOKEN),this.dataImportService=viewModel.getInjector().get(DataImportService),this.dataExportService=viewModel.getInjector().get(DataExportService)}CommonDataImport(type,ruleID,option){if(1===type)this.notify.info("不支持当前配置，请配置导入命令构件类型为0");else{if(2!==type)return this.ImportByRule(ruleID,option);this.notify.info("不支持当前配置，请配置导入命令构件类型为0")}}ImportByRule(ruleID,option){return this.ImportCommonEntry(ruleID,option,MethodType.CommonImport)}ImportCommonEntry(ruleID,option,methodType){const su=DataIEUtils.getSu(DataIEUtils.getBaseUrl(this.viewModel.repository)),optionJson=this.ParseImportJson(option);if(methodType===MethodType.ImportSubTableDetail&&isNullOrEmpty(ruleID)&&isNullOrEmpty(optionJson.importChildNodeCode)){const msg="规则id为空时，根据Vo查找规则，导入子表必须配置子表节点";return this.notify.warning(msg),Promise.reject(msg)}const postBody={body:{ruleId:ruleID,su:su,ruleType:0,voId:optionJson.voId,methodType:methodType,importChildNodeCode:optionJson.importChildNodeCode,enableRuntimeCustom:!0===optionJson.enableRuntimeCustom}},loading=FLoadingService.show({message:this.localePipe.transform("loading")});return this.http.request("POST","/api/runtime/dip/v1.0/dataIeRuleManage/getRecordRuleInfo",{headers:{},body:postBody.body}).then((recordRule=>{var _a;return null==(_a=null==loading?void 0:loading.value)||_a.close(),this.ImportByRuleImpl(ruleID,optionJson,methodType,recordRule).then((s=>{}))}),(error=>{var _a;return null==(_a=null==loading?void 0:loading.value)||_a.close(),this.notify.error(error.error.Message),Promise.reject()}))}ImportByRuleImpl(ruleID,optionJson,methodType,recordRule){return new Promise(((resolve,reject)=>{if(methodType===MethodType.DownloadImportTemplate){this.loadingService.show();const ruleId=recordRule.ruleId?recordRule.ruleId:ruleID;DataIEUtils.getSu(DataIEUtils.getBaseUrl(this.viewModel.repository)),isNullOrEmpty(optionJson.su)&&!isNullOrEmpty(this.viewModel.repository)&&(optionJson.su=DataIEUtils.getSu(DataIEUtils.getBaseUrl(this.viewModel.repository))),this.dataImportService.DownloadTemplate4HandCraft(ruleId,optionJson,null,this.viewModel.repository);const unSubscriber=this.dataImportService.complete.subscribe((data=>{this.loadingService.clearAll(),""===data?(this.notify.success(this.i18nResource.download_template_ok),unSubscriber.unsubscribe(),subject.next(!0),subject.complete()):(this.notify.error(this.i18nResource.download_template_fail+data),unSubscriber.unsubscribe(),subject.next(!1),subject.complete())}))}else{if(void 0!==optionJson.customImportTitle&&null!==optionJson.customImportTitle){if(optionJson.customImportTitle.length>20){const msg=this.localePipe.transform("export_title_length");return this.notify.warning(msg),void reject(msg)}if(DataIEUtils.JudgeIllegalChar(optionJson.customImportTitle)){const msg=this.localePipe.transform("export_title_unjudge");return this.notify.warning(msg),void reject(msg)}}const args=new ImportDialogArgs;args.ruleId=ruleID,args.importOption=optionJson,args.recordRule=recordRule,args.validateScriptPath=recordRule.validateScriptPath,args.enableReimport=recordRule.enableReimport,args.methodType=methodType;const vnode=h(ImportDialogSelector,{args:args}),modalService=this.injector.get(MODAL_SERVICE_TOKEN),modalOptions={title:this.localePipe.transform("data_import"),width:580,height:242,showCloseButton:!0,showMaxButton:!1,showMinButton:!1,fitContent:!1,showButtons:!0,resizeable:!0};modalOptions.render=app=>(app.provide("ViewModel",this.viewModel),app.provide("LocalePipe",this.localePipe),vnode),modalOptions.acceptCallback=()=>{const response=vnode.component.exposed.response(),commonParams=new CommonParams;return commonParams.methodType=methodType,commonParams.ruleId=ruleID,commonParams.option=response.importOption,commonParams.repository=this.viewModel.repository,commonParams.suffix=response.suffix,commonParams.enableReimport=!1,commonParams.fileBase64Str=response.fileBase64Str,commonParams.fileName=response.fileName,this.dataImportService.ImportByRuleId(commonParams)},modalOptions.rejectCallback=()=>{},modalService.open(modalOptions)}}))}CommonDataExport(type,ruleID,option){if(1===type)this.notify.info("不支持当前配置，请配置导出命令构件类型为0");else if(2===type)this.notify.info("不支持当前配置，请配置导出命令构件类型为0");else{if(3!==type)return this.ExportByRule(ruleID,option);this.notify.info("不支持当前配置，请配置导出命令构件类型为0")}}ExportByRule(ruleID,option){return this.ExportCommonEntry(ruleID,option,MethodType.CommonExport)}ExportCommonEntry(ruleID,option,methodType){const optionJson=this.ParseExportJson(option);if(isNullOrEmpty(ruleID)&&isNullOrEmpty(optionJson.voId)&&this.notify.warning(this.localePipe.transform("ruleid_void_empty_error")),isNullOrEmpty(optionJson.su)&&(optionJson.su=DataIEUtils.getSu(DataIEUtils.getBaseUrl(this.viewModel.repository))),methodType!==MethodType.ExportCurrentChildData||!isNullOrEmpty(ruleID)||!isNullOrEmpty(optionJson.exportChildNodeCode))return this.ExportByRuleImpl(ruleID,optionJson,methodType);this.notify.warning(this.localePipe.transform("vo_child_node_empty_error"))}ExportByRuleImpl(ruleID,optionJson,mType,recordRule){if(void 0!==optionJson.customExportTitle&&null!==optionJson.customExportTitle){if(optionJson.customExportTitle.length>20)return void this.notify.warning(this.localePipe.transform("export_title_length"));if(DataIEUtils.JudgeIllegalChar(optionJson.customExportTitle))return void this.notify.warning(this.localePipe.transform("export_title_unjudge"))}if(isNullOrEmpty(recordRule)||isNullOrEmpty(recordRule.ruleId)||(ruleID=recordRule.ruleId),mType==MethodType.CommonExport)return this.dataExportService.ExportById(ruleID,optionJson);MethodType.ExportCurrentTableData}ParseExportJson(option){const voId=this.viewModel.id;if(isNullOrEmpty(option)||isNullOrEmpty(option))return{filter:null,voId:voId,customfilename:"",applyui:!1,vsto:!1};let optionJson=null;try{optionJson=JSON.parse(option)}catch{return this.notify.warning(this.localePipe.transform("params_json_error")),{filter:null,voId:voId,customfilename:"",applyui:!1,vsto:!1}}return"object"!=typeof optionJson&&(optionJson={}),optionJson.voId=voId,optionJson}ListFormExport(ruleID,option){}CardDataImport(dataId,ruleID,option){}DownloadTemplate(ruleID,option){}ParseImportJson(option){if("object"==typeof option)return option;if(isNullOrEmpty(option))return{};let optionJson=null;try{optionJson=JSON.parse(option)}catch{return this.notify.warning(this.i18nResource.params_json_error),{}}"object"!=typeof optionJson&&(optionJson={});let globalParam=optionJson.globalParam;return null!=globalParam&&(optionJson.GlobalParam=globalParam),optionJson}submitWithBizDefKey(dataId,bizDefKey){const param={dataId:dataId,bizDefKey:bizDefKey};return new Promise(((resolve,reject)=>{if(this.isParamVaild(param))return this.loadStartProcessJs().then((()=>{window.gspWfService.submit(param,(notifyParam=>{this.notifyMessage(notifyParam,resolve,reject)}))})).catch((error=>{reject(error)}));reject(this.localePipe.transform("invalidParam"))}))}cancelSubmitWithDataId(dataId,bizDefKey){const param={dataId:dataId,bizDefKey:bizDefKey};return new Promise(((resolve,reject)=>{if(this.isParamVaild(param))return this.loadStartProcessJs().then((()=>{window.gspWfService.cancelSubmit(param,(notifyParam=>{this.notifyMessage(notifyParam,resolve,reject)}))})).catch((error=>{reject(error)}));reject(this.localePipe.transform("invalidParam"))}))}batchSubmitWithBizDefKey(dataIds,bizDefKey){const param={dataIds:dataIds,bizDefKey:bizDefKey};return new Promise(((resolve,reject)=>{if(this.isParamVaild(param))return this.loadStartProcessJs().then((()=>{window.gspWfService.batchSubmit(param,(notifyParam=>{this.notifyMessage(notifyParam,resolve,reject)}))})).catch((error=>{reject(error)}));reject(this.localePipe.transform("invalidParam"))}))}batchCancelSubmitWithDataId(dataIds,bizDefKey){const param={dataIds:dataIds,bizDefKey:bizDefKey};return new Promise(((resolve,reject)=>{if(this.isParamVaild(param))return this.loadStartProcessJs().then((()=>{window.gspWfService.batchCancelSubmit(param,(notifyParam=>{this.notifyMessage(notifyParam,resolve,reject)}))})).catch((error=>{reject(error)}));reject(this.localePipe.transform("invalidParam"))}))}childSubmit(parentDataId,childDataId,bizDefKey){const params={dataId:parentDataId+","+childDataId,bizDefKey:bizDefKey};return new Promise(((resolve,reject)=>{if(this.isParamVaild(params))return this.loadStartProcessJs().then((()=>{window.gspWfService.submit(params,(notifyParam=>{this.notifyMessage(notifyParam,resolve,reject)}))})).catch((error=>{reject(error)}));reject(this.localePipe.transform("invalidParam"))}))}childCancelSubmit(parentDataId,childDataId,bizDefKey){const params={dataId:parentDataId+","+childDataId,bizDefKey:bizDefKey};return new Promise(((resolve,reject)=>{if(this.isParamVaild(params))return this.loadStartProcessJs().then((()=>{window.gspWfService.cancelSubmit(params,(notifyParam=>{this.notifyMessage(notifyParam,resolve,reject)}))})).catch((error=>{reject(error)}));reject(this.localePipe.transform("invalidParam"))}))}childBatchSubmit(parentDataId,childDataIds,bizDefKey){const dataIds=childDataIds.map((childDataId=>`${parentDataId},${childDataId}`));let params={dataIds:dataIds,bizDefKey:bizDefKey};return new Promise(((resolve,reject)=>{if(this.isParamVaild(params))return this.loadStartProcessJs().then((()=>{this.batchSubmitWithBizDefKey(dataIds,bizDefKey).then((result=>{})).catch((error=>{reject(error)}))})).catch((error=>{reject(error)}));reject(this.localePipe.transform("invalidParam"))}))}childBatchCancelSubmit(parentDataId,childDataIds,bizDefKey){const dataIds=childDataIds.map((childDataId=>`${parentDataId},${childDataId}`));let params={dataIds:dataIds,bizDefKey:bizDefKey};return new Promise(((resolve,reject)=>{if(this.isParamVaild(params))return this.loadStartProcessJs().then((()=>{this.batchCancelSubmitWithDataId(dataIds,bizDefKey).then((result=>{})).catch((error=>{reject(error)}))})).catch((error=>{reject(error)}));reject(this.localePipe.transform("invalidParam"))}))}viewFlowChartWithForecast(bizDefKey,dataId){const params={bizDefKey:bizDefKey,dataId:dataId,withTitle:"true"};this.openFlowChartMenu(params)}viewFlowChartByDataId(dataId){const params={dataId:dataId,withTitle:"true"};this.openFlowChartMenu(params)}openFlowChartMenu(params){if(this.isParamVaild(params)){const options={appType:"menu",funcId:"WFViewFlowChart",appId:"",appEntrance:"",isNewTab:!0,tabId:(new Date).getTime().toString(),queryStringParams:new Map(Object.entries(params))};this.runtimeFrameWorkService.openMenu(options)}}async viewApprovalLog(dataId,style){if(!dataId)return void FMessageBoxService.error(this.localePipe.transform("dataIdIsNull"),"");if(!style)return void FMessageBoxService.error(this.localePipe.transform("styleIsNull"),"");if(!window.gspTaskCenterBizLog)try{await module.import("/platform/runtime/wf/webapp/mobiletaskcenter/taskComment.js?v="+Math.random())}catch(error){}const modalService=this.injector.get(MODAL_SERVICE_TOKEN),vnode=h(TaskCommentSelector,{style:{width:"100%",height:"100%",position:"relative",overflow:"auto",padding:"8px 20px 8px 20px"}});let modalOptions={fitContent:!1,width:"datagrid"==style?960:520,height:"datagrid"==style?500:520,showButtons:!1,title:this.localePipe.transform("approvalLog"),showMaxButton:!0,resizeable:!0,render:app=>(app.provide(GSP_WF_VIEWMODEL_INJECTION_TOKEN,this.viewModel),vnode)};if(modalService.open(modalOptions),"datagrid"===style){const params={bizInstId:dataId,container:vnode.el,showHeader:!1,showViewProcess:!1};window.gspTaskCenterBizLog.ApprovalLogUtil.getWebHtml(params).then((()=>{}))}else{const payload={bizInstId:dataId,container:vnode.el,terminal:"web",typeId:"wf",isIncludeHistory:!1,ifForecast:!1,showHeader:!1,showForecastBtn:!0};window.gspTaskCenterBizLog.ApprovalLogUtil.getWebLogsHtml(payload).then((()=>{}))}}loadStartProcessJs(){return new Promise(((resolve,reject)=>{window.gspWfService?resolve(!0):module.import("/platform/runtime/task/web/start-process/gsp.wf.js?v="+Math.random()).then((()=>{resolve(!0)})).catch((error=>{reject(error)}))}))}notifyMessage(notifyParam,resolve,reject){const notifyService=this.injector.get(NOTIFY_SERVICE_TOKEN,null);let message=notifyParam.message;switch(notifyParam.type){case"success":null==notifyService||notifyService.success(message),resolve(!0);break;case"warning":null==notifyService||notifyService.warning(message),resolve(!0);break;case"error":null==notifyService||notifyService.error(message),reject(message)}}isParamVaild(params){const keys=Object.keys(params);return keys.includes("dataId")&&!params.dataId?(FMessageBoxService.error(this.localePipe.transform("dataIdIsNull"),""),!1):keys.includes("bizDefKey")&&!params.bizDefKey?(FMessageBoxService.error(this.localePipe.transform("bizDefKeyIsNull"),""),!1):!!(!keys.includes("dataIds")||params.dataIds&&params.dataIds.length)||(FMessageBoxService.error(this.localePipe.transform("dataIdsEmpty"),""),!1)}}exports("DataImportExportService",DataImportExportService);const LANG_RESOURCES={"zh-CHS":{import_file:"导入文件",choose_file:"选择文件",import_placeholder:"请选择需要导入的文件",file_type:"文件类型",last_modify_date:"最后修改时间",download_template:"下载模板",ie_cancel:"取消",ie_ok:"确定",data_import:"数据导入",data_export:"数据导出",data_export_customfile:"自选文件类型数据导出",choose_truefile:"请重新选择上传的文件",download_template_ok:"下载模板成功",download_template_fail:"下载模板失败！失败原因：",upload_fail:"上传失败！原因：",import_success:"文件导入成功！",import_form_success:"数据导入表单成功！",import_fail:"文件导入失败！失败原因：",master_table_noid:"未获取到主表ID值，请确认主表是否有数据",sub_table_noid:"未获取到子表ID值，请确认子表是否存在选中数据",sub_table_error:"导入子子表所需子表组件标识importChildChildNodeCode配置错误，请确认",bind_form_error:"绑定数据错误，请检查数据是否有重复",validate_result_show:"校验结果展示",extract_result_show:"文件抽取结果",file_download_fail:"文件下载失败",export_success:"导出成功，请等待文件下载完成",export_fail:"导出失败！失败原因：",download_vsto:"下载Excel组件",export_pdf:"导出PDF",order:"序号",validate_type:"校验类型",table_name:"对象名称",illegal_data_location:"数据校验信息",validate_result:"校验结果.pdf",wps_table:"WPS 表格(*.et)",wps_table_m:"WPS 表格（模板文件）(*.ett)",customexport_no_column:"【选中信息项】未包含有效的字段，请选择！",vsto_component:"数据导出组件.msi",data_entity:"数据实体",selected_item:"选中信息项",upload_filetype_nomatch:"上传的文件与选择的文件类型不匹配",save_customrule_fail:"保存自定义导入规则失败，失败原因：",ruleselect_code:"编号",ruleselect_name:"名称",ruleselect_rulesource:"规则来源",rule_type:"规则类型",ruleselect_syspreset:"系统预置",ruleselect_custom:"自定义",ruleselect_billcategory:"单据种类",ruleselect_creator:"创建人",ruleselect_filetype:"文件类型",ruleselect_selectrulenotify:"请选择一条规则",rule_list:"规则选择",password_set:"密码设置",package_set:"批次大小",password_tip:"密码为空时将使用导出规则配置的默认密码",select_data_export:"请先选择要导出的数据",without_rule_component_select_error:"前端组件获取失败,请检查formComponent配置是否正确",without_rule_element_error:"列表组件取数失败，所选组件无字段数据",without_rule_component_illegal:"列表组件配置无效，不支持的组件类型！",select_xlsx_notify:"该文件类型不支持宏设置，会导致部分下拉帮助失效，请知晓",ruleselect_updatetime:"更新时间",ruleselect_listempty:"可选规则列表为空",import_loading:"正在导入...",after_validate_suspend:"请修改文件重新进行导入",MandatoryImport:"强制导入",Ignore:"忽略",AllMandatoryImport:"全部强制导入",AllIgnore:"全部忽略",export_validate_result_pdf:"导出校验结果(PDF)",export_validate_result_excel:"导出校验结果(EXCEL)",continue:"继续",cancel:"中止",you_have:"您有",warning_info_1:"条校验不符合项，如继续执行，",warning_info_2_core:"被忽略的数据将不会被导入系统，强制导入可能会影响数据的质量",error_info:"条校验不符合项，请参照校验信息修改导入模板",security_level:"密级等级",importfile_toobig:"当前导入文件太大，最大允许的文件大小：","partdata-import-success":"部分数据导入完成",operation:"操作",extend_operation:"扩展操作",product_preset:"产品预置",project_define:"项目定义",user_custom:"用户自定义",base_rule:"基础规则",extend_rule:"扩展规则",isNot_null:"不能为空",length_exceed:"长度超出限制",forever_error:"字段不允许在扩展规则中删除",template_code:"模板编号",template_name:"模板名称",get_form_data_failed:"未获取到有效的前端表单，请确认表单名称和表格id配置是否正确",enable_filter_by_front:"按表单字段导出",enable_private_extend_rule:"仅当前用户可见",move_up:"上移",move_down:"下移",move_top:"置顶",move_bottom:"置底",save_success:"保存成功",object_name:"对象名称",column_name:"列名",export_template_config:"导出模板配置",delete_success:"删除成功",delete_success_after_ok:"删除成功，点击确定后生效",create_extend:"创建扩展",modify:"修改",delete:"删除",confirm_delete:"确认删除?",check_all:"全选",uncheck_all:"全不选",select_one:"主对象至少选择一个字段",extend_rule_create_error:"冲突场景不允许创建扩展规则，原因：基础规则配置了多级表头、带模板导出或自定义抽取构件",export_range_set:"导出范围",export_page_all:"全部",export_page_current:"当前页",export_row_custom:"自定义",export_row_checked:"选中行",export_checked_null:"选中行信息配置错误或未选中数据，请确认",export_current_null:"当前页信息配置错误，请确认",export_total_data_null:"未获取到打包导出信息，执行默认导出逻辑",extend_set:"高级设置",export_page_error:"自定义导出范围设置有误，请检查",data_list_export:"列表导出",export_current_data_component_error:"导出选中行错误，当前场景请配置正确的导出参数：selectFilterGridComponent",export_custom_tip:"数据总条数",export_setting:"导出设置",export_element_setting:"导出字段设置",rule_name_error:"规则名称存在不允许的字符或词组，请检查",template_param_empty_error:"模板下载参数缺失，请确认",validate_data_empty_error:"校验数据为空",download_excel_validate_result_error:"下载excel校验文件错误",http_empty_error:"web调用异常，无http且未获取到repository",return_data_empty:"返回数据为空",pack_info_empty:"请传入打包信息",pack_rule_id_empty:"请传入需要打包导出的规则id",params_json_error:"传入的参数转换JSON对象异常，已使用默认参数",export_failed:"数据导出失败！失败原因：服务端响应异常，请检查",child_need_parentid:"主从表导入场景中，以下从表未带ParentID字段：",can_not_export_current:"不支持导出当前页",can_not_export_child:"不支持导出子表数据",type_unsupport:"不支持配置该类型:",okButtonCallback_error:"表单扩展构件执行失败，请咨询系统管理员",ruleid_void_empty_error:"未配置规则id，且无法获取到表单对应的voId，请检查",vo_child_node_empty_error:"规则id为空时，根据Vo查找规则，导出子表必须配置子表节点",export_title_length:"自定义标题名称的长度太长",export_title_unjudge:"自定义标题名称存在不合法字符",model_child_node_empty_error:"规则id为空时，根据模型查找规则，导入子表必须配置子表节点",pack_data_size_error:"未获取到总数据量或数据量为0，无法进行分批打包导出，请检查",import_main_id_empty_error:"未读取到主表数据id，不允许导入",extend_rule_delete_error:"扩展规则删除出错",export_data_size_exceed:"导出数据量超出限制，采取截断策略丢弃多余的数据",import_child_child_node_code_empty:"导入子子表数据须配置子表对象编号，请联系表单开发人员",form_layer_error:"获取表单多级表头失败，继续导出将采用无多级表头方案",form_layer_error_ass:"获取表单多级表头失败，继续导出将采用无多级表头方案，按表单字段导出不支持对带出子字段进行拆分",export_mode_error:"exportMode参数设置无效，将采用默认值"},"zh-CHT":{import_file:"導入文件",choose_file:"選擇文件",import_placeholder:"請選擇需要導入的文件",file_type:"文件類型",last_modify_date:"最後修改時間",download_template:"下載模板",ie_cancel:"取消",ie_ok:"確定",data_import:"數據導入",data_export:"數據導出",data_export_customfile:"自選文件類型數據導出",choose_truefile:"請重新選擇上傳的文件",download_template_ok:"下載模板成功",download_template_fail:"下載模板失敗！失敗原因：",upload_fail:"上傳失敗！原因：",import_success:"文件導入成功！",import_form_success:"文件導入表單成功！",import_fail:"文件導入失敗！失敗原因：",master_table_noid:"未獲取到主表ID值，請確認主表是否有數據",sub_table_noid:"未獲取到子表ID值，請確認子表是否有數據",sub_table_error:"導入子子表所需子表組件標識importChildChildNodeCode配置錯誤，請確認",bind_form_error:"綁定數據錯誤，請檢查數據是否有重複",validate_result_show:"校驗結果展示",extract_result_show:"文件抽取結果",file_download_fail:"文件下載失敗",export_success:"導出成功，請等待文件下載完成",export_fail:"導出失敗！失敗原因：",download_vsto:"下載Excel組件",export_pdf:"導出PDF",order:"序號",validate_type:"校驗類型",table_name:"對象名稱",illegal_data_location:"數據校驗信息",validate_result:"校驗結果.pdf",wps_table:"WPS 表格(*.et)",wps_table_m:"WPS 表格（模板文件）(*.ett)",customexport_no_column:"【選中信息項】未包含有效的字段，請選擇！",vsto_component:"數據導出組件.msi",data_entity:"數據實體",selected_item:"選中信息項",upload_filetype_nomatch:"上傳的文件與選擇的文件類型不匹配",save_customrule_fail:"保存自定義導入規則失敗，失敗原因：",ruleselect_code:"編號",ruleselect_name:"名稱",ruleselect_rulesource:"規則來源",rule_type:"規則類型",ruleselect_syspreset:"系統預置",ruleselect_custom:"自定義",ruleselect_billcategory:"單據種類",ruleselect_creator:"創建人",ruleselect_filetype:"文件類型",ruleselect_selectrulenotify:"請選擇一條規則",rule_list:"規則選擇",password_set:"密碼設置",password_tip:"密碼為空時將用戶code作為默認密碼",forever_error:"字段不允許在擴展規則中刪除",select_data_export:"請先選擇要導出的數據",without_rule_component_select_error:"無規則導出前端取數失敗,component配置有誤,請檢查",without_rule_element_error:"無規則導出前端取數失敗:所選組件不包含正確的viewModel信息,請檢查",without_rule_component_illegal:"無規則導出組件配置無效,不支持的組件類型!",select_xlsx_notify:"該文件類型不支持宏設置，會導致部分下拉幫助失效，請知曉",ruleselect_updatetime:"更新時間",ruleselect_listempty:"可選規則列表為空",import_loading:"正在導入...",after_validate_suspend:"請修改文件重新進行導入",MandatoryImport:"強制導入",Ignore:"忽略",AllMandatoryImport:"全部強制導入",AllIgnore:"全部忽略",export_validate_result_pdf:"導出校驗結果(PDF)",export_validate_result_excel:"導出校驗結果(EXCEL)",continue:"繼續",cancel:"中止",you_have:"您有",warning_info_1:"條校驗不符合項，如繼續執行，",warning_info_2_core:"被忽略的數據將不會被導入系統，強制導入可能會影響數據的質量",error_info:"條校驗不符合項，請參照校驗信息修改導入模板",security_level:"密級等級",importfile_toobig:"當前導入文件太大，最大允許的文件大小：","partdata-import-success":"部分數據導入完成",operation:"操作",extend_operation:"操作",product_preset:"產品預置",project_define:"項目定義",user_custom:"用戶自定義",base_rule:"基礎規則",extend_rule:"擴展規則",isNot_null:"不能為空",template_code:"模板編號",template_name:"模板名稱",get_form_data_failed:"未獲取到有效的前端表單，請確認表單名稱和表格id配置正確",move_up:"上移",move_down:"下移",move_top:"置頂",move_bottom:"置底",save_success:"保存成功",object_name:"對象名稱",column_name:"列名",export_template_config:"導出模板配置",delete_success:"刪除成功",delete_success_after_ok:"刪除成功，點擊確定後生效",create_extend:"創建擴展",modify:"修改",delete:"刪除",confirm_delete:"確認刪除?",extend_set:"高級導出設置",check_all:"全選",uncheck_all:"全不選",select_one:"主對象至少選擇一個字段",extend_rule_create_error:"當前規則設置了多級表頭或者寫文件構件，不允許創建擴展規則",enable_filter_by_front:"按表單字段導出",enable_private_extend_rule:"僅當前用戶可見",export_setting:"導出設置",export_element_setting:"導出字段設置",export_range_set:"導出範圍",export_page_all:"全部",export_page_current:"當前頁",export_row_custom:"自定義",export_row_checked:"選中行",export_checked_null:"未獲取到選中行信息，請聯繫表單配置人員確認",export_current_null:"未獲取到當前頁信息，請聯繫表單配置人員確認",export_total_data_null:"未獲取到打包導出信息，執行默認導出邏輯",export_page_error:"自定義導出範圍設置有誤，請檢查",data_list_export:"列表導出",export_current_data_component_error:"導出選中行錯誤，當前場景請配置正確的導出參數：selectFilterGridComponent",export_custom_tip:"數據總條數",rule_name_error:"規則名稱存在不允許的字符，請檢查",param_empty_error:"模板下載參數缺失，請確認",data_empty_error:"校驗數據為空",download_excel_validate_result_error:"下載excel校驗文件錯誤",http_empty_error:"web調用異常，無http且未獲取到repository","return-data-empty":"返回數據為空",pack_info_empty:"請傳入打包信息",pack_rule_id_empty:"請傳入需要打包導出的規則id",params_json_error:"傳入的參數轉換JSON對象異常，已使用默認參數",export_failed:"數據導出失敗！失敗原因：服務端響應異常，請檢查",child_need_parentid:"'主從表導入場景中，以下從表為帶ParentID字段：",can_not_export_current:"不支持導出當前頁",can_not_export_child:"不支持導出子表數據",type_unsupport:"不支持配置該類型:",okButtonCallback_error:"表單擴展構件執行失敗，請咨詢係統管理員",ruleid_void_empty_error:"未配置規則id，且無法獲取到表單對應的voId，請檢查",vo_child_node_empty_error:"規則id為空時，根據Vo查找規則，導出子表必須配置子表節點",export_title_length:"自定義標題名稱的長度太長",export_title_unjudge:"自定義標題名稱存在不合法字符",model_child_node_empty_error:"規則id為空時，根據模型查找規則，導入子表必須配置子表節點",pack_data_size_error:"為獲取到數據總量或數據量為0，無法進行分批打包導出，請檢查",import_main_id_empty_error:"未獲取到主表id，不允許導入",extend_rule_delete_error:"擴展規則刪除出錯",export_data_size_exceed:"導出數據量超出限製，丟棄多餘數據",import_child_child_node_code_empty:"導入子子表須配置子表對象編號，請聯繫表單開發人員",form_layer_error:"獲取表單多級表頭失敗，繼續導出將采用無多級表頭方案",form_layer_error_ass:"獲取表單多級表頭配置失敗，繼續導出將採用無多級表頭方案，按表單字段導出不支持對帶出子字段進行拆分",export_mode_error:"exportMode參數設置錯誤，將使用默認值"},en:{import_file:"Import File",choose_file:"Choose File",import_placeholder:"please select the file to be imported",file_type:"File Type",last_modify_date:"last modify date",download_template:"Download Template",ie_cancel:"Cancel",ie_ok:"OK",data_import:"Data Import",data_export:"Data Export",data_export_customfile:"Data Export For Custom FileType",choose_truefile:"Please select the correct file",download_template_ok:"Download template successful",download_template_fail:"Failed to download template! Reason for failure: ",upload_fail:"Upload failed! The reason: ",import_success:"File imported successfully!",import_form_success:"File import form succeeded!",import_fail:"File import failed! Reason for failure: ",master_table_noid:"The master table ID value is not obtained. Please confirm whether the master table has data",sub_table_noid:"The sub table ID value is not obtained. Please confirm whether the sub table has data",sub_table_error:"param importChildChildNodeCode error",bind_form_error:"Binding data error, please check for duplicate data",validate_result_show:"Display Verification",extract_result_show:"extract result",file_download_fail:"File download failed!",export_success:"Export Succeeded",export_fail:"Export failed! Reason for failure: ",download_vsto:"Download Excel Component",export_pdf:"Export PDF",order:"Order",validate_type:"Type",table_name:"Table Name",illegal_data_location:"Data Verification Information",validate_result:"validate_result.pdf",wps_table:"WPS Form(*.et)",wps_table_m:"WPS Form(Template File)(*.ett)",customexport_no_column:"[Selected Items] does not contain valid fields, please select!",vsto_component:"data_export_component.msi",data_entity:"Data Entity",selected_item:"Selected Items",upload_filetype_nomatch:"The uploaded file does not match the selected file type",save_customrule_fail:"Failed to save custom import rules, reason for failure: ",ruleselect_code:"Code",ruleselect_name:"Name",ruleselect_rulesource:"RuleSource",rule_type:"RuleType",ruleselect_syspreset:"SysPreset",ruleselect_custom:"Custom",ruleselect_billcategory:"BillCateGory",ruleselect_creator:"Creator",ruleselect_filetype:"FileType",ruleselect_selectrulenotify:"Please select a rule",rule_list:"Rule Selection",password_set:"Password",password_tip:"When the password is empty, use the user code as the default password.",select_data_export:"Please select the data to export first",select_xlsx_notify:"This file type does not support macro settings, which will cause some drop-down help to fail, please be aware",without_rule_component_select_error:"Irregular export front-end fetching failed, component configuration is incorrect, please check",without_rule_element_error:"Random export front-end fetching failed: The selected component does not contain the correct viewModel information, please check",without_rule_component_illegal:"Invalid export component configuration, unsupported component type!",ruleselect_updatetime:"UpdateTime",ruleselect_listempty:"The list of optional rules is empty",import_loading:"Import Loading...",forever_error:"Field is forbidden to delete",after_validate_suspend:"Please modify the file to import again",MandatoryImport:"MandatoryImport",Ignore:"Ignore",AllMandatoryImport:"AllMandatoryImport",AllIgnore:"AllIgnore",export_validate_result_pdf:"Export Result(PDF)",export_validate_result_excel:"Export Result(EXCEL)",continue:"Continue",cancel:"Suspend",you_have:"You have ",warning_info_1:" pieces of item that have not passed the verification. If you continue to execute, ",warning_info_2_core:" the ignored data will not be imported into the system. Forcing import may affect the quality of the data.",error_info:" pieces of item that have not passed the verification, please refer to the verification information to modify the import template.",security_level:"Security Level",importfile_toobig:"The current import file is too big, the maximum allowable file size: ","partdata-import-success":"Partial data import completed",operation:"Operation",extend_operation:"Extend Operation",product_preset:"Product Preset",project_define:"Project Defined",user_custom:"User Defined",base_rule:"base rule",extend_rule:"extend rule",isNot_null:" cannot be empty",get_form_data_failed:"Failed！Please confirm whether the form name and form id are configured correctly",template_code:"Code",template_name:"Name",move_up:"Up",move_down:"Down",move_top:"Top",move_bottom:"Bottom",save_success:"save successful",object_name:"Object Name",column_name:"Column Name",export_template_config:"Export Template Configuration",delete_success:"delete successful",delete_success_after_ok:"Delete successfully, click OK to take effect",create_extend:"Create Extension",modify:"Modify",delete:"Delete",confirm_delete:"Confirm delete?",check_all:"CheckAll",extend_set:"Export advanced settings",uncheck_all:"UncheckAll",select_one:"main object select at least one field",extend_rule_create_error:"The current rule sets a multi-level header or writes a file component, and it is not allowed to create extended rules",enable_filter_by_front:"export by list fields",enable_private_extend_rule:"private rule",export_setting:"export set",export_element_setting:"export fields set",export_range_set:"range",export_page_all:"all",export_page_current:"page",export_row_custom:"custom",export_row_checked:"checked",export_checked_null:"Selected row information not obtained,please check it",export_current_null:"current page information not obtained,please check it",export_total_data_null:"package export information not obtained,execute default export mode",export_page_error:"Incorrect custom data range setting",data_list_export:"list export",export_current_data_component_error:"checked data failed, please set correct selectFilterGridComponent",export_custom_tip:"number of data",rule_name_error:"rule name contains characters that are not allowed",param_empty_error:"params error",data_empty_error:"data is empty",download_excel_validate_result_error:"download excel validate result error",http_empty_error:"web exception，http is empty","return-data-empty":"return data is empty",pack_info_empty:"packItem info is empty",pack_rule_id_empty:"packItem info should contain rule id",params_json_error:"prams can not be convert to JSON string,default value adopted",export_failed:"export failed",child_need_parentid:"following table does not have ParentID:",can_not_export_current:"export current page is not support",can_not_export_child:"export child data is not support",type_unsupport:"this type is not allow:",okButtonCallback_error:"Form extension component execution failed, please consult the system administrator",ruleid_void_empty_error:"both rule id and vo-id are empty",vo_child_node_empty_error:"export child data must have child node",export_title_length:"title size exceed",export_title_unjudge:"title name contains illegal characters",model_child_node_empty_error:"import child data must have child node",pack_data_size_error:"data size is null or zero",import_main_id_empty_error:"main table data id is null",extend_rule_delete_error:"delete extend rule error",export_data_size_exceed:"data size is exceed",import_child_child_node_code_empty:"Importing sub table data requires configuring sub table code. Please contact the form developer",form_layer_error:"get form layer failed",form_layer_error_ass:"Failed to obtain form configuration. Exporting by form field does not support splitting of association child fields",export_mode_error:"exportMode error"}};class LocalePipe{constructor(){__publicField(this,"defaultLang","zh-CHS"),__publicField(this,"langCode"),__publicField(this,"lang"),this.langCode=localStorage.getItem("languageCode")||this.defaultLang,this.lang=LANG_RESOURCES[this.langCode]||LANG_RESOURCES[this.defaultLang]}transform(name){return this.lang[name]||name}}exports("DataIECommandProviders",[{provide:DataImportExportService,useClass:DataImportExportService,deps:[ViewModel,Injector,HttpClient,LocalePipe,RuntimeFrameworkService]},{provide:LocalePipe,useClass:LocalePipe,deps:[]},{provide:DataImportService,useClass:DataImportService,deps:[ViewModel,Injector,HttpClient,LocalePipe,RuntimeFrameworkService]},{provide:DataExportService,useClass:DataExportService,deps:[ViewModel,Injector,HttpClient,LocalePipe,RuntimeFrameworkService]}]);const GspWfStartProcessVue={install(app){[...WfTaskCommentCreationVueComponents].forEach((comp=>{app.use(comp)}))}};exports({GspWfStartProcessVue:GspWfStartProcessVue,default:GspWfStartProcessVue})}}}));
