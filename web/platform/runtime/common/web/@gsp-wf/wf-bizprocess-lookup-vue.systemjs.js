/*! Last Update Time: 2025-07-29 15:39:40 */
/*! Last Update Time: 2025-07-29 15:39:40 */
var __defProp=Object.defineProperty,__defNormalProp=(obj,key,value)=>key in obj?__defProp(obj,key,{enumerable:!0,configurable:!0,writable:!0,value:value}):obj[key]=value,__publicField=(obj,key,value)=>(__defNormalProp(obj,"symbol"!=typeof key?key+"":key,value),value);System.register(["vue","axios","@farris/ui-vue"],(function(exports,module){"use strict";var defineComponent,ref,watch,onMounted,createVNode,axios,FLookup;return{setters:[module2=>{defineComponent=module2.defineComponent,ref=module2.ref,watch=module2.watch,onMounted=module2.onMounted,createVNode=module2.createVNode},module2=>{axios=module2.default},module2=>{FLookup=module2.FLookup}],execute:function(){const WfBizProcessLookUpUiState_processUri="/api/runtime/wf/v1.0/dtProcDefs",WfBizProcessLookUpUiState_flowFormUri="/api/runtime/wf/v1.0/flowForm/query",LANG_RESOURCES={"zh-CHS":{processCategory:"流程分类",name:"名称",code:"编号"},"zh-CHT":{processCategory:"流程分類",name:"名稱",code:"編號"},en:{processCategory:"Process Category",name:"Name",code:"Code"}};const LOCALE_PIPE=new class LocalePipe{constructor(){__publicField(this,"defaultLang","zh-CHS"),__publicField(this,"langCode"),__publicField(this,"lang"),this.langCode=localStorage.getItem("languageCode")||this.defaultLang,this.lang=LANG_RESOURCES[this.langCode]||LANG_RESOURCES[this.defaultLang]}transform(name){return this.lang[name]||name}},WfBizProcessLookUp=defineComponent({name:"WfBizProcessLookUp",props:{id:String,title:{type:String,default:""},width:{type:Number,default:550},sourceType:{type:String,default:"flowform"},disabled:{type:Boolean,default:!1},multiSelect:{type:Boolean,default:!0},pagination:{type:Object,default:{enable:!0,showLimits:!0,sizeLimits:[10,20,30,50,100],size:20,index:1,total:0,mode:"server",showGoto:!1}},displayTxt:{type:String,default:""},bindingData:{type:Object,default:{id:"",name:""}},filter:{type:Object,default:{}},enableFlowFormAut:{type:Boolean,default:!1},authParams:{type:Object,default:{}},textField:{type:String,default:"name"},editable:{type:Boolean,default:!1},allowFreeInput:{type:Boolean,default:!1}},emits:["afterConfirm","afterClear","valueChange"],setup(props,context){const container=ref(),sourceType=ref(props.sourceType),dataUri=ref(),columns=ref(),searchFields=ref(),lookupRef=ref(null),bindingData=ref(props.bindingData),filter=ref(props.filter),textField=ref(props.textField),defaultCustomData=ref({sourceType:sourceType.value,filter:filter.value,enableFlowFormAut:props.enableFlowFormAut,authParams:props.authParams});switch(sourceType.value){case"process":columns.value=[{field:"bizDefName",title:LOCALE_PIPE.transform("processCategory"),resizable:!0,width:100},{field:"name",title:LOCALE_PIPE.transform("name"),resizable:!0,width:100}],dataUri.value=WfBizProcessLookUpUiState_processUri,searchFields.value=[{label:LOCALE_PIPE.transform("processCategory"),value:"bizDefName"},{label:LOCALE_PIPE.transform("name"),value:"name"}];break;case"flowform":columns.value=[{field:"code",title:LOCALE_PIPE.transform("code"),resizable:!0,width:80},{field:"name",title:LOCALE_PIPE.transform("name"),resizable:!0,width:100}],dataUri.value=WfBizProcessLookUpUiState_flowFormUri,searchFields.value=[{label:LOCALE_PIPE.transform("code"),value:"code"},{label:LOCALE_PIPE.transform("name"),value:"name"}]}watch((()=>props.bindingData),(newValue=>{bindingData.value=newValue})),onMounted((()=>{}));const onBeforeOpenDialog=$event=>({canOpen:!0,data:{filter:filter.value,sourceType:sourceType.value,enableFlowFormAut:props.enableFlowFormAut,authParams:props.authParams}}),onDictPicked=$event=>(context.emit("afterConfirm",$event),{closeDialog:!0}),onClear=$event=>{context.emit("afterClear",$event)},loader=(url,params)=>{const customData=null!=params.customData?params.customData:defaultCustomData.value,index=params.pageIndex||1,size=params.pageSize||20,searchValue=JSON.parse(params.searchValue);if(searchValue.searchValue||customData.filter){let query="?param=",filter2="{";if(searchValue.searchValue&&("*"===searchValue.searchField?customData&&"flowform"===customData.sourceType?filter2+=`"allColumns":"${searchValue.searchValue}",`:filter2+=`"bizDefNameOrProcName":"${searchValue.searchValue}",`:customData&&"flowform"===customData.sourceType?filter2+=`"${searchValue.searchField}":"${searchValue.searchValue}",`:"bizDefName"===searchValue.searchField?filter2+=`"bizDefNameLike":"${searchValue.searchValue}",`:filter2+=`"processNameLike":"${searchValue.searchValue}",`),customData&&customData.filter){const keys=Object.keys(customData.filter);if(keys&&keys.length>0)for(const key of keys)customData.filter[key]&&(filter2+=`"${key}":"${customData.filter[key]}",`)}customData&&"flowform"===customData.sourceType&&customData.enableFlowFormAut&&(filter2+='"enableFlowFormAut":true,',customData.authParams&&(filter2+=`"authParams":${JSON.stringify(customData.authParams)},`)),filter2&&","===filter2[filter2.length-1]&&(filter2=filter2.substring(0,filter2.length-1)),filter2+="}",filter2=encodeURIComponent(filter2),query+=filter2+`&pageNum=${index}&pageSize=${size}`,url+=query}else url+=`?pageNum=${index}&pageSize=${size}`;return customData&&"process"===customData.sourceType&&customData.authParams&&(url+=`&authParams=${JSON.stringify(customData.authParams)}`),axios.get(url).then((re=>({items:re.data.content,total:re.data.totalElements,displayType:"LIST",columns:columns.value,pageInfo:{pageIndex:re.data.pageable.pageNumber+1,pageSize:re.data.pageable.pageSize,pageList:[10,20,30,50,100],enablePager:!0}})))};return()=>createVNode("div",{ref:container},[createVNode(FLookup,{id:"lookup",title:props.title,disabled:props.disabled,ref:lookupRef,uri:dataUri.value,multiSelect:props.multiSelect,columns:columns.value,modelValue:bindingData.value.name,"onUpdate:modelValue":[$event=>bindingData.value.name=$event,newValue=>{context.emit("valueChange",newValue)}],idValue:bindingData.value.id,"onUpdate:idValue":$event=>bindingData.value.id=$event,displayType:"LIST","text-field":textField.value,"id-field":"id",pagination:props.pagination,width:props.width,"dict-picking":onBeforeOpenDialog,"dict-picked":onDictPicked,"enable-search-bar":!0,"show-all-search-columns":!0,"search-fields":searchFields.value,editable:props.editable,loader:loader,onClear:onClear,allowFreeInput:props.allowFreeInput},null)])}}),wfBizprocessLookupComponent=exports("wfBizprocessLookupComponent",(component=>{const c=component;return c.install=function(app){app.component(c.name,component)},component})(WfBizProcessLookUp));exports("default",{install(app){[wfBizprocessLookupComponent].forEach((comp=>{app.use(comp)}))}})}}}));
